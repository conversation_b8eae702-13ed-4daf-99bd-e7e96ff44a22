import { Form, Input, InputNumber, InputRef, Space, Typography } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

interface IBarcodeInput {
  onPressEnter: (n, qty) => void;
  // eslint-disable-next-line react/require-default-props
  range?: number[];
  innerRef?: React.RefObject<any>;
  enableBarcodeScanCutoff?: boolean;
  barcodeCutoffBeforeDigits?: number;
  barcodeCutoffAfterDigits?: number;
}

const { Text } = Typography;

const BarcodeInput: React.FC<IBarcodeInput> = (props) => {
  const {
    onPressEnter,
    range,
    innerRef,
    enableBarcodeScanCutoff,
    barcodeCutoffBeforeDigits,
    barcodeCutoffAfterDigits,
  } = props;
  const [form] = Form.useForm();
  let [min, max] = [-50, 50];
  if (range && range?.length > 0) {
    [min, max] = range;
  }

  const inputRef = useRef<InputRef>(null);

  const [aliasValue, setAliasValue] = useState('');
  const [isValidBarcode, setIsValidBarcode] = useState(false);

  useEffect(() => {
    inputRef.current?.focus();
  }, [inputRef]);

  const sliceAlias = useMemo(() => {
    if (enableBarcodeScanCutoff && aliasValue) {
      const startPos = Number(barcodeCutoffBeforeDigits);
      const endPos = aliasValue.length - Number(barcodeCutoffAfterDigits);
      return aliasValue.substring(startPos, endPos) || '';
    }
    return '';
  }, [enableBarcodeScanCutoff, barcodeCutoffBeforeDigits, barcodeCutoffAfterDigits, aliasValue]);

  useImperativeHandle(innerRef, () => ({
    reset: () => {
      form.resetFields();
    },
    focus: () => {
      inputRef.current?.focus();
    },
  }));

  const validateBarcodeCutoff = useCallback(
    (_, value) => {
      value = value.trim();
      if (enableBarcodeScanCutoff && value) {
        const minRequiredLength = Number(barcodeCutoffBeforeDigits) + Number(barcodeCutoffAfterDigits);
        if (value.length <= minRequiredLength) {
          setIsValidBarcode(false);
          return Promise.reject(
            i18n.t('global.barcodeTooShort', {
              // 至少需要输入minRequiredLength + 1位，保证条码不为空
              required: minRequiredLength + 1,
              current: value.length,
            }),
          );
        }
      }
      setIsValidBarcode(true);
      return Promise.resolve();
    },
    [enableBarcodeScanCutoff, barcodeCutoffBeforeDigits, barcodeCutoffAfterDigits],
  );

  const handlePressEnter = () => {
    const formData = form.getFieldsValue();
    if (onPressEnter) {
      const barcode = enableBarcodeScanCutoff ? sliceAlias : formData.barcode;
      onPressEnter(barcode, formData.operationQty);
    }
    setAliasValue('');
    form.setFieldsValue({ barcode: '' });
  };
  return (
    <div>
      <Form form={form}>
        <Space direction="horizontal" align="start">
          <Form.Item
            name="barcode"
            rules={[{ validator: validateBarcodeCutoff }]}
            extra={
              enableBarcodeScanCutoff ? (
                <p className="flex flex-col">
                  <span>
                    {i18n.t('global.barcodeCutoffTips', {
                      prefix: barcodeCutoffBeforeDigits,
                      after: barcodeCutoffAfterDigits,
                    })}
                  </span>
                  {enableBarcodeScanCutoff && aliasValue && sliceAlias && isValidBarcode && (
                    <span>
                      {i18n.t('global.sliceAlias')}
                      <span className="text-base font-bold">{sliceAlias}</span>
                    </span>
                  )}
                </p>
              ) : undefined
            }
            style={{ marginBottom: 0 }}
          >
            <Input
              style={{ width: 300 }}
              size="large"
              placeholder={i18n.t('global.scanBarcode')}
              autoFocus
              onPressEnter={handlePressEnter}
              autoComplete="off"
              onChange={(e) => setAliasValue(e.target.value)}
              ref={inputRef}
            />
          </Form.Item>
          <Form.Item name="operationQty" style={{ marginBottom: 0 }} initialValue={1}>
            <InputNumber min={min} max={max} size="large" precision={0} />
          </Form.Item>
          <div className="flex h-12 items-center">
            <Text type="secondary">{`${i18n.t('global.operateQty')}(${min}~${max})`}</Text>
          </div>
        </Space>
      </Form>
    </div>
  );
};

export default BarcodeInput;
