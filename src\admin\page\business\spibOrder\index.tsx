import { Button, DatePicker, Input, Space } from 'antd';
import * as Spib<PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/api/sample/SpibOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Spin from 'common/components/Spin';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import AddDrawer from './components/AddDrawer';
import { useCommonFn } from './common';

const SpibOrder: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [addDrawerOpen, setAddDrawerOpen] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [permission] = usePermission('A:P:IB');
  const createPermission = permission.codes.includes('CREATE');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');
  const resetPermission = permission.codes.includes('RESET');
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([
    { code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER', defaultValue: 7 },
  ]);
  const { confirm, reset, cancel } = useCommonFn();

  const navigate = useNavigate();

  let defaultPartnerValue: string[] = [];

  if (currentUser.type === 'WAREHOUSE') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(async (params: any, tabActiveKey: string) => {
    if (params.createDateRange) {
      params.createdStart = params.createDateRange[0].startOf('day');
      params.createdEnd = params.createDateRange[1].endOf('day');
    }
    delete params.createDateRange;

    if (params.code) {
      params.code = `%${params.code}%`;
    }
    const payload = {
      ...params,
    };

    switch (tabActiveKey) {
      case 'ALL':
        payload.status = ['NEW', 'PROCESSING', 'FINISHED'];
        break;
      case 'NEW':
        payload.status = ['NEW'];
        break;
      case 'PROCESSING':
        payload.status = ['PROCESSING'];
        break;
      case 'FINISHED':
        payload.status = ['FINISHED'];
        break;
      case 'CANCELLED':
        payload.status = ['CANCELED'];
        break;
      default:
        break;
    }

    return SpibOrderApi.List(payload);
  }, []);

  const addBtnOnClick = () => {
    setAddDrawerOpen(true);
  };

  const addDrawerOnClose = () => {
    setAddDrawerOpen(false);
  };

  const confirmBtnOnClick = async (record) => {
    if (confirm) await confirm(record.id, record.code);
    powerTableRef.current?.load();
  };

  const resetBtnOnClick = async (record) => {
    if (reset) await reset(record.id, record.code);
    powerTableRef.current?.load();
  };
  const deleteBtnOnClick = async (record) => {
    if (cancel) await cancel(record.id, record.code);
    powerTableRef.current?.load();
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smCode',
      label: i18n.t('global.sampleCode'),
      inputComponent: <Input />,
    },
    {
      name: 'fromPartnerIds',
      label: i18n.t('global.from'),
      inputComponent: <PartnerSelect types={['WAREHOUSE']} multiple />,
    },
    {
      name: 'partnerIds',
      label: i18n.t('global.to'),
      inputComponent: <PartnerSelect types={['WAREHOUSE']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'orderTypeId',
      label: i18n.t('global.orderType'),
      inputComponent: <OrderTypeSelect type="IB" categoryCode="WAREHOUSE" valueField="id" />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      width: 200,
      fixed: 'left',
    },
    {
      title: i18n.t('global.sourceOrderCode'),
      width: 260,
      ellipsis: true,
      render: (text, record) => {
        return <AdditionCodeViewer codes={[{ label: i18n.t('global.sourceOrderCode'), value: record.sourceCode }]} />;
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'smCode',
      valueType: 'text',
      width: 180,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PROCESSING: 'blue',
                FINISHED: 'green',
                CANCELED: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.from'),
      width: 180,
      valueType: 'text',
      dataIndex: 'fromPartnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.to'),
      width: 180,
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.type'),
      valueType: 'text',
      dataIndex: 'orderTypeName',
      ellipsis: {
        showTitle: false,
      },
      tooltip: true,
      width: 160,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (confirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-orange" />,
      isDisabled: (record) => record.status === 'FINISHED' || record.status === 'CANCELED' || record.actQty === 0,
      onClick: (record) => {
        confirmBtnOnClick(record);
      },
    });
  }

  if (resetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-yellow" />,
      isDisabled: (record) => record.status === 'FINISHED' || record.status === 'CANCELED' || record.actQty === 0,
      onClick: (record) => {
        resetBtnOnClick(record);
      },
    });
  }

  if (cancelPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.cancel'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      isDisabled: (record) => record.status === 'FINISHED' || record.status === 'CANCELED',
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues = {
    createDateRange: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      {initialized ? (
        <>
          <AppHeader
            toolbar={
              <Space>
                {createPermission && (
                  <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                    {i18n.t('global.new')}
                  </Button>
                )}
              </Space>
            }
          />
          <PowerTable
            innerRef={powerTableRef}
            rowKey="id"
            columns={columns}
            quickSearchFieldsConfig={quickSearchFieldsConfig}
            searchFieldsConfig={searchFieldsConfig}
            refreshBtnVisible
            searchPanelVisible={false}
            searchPanelCollapsible
            searchPanelInitialValues={searchPanelInitialValues}
            defaultPageSize={10}
            pagination
            autoLoad
            enableCache
            cacheKey="SPIB_ORDER"
            tableProps={{
              sticky: {
                offsetHeader: 96,
              },
              onRow: (record) => ({
                onClick: () => {
                  navigate(`/app/spib/${record.id}`);
                },
              }),
            }}
            defaultSorter={{ field: 'created', order: 'DESCEND' }}
            tabStatus={[
              {
                code: 'ALL',
                name: i18n.t('global.all'),
              },
              { code: 'NEW', name: i18n.t('global.newOrder') },
              {
                code: 'PROCESSING',
                name: i18n.t('global.processing'),
              },
              {
                code: 'FINISHED',
                name: i18n.t('global.finished'),
              },
              {
                code: 'CANCELLED',
                name: i18n.t('global.cancelled'),
              },
            ]}
            request={fetchData}
          />
          <AddDrawer open={addDrawerOpen} onRefresh={() => powerTableRef.current?.load()} onClose={addDrawerOnClose} />
        </>
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default SpibOrder;
