import { Form, Input, InputNumber, Popconfirm } from 'antd';
import * as SampleColorApi from 'common/api/sample/SampleColor';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import CheckFillIcon from 'common/assets/icons/icon-check-fill.svg?react';
import CloseFillIcon from 'common/assets/icons/icon-close-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import Button from 'common/components/Button';
import PowerTable, { IPowerTableInnerRef } from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useRef, useState } from 'react';

const EditableCell = ({ editing, dataIndex, title, inputType, children, ...restProps }) => {
  const inputNode = inputType === 'number' ? <InputNumber /> : <Input autoFocus />;

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{
            margin: 0,
          }}
          rules={[
            {
              required: true,
              message: `${i18n.t('global.pleaseInput')} ${title}!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

interface ColorOperationProps {
  grpid?: string;
}

const ColorOperateForm: React.FC<ColorOperationProps> = (props) => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { grpid } = props;
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');
  const [saving, setSaving] = useState(false);
  const [adding, setAdding] = useState(false);
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP');
  const modifyPermission = permission.codes.includes('MODIFY_COLORS');

  const inputRef = useRef<any>(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus({ cursor: 'end' });
    }, 500);
  }, [inputRef]);

  const fetchData = (params) =>
    SampleColorApi.List({
      ...params,
      smColorGrpId: grpid,
      enablePage: false,
    });

  const refresh = () => {
    setLoading(true);
    try {
      powerTableRef.current?.load();
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (grpid) {
      refresh();
    }
    // eslint-disable-next-line
  }, []);

  const isEditing = (record) => record.id === editingKey;

  const edit = (record) => {
    form.setFieldsValue({
      name: '',
      age: '',
      address: '',
      ...record,
    });
    setEditingKey(record.id);
  };

  const onDelete = async (id) => {
    const hide = NoticeUtil.processing();
    try {
      await SampleColorApi.Delete({ id });
      hide();
      NoticeUtil.success();
      refresh();
    } catch (e) {
      hide();
    }
  };

  const cancel = (e) => {
    e.preventDefault();
    setEditingKey('');
  };

  const save = async (record) => {
    setSaving(true);
    try {
      const row = await form.validateFields();
      await SampleColorApi.Update({
        ...record,
        ...row,
      });
      setEditingKey('');
      setSaving(false);
      NoticeUtil.success();
      refresh();
    } catch (e) {
      setSaving(false);
    }
  };

  const onAddFormFinish = async (values) => {
    setAdding(true);
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      await SampleColorApi.Create({
        colorGroupId: grpid,
        disabled: false,
        ...values,
      });
      NoticeUtil.success();
      setAdding(false);
      addForm.resetFields();
      refresh();
    } catch (e) {
      setAdding(false);
    }
  };

  const columns: { [key: string]: any }[] = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sort: true,
      minWidth: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      width: 200,
      editable: true,
    },
  ];

  if (modifyPermission) {
    columns.push({
      title: i18n.t('global.operation'),
      dataIndex: 'operation',
      align: 'center',
      width: 120,
      render: (_, record) => {
        const editable = isEditing(record);
        if (editable) {
          return saving ? (
            <span>
              <span>{i18n.t('global.saving')}</span>
            </span>
          ) : (
            <div className="flex items-center justify-center gap-x-2">
              <Button onClick={() => save(record)} icon={<CheckFillIcon className="fill-lead-green" />} />
              <Button onClick={cancel} icon={<CloseFillIcon className="fill-lead-slate" />} />
            </div>
          );
        }
        return editingKey === '' ? (
          <div className="flex items-center justify-center gap-x-2">
            <Button
              className="text-lead-orange"
              icon={<EditBoxLineIcon className="fill-lead-orange" />}
              onClick={() => edit(record)}
            />
            <Popconfirm
              title={i18n.t('global.confirmDelete')}
              onConfirm={() => onDelete(record.id)}
              placement="topRight"
            >
              <Button className="text-lead-red" icon={<DeleteBinLineIcon className="fill-lead-red" />} />
            </Popconfirm>
          </div>
        ) : (
          <div className="h-8 text-center leading-8"> -- </div>
        );
      },
    });
  }

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }

    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });
  return (
    <div>
      <Form form={form} component={false}>
        <PowerTable
          initialized
          rowKey="id"
          columns={mergedColumns}
          innerRef={powerTableRef}
          refreshBtnVisible={false}
          defaultPageSize={10}
          autoLoad
          request={fetchData}
          tableProps={{
            size: 'small',
            sticky: true,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            rowClassName: 'editable-row',
            scroll: { y: 400 },
            rowKey: 'id',
            loading,
          }}
        />
      </Form>
      {modifyPermission && (
        <Form form={addForm} layout="inline" onFinish={onAddFormFinish} style={{ margin: '10px 2px' }}>
          <Form.Item
            name="code"
            rules={[
              {
                required: true,
                message: i18n.t('global.fieldCanNotBeNull'),
              },
            ]}
            className="w-[200px]"
          >
            <Input placeholder={i18n.t('global.code')} ref={inputRef} />
          </Form.Item>
          <Form.Item
            name="name"
            rules={[
              {
                required: true,
                message: i18n.t('global.fieldCanNotBeNull'),
              },
            ]}
            className="w-[200px]"
          >
            <Input placeholder={i18n.t('global.name')} />
          </Form.Item>
          <Button type="success" icon={<AddFillIcon className="fill-white" />} htmlType="submit" loading={adding}>
            {i18n.t('global.new')}
          </Button>
        </Form>
      )}
    </div>
  );
};

export default ColorOperateForm;
