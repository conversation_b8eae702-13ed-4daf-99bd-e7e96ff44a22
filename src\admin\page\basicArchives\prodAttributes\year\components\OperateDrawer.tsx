import { Form, Input, Switch } from 'antd';
import * as YearApi from 'common/api/core/Year';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IYearItem } from '../data';

interface IOperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  yearId: string | undefined;
  onSubmit: (values: IYearItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<IOperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, yearId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:ATTRIBUTE:YEAR');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchYear = useCallback(async () => {
    setLoading(true);
    try {
      const year: any = await YearApi.Get({ id: yearId });
      if (typeof year.disabled === 'boolean') {
        year.status = !year.disabled;
      }
      form.setFieldsValue(year);
      setCurrent(year);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [yearId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (yearId) fetchYear();
      setTimeout(() => {
        if (yearId) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [yearId, visible, fetchYear]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IYearItem);
    }
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editYear')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newYear')
  );
  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await YearApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchYear();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!yearId}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      open={visible}
      onOk={handleSubmit}
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={codeInputRef} autoFocus={!current} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
