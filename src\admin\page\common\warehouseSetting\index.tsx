import { QuestionCircleOutlined } from '@ant-design/icons';
import { Checkbox, Col, Form, InputNumber, message, Radio, Row, Select, Switch, Tooltip } from 'antd';
import * as ConfigApi from 'common/api/core/Config';
import SaveButton from 'common/components/Button/Save';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useState } from 'react';

interface IWarehouseSetting {
  form?: any;
  onOk?: () => void;
  onFailed?: () => void;
}

const WarehouseSetting: React.FC<IWarehouseSetting> = (props) => {
  const onOk = props.onOk || (() => {});
  const onFailed = props.onFailed || (() => {});
  const [saving, setSaving] = useState<boolean>(false);
  const [crossDisabled, setCrossDisabled] = useState<boolean>(true);
  const [overDisabled, setOverDisabled] = useState<boolean>(true);
  const [configList, setConfigList] = useState<any[]>([]);
  const [permission] = usePermission('A:SETTING');
  const editPermission = permission.codes.includes('EDIT_W_S');
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formItemIsShowMap, setFormItemIsShowMap] = useState<Record<string, boolean>>({});
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];

  const { Option } = Select;

  const options = [
    { label: i18n.t('global.newOrder'), value: 'NEW' },
    {
      label: i18n.t('global.processing'),
      value: 'PROCESSING',
    },
    {
      label: i18n.t('global.finished'),
      value: 'FINISHED',
    },
    { label: i18n.t('global.cancelled'), value: 'CANCELED' },
  ];

  const plainOptions: any = [
    { label: i18n.t('global.confusionSize'), value: '2' },
    { label: i18n.t('global.confusionColor'), value: '3' },
    { label: i18n.t('global.confusionProd'), value: '4' },
  ];
  const [plainOptionsData, setPlainOptionsData] = useState<any>(plainOptions);

  const fetchConfigList = async () => {
    setLoading(true);
    try {
      const formValues: { [key: string]: any } = {};
      const res = await ConfigApi.List({
        enablePage: false,
        configTypes: ['WAREHOUSE'],
      });
      res.data.push({
        code: 'CROSS_RULE',
      });
      setData(res.data);
      const showMap: Record<string, boolean> = {};
      res.data.forEach((item) => {
        showMap[item.code] = true;
        if (item.code === 'CROSS_PROD') {
          if (item.value === 'true') {
            plainOptions[0].disabled = true;
            plainOptions[1].disabled = true;
            setPlainOptionsData(plainOptions);
          }
        }
        // if (item.code === 'CROSS_SIZE') {}
        // if (item.code === 'CROSS_COLOR') {}
        // if (item.code === 'CROSS_PROD') {}
        if (item.code === 'OVER_ORDER' || item.code === 'OVER_SKU' || item.code === 'WAY_WI_WEIGHT_OFFSET_VALUE') {
          item.value *= 100;
        }
        formValues[item.code] = item.value === 'true' || item.value === 'false' ? item.value === 'true' : item.value;
      });
      if (formValues.APP_W_ORDER_DELETE_STATUS) {
        formValues.APP_W_ORDER_DELETE_STATUS = formValues.APP_W_ORDER_DELETE_STATUS.split(',');
      }
      form.setFieldsValue(formValues);
      setFormItemIsShowMap(showMap);
      setConfigList(res.data);
    } catch (e) {}
    setLoading(false);
  };
  const onFinish = async (values) => {
    setSaving(true);
    const payload: { data: { id: string; value: any }[] } = {
      data: [],
    };
    if (values.CROSS_TAG) {
      if (!values.CROSS_RULE || values.CROSS_RULE.length === 0) {
        message.warning(i18n.t('global.plCheckCrossSellingRules'));
        onFailed();
        setSaving(false);
        return;
      }
      if (values.CROSS_RULE.includes('2') && values.CROSS_RULE.includes('3')) {
        values.CROSS_SIZE = true;
        values.CROSS_COLOR = true;
        values.CROSS_PROD = false;
      } else if (values.CROSS_RULE.includes('2')) {
        values.CROSS_SIZE = true;
        values.CROSS_PROD = false;
        values.CROSS_COLOR = false;
      } else if (values.CROSS_RULE.includes('3')) {
        values.CROSS_COLOR = true;
        values.CROSS_PROD = false;
        values.CROSS_SIZE = false;
      } else if (values.CROSS_RULE.includes('4')) {
        values.CROSS_PROD = true;
        values.CROSS_SIZE = false;
        values.CROSS_COLOR = false;
      }
    } else {
      values.CROSS_SIZE = false;
      values.CROSS_COLOR = false;
      values.CROSS_PROD = false;
    }
    if (values.OVER_TAG) {
      if (values.OVER_RULE === 'OVER_ORDER') {
        values.OVER_ORDER = values.OVER_VALUE / 100;
        values.OVER_SKU = 0;
      } else if (values.OVER_RULE === 'OVER_SKU') {
        values.OVER_SKU = values.OVER_VALUE / 100;
        values.OVER_ORDER = 0;
      }
    } else {
      values.OVER_SKU = 0;
      values.OVER_ORDER = 0;
    }

    if (values.WAY_WI_WEIGHT_OFFSET_VALUE) {
      values.WAY_WI_WEIGHT_OFFSET_VALUE /= 100;
    }

    if (values.APP_W_ORDER_DELETE_STATUS?.length > 0) {
      values.APP_W_ORDER_DELETE_STATUS = values.APP_W_ORDER_DELETE_STATUS.toString();
    } else {
      values.APP_W_ORDER_DELETE_STATUS = '';
    }
    delete values.CROSS_RULE;
    delete values.OVER_RULE;
    delete values.OVER_VALUE;
    Object.keys(values).forEach((key) => {
      let val = values[key];
      let id = '';
      const item = data.find((n) => n.code === key);
      if (item) id = item.id;
      if (typeof val === 'boolean') val = val.toString();

      payload.data.push({
        id,
        value: val,
      });
    });
    try {
      await ConfigApi.Update(payload);
      fetchConfigList();
      NoticeUtil.success(i18n.t('global.saveSuccess'));
      onOk();
    } catch {
      onFailed();
    }
    setSaving(false);
  };

  const handleCrossRuleChange = (val) => {
    const payLoad = [...val];
    if (payLoad.indexOf('4') !== -1) {
      plainOptions[0].disabled = true;
      plainOptions[1].disabled = true;
      form.setFieldsValue({ CROSS_RULE: ['4'] });
    } else {
      plainOptions[0].disabled = false;
      plainOptions[1].disabled = false;
      form.setFieldsValue({ CROSS_RULE: payLoad });
    }
    setPlainOptionsData(plainOptions);
  };

  useEffect(() => {
    fetchConfigList();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (!crossDisabled) {
      form.setFieldsValue({
        OVER_RULE: 'OVER_ORDER',
      });
    }
  }, [crossDisabled, form]);

  useEffect(() => {
    const defaultArr: any = {
      CROSS_TAG: false,
      CROSS_RULE: [],
      OVER_TAG: false,
      OVER_RULE: 'OVER_ORDER',
      OVER_VALUE: 0,
    };
    configList.forEach((item) => {
      if (item.code === 'CROSS_TAG') {
        if (item.value === 'true') {
          // setCrossSwitch('checked');
          setCrossDisabled(false);
          defaultArr.CROSS_TAG = true;
        } else {
          // setCrossSwitch('');
          setCrossDisabled(true);
          defaultArr.CROSS_TAG = false;
        }
      }
      if (item.code === 'OVER_TAG') {
        if (item.value === 'true') {
          // setOverSwitch('checked');
          setOverDisabled(false);
          defaultArr.OVER_TAG = true;
        } else {
          // setOverSwitch('');
          setOverDisabled(true);
          defaultArr.OVER_TAG = false;
        }
      }
      if (item.value !== 'false') {
        switch (item.code) {
          case 'CROSS_SIZE':
            defaultArr.CROSS_RULE.push('2');
            break;
          case 'CROSS_COLOR':
            defaultArr.CROSS_RULE.push('3');
            break;
          case 'CROSS_PROD':
            defaultArr.CROSS_RULE.push('4');
            break;
          case 'OVER_ORDER':
            if (item.value > 0) {
              defaultArr.OVER_RULE = 'OVER_ORDER';
              defaultArr.OVER_VALUE = item.value;
            }
            break;
          case 'OVER_SKU':
            if (item.value > 0) {
              defaultArr.OVER_RULE = 'OVER_SKU';
              defaultArr.OVER_VALUE = item.value;
            }
            break;
          default:
            break;
        }
      }
    });
    form.setFieldsValue(defaultArr);
  }, [configList, form]);

  return (
    <Form
      layout="vertical"
      initialValues={{
        OVER_RULE: 'OVER_ORDER',
        CROSS_TAG: false,
        OVER_TAG: false,
      }}
      onFinish={onFinish}
      onFinishFailed={onFailed}
      form={form}
    >
      {!loading ? (
        <>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.general')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_W_ORDER_DELETE_DAYS && (
                <Form.Item name="APP_W_ORDER_DELETE_DAYS" label={i18n.t('global.appWOrderDeleteDays')}>
                  <InputNumber min={0} />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_W_ORDER_DELETE_STATUS && (
                <Form.Item name="APP_W_ORDER_DELETE_STATUS" label={i18n.t('global.appWOrderDeleteStatus')}>
                  <Checkbox.Group options={options} />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAREHOUSE_NO_ORDER_TYPE_REQUIRED && (
                <Form.Item
                  name="WAREHOUSE_NO_ORDER_TYPE_REQUIRED"
                  label={i18n.t('global.warehouseNoOrderTypeRequired')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WI_ACT_QTY_LESS_NOT_CONFIRM && (
                <Form.Item
                  name="WI_ACT_QTY_LESS_NOT_CONFIRM"
                  label={i18n.t('global.inboundActualQtyLessThanOrderQtyNotAllowConfirm')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WO_ACT_QTY_LESS_NOT_CONFIRM && (
                <Form.Item
                  name="WO_ACT_QTY_LESS_NOT_CONFIRM"
                  label={i18n.t('global.outboundActualQtyLessThanOrderQtyNotAllowConfirm')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.inbound')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WI_SAVE_BOX_AUTO_CONFIRM_BOX && (
                <Form.Item
                  label={i18n.t('global.wiAutoConfirmWhenSaveWithBox')}
                  name="WAY_WI_SAVE_BOX_AUTO_CONFIRM_BOX"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WI_SAVE_ORDER_AUTO_CONFIRM_BOX && (
                <Form.Item
                  label={i18n.t('global.wiAutoConfirmWhenSaveWithOrder')}
                  name="WAY_WI_SAVE_ORDER_AUTO_CONFIRM_BOX"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WI_BOX_CHECK_WAY && (
                <Form.Item
                  label={
                    <>
                      {i18n.t('global.woBoxCheckWay')}
                      <Tooltip
                        title={
                          <div>
                            {i18n.t('global.thoroughfareInBoundCheckWay')}
                            <br />
                            RFID：
                            {i18n.t('global.checkWayByRFID')}
                            <br />
                            SKU：
                            {i18n.t('global.checkWayBySKU')}
                          </div>
                        }
                      >
                        <QuestionCircleOutlined className="ml-1 text-blue-500" />
                      </Tooltip>
                    </>
                  }
                  name="WAY_WI_BOX_CHECK_WAY"
                >
                  <Select style={{ width: 250 }}>
                    <Option value="SKU">SKU</Option>
                    <Option value="RFID">RFID</Option>
                  </Select>
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WI_RFID_POSITION_VALID_MODE && (
                <Form.Item name="WI_RFID_POSITION_VALID_MODE" label={i18n.t('global.WIRFIDPositionValidMode')}>
                  <Radio.Group className="flex flex-wrap gap-y-2">
                    <Radio value="ALLOW_REMIND">
                      {i18n.t('global.allowRemind')}{' '}
                      <Tooltip title={i18n.t('global.allowRemindTooltipWarehouse')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="ALLOW_NOT_REMIND">
                      {i18n.t('global.allowNotRemind')}{' '}
                      <Tooltip title={i18n.t('global.allowNotRemindTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="NOT_ALLOW">
                      {i18n.t('global.notAllow')}{' '}
                      <Tooltip title={i18n.t('global.notAllowTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.OVER_TAG && (
                <Form.Item label={i18n.t('global.overTag')} name="OVER_TAG" valuePropName="checked">
                  <Switch
                    onChange={(value) => {
                      setOverDisabled(!value);
                    }}
                  />
                </Form.Item>
              )}
              <Form.Item label={i18n.t('global.overRule')} name="OVER_RULE">
                <Radio.Group disabled={overDisabled}>
                  <Radio value="OVER_ORDER">{i18n.t('global.order')}</Radio>
                  <Radio value="OVER_SKU" disabled={!crossDisabled}>
                    {i18n.t('global.barcode')}
                  </Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                label={i18n.t('global.overValue')}
                name="OVER_VALUE"
                rules={
                  !overDisabled
                    ? [
                        {
                          validator: (_, value) =>
                            value >= 0.1 ? Promise.resolve() : Promise.reject(i18n.t('global.overRuleTip')),
                        },
                      ]
                    : []
                }
              >
                <InputNumber min={0.1} disabled={overDisabled} precision={1} />
              </Form.Item>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.CROSS_TAG && (
                <Form.Item label={i18n.t('global.crossTag')} name="CROSS_TAG" valuePropName="checked">
                  <Switch
                    onChange={(value) => {
                      setCrossDisabled(!value);
                    }}
                  />
                </Form.Item>
              )}
              {formItemIsShowMap.CROSS_RULE && (
                <Form.Item label={i18n.t('global.crossRule')} name="CROSS_RULE">
                  <Checkbox.Group
                    options={plainOptionsData}
                    disabled={crossDisabled}
                    onChange={handleCrossRuleChange}
                  />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WO_GENERATE_WI && (
                <Form.Item
                  name="WO_GENERATE_WI"
                  label={i18n.t('global.WOGenerateWI')}
                  labelAlign="right"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SO_GENERATE_WI && (
                <Form.Item
                  name="SO_GENERATE_WI"
                  label={i18n.t('global.SOGenerateWI')}
                  labelAlign="right"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.FO_GENERATE_WI && (
                <Form.Item
                  name="FO_GENERATE_WI"
                  label={i18n.t('global.FOGenerateWI')}
                  labelAlign="right"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WI_CHECK_WEIGHT && (
                <Form.Item
                  name="WAY_WI_CHECK_WEIGHT"
                  label={i18n.t('global.wayWiCheckWeight')}
                  labelAlign="right"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WI_WEIGHT_OFFSET_VALUE && (
                <Form.Item name="WAY_WI_WEIGHT_OFFSET_VALUE" label={i18n.t('global.wayWiWeightOffsetValue')}>
                  <InputNumber min={1} />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WI_SAVE_BOX_ENABLE_BOX_SPEC && (
                <Form.Item
                  label={
                    <>
                      {i18n.t('global.enableBoxSpec')}
                      <Tooltip title={<div>{i18n.t('global.inboundNewBoxSelectSpec')}</div>}>
                        <QuestionCircleOutlined className="ml-1 text-blue-500" />
                      </Tooltip>
                    </>
                  }
                  name="WI_SAVE_BOX_ENABLE_BOX_SPEC"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_WI_CREATE_BOX && (
                <Form.Item name="APP_WI_CREATE_BOX" label={i18n.t('global.appWICreateBox')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WIB_BARCODE_PACKING_THRESHOLD && (
                <Form.Item
                  label={i18n.t('global.inboundBarcodePackingOperationThreshold', {
                    client: i18n.t('global.warehouse'),
                  })}
                  name="WIB_BARCODE_PACKING_THRESHOLD"
                >
                  <InputNumber min={1} />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.outbound')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WO_SAVE_ORDER_AUTO_CONFIRM_BOX && (
                <Form.Item
                  label={i18n.t('global.woAutoConfirmWhenSaveWithOrder')}
                  name="WAY_WO_SAVE_ORDER_AUTO_CONFIRM_BOX"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WO_RFID_POSITION_VALID_MODE && (
                <Form.Item name="WO_RFID_POSITION_VALID_MODE" label={i18n.t('global.WORFIDPositionValidMode')}>
                  <Radio.Group className="flex flex-wrap gap-y-2">
                    <Radio value="ALLOW_REMIND">
                      {i18n.t('global.allowOutboundWithNotification')}{' '}
                      <Tooltip title={i18n.t('global.allowRemindTooltipWarehouse')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="ALLOW_NOT_REMIND">
                      {i18n.t('global.allowOutboundWithoutNotification')}{' '}
                      <Tooltip title={i18n.t('global.allowNotRemindTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="NOT_ALLOW">
                      {i18n.t('global.outboundNotAllowed')}{' '}
                      <Tooltip title={i18n.t('global.notAllowTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WAY_WO_CHECK_RATIO && (
                <Form.Item
                  name="WAY_WO_CHECK_RATIO"
                  label={i18n.t('global.wayWoCheckRatio')}
                  labelAlign="right"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WOB_BARCODE_PACKING_THRESHOLD && (
                <Form.Item
                  label={i18n.t('global.outboundBarcodePackingOperationThreshold', {
                    client: i18n.t('global.warehouse'),
                  })}
                  name="WOB_BARCODE_PACKING_THRESHOLD"
                >
                  <InputNumber min={1} />
                </Form.Item>
              )}
            </Col>
          </Row>
          {!props.form && (
            <Form.Item className="sticky bottom-0 mb-0 w-full bg-white py-5 pl-3">
              <SaveButton htmlType="submit" loading={saving} disabled={!editPermission} />
            </Form.Item>
          )}
        </>
      ) : (
        <div className="flex min-h-[theme('spacing.96')] items-center justify-center">
          <Spin />
        </div>
      )}
    </Form>
  );
};

export default WarehouseSetting;
