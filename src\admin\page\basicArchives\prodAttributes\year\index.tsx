import { Button, Input } from 'antd';
import * as YearApi from 'common/api/core/Year';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import DisableSelect from 'common/components/Select/DisableSelect';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import OperateDrawer from './components/OperateDrawer';
import { IYearItem } from './data';

const Year: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IYearItem> | undefined>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [permission] = usePermission('A:BASE:ATTRIBUTE:YEAR');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await YearApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const fetchData = useCallback(async (params) => {
    return YearApi.List(params);
  }, []);

  const handleSubmit = async (values: IYearItem) => {
    const id = current ? current.id : '';

    setConfirmLoading(true);
    try {
      if (id) {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await YearApi.Update({
          ...current,
          ...values,
        });
      } else {
        values.disabled = false;
        values.code = values.code.trim();
        values.name = values.name.trim();
        await YearApi.Create(values);
      }
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'disabled',
      label: i18n.t('global.status'),
      inputComponent: <DisableSelect />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        rightToolbar={[
          createPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="YEAR"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  setCurrent(record);
                  setVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />

      <OperateDrawer
        visible={visible}
        confirmLoading={confirmLoading}
        yearId={current ? current.id : ''}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default Year;
