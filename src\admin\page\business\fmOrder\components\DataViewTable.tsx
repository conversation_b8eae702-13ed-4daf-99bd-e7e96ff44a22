import { Progress } from 'antd';
import * as BfadOrderApi from 'common/api/factory/BfadOrder';
import * as FadOrderApi from 'common/api/factory/FadOrder';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';

export interface DataViewTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface IDataViewTable {
  fmOrderCode: string;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<DataViewTableInnerRef | undefined>;
}

const DataViewTable: React.FC<IDataViewTable> = (props) => {
  const { fmOrderCode, innerRef } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const fetchData = useCallback(() => {
    if (currentUser.mode === 'BINDING') {
      return BfadOrderApi.List({
        fmOrderCode,
      });
    }
    return FadOrderApi.List({
      fmOrderCode,
    });
    // eslint-disable-next-line
  }, [fmOrderCode]);

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    powerTableRef.current?.load();
  }, [fmOrderCode]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      sorter: true,
      width: 180,
    },
    {
      title: i18n.t('global.targetWarehouse'),
      dataIndex: 'toPartnerName',
      width: 200,
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      width: 150,
      sorter: true,
    },
    {
      title: i18n.t('global.confirmStatus'),
      auto: true,
      minWidth: 220,
      render: (value, record) => {
        const diff: any = record.passQty / record.qty;
        return (
          <div style={{ width: 200 }}>
            {record.status === 'PART_PASS' && (
              <>
                <span>{`${i18n.t('global.pass')}：${record.passQty} ${i18n.t('global.notPass')}：${
                  record.qty - record.passQty
                }`}</span>
                <Progress percent={diff.toFixed(4) * 100} status="active" size="small" />
              </>
            )}
            {record.status === 'PASS' && (
              <>
                <span>{i18n.t('global.allPass')}</span>
                <Progress percent={100} size="small" />
              </>
            )}
            {record.status === 'NOT_PASS' && (
              <>
                <span>{i18n.t('global.allNotPass')}</span>
                <Progress percent={100} size="small" status="exception" />
              </>
            )}
            {record.status === 'NEW' && (
              <>
                <span>{i18n.t('global.unreviewed')}</span>
                <Progress percent={0} size="small" />
              </>
            )}
            {record.status === 'CANCEL' && (
              <>
                <span>{i18n.t('global.cancelled')}</span>
                <Progress percent={0} size="small" />
              </>
            )}
          </div>
        );
      },
    },
    {
      title: i18n.t('global.confirmTime'),
      dataIndex: 'created',
      valueType: 'dateTime',
      width: 200,
      sorter: true,
    },
  ];

  return (
    <PowerTable
      innerRef={powerTableRef}
      columns={columns}
      pagination
      tableProps={{
        sticky: true,
      }}
      refreshBtnVisible={false}
      request={fetchData}
    />
  );
};

export default DataViewTable;
