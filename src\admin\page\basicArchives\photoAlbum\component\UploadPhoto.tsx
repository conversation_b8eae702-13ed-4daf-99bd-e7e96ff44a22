/**
 * Create by codegen at 2022-06-28
 * 相册照片  - 新增，上传，编辑
 */
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
} from '@dnd-kit/core';

import { arrayMove, rectSwappingStrategy, SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { faImage } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { UploadProps } from 'antd';
import { Button, Empty, List, message, Popover, Progress, Space, Tooltip, Typography, Upload } from 'antd';
import { baseUrl, getToken } from 'common/api/Base';
import * as PhotoAlbumApi from 'common/api/core/PhotoAlbum';
import * as PartnerPhotoAlbumApi from 'common/api/core/PartnerPhotoAlbum';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as NoticeUtil from 'common/utils/Notice';
import Modal from 'common/components/Modal';

import PhotoItem from './PhotoItem';

import styles from './uploadPhoto.module.css';

const { Text, Link, Paragraph } = Typography;

interface UploadPhotoProps {
  type?: 'SHOP' | 'COMMON';
  current?: Record<string, any>;
  visible?: boolean;
  onCancel?: () => void;
  /**
   * 权限
   * */
  permission?: Record<string, any>;
}

const UploadPhoto: React.FC<UploadPhotoProps> = (props) => {
  const { type: _type, current, visible, onCancel, permission } = props;
  const type = _type ?? 'SHOP';
  const [data, setData] = useState<{ id: UniqueIdentifier }[]>([]);
  const [selectRows, setSelectRows] = useState<Record<string, any>[]>([]);
  const [selectKeys, setSelectKeys] = useState<any[]>([]);
  const [isSelect, setIsSelect] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const fileDoneUid = useRef<string[]>([]);
  const fileDoneList = useRef<Record<string, any>[]>([]);
  const [createdParams, setCreatedParams] = useState<Record<string, any>[]>([]);
  const [isAllSelect, setIsAllSelect] = useState<boolean>(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // 激活拖拽前，鼠标需要移动8px
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const fetchData = useCallback(async () => {
    const params: Record<string, any> = {
      enablePage: false,
      orderByField: 'sort',
      orderByMethod: 'ASCEND',
    };
    try {
      let resp: any;
      if (type === 'SHOP') {
        params.partnerPhotoAlbumId = current?.id;
        resp = await PartnerPhotoAlbumApi.LineList(params);
      } else {
        params.photoAlbumId = current?.id;
        resp = await PhotoAlbumApi.LineList(params);
      }
      setData(resp.data);
    } catch (e) {}
  }, [current, type]);
  const handleDragEnd = async (event: any) => {
    const { active, over } = event;

    if (active.id && over.id && active.id !== over.id) {
      const oldIndex = data.findIndex((n) => n.id === active.id);
      const newIndex = data.findIndex((n) => n.id === over.id);
      const newData = arrayMove(data, oldIndex, newIndex);

      setData(newData);
      try {
        const params = {
          id: current?.id,
          data: newData.map((n, index) => ({
            lineId: n.id,
            sort: index,
          })),
        };
        let resp: any;
        if (type === 'SHOP') {
          resp = await PartnerPhotoAlbumApi.BatchUpdate(params);
        } else {
          resp = await PhotoAlbumApi.BatchUpdate(params);
        }
        if (resp) {
          NoticeUtil.success();
        }
      } catch (e) {}
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: `${baseUrl}/api/file/i/upload`,
    accept: '.png,.jpg,.jpeg',
    multiple: true,
    showUploadList: false,
    headers: {
      authorization: getToken(),
    },
    fileList,
    onChange(info) {
      const { file } = info;
      if (file.status === 'done') {
        fileDoneUid.current.push(file.uid);
        const find = fileDoneList.current.find((n) => n.uid === file.uid);
        if (!find) fileDoneList.current.push(file);
      }
      const fileNoDone = info.fileList.filter((n) => !fileDoneUid.current.includes(n.uid));
      if (fileDoneUid.current.length === 0) {
        setFileList([...info.fileList]);
      } else {
        setFileList([...fileDoneList.current, ...fileNoDone]);
      }
    },
  };

  const operationOnClick = () => {
    setIsSelect(!isSelect);
    setIsAllSelect(false);
    setSelectKeys([]);
    setSelectRows([]);
  };

  const selectOnChange = (record) => {
    if (selectKeys.includes(record.id)) {
      setSelectKeys(selectKeys.filter((n) => n !== record.id));
      setSelectRows(selectRows.filter((n) => n.id !== record.id));
      setIsAllSelect(false);
    } else {
      setSelectRows([...selectRows, record]);
      setSelectKeys([...selectKeys, record.id]);
    }
  };

  const allSelectOnClick = () => {
    if (!isAllSelect) {
      setSelectRows(data);
      setSelectKeys(data.map((n) => n.id));
      setIsAllSelect(true);
    } else {
      setSelectRows([]);
      setSelectKeys([]);
      setIsAllSelect(false);
    }
  };

  const deleteListNode = () => (
    <List dataSource={selectRows} renderItem={(item) => <List.Item>{item.fileName}</List.Item>} />
  );

  const deleteOnClick = (record?) => {
    const params = record ? [record.id] : selectKeys;
    if (params.length === 0) {
      message.info(i18n.t('global.selectDeletePhoto'));
      return;
    }
    NoticeUtil.confirm({
      title: record ? i18n.t('global.confirmDeletePhoto') : i18n.t('global.confirmDeleteListPhoto'),
      content: record ? `${record.fileName}` : deleteListNode(),
      okType: 'danger',
      maskTransitionName: '',
      transitionName: '',
      onOk: async () => {
        try {
          if (type === 'SHOP') {
            await PartnerPhotoAlbumApi.LineDelete({
              ids: params,
            });
          } else {
            await PhotoAlbumApi.LineDelete({
              ids: params,
            });
          }
          NoticeUtil.success();
          fetchData();
          setIsSelect(false);
          setSelectKeys([]);
          setSelectRows([]);
          setIsAllSelect(false);
        } catch (e) {}
      },
    });
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  useEffect(() => {
    if (visible && current) {
      fetchData();
    } else {
      setData([]);
      setIsSelect(false);
      setSelectKeys([]);
      setSelectRows([]);
      setFileList([]);
    }
  }, [visible, current, fetchData]);

  useEffect(() => {
    // if (fileList.length === 0) return;
    // eslint-disable-next-line
    const allDone = fileList.every((item) => item.hasOwnProperty('response'));
    if (allDone) {
      const params: Record<string, any>[] = [];
      let currentIndex = 0;
      fileList.forEach((item) => {
        if (item.status === 'done' && item.response) {
          const obj: Record<string, any> = {
            sort: currentIndex,
            fileName: item.name,
            source: item.response,
          };
          if (type === 'SHOP') {
            obj.partnerPhotoAlbumId = current?.id;
          } else {
            obj.photoAlbumId = current?.id;
          }
          currentIndex += 1;
          params.push(obj);
        }
      });

      setCreatedParams(params);
    }
  }, [fileList, type, current]);

  const createdPhoto = useCallback(
    (params) =>
      new Promise((resolve, reject) => {
        let resp: any;
        if (type === 'SHOP') {
          resp = PartnerPhotoAlbumApi.LineCreate(params);
        } else {
          resp = PhotoAlbumApi.LineCreate(params);
        }
        if (resp) {
          resolve(resp);
        } else {
          reject(resp);
        }
      }),
    [type],
  );
  const promiseAll = useCallback(async () => {
    try {
      const resp: any = await Promise.all(createdParams.map((item) => createdPhoto(item)));
      const result = resp.every((value) => value);
      if (result) {
        fetchData();
        NoticeUtil.success();
        setFileList([]);
      }
    } catch (err) {}
    fileDoneUid.current = [];
    fileDoneList.current = [];
    setCreatedParams([]);
    // eslint-disable-next-line
  }, [createdParams]);

  useEffect(() => {
    if (createdParams.length > 0) {
      promiseAll();
    }
    // eslint-disable-next-line
  }, [createdParams]);

  const popoverContent = (
    <div style={{ width: 420 }}>
      <List
        dataSource={fileList}
        renderItem={(item) => (
          <List.Item key={item.uid}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <FontAwesomeIcon icon={faImage} style={{ marginRight: 8 }} />
              <Tooltip title={item.name}>
                <Paragraph ellipsis={{ rows: 1 }} style={{ width: 150, margin: 0 }}>
                  {item.name}
                </Paragraph>
              </Tooltip>
            </div>
            <Progress
              percent={item.percent.toFixed(0)}
              style={{ width: 200 }}
              size="small"
              status={item.status === 'error' ? 'exception' : undefined}
            />
          </List.Item>
        )}
      />
    </div>
  );

  return (
    <Modal
      title={
        <>
          {i18n.t('global.albumImages')}
          {' - '}
          {current?.name}
        </>
      }
      open={visible}
      width={1160}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button type="default" onClick={modalOnCancel}>
            {i18n.t('global.close')}
          </Button>
          <Space size="middle">
            <Text>
              {i18n.t('global.totalNumber')}：<Link style={{ fontSize: 20 }}>{data.length}</Link>
            </Text>
            {fileList.length > 0 && (
              <Popover
                placement="topLeft"
                content={popoverContent}
                title={`${i18n.t('global.uploadIng')}：${fileList.length}`}
              >
                <div className={styles['tooltip-btn']}>
                  {i18n.t('global.uploadIng')}：<Link style={{ fontSize: 20 }}>{fileList.length}</Link>
                </div>
              </Popover>
            )}
          </Space>
        </div>
      }
      keyboard={false}
      transitionName=""
      maskTransitionName=""
    >
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        {permission?.uploadPermission && (
          <Upload {...uploadProps}>
            <Button type="primary">{i18n.t('global.upload')}</Button>
          </Upload>
        )}
        <Space>
          {isSelect && (
            <>
              <Button type="default" danger onClick={() => deleteOnClick()}>
                {i18n.t('global.delete')}({selectKeys.length})
              </Button>
              <Button type="default" onClick={allSelectOnClick}>
                {i18n.t('global.allSelect')}
              </Button>
            </>
          )}
          {permission?.deletePhoPermission && (
            <Button
              className={`${styles['filter-toggle-btn']} ${styles['with-text']} ${isSelect ? `${styles.active}` : ''}`}
              type={isSelect ? 'text' : 'link'}
              onClick={operationOnClick}
            >
              {i18n.t('global.bulkOperations')}
            </Button>
          )}
        </Space>
      </div>
      {data.length > 0 ? (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={data} strategy={rectSwappingStrategy}>
            <div style={{ height: 500, overflow: 'auto', marginTop: 16 }}>
              <Space size={[20, 20]} wrap>
                {data.map((item) => (
                  <PhotoItem
                    key={item.id}
                    id={item.id}
                    current={item}
                    isLoading={!isSelect}
                    isSelectIcon={selectKeys.includes(item.id)}
                    permission={permission}
                    deleteOnClick={deleteOnClick}
                    selectOnClick={selectOnChange}
                  />
                ))}
              </Space>
            </div>
          </SortableContext>
        </DndContext>
      ) : (
        <Empty />
      )}
    </Modal>
  );
};

export default UploadPhoto;
