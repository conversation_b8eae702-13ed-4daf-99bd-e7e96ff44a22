import { Tag, Tree } from 'antd';
import { TreeProps } from 'antd/es/tree';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';

interface IPermissionTreeProps {
  treeData?: TreeProps['treeData'];
  defaultCheckedKeys?: any[];
  searchValue: string;
  checkChange?: (allSelectedKeys: any[]) => void;
}

const PermissionTree: React.FC<IPermissionTreeProps> = (props) => {
  const { treeData, checkChange, defaultCheckedKeys, searchValue } = props;
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [dataList, setDataList] = useState<any[]>([]);
  const generateList = (data) => {
    const newDataList: any[] = [];
    const loop = (d) => {
      for (let i = 0; i < d.length; i += 1) {
        const node = d[i];
        const { key, title } = node;
        newDataList.push({ key, title });
        if (node.children) {
          loop(node.children);
        }
      }
    };
    loop(data);
    setDataList(newDataList);
  };

  useEffect(() => {
    generateList(treeData);
  }, [treeData]);

  useEffect(() => {
    if (defaultCheckedKeys) {
      setCheckedKeys(defaultCheckedKeys);
    }
  }, [defaultCheckedKeys]);

  const onExpand = (expandedKeys) => {
    // if not set autoExpandParent to false,
    // if children expanded, parent can not collapse.
    // or, you can remove all expanded children keys.
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  const onCheck = (checkedKeys, e) => {
    setCheckedKeys(checkedKeys);
    if (checkChange) {
      checkChange(checkedKeys.concat(e.halfCheckedKeys));
    }
  };

  const getParentKey = (key, tree) => {
    let parentKey;
    for (let i = 0; i < tree.length; i += 1) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  useEffect(() => {
    const expandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(searchValue) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);

    setExpandedKeys(expandedKeys);
    setAutoExpandParent(true);
    // eslint-disable-next-line
  }, [searchValue]);

  const titleRender = (nodeData) => (
    <div>
      {nodeData.title}
      {nodeData.type === 'MENU' && (
        <Tag style={{ marginLeft: 8 }} color="gold">
          {i18n.t('global.menu')}
        </Tag>
      )}
      {nodeData.type === 'VIEW' && (
        <Tag style={{ marginLeft: 8 }} color="blue">
          {i18n.t('global.interface')}
        </Tag>
      )}
      {nodeData.type === 'BLOCK' && (
        <Tag style={{ marginLeft: 8 }} color="blue">
          {i18n.t('global.block')}
        </Tag>
      )}
      {nodeData.type === 'ITEM' && (
        <Tag style={{ marginLeft: 8 }} color="blue">
          {i18n.t('global.item')}
        </Tag>
      )}
      {nodeData.type === 'ACTION' && (
        <Tag style={{ marginLeft: 8 }} color="blue">
          {i18n.t('global.operation')}
        </Tag>
      )}
    </div>
  );

  const loop = (data) =>
    data.map((item) => {
      const index = item.title.indexOf(searchValue);
      const beforeStr = item.title.substr(0, index);
      const afterStr = item.title.substr(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span className="site-global-tree-search-value">{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (item.children) {
        return {
          title,
          key: item.key,
          type: item.type,
          children: loop(item.children),
        };
      }

      return {
        title,
        key: item.key,
        type: item.type,
      };
    });

  return (
    <div>
      <Tree
        checkable
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onCheck={onCheck}
        checkedKeys={checkedKeys}
        selectable={false}
        // onSelect={onSelect}
        // selectedKeys={selectedKeys}
        treeData={loop(treeData)}
        titleRender={titleRender}
      />
    </div>
  );
};

export default PermissionTree;
