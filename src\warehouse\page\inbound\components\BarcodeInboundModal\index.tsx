import { faExpand } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Col, message, Row, Space, Spin, Switch } from 'antd';
import { ModalProps } from 'antd/es/modal';
import * as Sku<PERSON><PERSON> from 'common/api/core/Sku';
import * as BwibOrderApi from 'common/api/warehouse/BwibOrder';
import * as Wib<PERSON><PERSON>r<PERSON><PERSON> from 'common/api/warehouse/WibOrder';
import ExchangeIcon from 'common/assets/icons/icon-exchange.svg?react';
import BarcodeInput from 'common/components/BarcodeInput';
import Button from 'common/components/Button';
import FlashStatistic from 'common/components/FlashStatistic';
import Modal from 'common/components/Modal';
import BoxSpecSelect from 'common/components/Select/BoxSpecSelect';
import useSetting from 'common/hooks/useSetting';
import useSpeech from 'common/hooks/useSpeech';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { offWindowBeforeUnload, onWindowBeforeUnload } from 'common/utils';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as MathUtil from 'common/utils/Math';
import * as NoticeUtil from 'common/utils/Notice';
import BarcodeAliasModal from 'common/components/BarcodeAliasModal';
import useSearchAlias from 'common/components/BarcodeAliasModal/hook/useSearchAlias';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';
import BoxCodeModal from '../RfidInboundModal/BoxCodeModal';

import { ITableBaseItem } from '../../data';
import DataViewTable from './DataViewTable';

message.config({
  maxCount: 3,
});

interface ITableDataItem extends ITableBaseItem {
  barcode: string;
  qty: number;
  operateQty: number;
  normalQty: number;
  errorQty: number;
  overQty: number;
  actQty: number;
  availableQty: number;
  notInRange?: boolean;
  alias?: any[];
  source?: string;
  brandName?: string;
}

interface IInboundModalProps {
  wibOrderPreBoxId?: string;
  wibOrderBoxId?: string;
  modalProps?: ModalProps;
  wibOrderBoxStatus: string;
  onOk: () => void;
  wibOrderId?: string;
  wibOrderBoxCode: string;
  onOperateModeChange: () => void;
  fmOrderCode: string;
  localTag: boolean;
  setting: Record<string, any>;
  innerRef: any;
}

interface IScreenDataProps {
  boxCode: string;
  barcode: string;
  qty: number;
  operateMode?: string;
}

const localBoxKey = 'WAREHOUSE_INBOUND_CUSTOM_BOX_CODE';
const boxSpecIdKey = 'WAREHOUSE_INBOUND__BOX_SPEC_ID';

const BarcodeInboundModal: React.FC<IInboundModalProps> = (props) => {
  const {
    innerRef,
    wibOrderPreBoxId,
    wibOrderBoxId,
    modalProps,
    onOk,
    wibOrderId,
    wibOrderBoxCode,
    onOperateModeChange,
    localTag,
    fmOrderCode,
    wibOrderBoxStatus,
    setting,
  } = props;
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [normalData, setNormalData] = useState<IScreenDataProps[]>([]);
  const [errorData, setErrorData] = useState<IScreenDataProps[]>([]);
  const [overData, setOverData] = useState<IScreenDataProps[]>([]);
  const [tableData, setTableData] = useState<ITableDataItem[]>([]);
  const [skuFetching, setSkuFetching] = useState(false);
  const [overType, setOverType] = useState<string>('');
  const [normalQty, setNormalQty] = useState<number>(0);
  const [overQty, setOverQty] = useState<number>(0);
  const [overPercent, setOverPercent] = useState<number>(0);
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { OVER_TAG: overTag, CROSS_TAG: crossTag, OVER_ORDER: overOrder, OVER_SKU: overSku } = setting;
  const { playSound, stopSound } = useSpeech();
  const [isCustomizedBoxCode, setIsCustomizedBoxCode] = useState<boolean>(false);
  const [boxCodeModalVisible, setBoxCodeModalVisible] = useState(false);
  const [customBoxCode, setCustomBoxCode] = useState<string>('');
  const [boxSpecId, setBoxSpecId] = useState<string>();

  const {
    WI_SAVE_BOX_ENABLE_BOX_SPEC: enableBoxSpec,
    ENABLE_BARCODE_SCAN_CUTOFF: enableBarcodeScanCutoff,
    BARCODE_CUTOFF_BEFORE_DIGITS: barcodeCutoffBeforeDigits,
    BARCODE_CUTOFF_AFTER_DIGITS: barcodeCutoffAfterDigits,
    WIB_BARCODE_PACKING_THRESHOLD: barcodePackingThreshold,
  } = useSetting([
    { code: 'WI_SAVE_BOX_ENABLE_BOX_SPEC', valueType: 'BOOLEAN' },
    { code: 'ENABLE_BARCODE_SCAN_CUTOFF', valueType: 'BOOLEAN' },
    { code: 'BARCODE_CUTOFF_BEFORE_DIGITS', valueType: 'NUMBER' },
    { code: 'BARCODE_CUTOFF_AFTER_DIGITS', valueType: 'NUMBER' },
    { code: 'WIB_BARCODE_PACKING_THRESHOLD', valueType: 'NUMBER' },
  ]);

  const {
    barcodeAliasModalOpen,
    barcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  } = useSearchAlias();

  const normalQtyRef = useRef<any>();
  const overQtyRef = useRef<any>();
  const errorQtyRef = useRef<any>();
  const barcodeInputRef = useRef<any>();
  const isOperate = useRef(false);

  const playAndNotify = useCallback(
    (text: string, type: 'success' | 'error' | 'warn' | 'info', val?: string) => {
      NoticeUtil[type](val ? `${text} ${val}` : text);
      playSound({ text });
    },
    [playSound],
  );

  const baseItem = (data?) => ({
    prodCode: data?.prodCode || '',
    colorCode: data?.colorCode || '',
    colorName: data?.colorName || '',
    sizeCode: data?.sizeCode || '',
    sizeName: data?.sizeName || '',
    skuName: data?.skuName || '',
    specCode: data?.specCode || '',
    specName: data?.specName || '',
  });

  useImperativeHandle(innerRef, () => ({
    getIsOperate: () => isOperate.current,
  }));

  const operateMode = 'SKU';

  const fetchPreBoxRfidsData = async () => {
    const data: ITableDataItem[] = [];
    setLoading(true);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BwibOrderApi.PreBoxRfids({
          boxId: wibOrderPreBoxId,
          wibOrderId,
          enablePage: false,
        });
      } else {
        result = await WibOrderApi.PreBoxRfids({
          boxId: wibOrderPreBoxId,
          wibOrderId,
          enablePage: false,
        });
      }

      if (result.data.length > 0) {
        result.data.forEach((item) => {
          const dataItem = data.find((n) => n.barcode === item.barcode);
          if (dataItem) {
            dataItem.qty += item.qty;
          } else {
            data.push({
              qty: item.qty,
              availableQty: 0,
              operateQty: 0,
              normalQty: 0,
              errorQty: 0,
              overQty: 0,
              actQty: 0,
              barcode: item.barcode,
              ...baseItem(item),
            });
          }
        });
      }
    } catch (e) {}
    setLoading(false);
    return data || [];
  };

  const fetchBoxRfidsData = async (data) => {
    const tableData: ITableDataItem[] = data;
    setLoading(true);
    try {
      const params: any = {
        wibOrderId,
        enablePage: false,
      };
      if (wibOrderBoxId) {
        params.wibBoxId = wibOrderBoxId;
      }
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BwibOrderApi.Rfids(params);
      } else {
        result = await WibOrderApi.Rfids(params);
      }

      if (result.data.length > 0) {
        const normalData: IScreenDataProps[] = [];
        result.data.forEach((item) => {
          if (wibOrderPreBoxId && item.boxId !== wibOrderPreBoxId) {
            return;
          }
          const tableDataItem = tableData.find((n) => n.barcode === item.barcode);

          if (tableDataItem) {
            if (wibOrderBoxId) {
              tableDataItem.actQty = item.qty;
              tableDataItem.normalQty += item.qty;
              tableDataItem.operateQty += item.qty;
            }
          } else {
            const qty = wibOrderPreBoxId || wibOrderBoxId ? item.qty : 0;
            tableData.push({
              qty: 0,
              availableQty: 0,
              normalQty: qty,
              operateQty: qty,
              actQty: qty,
              errorQty: 0,
              overQty: 0,
              barcode: item.barcode,
              ...baseItem(item),
            });
          }
        });
        tableData.forEach((item) => {
          if (item.normalQty > 0) {
            normalData.push({
              barcode: item.barcode,
              boxCode: wibOrderBoxCode,
              qty: item.normalQty,
              operateMode,
            });
          }
        });
        const normalQty = normalData.reduce((prev, next) => prev + next.qty, 0);
        setNormalQty(normalQty);
        setNormalData(normalData);
      }

      let linesRes;
      if (currentUser.mode === 'BINDING') {
        linesRes = await BwibOrderApi.Lines({
          wibOrderId,
          enablePage: false,
        });
      } else {
        linesRes = await WibOrderApi.Lines({
          wibOrderId,
          enablePage: false,
        });
      }

      tableData.forEach((item) => {
        const linesItem = linesRes.data.find((n) => n.barcode === item.barcode);
        const orderAvailableQty = Math.floor(
          MathUtil.accSub(MathUtil.accMul(linesItem.qty, MathUtil.accAdd(1, overPercent)), linesItem.actQty),
        );
        if (!overType) {
          item.availableQty = item.qty - item.actQty;
          if (orderAvailableQty < item.availableQty) {
            item.availableQty = wibOrderBoxId || wibOrderPreBoxId ? orderAvailableQty : 0;
          }
        } else {
          item.availableQty = orderAvailableQty;
        }
      });
    } catch (e) {}
    setLoading(false);
    return tableData || [];
  };

  const fetchOrderLinesData = async () => {
    let data: ITableDataItem[] = [];
    setLoading(true);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BwibOrderApi.Lines({
          wibOrderId,
          enablePage: false,
        });
      } else {
        result = await WibOrderApi.Lines({
          wibOrderId,
          enablePage: false,
        });
      }

      result.data.forEach((item) => {
        let actQty = 0;
        if (!wibOrderBoxId) {
          actQty = item.actQty;
        }
        data.push({
          qty: item.qty,
          availableQty: 0,
          actQty,
          normalQty: 0,
          errorQty: 0,
          overQty: 0,
          operateQty: 0,
          barcode: item.barcode,
          ...baseItem(item),
        });
      });
    } catch (e) {}
    data = data.filter((n) => n.qty !== 0);
    setLoading(false);
    return data || [];
  };

  const checkOverAndError: () => boolean = () => {
    const hasOver = overData.length > 0 || overQty > 0;
    const hasError = errorData.length > 0;
    if (hasOver) {
      overQtyRef.current?.remind();
    }
    if (hasError) {
      errorQtyRef.current?.remind();
    }
    if (hasOver || hasError) {
      const text = i18n.t('global.pleaseHandleOverAndError');
      playAndNotify(text, 'warn');
    }
    return hasOver || hasError;
  };

  const screenReaderDataByRoles = (inputBarcode, size, tableIndex) => {
    if (skuFetching) {
      const text = i18n.t('global.lastBarcodeProcessingTips');
      playAndNotify(text, 'warn');
      return;
    }

    // 当找到记录时，使用表格中的实际条码；否则使用输入的条码
    const barcode = tableIndex !== -1 ? tableData[tableIndex].barcode : inputBarcode;

    if (tableIndex !== -1 && tableData[tableIndex].rfidTag) {
      const text = i18n.t('global.prodOnlyForRFID');
      playAndNotify(text, 'warn');
      return;
    }
    let operateSize = size;
    const errorQty = errorData.find((n) => n.barcode === barcode)?.qty || 0;
    const operateQty = tableData.find((n) => n.barcode === barcode)?.operateQty || 0;
    const currentNormalQty = normalData.find((n) => n.barcode === barcode)?.qty || 0;
    let currentOverQty = 0;
    if (overType === 'OVER_ORDER') {
      currentOverQty = overQty;
    } else {
      currentOverQty = overData.find((n) => n.barcode === barcode)?.qty || 0;
    }
    const pushNormalData = (count) => {
      const index = normalData.findIndex((n) => n.barcode === barcode);
      if (index !== -1) {
        normalData[index].qty = count;
        if (count === 0) {
          normalData.splice(index, 1);
        }
      } else if (count !== 0) {
        normalData.push({
          barcode,
          qty: count,
          boxCode: wibOrderBoxCode,
          operateMode,
        });
      }
      setNormalData(JSON.parse(JSON.stringify(normalData)));
      isOperate.current = true;
    };

    const pushOverData = (count) => {
      const index = overData.findIndex((n) => n.barcode === barcode);
      if (index !== -1) {
        overData[index].qty = count;
        if (count === 0) {
          overData.splice(index, 1);
        }
      } else if (count !== 0) {
        overData.push({
          barcode,
          qty: count,
          boxCode: wibOrderBoxCode,
        });
      }
      setOverData(JSON.parse(JSON.stringify(overData)));
      isOperate.current = true;
    };

    const pushErrorData = (count) => {
      const index = errorData.findIndex((n) => n.barcode === barcode);
      if (index !== -1) {
        errorData[index].qty = count;
        if (count === 0) {
          errorData.splice(index, 1);
        }
      } else if (count !== 0) {
        errorData.push({
          barcode,
          qty: count,
          boxCode: wibOrderBoxCode,
        });
      }
      setErrorData(JSON.parse(JSON.stringify(errorData)));
      isOperate.current = true;
    };

    const handleOverQtyBySKU = () => {
      if (currentNormalQty + currentOverQty + operateSize === 0 && tableData[tableIndex].qty === 0) {
        pushNormalData(0);
        pushOverData(0);
      } else {
        if (currentOverQty < Math.abs(operateSize)) {
          pushOverData(0);
          operateSize += currentOverQty;
        } else {
          pushOverData(currentOverQty + operateSize);
          operateSize = 0;
        }
        pushNormalData(currentNormalQty + operateSize);
        tableData.unshift(...tableData.splice(tableIndex, 1));
      }
    };

    const handleOverQtyByOrder = () => {
      if (operateQty + operateSize === 0) {
        pushNormalData(0);
        currentOverQty += operateSize;
      } else {
        pushNormalData(currentNormalQty + operateSize);
        if (currentOverQty < Math.abs(operateSize)) {
          operateSize += currentOverQty;
          currentOverQty = 0;
          tableData.unshift(...tableData.splice(tableIndex, 1));
        } else {
          currentOverQty += operateSize;
        }
      }
      setOverQty(JSON.parse(JSON.stringify(currentOverQty)));
    };

    const addTableItem = async () => {
      const hide = message.loading(i18n.t('global.loading'), 0);
      setSkuFetching(true);
      try {
        const res: any = await SkuApi.FindByAlias({
          alias: [inputBarcode],
        });
        searchAliasByBarcode({
          barcode: inputBarcode,
          operateSize,
          searchData: res.data,
          callback(foundBarcode, operateSize, index) {
            if (index === -1) {
              const text = i18n.t('global.notFoundBarcode');
              playAndNotify(text, 'warn', `[${inputBarcode}]`);
            } else {
              const barcodeItem = res.data[index];
              if (barcodeItem.rfidTag) {
                const text = i18n.t('global.prodOnlyForRFID');
                playAndNotify(text, 'warn');
                setSkuFetching(false);
                hide();
                return;
              }
              // 使用 barcodeItem.barcode 作为实际条码
              const actualBarcode = barcodeItem.barcode;
              tableData.unshift({
                barcode: actualBarcode,
                qty: 0,
                availableQty: 0,
                normalQty: 0,
                errorQty: 0,
                overQty: 0,
                actQty: 0,
                operateQty: operateSize,
                rfidTag: barcodeItem.rfidTag,
                disturbTag: barcodeItem.disturbTag,
                notInRange: true,
                ...baseItem(barcodeItem),
                skuName: barcodeItem.name,
              });
              normalData.push({
                barcode: actualBarcode,
                qty: operateSize,
                boxCode: wibOrderBoxCode,
                operateMode,
              });
              setNormalData(JSON.parse(JSON.stringify(normalData)));
              setTableData(JSON.parse(JSON.stringify(tableData)));
              if (overType === 'OVER_ORDER' && overQty > 0) {
                setOverQty(overQty + operateSize);
              }
              const text = i18n.t('global.scanSuccess');
              playAndNotify(text, 'success');
            }
          },
        });
      } catch (e) {}
      hide();
      setSkuFetching(false);
      isOperate.current = true;
    };

    if (operateSize > 0) {
      if (localTag && !fmOrderCode) {
        if (tableIndex !== -1) {
          pushNormalData(currentNormalQty + operateSize);
        } else {
          addTableItem();
          return;
        }
      } else if (tableIndex !== -1) {
        if (!crossTag && currentNormalQty + operateSize > tableData[tableIndex].qty && !overTag) {
          const text = i18n.t('global.readedQtyExcessOrderQty');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }
        pushNormalData(currentNormalQty + operateSize);
      } else {
        if (!crossTag) {
          const text = i18n.t('global.barcodeNotInOrder');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }
        addTableItem();
        return;
      }
    } else {
      if (operateQty + operateSize < 0) {
        const text = i18n.t('global.deleteQtyExcess');
        playAndNotify(text, 'warn');
        return;
      }
      if (errorQty > 0) {
        if (errorQty < Math.abs(operateSize)) {
          const text = i18n.t('global.deleteQtyExcess');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }
        if (errorQty + operateSize === 0 && tableData[tableIndex].qty === 0) {
          if (wibOrderPreBoxId) {
            tableData.splice(tableIndex, 1);
            pushNormalData(0);
          }
          pushErrorData(0);
        } else {
          pushErrorData(errorQty + operateSize);
        }
      } else if (currentOverQty > 0) {
        if (overType === 'OVER_ORDER') {
          handleOverQtyByOrder();
        } else {
          handleOverQtyBySKU();
        }
      } else {
        if (operateQty + operateSize < 0) {
          const text = i18n.t('global.deleteQtyExcess');
          playAndNotify(text, 'warn');
          return;
        }
        pushNormalData(currentNormalQty + operateSize);
      }
    }
    const text = i18n.t('global.scanSuccess');
    playAndNotify(text, 'success');

    if (overQty === 0 && errorData.length === 0) {
      const normalQty = normalData.reduce((prev, next) => prev + next.qty, 0);
      setNormalQty(normalQty);
    }

    tableData[tableIndex].operateQty += operateSize;
    if (tableData[tableIndex].qty === 0 && tableData[tableIndex].operateQty === 0) {
      tableData.splice(tableIndex, 1);
    }
    if (overType === 'OVER_ORDER' && operateSize > 0 && overQty > 0) {
      setOverQty(overQty + operateSize);
    }
    setTableData(JSON.parse(JSON.stringify(tableData)));
  };

  const saveBtnOnClick = async () => {
    if (isCustomizedBoxCode && !customBoxCode) {
      const text = i18n.t('global.pleaseInputBoxCode');
      playAndNotify(text, 'warn');
      setBoxCodeModalVisible(true);
      return;
    }

    if (checkOverAndError()) {
      return;
    }
    setSaving(true);

    const payload: Record<string, any> = {
      data: normalData.map((item: Record<string, any>) => {
        if (isCustomizedBoxCode && customBoxCode) {
          item.customBoxCode = customBoxCode;
        }
        if (enableBoxSpec && boxSpecId) {
          item.boxSpecId = boxSpecId;
        }
        return item;
      }),
      wibOrderId,
    };

    try {
      if (currentUser.mode === 'BINDING') {
        await BwibOrderApi.Save(payload);
      } else {
        await WibOrderApi.Save(payload);
      }
      const text = i18n.t('global.saveSuccess');
      playAndNotify(text, 'success');
      if (onOk) onOk();
    } catch (err: any) {
      let responseData: any[] = [];
      if (err.response?.data?.detailMsg) {
        responseData = JSON.parse(err.response.data.detailMsg);
      }
      if (responseData.length > 0) {
        const text = i18n.t('global.pleaseHandleOverAndError');
        playAndNotify(text, 'warn');
        const responseOverData: any[] = responseData.filter(
          (item) => item.type === 'SKU_OVER_ERROR' || item.type === 'ORDER_OVER_ERROR',
        );
        const responseErrorData: any[] = responseData.filter(
          (item) => item.type === 'CROSS_ERROR' || item.type === 'OTHER_ERROR',
        );
        const newOverData: IScreenDataProps[] = [];
        const newErrorData: IScreenDataProps[] = [];
        let newNormalQty = normalQty;

        if (responseOverData[0].type === 'ORDER_OVER_ERROR') {
          setOverQty(responseOverData[0].overQty);
          newNormalQty -= responseOverData[0].overQty;
        } else if (responseOverData[0].type === 'SKU_OVER_ERROR') {
          responseOverData.forEach((item) => {
            newOverData.push({
              boxCode: wibOrderBoxCode,
              barcode: item.barcode,
              qty: item.overQty,
            });
          });
          const overQty = newOverData.reduce((prev, next) => prev + next.qty, 0);
          newNormalQty -= overQty;
          setOverData(newOverData);
        }

        responseErrorData.forEach((item) => {
          newErrorData.push({
            boxCode: wibOrderBoxCode,
            barcode: item.barcode,
            qty: item.errQty,
          });
        });
        const errorQty = newErrorData.reduce((prev, next) => prev + next.qty, 0);
        newNormalQty -= errorQty;
        setErrorData(newErrorData);
        setNormalQty(newNormalQty);
        if (responseOverData[0].type !== 'ORDER_OVER_ERROR') {
          responseData.forEach((item) => {
            const responseQty = item.errQty === 0 ? item.overQty : item.errQty;
            const normalDataIndex = normalData.findIndex((n) => n.barcode === item.barcode);
            if (responseQty) {
              normalData[normalDataIndex].qty -= responseQty;
            }
            if (normalData[normalDataIndex].qty === 0) {
              normalData.splice(normalDataIndex, 1);
            }
          });
          setNormalData(JSON.parse(JSON.stringify(normalData)));
        }
      }
    }
    setSaving(false);
  };

  const barcodeInputOnPressEnter = (barcode, operateSize) => {
    if (!operateSize) {
      const text = i18n.t('global.pleaseInputOperateQty');
      playAndNotify(text, 'warn');
      return;
    }
    if (operateSize === 0) {
      const text = i18n.t('global.operateQtyError');
      playAndNotify(text, 'warn');
      return;
    }
    if (barcode) {
      searchAliasByBarcode({ barcode, searchData: tableData, operateSize, callback: screenReaderDataByRoles });
    }
  };

  const resetBtnOnClick = () => {
    const text = i18n.t('global.confirmDiscard');
    playSound({ text });
    NoticeUtil.confirm({
      content: text,
      onOk: () => {
        tableData.forEach((item) => {
          if (wibOrderBoxId) {
            item.availableQty += item.actQty;
          }
          item.operateQty = 0;
          item.overQty = 0;
          item.errorQty = 0;
        });
        setTableData(JSON.parse(JSON.stringify(tableData)));
        setNormalData([]);
        setErrorData([]);
        setOverData([]);
        setOverQty(0);
        setNormalQty(0);
        stopSound();
        barcodeInputRef.current?.reset();
      },
    });
  };

  const dataViewTableOnDelete = (data) => {
    const tableItem = tableData.find((n) => n.barcode === data);
    if (overType === 'OVER_ORDER' && tableItem) {
      if (overQty > 0) {
        if (overQty < tableItem.operateQty) {
          setNormalQty(normalQty - (tableItem.operateQty - overQty));
          setOverQty(0);
        } else {
          setOverQty(overQty - tableItem.operateQty);
        }
      }
    }

    setNormalData(normalData.filter((n) => n.barcode !== data));
    setErrorData(errorData.filter((n) => n.barcode !== data));
    setOverData(overData.filter((n) => n.barcode !== data));
    setTableData(tableData.filter((n) => n.barcode !== data));
  };

  const dataViewTableOnReset = async (data) => {
    const tableItem = tableData.find((n) => n.barcode === data);
    const newNormalData = normalData.filter((n) => n.barcode !== data);

    if (overType === 'OVER_ORDER' && tableItem) {
      if (overQty) {
        if (tableItem.operateQty > overQty) {
          setOverQty(0);
          setNormalQty(normalQty - (tableItem.operateQty - overQty));
        } else {
          setOverQty(overQty - tableItem.operateQty);
        }
      } else {
        setNormalQty(normalQty - tableItem.operateQty);
      }
    } else {
      const normalQty = newNormalData.reduce((prev, next) => prev + next.qty, 0);
      setNormalQty(normalQty);
    }

    if (tableItem) {
      if (wibOrderBoxId) {
        tableItem.availableQty += tableItem.actQty;
      }
      tableItem.operateQty = 0;
      tableItem.overQty = 0;
      tableItem.errorQty = 0;
    }
    setTableData(JSON.parse(JSON.stringify(tableData)));
    setErrorData(errorData.filter((n) => n.barcode !== data));
    setOverData(overData.filter((n) => n.barcode !== data));
    setNormalData(newNormalData);
  };

  const fetchSkuInfoByBarcodeList = async (data) => {
    const tableData: ITableDataItem[] = data;
    if (tableData.length > 0) {
      const barcodeList = tableData.map((n) => n.barcode);
      try {
        const result: any = await SkuApi.FindByBarcodeList({
          barcodeList,
        });
        result.forEach((item) => {
          const tableItem = tableData.find((n) => n.barcode === item.barcode);
          if (tableItem) {
            tableItem.alias = item.alias;
            tableItem.source = item.source;
            tableItem.brandName = item.brandName;
            tableItem.disturbTag = item.disturbTag;
            tableItem.rfidTag = item.rfidTag;
          }
        });
      } catch (e) {}
    }
    return tableData;
  };

  const initOrderData = async () => {
    let tableData: ITableDataItem[] = [];
    try {
      if (wibOrderPreBoxId) {
        tableData = await fetchPreBoxRfidsData();
      } else if (fmOrderCode || !localTag) {
        tableData = await fetchOrderLinesData();
      }
      tableData = await fetchBoxRfidsData(tableData);
      tableData = await fetchSkuInfoByBarcodeList(tableData);
      setTableData(tableData);
      // 按单装箱可编辑箱号
      if (!wibOrderPreBoxId && !wibOrderBoxId) {
        const checked = LocalStorageUtil.getItem(localBoxKey);
        setIsCustomizedBoxCode(checked);
        if (checked) setBoxCodeModalVisible(true);
      }

      // 这里使用原生的方式获取是因为，封装的方法获取数据时会修改数据，数字类型会因为精度问题而修改
      const localBoxSpecId = localStorage.getItem(LocalStorageUtil.makeKey(boxSpecIdKey));
      if (localBoxSpecId) setBoxSpecId(localBoxSpecId);
    } catch (e) {}
  };

  useEffect(() => {
    if (errorData.length > 0) {
      errorQtyRef.current?.remind();
    }

    tableData.forEach((item) => {
      item.errorQty = errorData.find((n) => item.barcode === n.barcode)?.qty || 0;
    });

    setTableData(JSON.parse(JSON.stringify(tableData)));
    // eslint-disable-next-line
  }, [errorData]);

  useEffect(() => {
    tableData.forEach((item) => {
      const overItem = overData.find((n) => item.barcode === n.barcode);
      if (overItem) {
        item.overQty = overItem.qty;
      } else {
        item.overQty = 0;
      }
    });
    setTableData(JSON.parse(JSON.stringify(tableData)));
    if (overType !== 'OVER_ORDER') {
      const overQty = overData.reduce((prev, next) => prev + next.qty, 0);
      setOverQty(overQty);
    }
    // eslint-disable-next-line
  }, [overData]);

  useEffect(() => {
    tableData.forEach((item) => {
      item.normalQty = normalData.find((n) => item.barcode === n.barcode)?.qty || 0;
    });
    setTableData(JSON.parse(JSON.stringify(tableData)));

    if (overType !== 'OVER_ORDER') {
      const normalQty = normalData.reduce((prev, next) => prev + next.qty, 0);
      setNormalQty(normalQty);
    }
    // eslint-disable-next-line
  }, [normalData]);

  useEffect(() => {
    normalQtyRef.current?.remind();
  }, [normalQty]);

  useEffect(() => {
    overQtyRef.current?.remind();
  }, [overQty]);

  useEffect(() => {
    if (errorData.length > 0 || overQty > 0) {
      let text = '';
      if (errorData.length > 0 && overQty > 0) {
        text = i18n.t('global.detectedErrorAndOverMsg', { errorQty: errorData.length, overQty });
      } else if (errorData.length > 0 && overQty === 0) {
        text = i18n.t('global.detectedErrorMsg', { errorQty: errorData.length });
      } else if (errorData.length === 0 && overQty > 0) {
        text = i18n.t('global.detectedOverMsg', { overQty });
      }
      playAndNotify(text, 'warn');
    }
    // eslint-disable-next-line
  }, [errorData, overQty, playAndNotify]);

  useEffect(() => {
    if (modalProps?.open === true) {
      initOrderData();
      onWindowBeforeUnload();
    } else {
      offWindowBeforeUnload();

      isOperate.current = false;
      setNormalData([]);
      setErrorData([]);
      setOverData([]);
      setTableData([]);
      setOverQty(0);
      setNormalQty(0);
      setLoading(false);
      setSaving(false);
      setOverType('');
      setOverPercent(0);
      setCustomBoxCode('');
      setIsCustomizedBoxCode(false);
      setBoxCodeModalVisible(false);
      stopSound();
    }
    // eslint-disable-next-line
  }, [modalProps?.open]);

  useEffect(() => {
    if (modalProps?.open) {
      if (overTag) {
        if (overOrder !== 0) {
          setOverType('OVER_ORDER');
          setOverPercent(overOrder * 100);
        } else if (overSku !== 0) {
          setOverType('OVER_SKU');
          setOverPercent(overSku * 100);
        }
      }
    }
    // eslint-disable-next-line
  }, [modalProps?.open]);

  let alertMessage = '';
  if (overType === 'OVER_ORDER') {
    alertMessage = `${i18n.t('global.excessByOrder')}(${overPercent}%)`;
  } else if (overType === 'OVER_SKU') {
    alertMessage = `${i18n.t('global.excessByBarcode')}(${overPercent}%)`;
  }

  const boxCodeOnChange = (checked) => {
    LocalStorageUtil.setItem(localBoxKey, checked);
    setIsCustomizedBoxCode(checked);
    if (checked) {
      setBoxCodeModalVisible(true);
    } else {
      setCustomBoxCode('');
    }
  };

  const boxCodeModalOnOk = (value) => {
    setCustomBoxCode(value);
    setBoxCodeModalVisible(false);
  };

  const boxSpecSelectOnChange = (id: string) => {
    setBoxSpecId(id);
    LocalStorageUtil.setItem(boxSpecIdKey, id.toString());
  };

  const handleBarcodeAliasModalOnOk = (selectedRows) => {
    barcodeAliasModalOnOk({ selectedRows, searchData: tableData, callback: screenReaderDataByRoles });
  };

  const modalTitle = (
    <div className="flex h-6 items-center justify-between">
      <div className="flex items-center gap-x-2">
        <span>
          {i18n.t('global.barcodeInbound')}
          {/* 预装箱显示箱号方式 */}
          {wibOrderBoxCode ? ` ${!wibOrderPreBoxId || !wibOrderBoxId ? `[${wibOrderBoxCode}]` : ''}` : ''}
        </span>
        {wibOrderBoxStatus === 'NEW' && (
          <Button type="ghost" icon={<ExchangeIcon className="fill-lead-slate" />} onClick={onOperateModeChange} />
        )}
        {isCustomizedBoxCode && (
          <Button
            type="default"
            icon={<FontAwesomeIcon icon={faExpand} />}
            onClick={() => setBoxCodeModalVisible(true)}
          >
            {customBoxCode || i18n.t('global.notScanBoxNumber')}
          </Button>
        )}
        {enableBoxSpec && <BoxSpecSelect className="w-48" value={boxSpecId} onChange={boxSpecSelectOnChange} />}
      </div>
      <div className="flex items-center gap-x-2">
        {!wibOrderPreBoxId && !wibOrderBoxId && (
          <div>
            <span className="mr-2 text-base">{i18n.t('global.customizedBoxNumbers')}</span>
            <Switch checked={isCustomizedBoxCode} onChange={boxCodeOnChange} />
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      footer={false}
      title={modalTitle}
      centered
      fullScreen
      maskClosable={false}
      destroyOnClose
      {...modalProps}
      onCancel={() => {
        const commonLogic = () => {
          const fakeMouseEvent: any = {};
          if (modalProps?.onCancel) {
            modalProps.onCancel(fakeMouseEvent);
          }
        };
        if (isOperate.current) {
          const text = i18n.t('global.confirmDiscard');
          playSound({ text });
          NoticeUtil.confirm({
            content: text,
            onOk: () => {
              commonLogic();
            },
          });
        } else {
          commonLogic();
        }
      }}
      className="operate-modal"
    >
      <Spin spinning={loading}>
        <div className="flex h-full flex-col gap-y-4">
          <DataViewTable
            className="flex-auto"
            leftTBar={
              <Space size="large">
                <BarcodeInput
                  range={barcodePackingThreshold ? [-barcodePackingThreshold, barcodePackingThreshold] : undefined}
                  innerRef={barcodeInputRef}
                  onPressEnter={barcodeInputOnPressEnter}
                  enableBarcodeScanCutoff={enableBarcodeScanCutoff}
                  barcodeCutoffBeforeDigits={barcodeCutoffBeforeDigits}
                  barcodeCutoffAfterDigits={barcodeCutoffAfterDigits}
                />
                {overType && (
                  <span className="flex items-center rounded-full bg-lead-orange/[.12] px-3 py-1.5 text-sm font-semibold leading-5 text-lead-orange">
                    <span>{alertMessage}</span>
                  </span>
                )}
              </Space>
            }
            overType={overType}
            data={tableData}
            onDelete={dataViewTableOnDelete}
            onReset={dataViewTableOnReset}
            localTag={localTag}
          />
          <Row justify="space-between" align="bottom">
            <Col>
              <Space>
                <Button
                  type="success"
                  size="large"
                  loading={saving}
                  onClick={saveBtnOnClick}
                  disabled={!(normalData.length > 0)}
                >
                  {i18n.t('global.save')}
                </Button>
                {normalData.length > 0 && (
                  <Button danger size="large" onClick={resetBtnOnClick}>
                    {i18n.t('global.reset')}
                  </Button>
                )}
              </Space>
            </Col>
            <Col>
              <Space>
                <FlashStatistic
                  innerRef={normalQtyRef}
                  title={i18n.t('global.normal')}
                  value={normalQty}
                  type="success"
                />
                {overQty > 0 && (
                  <FlashStatistic
                    // label={(overType === 'OVER_ORDER' && '按单')
                    // || (overType === 'OVER_SKU' && '按条码') || ''}
                    innerRef={overQtyRef}
                    title={i18n.t('global.excessQty')}
                    value={overQty}
                    type="warning"
                  />
                )}
                {errorData.length > 0 && (
                  <FlashStatistic
                    innerRef={errorQtyRef}
                    title={i18n.t('global.exceptQty')}
                    value={errorData.reduce((prev, next) => prev + next.qty, 0)}
                    type="error"
                  />
                )}
              </Space>
            </Col>
          </Row>
        </div>
        <BoxCodeModal
          modalProps={{
            open: boxCodeModalVisible,
          }}
          boxCode={customBoxCode}
          onOk={boxCodeModalOnOk}
          onClose={() => setBoxCodeModalVisible(false)}
        />
        <BarcodeAliasModal
          open={barcodeAliasModalOpen}
          onCancel={barcodeAliasModalOnCancel}
          onOk={handleBarcodeAliasModalOnOk}
          data={barcodeAliasData}
          selectRowFiledId="barcode"
        />
      </Spin>
    </Modal>
  );
};

export default BarcodeInboundModal;
