import { Form } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import * as WobOrderApi from 'common/api/warehouse/WobOrder';
import * as BWobOrderApi from 'common/api/warehouse/BwobOrder';

interface IPrintModal {
  visible: boolean;
  onCancel: () => void;
  form?: any;
  onSave: (value) => void;
}

interface ILineData {
  qty: number;
  barcode: string;
  epc?: string;
}

const PrintModal: React.FC<IPrintModal> = (props) => {
  const { visible, onCancel, onSave } = props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const params: any = useParams();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const [lineData, setLineData] = useState<ILineData[]>([]);

  const fetchLinesData = async (data) => {
    try {
      data.wobOrderId = params.id;
      data.enablePage = false;
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BWobOrderApi.Lines(data);
      } else {
        res = await WobOrderApi.Lines(data);
      }

      const newLineData: ILineData[] = [];
      if (res.data.length > 0) {
        res.data.forEach((n) => {
          newLineData.push({
            qty: n.qty,
            barcode: n.barcode,
          });
        });
        setLineData(newLineData);
      }
    } catch (e) {}
  };

  useEffect(() => {
    if (visible) {
      fetchLinesData({});
    } else {
      form.resetFields();
    }
    // eslint-disable-next-line
  }, [visible]);

  const onOk = async () => {
    const value = await form.validateFields();
    value.data = lineData;
    value.sourceId = params.id;
    value.sourceType = 'WO';
    if (onSave) {
      onSave(value);
    }
  };

  return (
    <Modal title={i18n.t('global.newPrintTask')} open={visible} onOk={onOk} destroyOnClose onCancel={onCancel}>
      <Form form={form} layout="vertical">
        <Form.Item name="remark" label={i18n.t('global.remark')}>
          <TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PrintModal;
