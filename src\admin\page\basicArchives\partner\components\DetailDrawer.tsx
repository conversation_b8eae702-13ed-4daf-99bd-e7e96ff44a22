import { Form, Tabs } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/core/Partner';
import * as DevConfigSettingApi from 'common/api/core/DevConfigSetting';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import PartnerPayConfig, { PartnerPayConfigPropsRef } from './PartnerPayConfig';

import OperateForm, { OperateFormInnerRef } from './OperateForm';
import Storage from './Storage';

import './DetailDrawer.css';
import AntiTheftConfigTable, { AntiTheftConfigRef } from './antiTheftConfig/AntiTheftConfigTable';

interface IPartnerDetailProps {
  type: string;
  partnerId?: string;
  open?: boolean;
  onCancel?: () => void;
}

const DetailDrawer: React.FC<IPartnerDetailProps> = (props) => {
  const { type, partnerId, open, onCancel } = props;
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [tabsActiveKey, setTabsActiveKey] = useState('INFO');
  const operateFormRef = useRef<OperateFormInnerRef>();
  const payConfigRef = useRef<PartnerPayConfigPropsRef>();
  const antiTheftConfigRef = useRef<AntiTheftConfigRef>();
  const [currentSource, setCurrentSource] = useState<Record<string, any>>();

  const [permission] = usePermission('A:BASE:PARTNER');
  const editPermission = permission.codes.includes('EDIT');
  const delPermission = permission.codes.includes('DEL');
  const editPayConfigPermission = permission.codes.includes('EDIT_PAY_CONFIG');

  const titleMap = {
    WAREHOUSE: i18n.t('global.warehouseDetails'),
    SHOP: i18n.t('global.shopDetails'),
    FACTORY: i18n.t('global.factoryDetails'),
    SUPPLIER: i18n.t('global.supplierDetails'),
  };

  const fetchPartner = async () => {
    setLoading(true);
    try {
      const rec = await PartnerApi.Get({
        id: partnerId,
      });
      setRecord(rec);
      setCurrentSource(undefined);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const refresh = () => {
    // 1. 获取partner信息
    fetchPartner();
  };

  useEffect(() => {
    if (partnerId && open) {
      refresh();
      setTimeout(() => {
        operateFormRef.current?.focusNameInput();
      }, 300);
    }
    if (!open) {
      setRecord({});
      setTabsActiveKey('INFO');
    }
    // eslint-disable-next-line
  }, [partnerId, open]);

  const onFinish = async (values) => {
    if (values.area) {
      switch (values.area.length) {
        case 1:
          values.province = values.area[0];
          break;
        case 2:
          values.province = values.area[0];
          values.city = values.area[1];
          break;
        case 3:
          values.province = values.area[0];
          values.city = values.area[1];
          values.town = values.area[2];
          break;
        default:
          break;
      }
    }
    delete values.area;
    if (values.telNumber && values.telArea) {
      values.tel = `+(${values.telArea})${values.telNumber}`;
    } else if (values.telArea) {
      values.tel = `+(${values.telArea})`;
    } else {
      values.tel = values.telNumber;
    }
    if (values.mobileNumber && values.mobileArea) {
      values.mobile = `+(${values.mobileArea})${values.mobileNumber}`;
    } else if (values.mobileArea) {
      values.mobile = `+(${values.mobileArea})`;
    } else {
      values.mobile = values.mobileNumber;
    }
    delete values.telArea;
    delete values.mobileArea;
    delete values.telNumber;
    delete values.mobileNumber;
    values.disabled = !values.status;
    delete values.status;
    // setSaving(true);
    try {
      // 保存当前type为SHOP，payConfig字段的值
      if (type === 'SHOP' && record.payConfig && !values.payConfig) {
        values.payConfig = record.payConfig;
      }
      const payConfigValues: any = await payConfigRef.current?.getValues();
      if (payConfigValues) {
        const { payConfig, tabs, values: configValues } = payConfigValues;
        if (payConfig) values.payConfig = payConfig; // 设置门店支付配置是全局还是自定义
        // 保存停用启用支付方式
        if (tabs && tabs.length > 0 && editPayConfigPermission) {
          await DevConfigSettingApi.SetDisable({ category: tabs, partnerId: record.id });
        }
        // 保存支付配置值
        if (configValues && configValues.length > 0 && editPayConfigPermission) {
          await DevConfigSettingApi.Save({ data: configValues, partnerId: record.id });
        }
      }
      await PartnerApi.Update({
        id: record.id,
        enableAntiTheft: record?.enableAntiTheft,
        ...values,
      });
      fetchPartner();
      setSaving(false);
      if (onCancel) onCancel();
      NoticeUtil.success();
    } catch (e) {
      setSaving(false);
    }
  };

  const deletePartner = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await PartnerApi.Delete({ id: record.id });
          NoticeUtil.success();
          if (onCancel) onCancel();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    setTabsActiveKey('INFO');
    fetchPartner();
    if (currentSource) {
      form.setFieldsValue(currentSource);
    }
    payConfigRef.current?.refresh();
    antiTheftConfigRef.current?.refresh();
  };

  useEffect(() => {
    if (form && record) {
      if (record.tel?.indexOf(')') !== -1) {
        record.telArea = record.tel?.slice(2, record.tel.indexOf(')'));
      }
      record.telNumber = record.tel?.slice(record.tel.indexOf(')') + 1);
      if (record.mobile?.indexOf(')') !== -1) {
        record.mobileArea = record.mobile?.slice(2, record.mobile.indexOf(')'));
      }
      record.mobileNumber = record.mobile?.slice(record.mobile.indexOf(')') + 1);
      record.status = !record.disabled;
      setCurrentSource(record);
      form.setFieldsValue(record);
    }
  }, [form, record]);

  const drawerOnClose = () => {
    if (onCancel) onCancel();
  };

  const defaultValue: any = [];
  if (record.province) {
    defaultValue[0] = record.province;
    if (record.city) {
      defaultValue[1] = record.city;
      if (record.town) {
        defaultValue[2] = record.town;
      }
    }
  }

  const items = useMemo(() => {
    const result = [
      {
        label: i18n.t('global.information'),
        key: 'INFO',
        children: (
          <OperateForm
            innerRef={operateFormRef}
            type={type}
            form={form}
            name="editForm"
            onFinish={onFinish}
            statusVisible
            codeReadOnly
          />
        ),
      },
      {
        label: i18n.t('global.subWarehouse'),
        key: 'STOCKAGE',
        children: <Storage type={type} partnerRecord={record} />,
      },
    ];

    if (type === 'WAREHOUSE' || type === 'SHOP') {
      result.push({
        label: i18n.t('global.theftPreventionConfiguration'),
        key: 'ANTI_THEFT_CONFIG',
        children: <AntiTheftConfigTable innerRef={antiTheftConfigRef} partnerRecord={record} />,
      });
    }

    if (type === 'SHOP') {
      result.push({
        label: i18n.t('global.payConfig'),
        key: 'PAY_CONFIG',
        children: <PartnerPayConfig innerRef={payConfigRef} partnerId={record.id} payConfig={record.payConfig} />,
      });
    }
    return result;
    // eslint-disable-next-line
  }, [type, record, form]);

  return (
    <Drawer
      width={tabsActiveKey === 'ANTI_THEFT_CONFIG' ? 500 : 900}
      open={open}
      title={record ? `${titleMap[record.type]} [${record.code}]` : `${i18n.t('global.detail')} [${record.code}]`}
      destroyOnClose
      maskClosable={false}
      onClose={drawerOnClose}
      footer={
        <DrawerFooter
          applyPermission={editPermission}
          applyBtnProps={{
            loading: saving,
          }}
          onApply={async () => {
            if (tabsActiveKey === 'ANTI_THEFT_CONFIG') {
              await antiTheftConfigRef.current?.submit();
              if (onCancel) onCancel();
            } else if (form) form.submit();
          }}
          deletePermission={delPermission}
          onDelete={deletePartner}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="INFO"
          size="small"
          activeKey={tabsActiveKey}
          onChange={(e) => setTabsActiveKey(e)}
          items={items}
        />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
