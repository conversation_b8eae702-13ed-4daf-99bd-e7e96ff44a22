import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/list',
    method: 'post',
    data,
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/get',
    method: 'post',
    data,
  });

export const Boxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/boxes',
    method: 'post',
    data,
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/rfids',
    method: 'post',
    data,
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/lines',
    method: 'post',
    data,
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/confirm',
    method: 'post',
    data,
  });

export const FmProdList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/fm-prod-list',
    method: 'POST',
    data,
  });

export const Imports = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/imports',
    method: 'post',
    data,
    ...config,
  });

export const BatchImports = (data: RequestPayloadType, config: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/batch-imports',
    method: 'post',
    data,
    ...config,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/logs',
    method: 'post',
    data,
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/update',
    method: 'post',
    data,
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/cancel',
    method: 'post',
    data,
  });

export const BoxSave = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/box-save',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/box-reset',
    method: 'post',
    data,
  });

export const BoxDelete = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/box-delete',
    method: 'post',
    data,
  });

export const SumQuery = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/barcode/sum-query',
    method: 'post',
    data,
  });

export const MatchRuleList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/match-rule/list',
    method: 'post',
    data,
  });

export const PreBoxes = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/pre-boxes',
    method: 'post',
    data,
  });

export const PreBoxRfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/pre-box-rfids',
    method: 'post',
    data,
  });

export const BoxSkuSave = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/box-sku-save',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/batch-cancel',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/batch-confirm',
    method: 'post',
    data,
  });
