import { ReactNode } from 'react';

export type LogsItem = {
  boxCode: string;
  fromPartnerCode: string | null;
  fromPartnerId: string | null;
  fromPartnerName: string | null;
  fromPartnerType: string | null;
  fromPartnerTypeDesc: string | null;
  operateUser: string;
  operated: string;
  orderCode: string;
  partnerCode: string;
  partnerId: string;
  partnerName: string;
  partnerType: string;
  partnerTypeDesc: string;
  source: string;
  sourceDesc: string;
  toPartnerCode: string | null;
  toPartnerId: string | null;
  toPartnerName: string | null;
  toPartnerType: string | null;
  toPartnerTypeDesc: string | null;
  renderPartnerCode: string;
  renderPartnerName: string;
  renderPartnerType: string;
  renderPartnerTypeDesc: string;
  icon: ReactNode;
};
