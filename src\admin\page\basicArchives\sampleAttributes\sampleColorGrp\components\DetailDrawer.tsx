import { Form, Tabs } from 'antd';
import * as SampleColorGrpApi from 'common/api/sample/SampleColorGrp';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useState } from 'react';
import ColorOperateForm from './ColorOperateForm';
import ColorGrpOperateForm from './ColorGrpOperateForm';

import { ISampleColorGrpItem } from '../data';

interface EditModalProps {
  visible: boolean;
  confirmLoading: boolean;
  colorId: string | undefined;
  onSubmit: (values: ISampleColorGrpItem) => void;
  onCancel: () => void;
}

const DetailDrawer: React.FC<EditModalProps> = (props) => {
  const [form] = Form.useForm();
  const [grpid, setGrpid] = useState('');
  const { visible, confirmLoading, colorId, onSubmit, onCancel } = props;
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState('INFO');
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchColor = useCallback(async () => {
    setLoading(true);
    try {
      const colorGrp: any = await SampleColorGrpApi.Get({
        id: colorId,
      });
      setGrpid(colorGrp.id);
      setCurrent(colorGrp);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [colorId]);

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISampleColorGrpItem);
    }
  };

  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleColorGrpApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchColor();
    setActiveKey('INFO');
  };

  useEffect(() => {
    if (visible && colorId) {
      fetchColor();
    }
    return () => {
      setActiveKey('INFO');
    };
  }, [visible, colorId, fetchColor, form]);

  return (
    <Drawer
      title={
        <>
          {i18n.t('global.editColorGrp')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      width={600}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!colorId}
          onDelete={deleteBtnOnClick}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="1"
          animated={false}
          activeKey={activeKey}
          onChange={(e) => setActiveKey(e)}
          items={[
            {
              label: i18n.t('global.information'),
              key: 'INFO',
              children: <ColorGrpOperateForm current={current} form={form} onFinish={handleFinish} />,
            },
            {
              label: i18n.t('global.colors'),
              key: 'COLOR',
              children: <ColorOperateForm grpid={grpid} />,
            },
          ]}
        />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
