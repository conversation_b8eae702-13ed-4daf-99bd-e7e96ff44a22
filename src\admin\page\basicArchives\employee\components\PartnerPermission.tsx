import { Checkbox, Space, Tabs } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/core/Partner';
import * as PartnerPermissionApi from 'common/api/core/PartnerPermission';
import * as WarehouseApi from 'common/api/core/Warehouse';
import { PowerTableColumnsType } from 'common/components/PowerTable';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import ImportButton from 'common/components/Button/Import';
import { usePermission } from 'common/utils/Permission';
import { CurrentTypePartnerListItem, CurrentTypeWarehouseListItem } from './data';
import { DetailDrawerContext, TDetailDrawerContext } from './DetailDrawerReducer';
import PartnerPermissionTransferTable, {
  PartnerPermissionTransferTablePropsRef,
} from './PartnerPermissionTransferTable';
import PartnerImportModal from './PartnerImportModal';

interface PartnerPermissionPropsRef {
  recover: () => void;
}

interface PartnerPermissionProps {
  innerRef?: React.MutableRefObject<PartnerPermissionPropsRef | undefined>;
}

const PartnerPermission: React.FC<PartnerPermissionProps> = (props) => {
  const { innerRef } = props;
  const { state, dispatch } = useContext<TDetailDrawerContext>(DetailDrawerContext);
  const {
    empId,
    type,
    defaultAllPermissionFlag,
    allPermissionFlag,
    warehouseRowKeys,
    factoryRowKeys,
    shopRowKeys,
    tabActiveKey,
  } = state;
  const [loading, setLoading] = useState(false);
  const [partnerSourceList, setPartnerSourceList] = useState<CurrentTypePartnerListItem[]>([]);
  const [warehouseListSource, setWarehouseListSource] = useState<CurrentTypeWarehouseListItem[]>([]);
  const partnerPermissionTransferTableRef = useRef<PartnerPermissionTransferTablePropsRef | undefined>(undefined);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const [permission] = usePermission('A:BASE:EMPLOYEE');
  const partnerImportPermission = permission.codes.includes('PARTNER_IMPORT');

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const partnerListResp: any = await PartnerApi.List({
        enablePage: false,
        type: ['WAREHOUSE', 'SHOP', 'FACTORY'],
      });
      let partnerList = partnerListResp.data.map((n) => ({
        ...n,
        children: [],
      }));
      const partnerIds = partnerList.map((n) => n.id);
      const warehouseListResp: any = await WarehouseApi.List({
        enablePage: false,
        partnerIds,
      });
      const { data } = warehouseListResp;
      setWarehouseListSource(data);
      const warehouseList = data;
      // ------------------
      // 非选取'全部'时回显已选取权限
      let partnerPermissionList: any[] = [];
      if (!defaultAllPermissionFlag) {
        const partnerPermissionResp = await PartnerPermissionApi.List({
          empId,
          enablePage: false,
        });
        partnerPermissionList = partnerPermissionResp.data;
        dispatch({ type: 'setTypeSelectRowKeys', payload: partnerPermissionList, tabActiveKey: type });
      }

      // ------------------
      warehouseList.forEach((warehouse) => {
        const foundFromPartnerList = partnerList.find((item) => item.id === warehouse.partnerId);
        const foundFromPermissionList = partnerPermissionList.find((item) => item.warehouseId === warehouse.id);
        if (foundFromPermissionList) {
          warehouse.permissionDataId = foundFromPermissionList.id;
        }
        foundFromPartnerList.children.push(warehouse);
      });

      partnerList = partnerList.filter((n) => n.children.length !== 0);
      setPartnerSourceList(partnerList);

      setLoading(false);
    } catch {
      setLoading(false);
    }
  }, [defaultAllPermissionFlag, empId, type, dispatch]);

  const tabsOnChange = useCallback(
    (key) => {
      dispatch({ type: 'setTabActiveKey', payload: key });
      // tabs切换时把对应选中的数据保存在currentTypeSelectRowKeys
      const keysMap = {
        WAREHOUSE: warehouseRowKeys,
        SHOP: shopRowKeys,
        FACTORY: factoryRowKeys,
      };
      dispatch({ type: 'setCurrentTypeSelectRowKeys', payload: keysMap[key] || [] });
    },
    [warehouseRowKeys, shopRowKeys, factoryRowKeys, dispatch],
  );

  const isSelectAllOnChange = (e) => {
    dispatch({ type: 'setAllPermissionFlag', payload: e.target.checked });
  };

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      ellipsis: true,
      tooltip: true,
      width: 180,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      valueType: 'text',
      ellipsis: true,
      tooltip: true,
      auto: true,
      minWidth: 200,
    },
  ];

  useEffect(() => {
    dispatch({ type: 'setTabActiveKey', payload: type });
  }, [type, dispatch]);

  useEffect(() => {
    const allSelectedRowKeys = [...warehouseRowKeys, ...shopRowKeys, ...factoryRowKeys];
    const allSelectedRows = warehouseListSource.filter((item) => allSelectedRowKeys.includes(item.id));
    dispatch({ type: 'setAllSelectedRows', payload: allSelectedRows });
  }, [warehouseRowKeys, shopRowKeys, factoryRowKeys, dispatch, warehouseListSource]);

  useEffect(() => {
    if (tabActiveKey && partnerSourceList.length > 0 && warehouseListSource.length > 0) {
      const tableData = partnerSourceList.filter((n) => n.type === tabActiveKey);
      const warehouseList = warehouseListSource.filter((n) => n.type === tabActiveKey);
      dispatch({ type: 'setCurrentTypePartnerList', payload: tableData });
      dispatch({ type: 'setCurrentTypeWarehouseList', payload: warehouseList });
    }
  }, [tabActiveKey, partnerSourceList, warehouseListSource, dispatch]);

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      dispatch({ type: 'reset', ignoreType: true });
      fetchData();
      if (partnerPermissionTransferTableRef.current) {
        partnerPermissionTransferTableRef.current?.recover();
      }
    },
  }));

  useEffect(() => {
    if (empId) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [empId]);

  return (
    <div>
      <div className="relative">
        <Tabs
          defaultActiveKey={type}
          activeKey={tabActiveKey}
          onChange={tabsOnChange}
          items={[
            {
              label: i18n.t('global.warehouse'),
              key: 'WAREHOUSE',
              disabled: allPermissionFlag,
            },
            {
              label: i18n.t('global.shop'),
              key: 'SHOP',
              disabled: allPermissionFlag,
            },
            {
              label: i18n.t('global.factory'),
              key: 'FACTORY',
              disabled: allPermissionFlag,
            },
          ]}
          tabBarExtraContent={
            <Space>
              <Checkbox onChange={isSelectAllOnChange} checked={allPermissionFlag}>
                {i18n.t('global.allPermission')}
              </Checkbox>
              {partnerImportPermission && <ImportButton onClick={importBtnOnClick} />}
            </Space>
          }
        />
      </div>
      <Spin spinning={loading}>
        <div className="relative">
          {allPermissionFlag && (
            <div className="absolute bottom-0 left-0 right-0 top-0 z-40 h-full w-full cursor-not-allowed bg-white opacity-40" />
          )}
          <PartnerPermissionTransferTable
            innerRef={partnerPermissionTransferTableRef}
            leftColumns={tableColumns}
            rightColumns={tableColumns}
          />
        </div>
      </Spin>

      <PartnerImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: importModalOnCancel,
          maskClosable: false,
        }}
        onOk={() => {
          fetchData();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
    </div>
  );
};

export default PartnerPermission;
