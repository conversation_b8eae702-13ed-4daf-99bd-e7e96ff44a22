:root {
  /* 默认字体大小比例 */
  --font-scale-factor: 1;
  /* 默认内边距比例 */
  --padding-scale-factor: 1;

  /* Tailwind 字体大小变量 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Ant Design 组件字体大小变量 */
  --ant-font-size-sm: 12px;
  --ant-font-size-base: 14px;
  --ant-font-size-lg: 16px;
  --ant-font-size-xl: 20px;
  --ant-font-size-2xl: 24px;

  /* Ant Design 内边距变量 */
  --ant-padding-xs: 8px;
  --ant-padding-sm: 12px;
  --ant-padding-md: 16px;
  --ant-padding-lg: 24px;
  --ant-padding-xl: 32px;

  /* Ant Design 组件高度变量 */
  --ant-height-sm: 24px;
  --ant-height-base: 32px;
  --ant-height-lg: 40px;
  --ant-height-xl: 48px;

  /* Action 按钮基础尺寸变量 */
  --action-btn-width-sm: 24px;
  --action-btn-width-base: 32px;
  --action-btn-gap: 8px;
  --action-btn-padding-sm: 16px;
  --action-btn-padding-base: 24px;

  /* 工厂装箱中，配码模式表格高度*/
  --match-table-container-height: calc(100vh - 240px);
}

[data-ui-scale="large"] {
  --match-table-container-height: calc(100vh - 280px); /* 大字体模式调整 */
}

/* 大字体应用配置 */
[data-ui-scale="large"] {
  --font-scale-factor: 1.125; /* 字体放大 12.5%，如果这里调整了，也需要同步调整cssUtils.ts中的getCurrentScaleFactor */
  --padding-scale-factor: 1.125; /* 内边距同步放大 12.5% */

  /* 重新计算 Tailwind 字体大小 */
  --text-xs: calc(0.75rem * var(--font-scale-factor));
  --text-sm: calc(0.875rem * var(--font-scale-factor));
  --text-base: calc(1rem * var(--font-scale-factor));
  --text-lg: calc(1.125rem * var(--font-scale-factor));
  --text-xl: calc(1.25rem * var(--font-scale-factor));
  --text-2xl: calc(1.5rem * var(--font-scale-factor));
  --text-3xl: calc(1.875rem * var(--font-scale-factor));

  /* 重新计算 Ant Design 字体大小 */
  --ant-font-size-sm: calc(12px * var(--font-scale-factor));
  --ant-font-size-base: calc(14px * var(--font-scale-factor));
  --ant-font-size-lg: calc(16px * var(--font-scale-factor));
  --ant-font-size-xl: calc(20px * var(--font-scale-factor));
  --ant-font-size-2xl: calc(24px * var(--font-scale-factor));

  /* 重新计算 Ant Design 内边距 */
  --ant-padding-xs: calc(8px * var(--padding-scale-factor));
  --ant-padding-sm: calc(12px * var(--padding-scale-factor));
  --ant-padding-md: calc(16px * var(--padding-scale-factor));
  --ant-padding-lg: calc(24px * var(--padding-scale-factor));
  --ant-padding-xl: calc(32px * var(--padding-scale-factor));

  /* 重新计算 Ant Design 组件高度 */
  --ant-height-sm: calc(24px * var(--padding-scale-factor));
  --ant-height-base: calc(32px * var(--padding-scale-factor));
  --ant-height-lg: calc(40px * var(--padding-scale-factor));
  --ant-height-xl: calc(48px * var(--padding-scale-factor));

  --ant-width-sm: calc(24px * var(--padding-scale-factor));
  --ant-width-base: calc(32px * var(--padding-scale-factor));
  --ant-width-lg: calc(40px * var(--padding-scale-factor));
  --ant-width-xl: calc(48px * var(--padding-scale-factor));

  /* 重新计算列表 Action列 按钮尺寸 */
  --action-btn-width-sm: calc(24px * var(--padding-scale-factor));
  --action-btn-width-base: calc(32px * var(--padding-scale-factor));
  --action-btn-gap: calc(8px * var(--padding-scale-factor));
  --action-btn-padding-sm: calc(16px * var(--padding-scale-factor));
  --action-btn-padding-base: calc(24px * var(--padding-scale-factor));
}

/* 应用字体大小到所有文本元素 */
[data-ui-scale="large"] {
  font-size: calc(1rem * var(--font-scale-factor));
}

/* ========== 表单控件 ========== */
[data-ui-scale="large"] .ant-btn {
  font-size: var(--ant-font-size-base);
  padding: calc(17px * var(--padding-scale-factor)) calc(24px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-btn-icon-only {
  font-size: var(--ant-font-size-base);
  height: calc(36px * var(--padding-scale-factor));
  width: calc(36px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-btn-sm {
  padding: 0 var(--ant-padding-xs);
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-btn-lg {
  padding: var(--ant-padding-lg);
  font-size: var(--ant-font-size-xl);
}

[data-ui-scale="large"] .ant-input {
  font-size: var(--ant-font-size-base);
  padding: calc(6px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-input-sm {
  padding: calc(0px * var(--padding-scale-factor)) calc(7px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-input-lg {
  padding: calc(11px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-input-search-button {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-input-password {
  padding: calc(11px * var(--padding-scale-factor)) !important;
}

[data-ui-scale="large"] .ant-input-affix-wrapper {
  padding: calc(6px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-select {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-select .ant-select-selector {
  height: auto;
  padding: calc(4px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-select-multiple .ant-select-selector {
  height: auto;
  padding: calc(4.5px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-select-sm .ant-select-selector {
  padding: calc(3px * var(--padding-scale-factor)) calc(7px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-select-lg .ant-select-selector {
  padding: calc(3px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-select-item,
[data-ui-scale="large"] .ant-select-selection-item {
  font-size: var(--ant-font-size-base) !important;
  height: auto;
  padding-top: 2px;
  padding-bottom: 2px;
  line-height: normal;
  /* line-height: calc(var(--ant-height-base) - 2px); */
}

[data-ui-scale="large"] .ant-select-item {
    display: flex;
    align-items: center;
}

[data-ui-scale="large"] .ant-checkbox-wrapper {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-radio-wrapper {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-radio-group .ant-radio-button-wrapper {
  height: auto;
  font-size: var(--ant-font-size-base) !important;
  padding: calc(4px * var(--padding-scale-factor)) var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-switch {
  min-width: calc(44px * var(--padding-scale-factor));
  height: calc(22px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-segmented-group .ant-segmented-item {
  font-size: var(--ant-font-size-base) !important;
  height: var(--ant-height-base) !important;
  padding: 3px var(--ant-padding-md) !important;
}

/* ========== 数据展示 ========== */
[data-ui-scale="large"] .current-match-viewer-table .ant-table {
  font-size: var(--ant-font-size-2xl);
}

[data-ui-scale="large"] .current-match-viewer-table .ant-table-thead > tr > th {
  padding: var(--ant-padding-md) var(--ant-padding-md);
  font-size: var(--ant-font-size-2xl) !important;
}

[data-ui-scale="large"] .current-match-viewer-table .ant-table-tbody > tr > td {
  padding: var(--ant-padding-md) var(--ant-padding-md);
  font-size: var(--ant-font-size-2xl) !important;
}

[data-ui-scale="large"] .ant-table {
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-table-thead > tr > th {
  padding: var(--ant-padding-md) var(--ant-padding-md);
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-table-tbody > tr > td {
  padding: var(--ant-padding-md) var(--ant-padding-md);
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-table-small .ant-table-thead > tr > th,
[data-ui-scale="large"] .ant-table-small .ant-table-tbody > tr > td {
  padding: var(--ant-padding-xs) var(--ant-padding-xs);
}

[data-ui-scale="large"] .ag-header-cell-text {
  font-weight: 600;
}

[data-ui-scale="large"] .ag-header,
[data-ui-scale="large"] .ag-body {
    font-size: var(--ant-font-size-lg) !important;
}

[data-ui-scale="large"] .virtual-table-cell .ant-typography {
  font-size: var(--ant-font-size-lg);
}


[data-ui-scale="large"] .ant-pagination {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-pagination-item {
  min-width: calc(32px * var(--padding-scale-factor));
  height: calc(32px * var(--padding-scale-factor));
  line-height: calc(32px * var(--padding-scale-factor) - 2px);
}

[data-ui-scale="large"] .ant-pagination-prev,
[data-ui-scale="large"] .ant-pagination-next {
  min-width: calc(32px * var(--padding-scale-factor));
  height: calc(32px * var(--padding-scale-factor));
  line-height: calc(32px * var(--padding-scale-factor) - 2px);
}

[data-ui-scale="large"] .table-wrap-pagination .ant-btn-ghost {
  height: calc(36px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-tag {
  padding: calc(0px * var(--padding-scale-factor)) calc(7px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-sm);
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-row  {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-avatar {
  width: calc(32px * var(--padding-scale-factor));
  height: calc(32px * var(--padding-scale-factor));
  line-height: calc(32px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-avatar-lg {
  width: calc(40px * var(--padding-scale-factor));
  height: calc(40px * var(--padding-scale-factor));
  line-height: calc(40px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-avatar-sm {
  width: calc(24px * var(--padding-scale-factor));
  height: calc(24px * var(--padding-scale-factor));
  line-height: calc(24px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-descriptions-item-content,
[data-ui-scale="large"] .ant-descriptions-item-label {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-timeline-item-content {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-upload-text {
   font-size: var(--ant-font-size-lg) !important;
}

[data-ui-scale="large"] .ant-upload-hint {
   font-size: var(--ant-font-size-lg) !important;
}

[data-ui-scale="large"] .ant-tooltip-inner {
  font-size: var(--ant-font-size-lg);
}

/* ========== 导航 ========== */
[data-ui-scale="large"] .ant-tabs {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-tabs-tab {
  padding: var(--ant-padding-sm) var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-tabs-tab-btn {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-menu {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-menu-item {
  height: calc(40px * var(--padding-scale-factor));
  line-height: calc(40px * var(--padding-scale-factor));
  padding: 0 var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-menu-submenu-title {
  height: calc(40px * var(--padding-scale-factor));
  line-height: calc(40px * var(--padding-scale-factor));
  padding: 0 var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-breadcrumb {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-steps {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-steps-item-title {
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-steps-item-description {
  font-size: var(--ant-font-size-base);
}

/* ========== 布局和容器 ========== */
[data-ui-scale="large"] .ant-space {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-card {
  font-size: var(--ant-font-size-base);
  border-radius: calc(8px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-card-head {
  padding: 0 var(--ant-padding-lg);
  min-height: calc(56px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-card-head-title {
  font-size: var(--ant-font-size-lg);
  padding: var(--ant-padding-md) 0;
}

[data-ui-scale="large"] .ant-card-body {
  padding: var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-card-small .ant-card-head {
  min-height: calc(48px * var(--padding-scale-factor));
  padding: 0 var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-card-small .ant-card-body {
  padding: var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-form {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-form-item-label {
  padding-bottom: calc(8px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-form-item-label > label {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-form-item-explain,
[data-ui-scale="large"] .ant-form-item-extra {
  font-size: var(--ant-font-size-sm);
}

/* ========== 反馈 ========== */
[data-ui-scale="large"] .ant-modal {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-modal-header {
  padding: var(--ant-padding-lg) var(--ant-padding-lg) var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-modal-title {
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-modal-body {
  padding: var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-modal-footer {
  padding: var(--ant-padding-md) var(--ant-padding-lg) var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-modal-confirm-content {
    font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-drawer {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-drawer-header {
  padding: var(--ant-padding-lg) var(--ant-padding-lg) var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-drawer-title {
  font-size: var(--ant-font-size-lg);
}

[data-ui-scale="large"] .ant-drawer-body {
  padding: var(--ant-padding-lg);
}

[data-ui-scale="large"] .ant-message {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-notification {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-notification-notice {
  padding: var(--ant-padding-md);
  border-radius: calc(8px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-alert {
  font-size: var(--ant-font-size-base);
  padding: var(--ant-padding-sm) var(--ant-padding-md);
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-popover {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-popover-inner {
  border-radius: calc(8px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-popover-title {
  padding: calc(5px * var(--padding-scale-factor)) var(--ant-padding-md) calc(4px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-popover-inner-content {
  padding: var(--ant-padding-sm) var(--ant-padding-md);
}

[data-ui-scale="large"] .ant-tooltip {
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-tooltip-inner {
  padding: calc(6px * var(--padding-scale-factor)) calc(8px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

/* ========== 其他组件 ========== */
[data-ui-scale="large"] .ant-typography {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-typography h1.ant-typography,
[data-ui-scale="large"] .ant-typography-h1 {
  font-size: calc(38px * var(--font-scale-factor));
}

[data-ui-scale="large"] .ant-typography h2.ant-typography,
[data-ui-scale="large"] .ant-typography-h2 {
  font-size: calc(30px * var(--font-scale-factor));
}

[data-ui-scale="large"] .ant-typography h3.ant-typography,
[data-ui-scale="large"] .ant-typography-h3 {
  font-size: calc(24px * var(--font-scale-factor));
}

[data-ui-scale="large"] .ant-typography h4.ant-typography,
[data-ui-scale="large"] .ant-typography-h4 {
  font-size: calc(20px * var(--font-scale-factor));
}

[data-ui-scale="large"] .ant-typography h5.ant-typography,
[data-ui-scale="large"] .ant-typography-h5 {
  font-size: calc(16px * var(--font-scale-factor));
}

[data-ui-scale="large"] .ant-divider-inner-text {
  font-size: var(--ant-font-size-base);
  padding: 0 calc(1em * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-progress {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .current-match-viewer .ant-progress-text {
  font-size: var(--ant-font-size-2xl) !important;
}

[data-ui-scale="large"] .current-match-viewer .ant-progress-inner {
  width: 160px !important;
  height: 160px !important;
}

[data-ui-scale="large"] .ant-spin {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-spin-text {
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-empty {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-empty-description {
  font-size: var(--ant-font-size-sm);
}

/* ========== 日期选择器 ========== */
[data-ui-scale="large"] .ant-picker {
  font-size: var(--ant-font-size-base);
  padding: calc(6px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-picker-input > input {
  font-size: var(--ant-font-size-base) !important;
}

[data-ui-scale="large"] .ant-picker-panels {
  font-size: var(--ant-font-size-base) !important;;
}

[data-ui-scale="large"] .ant-picker-sm {
  padding: calc(0px * var(--padding-scale-factor)) calc(7px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-sm);
}

[data-ui-scale="large"] .ant-picker-lg {
  padding: calc(6.5px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
  font-size: var(--ant-font-size-lg);
}

/* ========== 数字输入框 ========== */
[data-ui-scale="large"] .ant-input-number {
  font-size: var(--ant-font-size-base);
  border-radius: calc(6px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-input-number-input {
  height: auto;
  padding: calc(6.5px * var(--padding-scale-factor)) calc(11px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-input-number-sm .ant-input-number-input {
  height: calc(var(--ant-height-sm) - 2px);
  padding: 0 calc(7px * var(--padding-scale-factor));
}

[data-ui-scale="large"] .ant-input-number-lg {
  height: var(--ant-height-lg);
}

[data-ui-scale="large"] .ant-input-number-lg .ant-input-number-input {
  height: calc(var(--ant-height-lg) - 2px);
  padding: 0 calc(11px * var(--padding-scale-factor));
}

/* ========== 滑块 ========== */
[data-ui-scale="large"] .ant-slider {
  font-size: var(--ant-font-size-base);
}

[data-ui-scale="large"] .ant-slider-mark-text {
  font-size: var(--ant-font-size-sm);
}

/* ========== 评分 ========== */
[data-ui-scale="large"] .ant-rate {
  font-size: calc(20px * var(--font-scale-factor));
}

/* ========== 自定义内边距工具类 ========== */
[data-ui-scale="large"] .ui-scale-p-xs { padding: var(--ant-padding-xs); }
[data-ui-scale="large"] .ui-scale-p-sm { padding: var(--ant-padding-sm); }
[data-ui-scale="large"] .ui-scale-p-md { padding: var(--ant-padding-md); }
[data-ui-scale="large"] .ui-scale-p-lg { padding: var(--ant-padding-lg); }
[data-ui-scale="large"] .ui-scale-p-xl { padding: var(--ant-padding-xl); }

[data-ui-scale="large"] .ui-scale-gap-xs { gap: var(--ant-padding-xs); }
[data-ui-scale="large"] .ui-scale-gap-sm { gap: var(--ant-padding-sm); }
[data-ui-scale="large"] .ui-scale-gap-md { gap: var(--ant-padding-md); }
[data-ui-scale="large"] .ui-scale-gap-lg { gap: var(--ant-padding-lg); }
[data-ui-scale="large"] .ui-scale-gap-xl { gap: var(--ant-padding-xl); }