import { Col, Row, Space } from 'antd';
import NextStepButton from 'common/components/Button/NextStep';
import PreStepButton from 'common/components/Button/PreStep';
// eslint-disable-next-line max-len
import BarcodeModalSelector from 'common/components/ModalSelector/BarcodeModalSelector';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useState } from 'react';

import SkuEditTable from './SkuEditTable';
import { TableDataContext } from './TableData';

interface IDataViewTable {
  visible: 'none' | 'block';
  getTableData: (n) => void;
  nextButtonOnClick: () => void;
  preButtonOnClick: () => void;
  loading: boolean;
}

const DataViewTable: React.FC<IDataViewTable> = (props) => {
  const { visible, getTableData, nextButtonOnClick, preButtonOnClick, loading } = props;
  const [barcodeModalSelectorVisible, setBarcodeModalSelectorVisible] = useState<boolean>(false);
  const [isSelect, setIsSelect] = useState<boolean>(false);
  const [currentProd, setCurrentProd] = useState<string[]>([]);
  const [currentBarcode, setCurrentBarcode] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { contextData } = useContext(TableDataContext);

  const barcodeSelectorModalOnOk = (value) => {
    const currentProd: string[] = [];
    value.forEach((item) => {
      if (!currentProd.some((n) => n === item.prodCode)) {
        currentProd.push(item.prodCode);
      }
    });
    if (value.length > 0) {
      setIsSelect(true);
    } else {
      setIsSelect(false);
    }
    setCurrentProd(currentProd);
    setDataSource(value);
    setCurrentBarcode(value.map((n) => n.id));
    setBarcodeModalSelectorVisible(false);
  };

  const selectTipMsg = i18n.t('global.selectTip', {
    currentProdCount: currentProd.length,
    skuCount: currentBarcode.length,
  });

  useEffect(() => {
    const skuData = contextData.sku;
    skuData.forEach((item) => {
      const currentSkuInfo = dataSource.find((n) => n.barcode === item.barcode);
      if (currentSkuInfo) {
        item.colorCode = currentSkuInfo.colorCode;
        item.sizeCode = currentSkuInfo.sizeCode;
      }
    });
    getTableData(contextData);
    // eslint-disable-next-line
  }, [contextData, dataSource]);

  const handleSkuEditTableDelete = (record) => {
    const newCurrentBarcode = currentBarcode.filter((n) => n !== record.id);
    if (newCurrentBarcode.length === 0) {
      setIsSelect(false);
    }
    setCurrentBarcode(newCurrentBarcode);
    const newDataSource = dataSource.filter((item) => item.id !== record.id);
    setDataSource(newDataSource);
    if (!newDataSource.some((n) => n.prodCode === record.prodCode)) {
      setCurrentProd(currentProd.filter((n) => n !== record.prodCode));
    }
  };

  return (
    <>
      <div style={{ display: visible }}>
        <SkuEditTable
          currentSku={dataSource}
          onDelete={handleSkuEditTableDelete}
          selectBarCode={() => setBarcodeModalSelectorVisible(true)}
        />
        <Row justify="start" style={{ marginTop: 20 }}>
          <Col>{selectTipMsg}</Col>
        </Row>
        <Space style={{ float: 'left', marginTop: 24 }}>
          <NextStepButton onClick={nextButtonOnClick} disabled={!isSelect} loading={loading} />
          <PreStepButton onClick={preButtonOnClick} />
        </Space>
      </div>
      <BarcodeModalSelector
        modalProps={{ open: barcodeModalSelectorVisible }}
        onOk={barcodeSelectorModalOnOk}
        onCancel={() => {
          setBarcodeModalSelectorVisible(false);
        }}
        defaultSelected={currentBarcode}
        multiple
      />
    </>
  );
};

export default DataViewTable;
