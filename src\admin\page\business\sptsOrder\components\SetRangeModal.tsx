import SampleInventoryRangeSetting from 'common/components/SampleInventoryRangeSetting';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';

interface ITsOrderSetRangeModalProps {
  confirmLoading?: boolean;
  visible: boolean;
  onSubmit: (values, range?) => void;
  onCancel: () => void;
  current?: Record<string, any>[];
}

const TsOrderSetRangeModal: React.FC<ITsOrderSetRangeModalProps> = (props) => {
  const { visible, onSubmit, onCancel, current, confirmLoading } = props;
  const [setRangeFormFilled, setSetRangeFormFilled] = useState(false);
  const [currentSelectData, setCurrentSelectData] = useState<Record<string, any>[]>([]);
  const onChange = (record) => {
    setSetRangeFormFilled(true);
    setCurrentSelectData(record);
  };

  const handleOk = () => {
    if (onSubmit) {
      onSubmit(currentSelectData);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  useEffect(() => {
    // reset state
    if (!visible) {
      setSetRangeFormFilled(false);
    }
  }, [visible]);

  useEffect(() => {
    if (current && current.length > 0) {
      const selectKeys = currentSelectData.map((n) => n.propId);
      const result = current.every((n) => selectKeys.includes(n.propId)) && currentSelectData.length === current.length;
      setSetRangeFormFilled(!result);
    }
  }, [currentSelectData, current]);

  return (
    <Modal
      title={i18n.t('global.setStockRange')}
      width={640}
      open={visible}
      bodyStyle={{
        padding: '24px',
        minHeight: '420px',
      }}
      destroyOnClose
      okButtonProps={{
        loading: confirmLoading,
        disabled: !setRangeFormFilled,
      }}
      onOk={handleOk}
      maskClosable={false}
      onCancel={handleCancel}
    >
      <SampleInventoryRangeSetting
        defaultActiveKeys={current && current.length > 0 ? current[0].propType : false}
        current={current}
        onChange={onChange}
      />
    </Modal>
  );
};

export default TsOrderSetRangeModal;
