import SpecGrpOperateForm from 'admin/page/basicArchives/prodAttributes/specGrp/components/SpecGrpOperateForm';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect } from 'react';

import { ISpecGrpItem } from '../data';

interface AddModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: ISpecGrpItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<AddModalProps> = (props) => {
  const [form] = Form.useForm();
  const { visible, confirmLoading, onSubmit, onCancel } = props;

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISpecGrpItem);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Drawer
      title={i18n.t('global.newSpecGrp')}
      okButtonProps={{
        loading: confirmLoading,
      }}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      okText={i18n.t('global.apply')}
      destroyOnClose
      onOk={handleSubmit}
      onClose={() => {
        onCancel();
        form.resetFields();
      }}
    >
      <SpecGrpOperateForm form={form} initialValues={{ sortId: 0, disabled: false }} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddDrawer;
