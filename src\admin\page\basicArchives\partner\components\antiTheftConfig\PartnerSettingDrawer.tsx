import { Checkbox, Col, Row } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useState } from 'react';
// import * as AntiTheftApi from 'common/api/core/AntiTheft';
import { BusinessType, businessTypeOption } from './common';

interface PartnerSettingDrawerProps {
  open: boolean;
  current: Partial<any> | undefined;
  onSubmit: (values: { id: string; businessTypeList: string[] }) => void;
}

const PartnerSettingDrawer: React.FC<PartnerSettingDrawerProps> = (props) => {
  const { onSubmit, current, open } = props;
  const [selectKeys, setSelectKeys] = useState<BusinessType[]>([]);

  const handleSubmit = useCallback(async () => {
    onSubmit({
      id: current?.id,
      businessTypeList: selectKeys,
    });
  }, [current, onSubmit, selectKeys]);

  const onClose = () => {
    handleSubmit();
  };

  const onCheckboxChange = (arr: BusinessType[]) => {
    setSelectKeys(arr);
  };

  useEffect(() => {
    if (current && open) {
      if (current.businessTypeList) {
        setSelectKeys(current.businessTypeList);
      } else {
        setSelectKeys([]);
      }
    }

    if (!open) {
      setSelectKeys([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, current]);

  return (
    <Drawer
      title={`${i18n.t('global.antiTheftDataSharingOperation')} [${current?.targetPartnerCode}]`}
      open={open}
      destroyOnClose
      onClose={onClose}
      footer={false}
    >
      <Checkbox.Group className="mt-4 w-full" value={selectKeys} onChange={onCheckboxChange}>
        <Row gutter={[24, 24]}>
          {businessTypeOption.map((i) => (
            <Col span={24}>
              <Checkbox value={i.value}>{i.label}</Checkbox>
            </Col>
          ))}
        </Row>
      </Checkbox.Group>
    </Drawer>
  );
};

export default PartnerSettingDrawer;
