import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useEffect, useRef, useState } from 'react';
import * as RfidFilterApi from 'common/api/core/RfidFilter';

import TypeForm from './TypeForm';

interface EditTypeDrawerProps {
  visible: boolean;
  current: Partial<any> | undefined;
  onOk: () => void;
  onClose: () => void;
}

const EditTypeDrawer: React.FC<EditTypeDrawerProps> = (props) => {
  const { onOk, onClose, current, visible } = props;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<any>(null);

  const onSave = async () => {
    setLoading(true);
    try {
      const values = await form.validateFields();

      const payload = {
        ...values,
        rfidFilterTypeId: current?.id,
      };

      await RfidFilterApi.TypeUpdate(payload);

      NoticeUtil.success();
      setLoading(false);
      if (onOk) onOk();
    } catch (e) {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (current && visible) {
      form.setFieldsValue(current);
    } else {
      form.resetFields();
    }
  }, [visible, current, form]);

  return (
    <Drawer
      title={`${i18n.t('global.editTagFilterType')}${current ? ` [${current.name}]` : ''}`}
      destroyOnClose
      open={visible}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          recoverPermission={false}
          onApply={onSave}
          deletePermission={false}
          cancelPermission
          onCancel={onClose}
        />
      }
    >
      <TypeForm
        codeInputRef={inputRef}
        form={form}
        layout="vertical"
        name="basic"
        initialValues={{ disable: false, defaultTag: false }}
      />
    </Drawer>
  );
};

export default EditTypeDrawer;
