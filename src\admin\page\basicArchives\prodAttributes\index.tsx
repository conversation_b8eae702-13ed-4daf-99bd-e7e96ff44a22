import Brand from 'admin/page/basicArchives/prodAttributes/brand';
import ColorGrp from 'admin/page/basicArchives/prodAttributes/colorGrp';
import Gender from 'admin/page/basicArchives/prodAttributes/gender';
import PriCategory from 'admin/page/basicArchives/prodAttributes/priCategory';
import SizeGrp from 'admin/page/basicArchives/prodAttributes/sizeGrp';
import SpecGrp from 'admin/page/basicArchives/prodAttributes/specGrp';
import SubCategory from 'admin/page/basicArchives/prodAttributes/subCategory';
import Year from 'admin/page/basicArchives/prodAttributes/year';
import TabsMenu from 'common/components/TabsMenu';
import AppHeader from 'common/layout/AppHeader';
// import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useState } from 'react';
// import { Link, Outlet, Route, Routes, useNavigate } from 'react-router-dom';

// export interface ProdAttributesProps {
//   children?: React.ReactNode;
// }

const currentMenuCode = 'A:BASE:ATTRIBUTE';

const ProdAttributes: React.FC = () => {
  // const { children } = props;
  // const { state } = useContext<TGlobalContext>(GlobalContext);
  // const { localPermissionList } = state;
  // 获取当前菜单编码的所有权限
  const [permission] = usePermission(currentMenuCode);
  // 筛选当前编码的有权限的子集菜单
  const permissionMenu = permission.allChild.filter((item: Record<string, any>) => item.depth === 4);
  // const navigate = useNavigate();
  const [activeMenuCode, setActiveMenuCode] = useState('');
  const tabsMenuOnChange = (record) => {
    setActiveMenuCode(record.code);
    // navigate(path);
  };

  useEffect(() => {
    if (permissionMenu.length > 0 && !activeMenuCode) {
      setActiveMenuCode(permissionMenu[0].code);
    }
    // eslint-disable-next-line
  }, [permissionMenu, activeMenuCode]);

  const elementMap: Record<string, any> = {
    'A:BASE:ATTRIBUTE:BRAND': <Brand />,
    'A:BASE:ATTRIBUTE:YEAR': <Year />,
    'A:BASE:ATTRIBUTE:GENDER': <Gender />,
    'A:BASE:ATTRIBUTE:PRI_CATEGORY': <PriCategory />,
    'A:BASE:ATTRIBUTE:SUB_CATEGORY': <SubCategory />,
    'A:BASE:ATTRIBUTE:COLOR_GRP': <ColorGrp />,
    'A:BASE:ATTRIBUTE:SIZE_GRP': <SizeGrp />,
    'A:BASE:ATTRIBUTE:SPEC_GRP': <SpecGrp />,
  };

  return (
    <div>
      <AppHeader />
      <div className="flex h-[calc(100vh_-_160px)] gap-x-6">
        <div className="sticky top-24 box-border h-full w-[200px] shrink-0 cursor-pointer overflow-y-auto">
          <TabsMenu code={currentMenuCode} menuList={permissionMenu} onClick={tabsMenuOnChange} />
        </div>
        <div className="h-full flex-1 overflow-y-auto">{elementMap[activeMenuCode]}</div>
      </div>
      {/* <Routes> */}
      {/*  <Route path="/app/prod-attribute" element={<Outlet />}> */}
      {/*    {permissionMenu.map((item) => { */}
      {/*      const local = getMenuItemByCode(localPermissionList, item.code); */}
      {/*      if (local) { */}
      {/*        const { code, route } = local; */}
      {/*        return ( */}
      {/*          <Route */}
      {/*            key={code} */}
      {/*            path={route?.path} */}
      {/*            // @ts-ignore */}
      {/*            element={route?.component ? <route.component /> : null} */}
      {/*          /> */}
      {/*        ); */}
      {/*      } */}
      {/*      return <></>; */}
      {/*    })} */}
      {/*  </Route> */}
      {/* </Routes> */}
    </div>
  );
};

export default ProdAttributes;
