import AddDrawer from 'admin/page/basicArchives/other/transferGroup/components/AddDrawer';
import DetailDrawer from 'admin/page/basicArchives/other/transferGroup/components/DetailDrawer';
import { Button, Input } from 'antd';
import * as TransferGroupApi from 'common/api/core/TransferGroup';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import ArrowRightDoubleFillIcon from 'common/assets/icons/icon-arrow-right-double-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import ImportButton from 'common/components/Button/Import';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import DisableSelect from 'common/components/Select/DisableSelect';
import Tag from 'common/components/Tag';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useMemo, useRef, useState } from 'react';

import ImportModal from './components/ImportModal';

const TransferGroup: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [permission] = usePermission('A:BASE:OTHER:TRANSFER-GROUP');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('UPDATE');
  const deletePermission = permission.codes.includes('DELETE');
  const importPermission = permission.codes.includes('CREATE');
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const [current, setCurrent] = useState<Record<string, any> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };
  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await TransferGroupApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const addBtnOnClick = () => {
    setAddVisible(true);
  };

  const addOnSubmit = async (values) => {
    setConfirmLoading(true);
    try {
      values.code = values.code.trim();
      values.name = values.name.trim();
      await TransferGroupApi.Create(values);
      setConfirmLoading(false);
      setAddVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const addOnClose = () => {
    setAddVisible(false);
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'disabled',
      label: i18n.t('global.status'),
      inputComponent: <DisableSelect />,
    },
  ];
  const operationModalHandleCancel = () => {
    setDetailDrawerVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  const tagMap = useMemo(() => {
    return {
      SHOP: {
        color: 'purple',
        desc: i18n.t('global.shop'),
      },
      WAREHOUSE: {
        color: 'blue',
        desc: i18n.t('global.warehouse'),
      },
      FACTORY: {
        color: 'green',
        desc: i18n.t('global.factory'),
      },
    };
  }, []);

  const tagNode = useCallback(
    (type) => (
      <Tag type="solid" color={tagMap[type].color}>
        {tagMap[type].desc}
      </Tag>
    ),
    [tagMap],
  );

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      ellipsis: true,
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.type'),
      render: (text, record) => (
        <div className="flex items-center gap-x-1">
          {tagNode(record.shipType)}
          <ArrowRightDoubleFillIcon className="fill-[#ABB8CC]" />
          {tagNode(record.receiptType)}
        </div>
      ),
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const fetchData = useCallback(async (params) => {
    let data: any = [];
    try {
      data = await TransferGroupApi.List(params);
    } catch {}
    return data;
  }, []);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        rightToolbar={[
          importPermission && <ImportButton onClick={importBtnOnClick} />,
          createPermission && (
            <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="TRANSFER_GROUP"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  setCurrent(record);
                  setDetailDrawerVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <AddDrawer visible={addVisible} confirmLoading={confirmLoading} onSubmit={addOnSubmit} onCancel={addOnClose} />
      <DetailDrawer
        visible={detailDrawerVisible}
        groupId={current ? current.id : ''}
        onCancel={operationModalHandleCancel}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: importModalOnCancel,
          maskClosable: false,
        }}
        onOk={() => {
          powerTableRef.current?.load();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
    </div>
  );
};

export default TransferGroup;
