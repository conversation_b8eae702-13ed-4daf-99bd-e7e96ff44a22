import * as <PERSON><PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON> from 'common/api/factory/BfobOrder';
import * as FobOrder<PERSON>pi from 'common/api/factory/FobOrder';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, { PowerTableColumnsType, SearchFieldsConfig } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useState } from 'react';

interface IBoxTableProps {
  orderId?: string;
  powerTableRef: any;
}

const BoxTable: React.FC<IBoxTableProps> = (props) => {
  const { orderId, powerTableRef } = props;

  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewModalLoading, setBoxDetailsPreviewModalLoading] = useState<boolean>(false);
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);

  const boxCodeOnClick = async (record) => {
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewModalLoading(true);
    setCurrentBoxCode(record.boxCode);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BfobOrderApi.Rfids({
          boxId: record.boxId,
          fobOrderId: orderId,
          enablePage: false,
        });
      } else {
        result = await FobOrderApi.Rfids({
          boxId: record.boxId,
          fobOrderId: orderId,
          enablePage: false,
        });
      }
      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewModalLoading(false);
  };

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      let result = { data: [] };
      try {
        if (currentUser.mode === 'BINDING') {
          result = await BfobOrderApi.Boxes({
            ...params,
            fobOrderId: orderId,
          });
        } else {
          result = await FobOrderApi.Boxes({
            ...params,
            fobOrderId: orderId,
          });
        }
      } catch (e) {}
      return result;
    },
    // eslint-disable-next-line
    [orderId],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <SearchInput placeholder={i18n.t('global.boxCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.box'),
      dataIndex: 'boxCode',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      minWidth: 200,
      auto: true,
      fixed: 'left',
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.boxedCount'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        request={fetchData}
        pagination
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              boxCodeOnClick(record);
            },
          }),
        }}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewModalLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxTable;
