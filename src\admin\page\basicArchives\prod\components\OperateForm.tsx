import { ProdContext, TProdContext } from 'admin/page/basicArchives/prod/reducer';
import { Col, Form, Image, Input, InputNumber, Row, Switch, Tooltip } from 'antd';
import { Store } from 'antd/es/form/interface';
import { FormProps } from 'antd/lib/form';
import * as FileApi from 'common/api/file/Img';
import CopperCoinFillIcon from 'common/assets/icons/icon-copper-coin-fill.svg?react';
import CopperCoinFillRedIcon from 'common/assets/icons/icon-copper-coin-fill-red.svg?react';
import EditFillIcon from 'common/assets/icons/icon-edit-fill.svg?react';
import BrandSelect from 'common/components/Select/BrandSelect';
import ColorGrpSelect from 'common/components/Select/ColorGrpSelect';
import GenderSelect from 'common/components/Select/GenderSelect';
import PriCategorySelect from 'common/components/Select/PriCategorySelect';
import SizeGrpSelect from 'common/components/Select/SizeGrpSelect';
import SpecGrpSelect from 'common/components/Select/SpecGrpSelect';
import SubCategorySelect from 'common/components/Select/SubCategorySelect';
import YearSelect from 'common/components/Select/YearSelect';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import EditImgModal from './EditImgModal';

import './OperateForm.css';

export interface OperateFormInnerRef {
  /**
   * Focus code input
   */
  focusCodeInput(): void;
  /**
   * Focus name input
   */
  focusNameInput(): void;
}

interface OperationFormProps extends FormProps {
  codeReadOnly?: boolean;
  statusVisible?: boolean;
  imageVisible?: boolean;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<OperateFormInnerRef | undefined>;
  refresh?: () => void;
}

export const extFieldFormItemNamePreFix = 'EXT_FIELD_';

const OperateForm: React.FC<OperationFormProps> = (props) => {
  const { onFinish, statusVisible, refresh, imageVisible, innerRef, codeReadOnly, ...formProps } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [imgEditModalVisible, setImgEditModalVisible] = useState(false);
  const { state: prodState } = useContext<TProdContext>(ProdContext);

  const { state } = useContext<TGlobalContext>(GlobalContext);
  const extFields = state.extFields.filter((n) => n.type === 'PROD' && !n.disabled);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];

  const disableColor = true;
  const disableSize = true;
  const disableSpec = true;
  const [enableColor, setEnableColor] = useState(!disableColor);
  const [enableSize, setEnableSize] = useState(!disableSize);
  const [enableSpec, setEnableSpec] = useState(!disableSpec);
  const showEditImgModal = () => {
    setImgEditModalVisible(true);
  };

  const handleImgEditModalCancel = () => {
    if (refresh) refresh();
    setImgEditModalVisible(false);
  };

  const onFormFinish = (values: Store) => {
    // console.log('values:', values);
    const { enableColor, enableSize, enableSpec, ...otherValues } = values;
    if (onFinish) {
      onFinish({
        disableColor: !enableColor,
        disableSize: !enableSize,
        disableSpec: !enableSpec,
        ...otherValues,
      });
    }
  };

  useImperativeHandle(innerRef, () => ({
    focusCodeInput: () => {
      codeInputRef.current?.focus({ cursor: 'end' });
    },
    focusNameInput: () => {
      nameInputRef.current?.focus({ cursor: 'end' });
    },
  }));

  useEffect(() => {
    if (form && prodState.prodRecord) {
      const disableColor = !prodState.prodRecord.disableColor;
      const disableSize = !prodState.prodRecord.disableSize;
      const disableSpec = !prodState.prodRecord.disableSpec;
      setEnableColor(disableColor);
      setEnableSize(disableSize);
      setEnableSpec(disableSpec);
      const init: Record<string, any> = { ...prodState.prodRecord };
      init.enableColor = disableColor;
      init.enableSize = disableSize;
      init.enableSpec = disableSpec;
      form.setFieldsValue(init);
    } else {
      form.resetFields();
    }
  }, [form, prodState.prodRecord]);

  return (
    <>
      <Form
        form={form}
        name="prodOperationForm"
        className="prod-operation-form"
        onFinish={onFormFinish}
        layout="vertical"
        {...formProps}
      >
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label={i18n.t('global.code')}
              name="code"
              rules={[
                {
                  required: true,
                  message: i18n.t('global.fieldCanNotBeNull'),
                },
              ]}
            >
              <Input ref={codeInputRef} readOnly={codeReadOnly} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={i18n.t('global.name')}
              name="name"
              rules={[
                {
                  required: true,
                  message: i18n.t('global.fieldCanNotBeNull'),
                },
              ]}
            >
              <Input ref={nameInputRef} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.brand')} name="brandId">
              <BrandSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.priCategory')} name="priCategoryId">
              <PriCategorySelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.subCategory')} name="subCategoryId">
              <SubCategorySelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.year')} name="yearId">
              <YearSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.gender')} name="genderId">
              <GenderSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.weight')} name="weight">
              <InputNumber addonAfter={i18n.t('global.gram')} min={0} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <div className="flex items-end">
              <Form.Item label={i18n.t('global.enableColor')} valuePropName="checked" name="enableColor">
                <Switch onChange={(e) => setEnableColor(e)} />
              </Form.Item>
              {enableColor && (
                <Form.Item
                  name="colorGrpId"
                  className="flex-auto"
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.fieldCanNotBeNull'),
                    },
                  ]}
                >
                  <ColorGrpSelect />
                </Form.Item>
              )}
            </div>
          </Col>
          <Col span={8}>
            <div className="flex items-end">
              <Form.Item label={i18n.t('global.enableSize')} valuePropName="checked" name="enableSize">
                <Switch onChange={(e) => setEnableSize(e)} />
              </Form.Item>
              {enableSize && (
                <Form.Item
                  name="sizeGrpId"
                  className="flex-auto"
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.fieldCanNotBeNull'),
                    },
                  ]}
                >
                  <SizeGrpSelect />
                </Form.Item>
              )}
            </div>
          </Col>
          <Col span={8}>
            <div className="flex items-end">
              <Form.Item label={i18n.t('global.enableSpec')} valuePropName="checked" name="enableSpec">
                <Switch onChange={(e) => setEnableSpec(e)} />
              </Form.Item>
              {enableSpec && (
                <Form.Item
                  name="specGrpId"
                  className="flex-auto"
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.fieldCanNotBeNull'),
                    },
                  ]}
                >
                  <SpecGrpSelect />
                </Form.Item>
              )}
            </div>
          </Col>
          <Col span={8}>
            <Form.Item
              label={
                <>
                  <span>{i18n.t('global.enableRfid')}</span>
                  <CopperCoinFillIcon className="ml-1 fill-blue-500" />
                </>
              }
              valuePropName="checked"
              name="rfidTag"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={
                <>
                  <span>{i18n.t('global.disturb')}</span>
                  <Tooltip title={i18n.t('global.disturbTip')}>
                    <CopperCoinFillRedIcon className="ml-1 fill-lead-red" />
                  </Tooltip>
                </>
              }
              valuePropName="checked"
              name="disturbTag"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          {statusVisible && (
            <Col span={8}>
              <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
                <Switch />
              </Form.Item>
            </Col>
          )}
          {imageVisible && (
            <Col span={24} className="mb-6">
              <div className="flex">
                <span>{i18n.t('global.image')}：</span>
                <div aria-label="edit" tabIndex={0} onKeyDown={() => {}} role="button" onClick={showEditImgModal}>
                  <EditFillIcon className="bottom-0 ml-2 fill-lead-slate" />
                </div>
              </div>
              {prodState.prodRecord?.defaultImg ? (
                <Image
                  preview={{
                    src: FileApi.Get(prodState.prodRecord?.defaultImg),
                  }}
                  alt=""
                  src={FileApi.Get(prodState.prodRecord?.defaultImg, 100, 100)}
                  style={{
                    width: '100px',
                    height: '100px',
                  }}
                />
              ) : (
                <Image
                  preview={{
                    src: prodState.prodRecord?.defaultImgUrl,
                  }}
                  alt=""
                  src={prodState.prodRecord?.defaultImgUrl || 'img/noImage.svg'}
                  style={{
                    width: '100px',
                    height: '100px',
                  }}
                />
              )}
            </Col>
          )}
          <Col span={24}>
            <div className="rounded-lg border border-lead-light-bg">
              <div className="mb-4 inline-flex h-9 w-full items-start justify-start gap-2.5 rounded-tl-lg rounded-tr-lg bg-lead-light-bg px-3 py-2">
                <div className="shrink grow basis-0 text-sm font-semibold leading-tight text-slate-700">
                  {i18n.t('global.extendedProperties')} ({extFields.length})
                </div>
              </div>
              <Row gutter={24} className="px-3">
                {extFields.map((item: any) => {
                  if (item.fieldType === 'TXT' || item.fieldType === 'URL' || item.fieldType === 'IMG') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={extFieldFormItemNamePreFix + item.code}>
                          <Input />
                        </Form.Item>
                      </Col>
                    );
                  }
                  if (item.fieldType === 'NUM' || item.fieldType === 'AMT') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={extFieldFormItemNamePreFix + item.code}>
                          <InputNumber style={{ width: 150 }} />
                        </Form.Item>
                      </Col>
                    );
                  }
                  return null;
                })}
              </Row>
            </div>
          </Col>
        </Row>
      </Form>
      <EditImgModal visible={imgEditModalVisible} onCancel={handleImgEditModalCancel} />
    </>
  );
};

export default OperateForm;
