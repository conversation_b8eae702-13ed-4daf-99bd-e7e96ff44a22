import { Form, Input } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { IRoleItem } from '../data';

interface IOperationModalProps {
  visible: boolean;
  confirmLoading?: boolean;
  onSubmit: (values: IRoleItem) => void;
  onClose: () => void;
}

const AddDrawer: React.FC<IOperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onClose, visible: open, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      codeInputRef.current?.focus();
    }
  }, [open, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IRoleItem);
    }
  };

  const getModalContent = () => (
    <Form layout="vertical" form={form} onFinish={handleFinish}>
      <Form.Item
        name="code"
        label={i18n.t('global.code')}
        rules={[
          {
            required: true,
            message: i18n.t('global.fieldCanNotBeNull'),
          },
        ]}
      >
        <Input ref={codeInputRef} />
      </Form.Item>
      <Form.Item
        label={i18n.t('global.name')}
        name="name"
        rules={[
          {
            required: true,
            message: i18n.t('global.fieldCanNotBeNull'),
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={3} />
      </Form.Item>
    </Form>
  );

  return (
    <Drawer
      title={i18n.t('global.newRole')}
      destroyOnClose
      open={open}
      okButtonProps={{ loading: confirmLoading }}
      okText={i18n.t('global.apply')}
      onOk={handleSubmit}
      onClose={() => {
        if (onClose) onClose();
      }}
    >
      {getModalContent()}
    </Drawer>
  );
};

export default AddDrawer;
