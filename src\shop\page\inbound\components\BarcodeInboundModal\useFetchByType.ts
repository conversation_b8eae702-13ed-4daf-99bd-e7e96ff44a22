import * as Sku<PERSON><PERSON> from 'common/api/core/Sku';
import * as BsibOrderApi from 'common/api/shop/BsibOrder';
import * as SibOrderApi from 'common/api/shop/SibOrder';

interface IUseFetchByType {
  /* 出库 / 入库 */
  type: 'outbound' | 'inbound';
  /* 是否是绑定模式 */
  isBinding: boolean;
}

export const useFetchByType = (props: IUseFetchByType) => {
  const { type, isBinding } = props;

  const fetchOrderLinesData = async (payload: any) => {
    try {
      let result;
      if (isBinding) {
        result = await BsibOrderApi.Lines(payload);
      } else {
        result = await SibOrderApi.Lines(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };

  const fetchPreBoxRfidsData = async (payload: any) => {
    try {
      let result;
      if (isBinding) {
        result = await BsibOrderApi.PreBoxRfids(payload);
      } else {
        result = await SibOrderApi.PreBoxRfids(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };

  const fetchBoxRfidsData = async (payload: any) => {
    try {
      let result;
      if (isBinding) {
        result = await BsibOrderApi.Rfids(payload);
      } else {
        result = await SibOrderApi.Rfids(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };
};
