import { ModalProps } from 'antd/es/modal';
import * as BwobOrderApi from 'common/api/warehouse/BwobOrder';
import * as WobOrderApi from 'common/api/warehouse/WobOrder';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onOk?: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onOk } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item) => {
      switch (item.toPartnerType) {
        case i18n.t('global.shop'):
          item.toPartnerType = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.toPartnerType = 'WAREHOUSE';
          break;
        case i18n.t('global.factory'):
          item.toPartnerType = 'FACTORY';
          break;
        default:
          break;
      }
    });
    try {
      setProgressPercent(0);
      if (currentUser.mode === 'BINDING') {
        await BwobOrderApi.BatchImports(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      } else {
        await WobOrderApi.BatchImports(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      }

      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  const template: TemplateItem[] = [
    {
      dataIndex: 'orderCode',
      display: i18n.t('global.sourceOrderCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.warehouseCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'warehouseCode',
      display: i18n.t('global.subWarehouseCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'toPartnerType',
      display: i18n.t('global.receiverTypeInfo'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'toPartnerCode',
      display: i18n.t('global.receiverCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'toWarehouseCode',
      display: i18n.t('global.toWarehouseCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'price',
      display: i18n.t('global.amount'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'qty',
      display: i18n.t('global.count'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'expressPartnerCode',
      display: i18n.t('global.logisticsCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'expressNumber',
      display: i18n.t('global.expressNumber'),
      type: 'STRING',
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.remark'),
      type: 'STRING',
    },
    {
      dataIndex: 'orderTypeCode',
      display: i18n.t('global.orderTypeCode'),
      type: 'STRING',
    },
  ];

  return (
    <Modal
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      destroyOnClose
      footer={false}
      width={1000}
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.warehouseOutBoundOrder')}
        template={template}
        onImport={onImport}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
