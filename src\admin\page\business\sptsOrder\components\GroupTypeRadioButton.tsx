import { Radio } from 'antd';
import { RadioGroupProps } from 'antd/lib/radio';
import i18n from 'common/utils/I18n';
import React from 'react';

interface IGroupTypeRadioButton extends RadioGroupProps {
  showByTag?: boolean;
}

const GroupTypeRadioButton: React.FC<IGroupTypeRadioButton> = (props) => {
  const { showByTag } = props;
  return (
    <Radio.Group buttonStyle="solid" {...props}>
      <Radio.Button value="BY_PROD">{i18n.t('global.byProd')}</Radio.Button>
      <Radio.Button value="BY_SKU">{i18n.t('global.bySku')}</Radio.Button>
      {showByTag && <Radio.Button value="BY_TAG">{i18n.t('global.byTag')}</Radio.Button>}
    </Radio.Group>
  );
};

export default GroupTypeRadioButton;
