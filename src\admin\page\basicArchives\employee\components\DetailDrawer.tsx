import { Form, Spin, Tabs } from 'antd';
import * as Employee<PERSON><PERSON> from 'common/api/core/Employee';
import * as PartnerPermissionApi from 'common/api/core/PartnerPermission';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useReducer, useRef, useState } from 'react';

import { DetailDrawerContext, initialState, reducer } from './DetailDrawerReducer';
import OperateForm, { OperateFormInnerRef } from './OperateForm';
import PartnerPermission from './PartnerPermission';

interface PartnerPermissionDrawerProps {
  open: boolean;
  onClose: () => void;
  empId: any;
}

const DetailDrawer: React.FC<PartnerPermissionDrawerProps> = (props) => {
  const { open, onClose, empId } = props;
  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState<string>('INFO');
  const [saving, setSaving] = useState(false);
  const operateFormRef = useRef<OperateFormInnerRef>();
  const [loading, setLoading] = useState<boolean>(false);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const partnerPermissionRef = useRef<any>(null);
  const [state, dispatch] = useReducer(reducer, initialState);
  const { allPermissionFlag, allSelectedRows, partnerPermissionList } = state;

  const [permission] = usePermission('A:BASE:EMPLOYEE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DEL');
  const setPermission = permission.codes.includes('SET_P_P');
  const resetPermission = permission.codes.includes('RESET');

  const fetchEmployee = useCallback(async () => {
    setLoading(true);
    try {
      const employee: any = await EmployeeApi.Get({
        id: empId,
      });
      if (employee.mobile?.indexOf(')') !== -1) {
        employee.mobileArea = employee.mobile?.slice(2, employee.mobile.indexOf(')'));
      }
      employee.mobile = employee.mobile?.slice(employee.mobile.indexOf(')') + 1);
      employee.status = !employee.disabled;

      form.setFieldsValue(employee);
      setCurrent(employee);
      // setIsAllSelect(employee.allPermission);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [empId, form]);

  const onSave = async () => {
    setSaving(true);
    let partners: any[] = [];
    if (activeKey === 'CUSTOM') {
      partners = allSelectedRows.map(({ partnerId, id, permissionDataId }) => ({
        id: permissionDataId || null,
        partnerId,
        warehouseId: id,
        checked: true,
      }));
      partnerPermissionList.forEach((item) => {
        const exist = partners.some((n) => n.warehouseId === item.warehouseId);
        if (!exist) {
          partners.push({
            id: item.id,
            partnerId: item.partnerId,
            warehouseId: item.warehouseId,
            checked: false,
          });
        }
      });
    }

    let values: Record<string, any> = {};
    try {
      values = await form.validateFields();
    } catch (error) {
      NoticeUtil.warn(i18n.t('global.basicInfoTips'));
      setSaving(false);
      return;
    }

    try {
      // 有编辑权限
      if (editPermission) {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await EmployeeApi.Update({
          ...current,
          ...values,
        });
      }

      // 有分配权限的权限
      if (setPermission) {
        await PartnerPermissionApi.Save([
          {
            allPermissionFlag,
            empId,
            partners,
          },
        ]);
      }
      setSaving(false);
      if (onClose) {
        onClose();
        dispatch({ type: 'reset' });
      }
      NoticeUtil.success();
    } catch (err) {
      setSaving(false);
    }
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await EmployeeApi.Delete({ id: current?.id });
          NoticeUtil.success();
          if (onClose) onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchEmployee();
    setActiveKey('INFO');
    if (partnerPermissionRef.current) {
      partnerPermissionRef.current.recover();
    }
  };

  useEffect(() => {
    if (current) {
      dispatch({ type: 'setAllPermissionFlag', payload: !!current.allPermission });
      dispatch({ type: 'setDefaultAllPermissionFlag', payload: !!current.allPermission });
      dispatch({ type: 'setType', payload: current.type });
    }
  }, [current]);

  useEffect(() => {
    if (empId) dispatch({ type: 'setEmpId', payload: empId });
  }, [empId]);

  useEffect(() => {
    if (open && empId) {
      fetchEmployee();
      operateFormRef.current?.focusNameInput();
    } else {
      form.resetFields();
      setActiveKey('INFO');
      dispatch({ type: 'reset' });
    }
    // eslint-disable-next-line
  }, [open, empId, form]);

  const items: any[] = [];

  if (editPermission) {
    items.push({
      label: i18n.t('global.information'),
      key: 'INFO',
      children: <OperateForm innerRef={operateFormRef} form={form} name="editForm" statusVisible codeReadOnly />,
    });
  }

  if (setPermission) {
    items.push({
      label: i18n.t('global.permission'),
      key: 'CUSTOM',
      children: <PartnerPermission innerRef={partnerPermissionRef} />,
    });
  }

  const resetBtnOnClick = async () => {
    if (resetPermission && empId) {
      NoticeUtil.confirm({
        title: i18n.t('global.confirmResetPasswordTips'),
        content: `${current?.code} - ${current?.name}`,
        okType: 'danger',
        onOk: async () => {
          try {
            await EmployeeApi.Reset({ empId });
            NoticeUtil.success();
            if (onClose) onClose();
          } catch (error) {}
        },
      });
    }
  };

  return (
    <Drawer
      destroyOnClose
      width={880}
      open={open}
      onClose={onClose}
      title={
        <>
          {i18n.t('global.employeeDetail')}
          {current && current.name && ` [${current.name}]`}
        </>
      }
      bodyStyle={{
        paddingTop: 0,
      }}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: saving,
          }}
          onApply={onSave}
          deletePermission={deletePermission}
          resetPermission={resetPermission}
          onDelete={deleteBtnOnClick}
          onReset={resetBtnOnClick}
        />
      }
    >
      <DetailDrawerContext.Provider value={{ state, dispatch }}>
        <Spin spinning={loading}>
          <Tabs
            size="small"
            onChange={(key) => {
              setActiveKey(key);
            }}
            animated={false}
            activeKey={activeKey}
            items={items}
          />
        </Spin>
      </DetailDrawerContext.Provider>
    </Drawer>
  );
};

export default DetailDrawer;
