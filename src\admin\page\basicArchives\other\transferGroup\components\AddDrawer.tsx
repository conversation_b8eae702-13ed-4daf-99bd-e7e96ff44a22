import OperateForm from 'admin/page/basicArchives/other/transferGroup/components/OperateForm';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { ITransferGroupItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: ITransferGroupItem) => void;
  onCancel: () => void;
}

const OperationModal: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, visible, confirmLoading } = props;
  const operateFormRef = useRef<any>(null);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        operateFormRef.current?.focusCodeInput();
      }, 300);
    } else {
      form.resetFields();
    }
  }, [visible, form]);
  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ITransferGroupItem);
    }
  };

  return (
    <Drawer
      title={i18n.t('global.newTransferGroup')}
      okButtonProps={{
        loading: confirmLoading,
      }}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      onOk={handleSubmit}
      onClose={() => {
        if (onCancel) onCancel();
      }}
    >
      <OperateForm innerRef={operateFormRef} form={form} statusVisible={false} onFinish={handleFinish} />
    </Drawer>
  );
};

export default OperationModal;
