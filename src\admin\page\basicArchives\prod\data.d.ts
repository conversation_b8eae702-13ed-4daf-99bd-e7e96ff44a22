import IBase from 'common/types/IBase';
import IExt from 'common/types/IExt';

export interface IProdItem extends IBase {
  brandCode: string;
  brandId: string;
  brandName: string;
  colorGrpCode: string;
  colorGrpId: string;
  colorGrpName: string;
  disableColor: boolean;
  disableSize: boolean;
  disableSpec: boolean;
  disabled: boolean;
  genderCode: string;
  genderId: string;
  genderName: string;
  priCategoryCode: string;
  priCategoryId: string;
  priCategoryName: string;
  code: string;
  name: string;
  remark: string;
  sizeGrpCode: string;
  sizeGrpId: string;
  sizeGrpName: string;
  specGrpCode: string;
  specGrpId: string;
  specGrpName: string;
  subCategoryCode: string;
  subCategoryId: string;
  subCategoryName: string;
  yearCode: string;
  yearId: string;
  yearName: string;
  ext: IExt;
  rfidTag: boolean;
  disturbTag: boolean;
  defaultImg: string;
  id: string;
  defaultImgUrl: string;
}

export interface ISkuItem extends IBase {
  barcode: string;
  colorCode: string;
  colorId: string;
  colorName: string;
  disabled: boolean;
  name: string;
  prodCode: string;
  prodId: string;
  prodName: string;
  sizeCode: string;
  sizeId: string;
  sizeName: string;
  specCode: string;
  specId: string;
  specName: string;
  remark: string;
  retailPrice: number;
  tagPrice: number;
  source: string;
  id: string;
  status: boolean;
}
