import { ColDef } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { Button, Row, Space, Tooltip } from 'antd';
import classNames from 'classnames';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import i18n from 'common/utils/I18n';
import React, { useCallback, useMemo } from 'react';
import InformationFillIcon from 'common/assets/icons/icon-information-fill.svg?react';

interface IDataViewTable {
  leftTBar?: React.ReactNode;
  data?: any[];
  onDelete: (n: string) => void;
  onReset: (n: string) => void;
  localTag: boolean;
  className?: string;
  showErrorColumns?: boolean;
}

const DataViewTable: React.FC<IDataViewTable> = (props) => {
  const { leftTBar, data, onDelete, onReset, localTag, className, showErrorColumns } = props;

  const wrapRef = React.useRef<HTMLDivElement>(null);

  const colDefs = useMemo(() => {
    const result: ColDef[] = [
      {
        field: 'barcode',
        headerName: i18n.t('global.barcode'),
        minWidth: 150,
        flex: 1,
      },
      {
        field: 'prodCode',
        headerName: i18n.t('global.productCode'),
        width: 160,
        cellRenderer: (params) => (
          <ProdCodeRowText prodCode={params.value} rfidTag={params.data.rfidTag} disturbTag={params.data.disturbTag} />
        ),
      },
      {
        field: 'skuName',
        headerName: i18n.t('global.name'),
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'colorName',
        headerName: i18n.t('global.color'),
        width: 130,
      },
      {
        field: 'sizeName',
        headerName: i18n.t('global.size'),
        width: 130,
      },
      {
        field: 'specName',
        headerName: i18n.t('global.spec'),
        width: 130,
      },
    ];

    // 条件性添加订单数量和可用数量列 (仅在非本地标签模式下显示)
    if (!localTag) {
      result.push(
        {
          field: 'qty',
          headerName: i18n.t('global.orderCount'),
          width: 130,
          headerClass: 'text-right',
          cellClass: 'text-right',
          type: 'numericColumn',
        },
        {
          field: 'availableQty',
          headerName: i18n.t('global.availableQty'),
          width: 130,
          headerClass: 'text-right',
          cellClass: 'text-right text-lead-green',
          type: 'numericColumn',
        },
      );
    }

    // 操作数量列
    result.push({
      field: 'operateQty',
      headerName: i18n.t('global.operateQty'),
      width: 130,
      headerClass: 'text-right',
      cellClass: 'text-right text-lead-green',
      type: 'numericColumn',
    });

    // 条件性添加错误数和超数列
    if (showErrorColumns) {
      result.push(
        {
          field: 'overQty',
          headerName: i18n.t('global.excessQty'),
          width: 100,
          cellClass: 'flex justify-end items-center',
          cellRenderer: (record) => {
            if (record.value > 0) {
              return (
                <Tooltip
                  placement="topRight"
                  title={
                    <div className="flex flex-col">
                      <span>{`${i18n.t('global.excessQty')}: ${record.value}`}</span>
                      <span>{i18n.t('global.exceptDealTip')}</span>
                    </div>
                  }
                >
                  <InformationFillIcon className="h-6 w-6 cursor-pointer fill-lead-orange" />
                </Tooltip>
              );
            }
            return null;
          },
        },
        {
          field: 'errorQty',
          headerName: i18n.t('global.exceptQty'),
          width: 100,
          cellClass: 'flex justify-end items-center',
          cellRenderer: (record) => {
            if (record.value > 0) {
              return (
                <Tooltip
                  placement="topRight"
                  title={
                    <div className="flex flex-col">
                      <span>{`${i18n.t('global.exceptQty')}: ${record.value}`}</span>
                      <span>{i18n.t('global.exceptDealTip')}</span>
                    </div>
                  }
                >
                  <InformationFillIcon className="h-6 w-6 cursor-pointer fill-lead-red" />
                </Tooltip>
              );
            }
            return null;
          },
        },
      );
    }

    // 操作列
    result.push({
      headerName: i18n.t('global.operation'),
      width: 150,
      cellRenderer: (params) => (
        <Space>
          {localTag && <Button onClick={() => onDelete(params.data.barcode)}>{i18n.t('global.delete')}</Button>}
          {!localTag && data?.find((n) => n.barcode === params.data.barcode)?.operateQty > 0 && (
            <Button onClick={() => onReset(params.data.barcode)}>{i18n.t('global.reset')}</Button>
          )}
        </Space>
      ),
    });

    return result;
  }, [data, localTag, showErrorColumns, onDelete, onReset]);

  const getRowId = useCallback((params) => params.data.barcode, []);

  return (
    <div className={classNames(className, 'flex flex-col')}>
      <Row justify="space-between" align="middle" className="mb-4">
        {leftTBar}
      </Row>
      <div ref={wrapRef} className={classNames('ag-theme-quartz h-full w-full flex-auto')}>
        <AgGridReact
          enableCellTextSelection
          getRowId={getRowId}
          ensureDomOrder
          rowData={data}
          columnDefs={colDefs}
          rowHeight={48}
          defaultColDef={{
            sortable: true,
            resizable: true,
          }}
        />
      </div>
    </div>
  );
};

export default DataViewTable;
