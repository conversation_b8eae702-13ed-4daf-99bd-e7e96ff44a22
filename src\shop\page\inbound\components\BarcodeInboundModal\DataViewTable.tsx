import { Button, Col, Row, Space, Typography } from 'antd';
import { TableProps } from 'antd/es/table';
import classNames from 'classnames';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import VirtualTable from 'common/components/VirtualTable';
import useWindowSize from 'common/hooks/useWindowSize';
import i18n from 'common/utils/I18n';
import React from 'react';

const { Text } = Typography;

interface IDataViewTable {
  leftTBar?: React.ReactNode;
  data?: any[];
  normalData?: any[];
  tableProps?: TableProps<any>;
  onDelete: (n: string) => void;
  onReset: (n: string) => void;
  localTag: boolean;
  className?: string;
}

const DataViewTable: React.FC<IDataViewTable> = (props) => {
  const { leftTBar, data, normalData, onDelete, onReset, localTag, className } = props;
  const { height } = useWindowSize();

  let barcodeTableColumns: any = [
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      ellipsis: true,
      width: 150,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      ellipsis: true,
      width: 160,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} rfidTag={record.rfidTag} disturbTag={record.disturbTag} />
      ),
      sorter: {
        compare: (a, b) => a.prodCode.localeCompare(b.prodCode),
      },
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'skuName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.color'),
      width: 130,
      ellipsis: true,
      render: (text, record) => <Text>{record.colorName}</Text>,
    },
    {
      title: i18n.t('global.size'),
      width: 130,
      ellipsis: true,
      render: (text, record) => <Text>{record.sizeName}</Text>,
    },
    {
      title: i18n.t('global.spec'),
      width: 130,
      ellipsis: true,
      render: (text, record) => <Text>{record.specName}</Text>,
    },
    {
      title: i18n.t('global.orderCount'),
      dataIndex: 'qty',
      align: 'right',
      width: 130,
      ellipsis: true,
      render: (text, record) => (
        <Text
          style={{
            fontSize: 20,
          }}
        >
          {record.qty}
        </Text>
      ),
    },
    {
      title: i18n.t('global.availableQty'),
      valueType: 'number',
      dataIndex: 'availableQty',
      sorter: {
        compare: (a, b) => a.availableQty - b.availableQty,
      },
      width: 130,
    },
    {
      title: i18n.t('global.operateQty'),
      valueType: 'number',
      dataIndex: 'operateQty',
      width: 130,
    },
    {
      title: i18n.t('global.operation'),
      valueType: 'operation',
      width: 200,
      render: (text, record) => (
        <Space>
          {record.qty === 0 && <Button onClick={() => onDelete(record.barcode)}>{i18n.t('global.delete')}</Button>}
          {normalData?.find((n) => n.barcode === record.barcode)?.qty > 0 && record.qty > 0 && (
            <Button onClick={() => onReset(record.barcode)}>{i18n.t('global.reset')}</Button>
          )}
        </Space>
      ),
    },
  ];

  if (localTag) {
    barcodeTableColumns = barcodeTableColumns.filter((n) => n.dataIndex !== 'qty' && n.dataIndex !== 'availableQty');
  }

  return (
    <div className={classNames(className, 'flex flex-col text-center')}>
      <Row justify="space-between" align="middle" className="mb-4">
        <Col>{leftTBar}</Col>
        <Col />
      </Row>
      <VirtualTable
        className="flex-auto"
        rowKey="barcode"
        dataSource={data}
        columns={barcodeTableColumns}
        scroll={{ y: height - 350, x: 1150 }}
      />
    </div>
  );
};

export default DataViewTable;
