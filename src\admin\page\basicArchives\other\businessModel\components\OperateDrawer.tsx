import { Checkbox, Form, Input } from 'antd';
import * as BusinessModelApi from 'common/api/core/BusinessModel';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IBusinessModelItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  businessId: string | undefined;
  onSubmit: (values: IBusinessModelItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, businessId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:OTHER:BUSINESS_MODEL');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchBusinessModel = useCallback(async () => {
    setLoading(true);
    try {
      const businessModel: any = await BusinessModelApi.Get({
        id: businessId,
      });
      businessModel.categoryCodeList = businessModel.categories.map((n) => n.code);
      form.setFieldsValue(businessModel);
      setCurrent(businessModel);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [businessId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (businessId) fetchBusinessModel();
      setTimeout(() => {
        if (businessId) {
          nameInputRef.current?.focus();
        } else {
          codeInputRef.current?.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [visible, businessId, fetchBusinessModel, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IBusinessModelItem);
    }
  };

  const plainOptions = [
    { label: i18n.t('global.warehouse'), value: 'WAREHOUSE' },
    { label: i18n.t('global.shop'), value: 'SHOP' },
  ];

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editBusinessModal')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newBusinessModal')
  );
  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await BusinessModelApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchBusinessModel();
  };

  return (
    <Drawer
      title={title}
      okButtonProps={{
        loading: confirmLoading,
      }}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!current}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={codeInputRef} readOnly={!!current} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item name="categoryCodeList" label={i18n.t('global.categoryCodeList')} rules={canNotBeNullRules}>
            <Checkbox.Group options={plainOptions} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
