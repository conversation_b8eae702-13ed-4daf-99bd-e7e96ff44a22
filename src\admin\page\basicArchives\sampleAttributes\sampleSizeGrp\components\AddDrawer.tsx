import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect } from 'react';
import SizeGrpOperateForm from './SizeGrpOperateForm';

import { ISampleSizeGrpItem } from '../data';

interface IAddModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: ISampleSizeGrpItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<IAddModalProps> = (props) => {
  const [form] = Form.useForm();
  const { visible, confirmLoading, onSubmit, onCancel } = props;

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISampleSizeGrpItem);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Drawer
      title={i18n.t('global.newSizeGrp')}
      okButtonProps={{
        loading: confirmLoading,
      }}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      okText={i18n.t('global.apply')}
      onOk={handleSubmit}
      onClose={() => {
        onCancel();
        form.resetFields();
      }}
    >
      <SizeGrpOperateForm form={form} initialValues={{ sortId: 0, disabled: false }} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddDrawer;
