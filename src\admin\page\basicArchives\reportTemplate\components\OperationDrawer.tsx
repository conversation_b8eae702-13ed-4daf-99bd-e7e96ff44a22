import { Button, Form, Input, message, Upload } from 'antd';
import { baseUrl, getToken } from 'common/api/Base';
import UploadCloudIcon from 'common/assets/icons/icon-upload-cloud.svg?react';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import LanguageSelect from 'common/components/Select/LanguageSelect';
import i18n from 'common/utils/I18n';
import React, { useEffect } from 'react';

import { ITemplateItem } from '../data';

interface OperationModalProps {
  open: boolean;
  confirmLoading: boolean;
  current: Partial<ITemplateItem> | undefined;
  onSubmit: (values: ITemplateItem) => void;
  onDelete: (values: Partial<ITemplateItem> | undefined) => void;
  onClose: () => void;
}

const OperationDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onClose, current, open, confirmLoading, onDelete } = props;

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (current) {
      form.setFieldsValue(current);
    }
  }, [current, form]);

  useEffect(() => {
    if (!open) {
      form.resetFields();
    }
  }, [open, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ITemplateItem);
    }
  };

  const uploadProps = {
    name: 'file',
    maxCount: 1,
    action: `${baseUrl}/api/file/f/template/upload`,
    headers: {
      authorization: getToken(),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        form.setFieldsValue({ source: info.file.response });
        message.success(`${info.file.name} ${i18n.t('global.uploadSuccess')}`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} ${i18n.t('global.uploadError')}`);
      }
    },
  };

  const deleteBtnOnclick = () => {
    if (onDelete) onDelete(current);
  };

  const onRecover = () => {
    form.setFieldsValue(current);
  };

  const onCancel = () => {
    onClose();
    form.resetFields();
  };

  return (
    <Drawer
      title={current ? `${i18n.t('global.editReportTemplate')} [${current.name}]` : i18n.t('global.newReportTemplate')}
      destroyOnClose
      open={open}
      okText={current ? i18n.t('global.apply') : i18n.t('global.ok')}
      onClose={onCancel}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={!!current}
          onDelete={deleteBtnOnclick}
          cancelPermission={!current}
          onCancel={onCancel}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
    >
      <Form layout="vertical" form={form} onFinish={handleFinish}>
        <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
          <Input autoFocus />
        </Form.Item>
        <Form.Item label={i18n.t('global.language')} name="language" rules={canNotBeNullRules}>
          <LanguageSelect />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={3} />
        </Form.Item>
        <Form.Item label={i18n.t('global.templateFile')} name="source" rules={canNotBeNullRules}>
          <Upload {...uploadProps}>
            <Button icon={<UploadCloudIcon className="fill-lead-dark" />}>{i18n.t('global.uploadTemplateFile')}</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default OperationDrawer;
