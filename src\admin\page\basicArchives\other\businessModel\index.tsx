import OperateDrawer from 'admin/page/basicArchives/other/businessModel/components/OperateDrawer';
import { Button, Select } from 'antd';
import * as BusinessModelApi from 'common/api/core/BusinessModel';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useRef, useState } from 'react';

import { IBusinessModelItem } from './data';

const { Option } = Select;

const BusinessModel: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IBusinessModelItem> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [permission] = usePermission('A:BASE:OTHER:BUSINESS_MODEL');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await BusinessModelApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const fetchData = async (params: Record<string, any>) => {
    let data: any = [];
    try {
      data = await BusinessModelApi.List(params);
    } catch {}
    return { data };
  };

  const handleSubmit = async (values: IBusinessModelItem) => {
    const id = current ? current.id : '';

    setConfirmLoading(true);
    try {
      if (id) {
        await BusinessModelApi.Update({
          // @ts-ignore
          id,
          ...values,
        });
      } else {
        values.code = values.code.trim();
        values.name = values.name.trim();
        await BusinessModelApi.Create(values);
      }
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'os',
      label: i18n.t('global.systemPresets'),
      inputComponent: (
        <Select placeholder={i18n.t('global.systemPresets')}>
          <Option value="true">{i18n.t('global.yes')}</Option>
          <Option value="false">{i18n.t('global.no')}</Option>
        </Select>
      ),
    },
    {
      name: 'categoryCode',
      label: i18n.t('global.categoryCodeList'),
      inputComponent: (
        <Select>
          <Option value="WAREHOUSE">{i18n.t('global.warehouse')}</Option>
          <Option value="SHOP">{i18n.t('global.shop')}</Option>
        </Select>
      ),
    },
  ];
  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      ellipsis: true,
      width: 180,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      width: 180,
      ellipsis: true,
    },
    {
      title: i18n.t('global.systemPresets'),
      dataIndex: 'os',
      valueType: 'boolean',
      width: 190,
    },
    {
      title: i18n.t('global.categoryCodeList'),
      render: (text, record) => (
        <span>
          {record.categories.length > 0 &&
            record.categories
              .map((n) => {
                switch (n.code) {
                  case 'WAREHOUSE':
                    return i18n.t('global.warehouse');
                  case 'SHOP':
                    return i18n.t('global.shop');
                  case 'FACTORY':
                  default:
                    return '';
                }
              })
              .join(',')}
        </span>
      ),
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      minWidth: 250,
      auto: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      isDisabled: (record) => record.os,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        rightToolbar={[
          createPermission && (
            <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        settingToolVisible
        pagination={false}
        autoLoad
        enableCache
        cacheKey="BUSINESS_MODEL"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  if (!record.os) {
                    setCurrent(record);
                    setVisible(true);
                  }
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <OperateDrawer
        visible={visible}
        confirmLoading={confirmLoading}
        businessId={current ? current.id : ''}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default BusinessModel;
