/**
 * 翻译生成器
 * > 生成react-intl的message内容
 * 功能：
 * 1. 递归扫描/src下的locale文件夹的所有文件
 * 2. 合并同名文件为json到目录src/translations
 *     现有目录文件如：
 *        /src/page/moduleA/locale/zh-CN.ts
 *        /src/page/moduleA/locale/en-US.ts
 *        /src/page/moduleB/locale/zh-CN.ts
 *        /src/page/moduleB/locale/en-US.ts
 *     将生成：
 *        # 合并moduleA及moduleB的zh-CN.ts内容
 *        /src/translations/zh-CN.json
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const mkdirp = require('mkdirp');

const targetDir = 'src/translations/locales';
const scanDir = 'src';

console.log('Translate generator execute ...');
console.log('targetDir：', path.join(process.cwd(), targetDir));

mkdirp(targetDir).then(made => {
  if (!made) {
    console.log('noting to make!');
    return;
  }
  console.log(`made directories, starting with ${made}`);
});

const getDirectories = function (src, callback) {
  glob(src + '/**/*', callback);
};

const getAbsoulePath = function (p) {
  return path.join(process.cwd(), p);
};

/**
 * {
 *   "zh-CN": {},
 *   "en-US": {}
 * }
 */
const messages = {};
/**
 * [{
 *   path: 'file/path.ts',
 *   error: 'error message'
 * }]
 */
const errorFiles = [];

function writeResultFiles () {
  Object.keys(messages).forEach((key) => {
    let val = messages[key];
    // console.log(getAbsoulePath(`${targetDir}/${key}.json`));
    console.log(getAbsoulePath(`${targetDir}/${key}.json`), JSON.stringify(val, null, '\t'));
    fs.writeFile(getAbsoulePath(`${targetDir}/${key}.json`), JSON.stringify(val, null, '\t'), (err) => {
      if (err) throw err;
    });
  });
}

getDirectories(scanDir, function (err, res) {
  if (err) throw err;
  const localeDirs = res.filter(n => n.endsWith('/locale'));
  localeDirs.forEach((localDir, id) => {
    console.log(id, localDir);
    const files = fs.readdirSync(getAbsoulePath(localDir));
    files.forEach(file => {
      if (!file.endsWith('.ts')) return;
      const languageCode = file.slice(0, file.lastIndexOf('.'));
      const filePath = getAbsoulePath(`${localDir}/${file}`);
      result = fs.readFileSync(filePath, 'utf8');

      console.log(`语言编码：${languageCode}`);
      console.log(`文件路径：${filePath}`);
      console.log(`文件内容：\n`, result);
      const match = result.match(/\{[\s\S]*\}/);
      if (match === 0) {
        errorFiles.push({ path: filePath, error: '无法匹配文件内容' });
        return;
      }
      let msgObj = {};
      try {
        msgObj = JSON.parse(match[0]);
      } catch {
        errorFiles.push({ path: filePath, error: '无法解析文件内容' });
        return;
      }
      Object.keys(msgObj).forEach((key) => {
        const val = msgObj[key];
        if (messages[languageCode]) {
          messages[languageCode][key] = val;
        } else {
          messages[languageCode] = {[key]: val}
        }
      });
    });
    console.log('files:', files);
  });
  console.log('messages:\n', messages);
  writeResultFiles();
});

// getDirectories(scanDir, function (err, res) {
//   if (err) {
//     console.log('Error', err);
//   } else {
//     console.log(res);
//   }
// });

// mkdirp.sync(targetDir, function(err) { 
//   if (err) throw err;
//   console.log('Done!');
//   // path exists unless there was an error
// });


