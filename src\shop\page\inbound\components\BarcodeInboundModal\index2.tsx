import { Col, Row, Space, Spin } from 'antd';
import { ModalProps } from 'antd/es/modal';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useReducer, useRef } from 'react';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import ExchangeIcon from 'common/assets/icons/icon-exchange.svg?react';
import Button from 'common/components/Button';
import * as Sku<PERSON><PERSON> from 'common/api/core/Sku';
import * as Bsib<PERSON>rder<PERSON>pi from 'common/api/shop/BsibOrder';
import * as SibOrder<PERSON>pi from 'common/api/shop/SibOrder';
import * as NoticeUtil from 'common/utils/Notice';
import useSpeech from 'common/hooks/useSpeech';
import useSetting from 'common/hooks/useSetting';
import BarcodeAliasModal from 'common/components/BarcodeAliasModal';
import useSearchAlias from 'common/components/BarcodeAliasModal/hook/useSearchAlias';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { offWindowBeforeUnload, onWindowBeforeUnload } from 'common/utils';
import BarcodeInput from 'common/components/BarcodeInput';

import FlashStatistic from 'common/components/FlashStatistic';
import { initialState, reducer } from './reducer';
import DataViewTable from './DataViewTable';

interface IBarcodeInboundModalProps {
  boxInfo: {
    id: string;
    preBoxId: string;
    code: string;
    status: string;
  };
  orderInfo: {
    id: string;
    localTag: boolean;
  };
  innerRef: any;
  modalProps?: ModalProps;
  onOk: () => void;
  onOperateModeChange: () => void;
}

const BarcodeInboundModal: React.FC<IBarcodeInboundModalProps> = (props) => {
  const { boxInfo, orderInfo, innerRef, modalProps, onOk, onOperateModeChange } = props;

  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const [state, dispatch] = useReducer(reducer, initialState);
  const { loading, saving, tableLinesData, normalData, normalTotalQty, abnormalTotal } = state;

  const {
    ENABLE_BARCODE_SCAN_CUTOFF: enableBarcodeScanCutoff,
    BARCODE_CUTOFF_BEFORE_DIGITS: barcodeCutoffBeforeDigits,
    BARCODE_CUTOFF_AFTER_DIGITS: barcodeCutoffAfterDigits,
  } = useSetting([
    { code: 'ENABLE_BARCODE_SCAN_CUTOFF', valueType: 'BOOLEAN' },
    { code: 'BARCODE_CUTOFF_BEFORE_DIGITS', valueType: 'NUMBER' },
    { code: 'BARCODE_CUTOFF_AFTER_DIGITS', valueType: 'NUMBER' },
  ]);

  const {
    barcodeAliasModalOpen,
    barcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  } = useSearchAlias();

  const { playSound, stopSound } = useSpeech();

  const isOperate = useRef(false);
  const barcodeInputRef = useRef<any>(null);
  const normalQtyRef = useRef<any>(null);

  useImperativeHandle(innerRef, () => ({
    getIsOperate: () => isOperate.current,
  }));

  const playAndNotify = useCallback(
    (text: string, type: 'success' | 'error' | 'warn' | 'info', val?: string) => {
      NoticeUtil[type](val ? `${text} ${val}` : text);
      playSound({ text });
    },
    [playSound],
  );

  const fetchOrderLinesData = async () => {
    try {
      let result;
      const payload = {
        sibOrderId: orderInfo.id,
        enablePage: false,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.Lines(payload);
      } else {
        result = await SibOrderApi.Lines(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };

  const fetchPreBoxRfidsData = async () => {
    try {
      let result;
      const payload = {
        boxId: boxInfo.preBoxId,
        sibOrderId: orderInfo.id,
        enablePage: false,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.PreBoxRfids(payload);
      } else {
        result = await SibOrderApi.PreBoxRfids(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };

  const fetchBoxRfidsData = async () => {
    try {
      let result;
      const payload = {
        sibBoxId: boxInfo.id,
        sibOrderId: orderInfo.id,
        enablePage: false,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.Rfids(payload);
      } else {
        result = await SibOrderApi.Rfids(payload);
      }
      return result.data || [];
    } catch {}
    return [];
  };

  const initData = async () => {
    try {
      dispatch({ type: 'setLoading', payload: true });
      let linesData: any[] = [];
      let boxRfidData: any[] = [];

      if (orderInfo.localTag) {
        // 本地单逻辑
        if (boxInfo.id) {
          // 本地单 - 编辑箱：请求箱明细，回显相关数据
          linesData = await fetchBoxRfidsData();
          dispatch({
            type: 'initData',
            payload: {
              linesData,
              localTag: orderInfo.localTag,
              callback: () => {
                normalQtyRef.current?.remind();
              },
            },
          });
        }
      } else {
        const promiseList: Promise<any>[] = [];
        // 非本地单逻辑
        if (boxInfo.preBoxId) {
          // 按预装箱装箱：请求预装箱明细
          promiseList.push(fetchPreBoxRfidsData());
        } else {
          // 按单据装箱：请求单据明细数据
          promiseList.push(fetchOrderLinesData());
        }

        if (boxInfo.id && boxInfo.status !== 'NEW') {
          // 编辑箱：根据单据/预装箱类型请求不同明细 + 请求箱明细，回显相关数据
          promiseList.push(fetchBoxRfidsData());
        }

        const results = await Promise.all(promiseList);
        linesData = results[0] || [];
        boxRfidData = results[1] || [];
        dispatch({
          type: 'initData',
          payload: {
            linesData,
            boxRfidData,
            callback: () => {
              normalQtyRef.current?.remind();
            },
          },
        });
      }
    } catch {
    } finally {
      dispatch({ type: 'setLoading', payload: false });
    }
  };

  useEffect(() => {
    if (modalProps?.open) {
      initData();
      onWindowBeforeUnload();
    } else {
      offWindowBeforeUnload();
      stopSound();
      isOperate.current = false;
      // 把 localTag 设置为 true 写死是为了关闭弹窗时，重置所有数据（不区分本地单和分本地单）
      dispatch({ type: 'resetState', payload: { localTag: true } });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalProps?.open]);

  const handleLocalScanDataByRules = async (barcode: string, operateSize: number) => {
    // 只能扫描当前表格中的条码，不能扫描别名，如果扫描别名，不通过 SkuApi.FindByAlias 会漏掉一些条码
    const tableDataLineItem = tableLinesData.find((n) => n.barcode === barcode);

    if (!tableDataLineItem) {
      // 如果条码不存在，则请求接口获取条码信息
      try {
        const res: any = await SkuApi.FindByAlias({
          alias: [barcode],
        });
        searchAliasByBarcode({
          barcode,
          operateSize,
          searchData: res.data,
          callback(foundBarcode: string, operateSize: number, index: number) {
            // callback 执行只会在找到一个条码 / 没找到才执行
            if (index === -1) {
              const text = i18n.t('global.notFoundBarcode');
              playAndNotify(text, 'warn', `[${barcode}]`);
              return;
            }
            // 新增一个条码到表格数据
            const tableDataLineItem = res.data[index];
            dispatch({
              type: 'tableLinesDataItemAdd',
              payload: {
                operateSize,
                tableDataLineItem,
                callback: () => {
                  requestAnimationFrame(() => {
                    normalQtyRef.current?.remind();

                    isOperate.current = true;
                    const text = `${i18n.t('global.scanSuccess')} [${barcode}]`;
                    playAndNotify(text, 'success');
                  });
                },
              },
            });
          },
        });
      } catch {}
    } else {
      // 如果条码存在，则更新表格数据
      searchAliasByBarcode({
        barcode,
        operateSize,
        searchData: tableLinesData,
        callback(foundBarcode: string, operateSize: number, index: number) {
          if (index === -1) {
            const text = i18n.t('global.notFoundBarcode');
            playAndNotify(text, 'warn', `[${barcode}]`);
            return;
          }
          const tableDataLineItem = tableLinesData[index];
          // 减少数是否超过了当前的操作数
          if (operateSize + tableDataLineItem.operateQty < 0) {
            const text = i18n.t('global.deleteQtyExcess');
            playAndNotify(text, 'warn', `[${barcode}]`);
            return;
          }

          dispatch({
            type: 'tableLinesDataItemUpdate',
            payload: {
              operateSize,
              barcode: tableDataLineItem.barcode,
              localTag: orderInfo.localTag,
              callback: () => {
                requestAnimationFrame(() => {
                  normalQtyRef.current?.remind();

                  isOperate.current = true;
                  const text = `${i18n.t('global.scanSuccess')} [${barcode}]`;
                  playAndNotify(text, 'success');
                });
              },
            },
          });
        },
      });
    }
  };

  const handleNotLocalScanDataByRules = async (barcode: string, operateSize: number) => {
    searchAliasByBarcode({
      barcode,
      operateSize,
      searchData: tableLinesData,
      callback(foundBarcode: string, operateSize: number, index: number) {
        if (index === -1) {
          const text = i18n.t('global.notFoundBarcode');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }

        const tableDataLineItem = tableLinesData[index];

        // 减少数是否超过了当前的操作数
        if (operateSize + tableDataLineItem.operateQty < 0) {
          const text = i18n.t('global.deleteQtyExcess');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }

        // 是否超了最大装箱数
        if (operateSize > tableDataLineItem.availableQty) {
          const text = i18n.t('global.readedQtyExcessRemainingQty');
          playAndNotify(text, 'warn', `[${barcode}]`);
          return;
        }

        dispatch({
          type: 'tableLinesDataItemUpdate',
          payload: {
            operateSize,
            barcode: tableDataLineItem.barcode,
            localTag: orderInfo.localTag,
            callback: () => {
              requestAnimationFrame(() => {
                normalQtyRef.current?.remind();

                isOperate.current = true;
                const text = `${i18n.t('global.scanSuccess')} [${barcode}]`;
                playAndNotify(text, 'success');
              });
            },
          },
        });
      },
    });
  };

  const handleBarcodeAliasModalOnOk = (selectedRows) => {
    barcodeAliasModalOnOk({
      selectedRows,
      searchData: tableLinesData,
      callback(selectedBarcode: string, operateSize: number, index: number) {
        if (index === -1) {
          // 不在条码表格中，新增一个条码到表格数据
          dispatch({
            type: 'tableLinesDataItemAdd',
            payload: {
              tableDataLineItem: selectedRows[0],
              operateSize,
              callback: () => {
                normalQtyRef.current?.remind();
              },
            },
          });
        } else {
          // 在条码表格中，更新表格数据
          dispatch({
            type: 'tableLinesDataItemUpdate',
            payload: {
              operateSize,
              barcode: selectedRows[0].barcode,
              localTag: orderInfo.localTag,
              callback: () => {
                normalQtyRef.current?.remind();
              },
            },
          });
        }
      },
    });
  };

  const handleBarcodeInputOnPressEnter = (barcode: string, operateSize: number) => {
    if (!operateSize) {
      const text = i18n.t('global.pleaseInputOperateQty');
      playAndNotify(text, 'warn');
      return;
    }
    if (operateSize === 0) {
      const text = i18n.t('global.operateQtyError');
      playAndNotify(text, 'warn');
      return;
    }

    if (orderInfo.localTag) {
      handleLocalScanDataByRules(barcode, operateSize);
    } else {
      handleNotLocalScanDataByRules(barcode, operateSize);
    }
  };

  const handleSaveBtnClick = async () => {
    try {
      // 模拟错误响应数据用于本地测试
      const mockError = {
        response: {
          data: {
            detailMsg: JSON.stringify([
              {
                message: '条码不在预装箱[FM20250731_001]内!',
                barcode: '2300600605',
                type: 'OTHER_ERROR',
                epc: null,
                overQty: 1,
                errQty: 1,
                uniqueCode: null,
              },
              // {
              //   message: '条码已超量!',
              //   barcode: '1604370602',
              //   type: 'CROSS_ERROR',
              //   epc: null,
              //   overQty: 2,
              //   errQty: 0,
              //   uniqueCode: null,
              // },
            ]),
          },
        },
      };
      throw mockError;

      dispatch({ type: 'setSaving', payload: true });

      const payload = {
        sibOrderId: orderInfo.id,
        data: normalData.map((item) => ({ barcode: item.barcode, qty: item.qty, operateMode: 'SKU' })),
      };

      if (currentUser.mode === 'BINDING') {
        await BsibOrderApi.Save(payload);
      } else {
        await SibOrderApi.Save(payload);
      }
      const text = i18n.t('global.saveSuccess');
      playAndNotify(text, 'success');
      if (onOk) onOk();
    } catch (err: any) {
      let responseData: any[] = [];
      if (err.response?.data?.detailMsg) {
        responseData = JSON.parse(err.response.data.detailMsg);
      }
      if (responseData.length > 0) {
        const text = i18n.t('global.pleaseHandleOverAndError');
        playAndNotify(text, 'warn');

        const responseErrorData: any[] = responseData.filter(
          (item) => item.type === 'CROSS_ERROR' || item.type === 'OTHER_ERROR',
        );
        dispatch({ type: 'setErrorDataInTableLine', payload: { errorData: responseErrorData } });
      }
    }
  };

  const modalTitle = (
    <div className="flex h-6">
      <div className="flex items-center gap-x-2">
        <span>
          {i18n.t('global.barcodeInbound')}
          {boxInfo.code ? ` [${boxInfo.code}]` : ''}
        </span>
        {boxInfo.status === 'NEW' && (
          <Button type="ghost" icon={<ExchangeIcon className="fill-lead-slate" />} onClick={onOperateModeChange} />
        )}
      </div>
    </div>
  );

  const handleModalCancel = () => {
    const commonLogic = () => {
      const fakeMouseEvent: any = {};
      if (modalProps?.onCancel) {
        modalProps.onCancel(fakeMouseEvent);
      }
    };
    if (isOperate.current) {
      const text = i18n.t('global.confirmDiscard');
      playSound({ text });
      NoticeUtil.confirm({
        content: text,
        onOk: () => {
          commonLogic();
        },
      });
    } else {
      commonLogic();
    }
  };

  return (
    <Modal
      fullScreen
      footer={false}
      title={modalTitle}
      centered
      maskClosable={false}
      destroyOnClose
      {...modalProps}
      onCancel={handleModalCancel}
      className="operate-modal"
    >
      <Spin spinning={loading}>
        <div className="flex h-full flex-col gap-y-4">
          <DataViewTable
            className="flex-auto"
            leftTBar={
              <Space size="large">
                <BarcodeInput
                  innerRef={barcodeInputRef}
                  onPressEnter={handleBarcodeInputOnPressEnter}
                  enableBarcodeScanCutoff={enableBarcodeScanCutoff}
                  barcodeCutoffBeforeDigits={barcodeCutoffBeforeDigits}
                  barcodeCutoffAfterDigits={barcodeCutoffAfterDigits}
                />
              </Space>
            }
            data={tableLinesData}
            onDelete={(barcode) => dispatch({ type: 'tableLinesDataItemDelete', payload: { barcode } })}
            onReset={(barcode) => dispatch({ type: 'tableLinesDataItemReset', payload: { barcode } })}
            localTag={orderInfo.localTag}
            showErrorColumns={abnormalTotal > 0}
          />
          <Row justify="space-between" align="bottom">
            <Col>
              <Space>
                <Button
                  type="success"
                  size="large"
                  loading={saving}
                  onClick={handleSaveBtnClick}
                  disabled={!(normalTotalQty > 0)}
                >
                  {i18n.t('global.save')}
                </Button>
                {normalTotalQty > 0 && (
                  <Button
                    danger
                    size="large"
                    onClick={() => dispatch({ type: 'resetState', payload: { localTag: orderInfo.localTag } })}
                  >
                    {i18n.t('global.reset')}
                  </Button>
                )}
              </Space>
            </Col>
            <Col>
              <Space style={{ marginLeft: 16 }} size="large">
                <FlashStatistic
                  innerRef={normalQtyRef}
                  title={i18n.t('global.normal')}
                  value={normalTotalQty}
                  type="success"
                />
              </Space>
            </Col>
          </Row>
        </div>
      </Spin>
      <BarcodeAliasModal
        open={barcodeAliasModalOpen}
        onCancel={barcodeAliasModalOnCancel}
        onOk={handleBarcodeAliasModalOnOk}
        data={barcodeAliasData}
        selectRowFiledId="barcode"
      />
    </Modal>
  );
};

export default BarcodeInboundModal;
