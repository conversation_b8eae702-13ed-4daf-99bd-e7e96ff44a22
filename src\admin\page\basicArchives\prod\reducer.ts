import { createContext } from 'react';

import { IProdItem, ISkuItem } from './data';

export const initialState: {
  skuList: ISkuItem[];
  prodRecord: IProdItem | null;
  disabled: boolean | null;
  prodId: string;
} = {
  skuList: [],
  prodRecord: null,
  disabled: null,
  prodId: '',
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'setSkuList'; payload: ISkuItem[] }
  | { type: 'setProdRecord'; payload: IProdItem | null }
  | { type: 'setDisabled'; payload: boolean | null }
  | { type: 'setProdId'; payload: string };

export function reducer(state: TStateType, action: TActionType): TStateType {
  switch (action.type) {
    case 'setSkuList':
      return { ...state, skuList: action.payload };
    case 'setProdRecord':
      return { ...state, prodRecord: action.payload };
    case 'setDisabled':
      return { ...state, disabled: action.payload };
    case 'setProdId':
      return { ...state, prodId: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TProdContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const ProdContext = createContext<TProdContext>({
  state: initialState,
  dispatch: () => undefined,
});
