import { ModalProps } from 'antd/es/modal';
import * as RfidApi from 'common/api/core/Rfid';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface ImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onFinish: () => void;
}

const ImportModal: React.FC<ImportModalProps> = (props) => {
  const { modalProps, onGoBack, onFinish } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const template: TemplateItem[] = [
    {
      dataIndex: 'epc',
      display: 'EPC',
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'tid',
      display: 'TID',
      type: 'STRING',
      required: false,
    },
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
      required: false,
    },
    {
      dataIndex: 'uniqueCode',
      display: i18n.t('global.uniqueCode'),
      type: 'STRING',
      required: false,
    },
    {
      dataIndex: 'gbCode',
      display: i18n.t('global.gbCode'),
      type: 'STRING',
      required: false,
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.warehouseCode'),
      type: 'STRING',
      required: false,
    },
    {
      dataIndex: 'partnerType',
      display: i18n.t('global.warehouseImporterType'),
      type: 'STRING',
      required: false,
    },
  ];

  const onImport = async (data) => {
    setProgressStatus('active');
    const extFields = template.filter((n) => n.dataIndex.startsWith('ext_')).map((n) => n.dataIndex.slice(4));
    data.forEach((item) => {
      const ext: any = {};
      extFields.forEach((extItem) => {
        ext[extItem] = item[`ext_${extItem}`];
        delete item[`ext_${extItem}`];
      });
      item.ext = ext;
    });
    try {
      setProgressPercent(0);
      data.forEach((item) => {
        if (item.partnerType === i18n.t('global.warehouse')) {
          item.partnerType = 'WAREHOUSE';
        } else if (item.partnerType === i18n.t('global.shop')) {
          item.partnerType = 'SHOP';
        } else if (item.partnerType === i18n.t('global.factory')) {
          item.partnerType = 'FACTORY';
        }
      });
      await RfidApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          // @ts-ignore
          timeout: window.globalConfig.prodImportTimeout.value,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      onFinish();
      setProgressPercent(100);
      setProgressStatus('success');
    } catch (e) {
      setProgressStatus('exception');
      throw e;
    }
  };

  return (
    <Modal width={960} title={i18n.t('global.import')} footer={false} destroyOnClose {...modalProps}>
      <Importer
        moduleName={i18n.t('global.tag')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
