import { Col, Input, Row } from 'antd';
import * as SampleRfidApi from 'common/api/sample/SampleRfid';
import * as SampleSkuApi from 'common/api/sample/SampleSku';
import Empty from 'common/components/Empty';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
// import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useState } from 'react';

import OperationLogs from './components/OperationLogs';

import styles from './index.module.css';

// const modeStorageKey = 'RFID-SOURCE-MODE';

const SampleRfidSource: React.FC = () => {
  const [source, setSource] = useState<Record<string, any> | undefined>(undefined);
  const [sku, setSku] = useState<Record<string, any> | undefined>(undefined);
  const [position, setPosition] = useState<Record<string, any>>();
  const [loading, setLoading] = useState<boolean>(false);
  // const [mode, setMode] = useState<'epc' | 'uniqueCode'>(LocalStorageUtil.getItem(modeStorageKey) || 'epc');
  const [isFetch, setIsFetch] = useState(false);

  const fetchData = async (value) => {
    setLoading(true);
    setSku(undefined);
    setSource(undefined);
    setPosition(undefined);
    setIsFetch(false);
    const epc = value;

    // 唯一码模式：先通过唯一码获取epc信息，再通过epc进行溯源
    // if (mode === 'uniqueCode') {
    //   try {
    //     const res: any = await SampleRfidApi.UniqueCode({
    //       uniqueCode: value,
    //     });
    //     if (!res) {
    //       setLoading(false);
    //       setIsFetch(false);
    //       NoticeUtil.warn(i18n.t('global.noUniqueCodeFound'));
    //       return;
    //     }
    //     epc = res.epc;
    //   } catch (e) {
    //     setLoading(false);
    //     return;
    //   }
    // }

    try {
      const newSource: any = await SampleRfidApi.Source({
        smEpc: epc,
      });
      if (!newSource) {
        setLoading(false);
        setIsFetch(false);
        NoticeUtil.warn(i18n.t('global.noEpcFound'));
        return;
      }

      setSource(newSource);

      const newSku = await SampleSkuApi.Get({
        id: newSource.smSkuId,
      });
      setSku(newSku || undefined);

      const resp: any = await SampleRfidApi.Position({
        smEpc: epc,
      });
      setPosition(resp || {});
    } catch (e) {}
    setIsFetch(true);
    setLoading(false);
  };

  const onSearch = (value) => {
    setSource(undefined);
    if (value) {
      fetchData(value);
    } else {
      setIsFetch(false);
    }
  };

  // const radioOnChange = (e) => {
  //   setMode(e.target.value);
  //   setSku(undefined);
  //   setSource(undefined);
  //   LocalStorageUtil.setItem(modeStorageKey, e.target.value);
  // };

  const data = [
    {
      title: i18n.t('global.sampleCode'),
      content: sku?.smCode,
    },
    {
      title: i18n.t('global.sampleName'),
      content: sku?.smName,
    },
    {
      title: i18n.t('global.barcode'),
      content: sku?.smBarcode,
    },
    {
      title: i18n.t('global.skuName'),
      content: sku?.smSkuName,
    },
    {
      title: i18n.t('global.brand'),
      content: sku?.smBrandName,
    },
    {
      title: i18n.t('global.priCategory'),
      content: sku?.smPriCategoryName,
    },
    {
      title: i18n.t('global.subCategory'),
      content: sku?.smSubCategoryName,
    },
    {
      title: i18n.t('global.year'),
      content: sku?.smYearName,
    },
    {
      title: i18n.t('global.gender'),
      content: sku?.smGender,
    },
    {
      title: i18n.t('global.color'),
      content: sku?.smColorName,
    },
    {
      title: i18n.t('global.size'),
      content: sku?.smSizeName,
    },
    {
      title: i18n.t('global.series'),
      content: sku?.smSeriesName,
    },
  ];

  // const options = [
  //   { label: 'EPC', value: 'epc' },
  //   { label: i18n.t('global.uniqueCode'), value: 'uniqueCode' },
  // ];
  return (
    <>
      <AppHeader />
      <div className="flex flex-col items-center justify-center">
        <div className="sticky top-24 z-40 flex w-full items-center justify-center bg-white">
          <div
            className={`sm:w-[100%] md:w-[90%] lg:w-[80%] xl:w-[70%] 2xl:w-[60%] ${
              sku && source ? 'mt-6 pb-6' : 'mt-[25vh]'
            }`}
          >
            {/* <Radio.Group className="mb-2" optionType="button" options={options} onChange={radioOnChange} value={mode} /> */}
            <Input.Search
              loading={loading}
              className={styles['input-search']}
              placeholder={i18n.t('global.pleaseEnterTagQuery')}
              autoFocus
              allowClear
              enterButton={i18n.t('global.search')}
              onSearch={onSearch}
            />
          </div>
        </div>
        {source && position && (
          <div className="mb-4 mt-6 flex w-full items-center justify-between px-[10px]">
            {source?.smEpc && <span className="text-xl font-semibold">{source.smEpc}</span>}
            {position && (
              <div>
                <span className="font-normal leading-5 text-lead-slate">{i18n.t('global.currentPosition')}</span>：
                <span className="font-semibold leading-5 text-lead-dark">
                  {position?.partnerName || i18n.t('global.unknown')}
                  {position?.statusDesc ? ` (${position.statusDesc})` : ''}
                </span>
              </div>
            )}
          </div>
        )}
        {isFetch && (
          <Row gutter={[20, 20]} className="w-full">
            <Col xxl={12} xl={12} lg={24} md={24} sm={24} xs={24}>
              <div className="border-soild rounded-lg border">
                <div className="flex h-9 items-center border-b bg-lead-light-bg pl-3 font-semibold leading-5 text-lead-dark">
                  {i18n.t('global.tagInfo')}
                </div>
                <div className="flex flex-col gap-y-3 px-3 pb-6 pt-3">
                  {sku ? (
                    data.map((item) => {
                      return (
                        <div key={item.title}>
                          <span className="font-normal text-lead-slate">{item.title}：</span>
                          <span>{item.content}</span>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex min-h-[408px] items-center justify-center">
                      <Empty />
                    </div>
                  )}
                </div>
              </div>
            </Col>
            <Col xxl={12} xl={12} lg={24} md={24} sm={24} xs={24}>
              <div className="border-soild rounded-lg border">
                <div className="flex h-9 items-center border-b bg-lead-light-bg pl-3 font-semibold leading-5 text-lead-dark">
                  {i18n.t('global.operationLogs')}
                </div>
                {source && source.data && source.data.length > 0 ? (
                  <div className="flex min-h-[408px] flex-col gap-y-3 px-3 pt-3">
                    <OperationLogs data={source.data} />
                  </div>
                ) : (
                  <div className="flex min-h-[408px] items-center justify-center">
                    <Empty />
                  </div>
                )}
              </div>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default SampleRfidSource;
