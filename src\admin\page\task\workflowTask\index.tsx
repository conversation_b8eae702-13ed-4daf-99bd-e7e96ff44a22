import { Select } from 'antd';
import * as WorkflowTaskApi from 'common/api/core/WorkflowTask';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import AppHeader from 'common/layout/AppHeader';
import { usePermission } from 'common/utils/Permission';
import i18n from 'common/utils/I18n';
import React, { useMemo, useRef, useState } from 'react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import WorkflowTaskLogDrawer from './components/WorkflowTaskLogDrawer';
import { workflowTaskStatusFilterOptions, workflowTaskTypeFilterOptions } from './utils';

const WorkflowTask: React.FC = () => {
  const [permission] = usePermission('A:TASK:WORKFLOW');
  const logPermission = permission.codes.includes('LOG');

  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [taskLogDrawerOpen, setTaskLogDrawerOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<any>(null);

  const fetchData = (params) => {
    if (params.status) {
      params.status = [params.status];
    }
    if (params.types) {
      params.types = [params.types];
    }
    return WorkflowTaskApi.List(params);
  };

  const openTaskLog = (record) => {
    setCurrentTask(record);
    setTaskLogDrawerOpen(true);
  };

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'types',
      label: i18n.t('global.type'),
      inputComponent: (
        <Select
          allowClear
          showSearch
          filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
          options={workflowTaskTypeFilterOptions}
        />
      ),
    },
    {
      name: 'status',
      label: i18n.t('global.status'),
      inputComponent: <Select allowClear options={workflowTaskStatusFilterOptions} />,
    },
  ];

  const tableColumns: PowerTableColumnsType = useMemo(() => {
    const columns: PowerTableColumnsType = [
      {
        title: i18n.t('global.orderCode'),
        dataIndex: 'orderCode',
        width: 200,
      },
      {
        title: i18n.t('global.type'),
        dataIndex: 'typeDesc',
        width: 150,
      },
      {
        title: i18n.t('global.status'),
        dataIndex: 'status',
        descDataIndex: 'statusDesc',
        valueType: 'status',
        statusConfig: {
          NONE: { status: 'Slate' },
          FAIL: { status: 'Red' },
          SUCCESS: { status: 'Green' },
        },
        width: 120,
      },
      {
        title: i18n.t('global.count'),
        dataIndex: 'count',
        width: 100,
        align: 'right',
      },
      {
        title: i18n.t('global.message'),
        dataIndex: 'message',
        minWidth: 200,
        auto: true,
      },
      {
        title: i18n.t('global.created'),
        dataIndex: 'created',
        sorter: true,
        width: 180,
      },
      {
        title: i18n.t('global.modified'),
        dataIndex: 'modified',
        sorter: true,
        width: 180,
      },
    ];

    if (logPermission) {
      columns.push({
        title: i18n.t('global.operation'),
        align: 'center',
        fixed: 'right',
        valueType: 'action',
        actionConfig: [
          {
            tooltip: i18n.t('global.log'),
            icon: <ListIcon className="fill-lead-dark" />,
            onClick: (record) => {
              openTaskLog(record);
            },
          },
        ],
      });
    }

    return columns;
  }, [logPermission]);

  return (
    <div>
      <AppHeader />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        searchFieldsConfig={searchFieldsConfig}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        // enableCache
        cacheKey="WORKFLOW_TASK"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <WorkflowTaskLogDrawer open={taskLogDrawerOpen} onClose={() => setTaskLogDrawerOpen(false)} task={currentTask} />
    </div>
  );
};

export default WorkflowTask;
