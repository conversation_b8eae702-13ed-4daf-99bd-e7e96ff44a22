import { DatePicker, Input, Select } from 'antd';
import * as Bsr<PERSON><PERSON>r<PERSON><PERSON> from 'common/api/shop/BsrOrder';
import * as SrO<PERSON>r<PERSON><PERSON> from 'common/api/shop/SrOrder';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Spin from 'common/components/Spin';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import ImportButton from 'common/components/Button/Import';
import { usePermission } from 'common/utils/Permission';
import ImportModal from './components/ImportModal';

const SrOrder: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [initialized, setInitialized] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:S:SR');
  const importPermission = permission.codes.includes('IMPORT');

  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([
    { code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER', defaultValue: 7 },
  ]);

  const navigate = useNavigate();

  let defaultPartnerValue: string[] = [];

  if (currentUser.type === 'SHOP') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    async (params: any, tabActiveKey: string) => {
      if (params.createDateRange) {
        params.startDateTime = params.createDateRange[0].startOf('day');
        params.endDateTime = params.createDateRange[1].endOf('day');
      }
      delete params.createDateRange;

      if (params.code) {
        params.code = `%${params.code}%`;
      }

      if (params.type) params.type = [params.type];

      const payload = {
        ...params,
      };

      switch (tabActiveKey) {
        case 'ALL':
          payload.status = ['NEW', 'FINISHED', 'CANCELED'];
          break;
        case 'NEW':
          payload.status = ['NEW'];
          break;
        case 'FINISHED':
          payload.status = ['FINISHED'];
          break;
        case 'CANCELLED':
          payload.status = ['CANCELED'];
          break;
        default:
          break;
      }
      let result: any;
      if (currentUser.mode === 'BINDING') {
        result = await BsrOrderApi.List(payload);
      } else {
        result = await SrOrderApi.List(payload);
      }
      return result;
    },
    [currentUser],
  );

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'orderCode',
      width: 200,
      fixed: 'left',
    },
    {
      title: i18n.t('global.sourceOrderCode'),
      width: 260,
      ellipsis: true,
      render: (text, record) => {
        return <AdditionCodeViewer codes={record.sourceCode} />;
      },
    },
    {
      title: i18n.t('global.serialNumber'),
      dataIndex: 'flowNumber',
      width: 200,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 200,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                FINISHED: 'green',
                CANCELED: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.shop'),
      dataIndex: 'warehouseName',
      valueType: 'text',
      tooltip: true,
      width: 200,
    },
    {
      title: i18n.t('global.payAmount'),
      dataIndex: 'payAmount',
      valueType: 'amount',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.payType'),
      codeDataIndex: 'payPlatform',
      nameDataIndex: 'payPlatformDesc',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.payScenario'),
      codeDataIndex: 'payScenes',
      nameDataIndex: 'payScenesDesc',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.retailType'),
      dataIndex: 'type',
      sorter: true,
      width: 200,
      render: (_, record) => <span>{record.typeDesc}</span>,
    },
    {
      title: i18n.t('global.retailQty'),
      dataIndex: 'srQty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.retailAmount'),
      dataIndex: 'srAmount',
      valueType: 'amount',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.returnQty'),
      dataIndex: 'srtQty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.returnAmount'),
      dataIndex: 'srtAmount',
      valueType: 'amount',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.payDate'),
      dataIndex: 'payDate',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'partnerIds',
      label: i18n.t('global.shop'),
      inputComponent: <PartnerSelect types={['SHOP']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'flowNumber',
      label: i18n.t('global.serialNumber'),
      inputComponent: <Input placeholder={i18n.t('global.serialNumber')} style={{ width: 280 }} />,
    },
    {
      name: 'type',
      label: i18n.t('global.retailType'),
      inputComponent: (
        <Select
          placeholder={i18n.t('global.retailType')}
          options={[
            { value: 'POS', label: i18n.t('global.posCheckout') },
            { value: 'SELF', label: i18n.t('global.selfServiceCheckout') },
          ]}
        />
      ),
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues = {
    createDateRange: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      {initialized ? (
        <>
          <AppHeader toolbar={importPermission && <ImportButton onClick={() => setImportModalVisible(true)} />} />
          <PowerTable
            innerRef={powerTableRef}
            rowKey="id"
            columns={columns}
            quickSearchFieldsConfig={quickSearchFieldsConfig}
            searchFieldsConfig={searchFieldsConfig}
            refreshBtnVisible
            searchPanelVisible={false}
            searchPanelCollapsible
            searchPanelInitialValues={searchPanelInitialValues}
            defaultPageSize={10}
            pagination
            autoLoad
            enableCache
            cacheKey="SR_ORDER"
            tableProps={{
              sticky: {
                offsetHeader: 96,
              },
              onRow: (record) => ({
                onClick: () => {
                  navigate(`/app/sr/${record.orderId}`);
                },
              }),
            }}
            defaultSorter={{ field: 'created', order: 'DESCEND' }}
            tabStatus={[
              {
                code: 'ALL',
                name: i18n.t('global.all'),
              },
              { code: 'NEW', name: i18n.t('global.newOrder') },
              {
                code: 'FINISHED',
                name: i18n.t('global.finished'),
              },
              {
                code: 'CANCELLED',
                name: i18n.t('global.cancelled'),
              },
            ]}
            request={fetchData}
          />
          <ImportModal
            modalProps={{
              open: importModalVisible,
              onCancel: () => {
                setImportModalVisible(false);
              },
              destroyOnClose: true,
              maskClosable: false,
              keyboard: false,
            }}
            onOk={() => {
              powerTableRef.current?.load();
            }}
          />
        </>
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default SrOrder;
