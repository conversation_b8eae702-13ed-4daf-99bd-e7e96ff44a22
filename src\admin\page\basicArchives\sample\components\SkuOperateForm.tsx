import { Col, Form, Input, InputNumber, Row, Switch } from 'antd';
import { FormProps } from 'antd/lib/form';
import * as SampleSkuApi from 'common/api/sample/SampleSku';
import * as SampleSkuImgApi from 'common/api/sample/SampleSkuImg';
import ImgUpload from 'common/components/ImgUpload/index';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useState } from 'react';
import SampleColorSelect from 'common/components/Select/SampleColorSelect';
import SampleSizeSelect from 'common/components/Select/SampleSizeSelect';
import { SampleContext, TSampleContext } from '../reducer';

import { ISkuItem } from '../data';

export interface OperationFormPropsRef {
  recover: () => void;
}
interface OperationFormProps extends FormProps {
  current?: Partial<ISkuItem> | undefined;
  statusVisible?: boolean;
  innerRef?: React.MutableRefObject<OperationFormPropsRef | undefined>;
}

const SkuOperateForm: React.FC<OperationFormProps> = (props) => {
  const { statusVisible, current, innerRef, ...formProps } = props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const { state } = useContext<TSampleContext>(SampleContext);
  const [defaultImgId, setDefaultImgId] = useState<string>();
  const [imgList, setImgList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Record<string, any>>({});

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const fetchSku = useCallback(async () => {
    if (!current) return;
    setLoading(true);
    try {
      const res: any = await SampleSkuApi.Get({ id: current.id });
      setCurrentRecord(res);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [current]);

  const handleDefaultImg = async (id) => {
    try {
      await SampleSkuImgApi.SetDefault({ id });
      setDefaultImgId(id);
    } catch (e) {}
  };

  const fetchImgList = useCallback(async () => {
    const res = await SampleSkuImgApi.List({
      smIds: [state.sampleRecord?.id],
    });
    if (res.data.length > 0 && currentRecord.id) {
      const isDefaultImgId = res.data.find((n) => n.smSkuId === currentRecord.id && n.defaultTag)?.id;
      if (!isDefaultImgId) {
        handleDefaultImg(res.data[0].id);
      }
    }
    setImgList(res.data.filter((n) => n.smSkuId === current?.id));
  }, [currentRecord, state.sampleRecord]);

  const handleSaveImg = async (source) => {
    try {
      const data: any = [
        {
          source,
          smId: state.sampleRecord?.id,
          smSkuId: current?.id,
        },
      ];
      await SampleSkuImgApi.Save({ data });
      fetchImgList();
    } catch (e) {}
  };

  const handleDeleteImg = async (id) => {
    try {
      await SampleSkuImgApi.Delete({ ids: [id] });
      fetchImgList();
    } catch (e) {}
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchSku();
      fetchImgList();
    },
  }));

  useEffect(() => {
    fetchImgList();
  }, [fetchImgList]);

  useEffect(() => {
    if (typeof currentRecord.disabled === 'boolean') {
      currentRecord.status = !currentRecord.disabled;
    }
    form.setFieldsValue(currentRecord);
  }, [currentRecord, form]);

  useEffect(() => {
    if (current) {
      fetchSku();
    }
  }, [current, fetchSku]);

  return (
    <Spin spinning={loading}>
      <Form form={form} {...formProps} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="smBarcode" label={i18n.t('global.barcode')} rules={canNotBeNullRules}>
              <Input readOnly={!!current} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.name')} name="smSkuName" rules={canNotBeNullRules}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.color')} name="smColorId">
              <SampleColorSelect grpid={state.sampleRecord ? state.sampleRecord.smColorGrpId : undefined} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.size')} name="smSizeId">
              <SampleSizeSelect grpid={state.sampleRecord ? state.sampleRecord.smSizeGrpId : undefined} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="costPrice" label={i18n.t('global.costPrice')} rules={canNotBeNullRules}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="retailPrice" label={i18n.t('global.smRetailPrice')} rules={canNotBeNullRules}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="tagPrice" label={i18n.t('global.barcodeTagPrice')} rules={canNotBeNullRules}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          {statusVisible && (
            <Col span={12}>
              <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
                <Switch />
              </Form.Item>
            </Col>
          )}
          {current && (
            <Col span={24}>
              <Form.Item label={i18n.t('global.skuImage')}>
                <ImgUpload
                  defaultImg
                  handleDefaultImg={handleDefaultImg}
                  handleSaveImg={handleSaveImg}
                  handleDeleteImg={handleDeleteImg}
                  value={imgList}
                  defaultImgId={defaultImgId}
                />
              </Form.Item>
            </Col>
          )}
        </Row>
      </Form>
    </Spin>
  );
};

export default SkuOperateForm;
