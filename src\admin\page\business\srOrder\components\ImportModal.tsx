import { ModalProps } from 'antd/es/modal';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useState } from 'react';
import * as BsrOrderApi from 'common/api/shop/BsrOrder';
import * as SrOrderApi from 'common/api/shop/SrOrder';

interface IImportModalProps {
  modalProps?: ModalProps;
  onOk?: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onOk } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item) => {
      switch (item.type) {
        case i18n.t('global.posCheckout'):
          item.type = 'POS';
          break;
        case i18n.t('global.selfServiceCheckout'):
          item.type = 'SELF';
          break;
        default:
          break;
      }
    });
    try {
      setProgressPercent(0);
      if (currentUser.mode === 'BINDING') {
        await BsrOrderApi.ResultBatchImport(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      } else {
        await SrOrderApi.ResultBatchImport(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      }

      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  const template: TemplateItem[] = [
    {
      dataIndex: 'epc',
      display: 'EPC',
      type: 'STRING',
    },
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'currentPrice',
      display: i18n.t('global.currentPrice'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'retailPrice',
      display: i18n.t('global.retailPrice'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'qty',
      display: i18n.t('global.count'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'flowNumber',
      display: i18n.t('global.serialNumber'),
      type: 'STRING',
    },
    {
      dataIndex: 'sourceCode',
      display: i18n.t('global.sourceCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.shopCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'warehouseCode',
      display: i18n.t('global.warehouseCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'otherCode',
      display: i18n.t('global.otherCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'srAmount',
      display: i18n.t('global.retailAmount'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'currentAmount',
      display: i18n.t('global.realAmount'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'srtAmount',
      display: i18n.t('global.returnAmount'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'type',
      display: i18n.t('global.retailType'),
      type: 'STRING',
      remark: [i18n.t('global.posCheckout'), i18n.t('global.selfServiceCheckout')].join(', '),
    },
    {
      dataIndex: 'makeDate',
      display: i18n.t('global.makeDate'),
      type: 'STRING',
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.remark'),
      type: 'STRING',
    },
  ];

  return (
    <Modal
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      destroyOnClose
      footer={false}
      width={1000}
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.shopRetailOrder')}
        template={template}
        onImport={onImport}
        // onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
