import * as Bwob<PERSON>rder<PERSON><PERSON> from 'common/api/warehouse/BwobOrder';
import * as WobOrderApi from 'common/api/warehouse/WobOrder';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface MatchRuleTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

export interface MatchRuleTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<MatchRuleTableInnerRef | undefined>;
}

const MatchRuleTable: React.FC<MatchRuleTableProps> = (props) => {
  const { innerRef } = props;
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId } = orderRecord;
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(
    async () => {
      let record;
      if (currentUser.mode === 'BINDING') {
        record = await BwobOrderApi.MatchRuleList({ id: orderId });
      } else {
        record = await WobOrderApi.MatchRuleList({ id: orderId });
      }
      return record;
    },
    // eslint-disable-next-line
    [orderId],
  );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'prodName',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.color'),
      valueType: 'codeName',
      dataIndex: 'colorCode',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      valueType: 'codeName',
      dataIndex: 'sizeCode',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.ratioValue'),
      dataIndex: 'ratio',
      sorter: true,
      valueType: 'number',
      width: 180,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      innerRef={powerTableRef}
      columns={columns}
      // sticky: true,
      refreshBtnVisible
      defaultSorter={{ field: 'barcode', order: 'DESCEND' }}
      request={fetchData}
      defaultPageSize={20}
      pagination
    />
  );
};

export default MatchRuleTable;
