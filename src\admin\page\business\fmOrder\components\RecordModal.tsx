import { Modal } from 'antd';
import i18n from 'common/utils/I18n';
import React from 'react';

import DataViewTable from './DataViewTable';

interface IRecordModal {
  visible: boolean;
  onCancel: () => void;
  fmOrderCode: string;
}

const RecordModal: React.FC<IRecordModal> = (props) => {
  const { visible, onCancel, fmOrderCode } = props;
  return (
    <Modal
      title={i18n.t('global.fadOrderRecord')}
      open={visible}
      width={window.innerWidth - 300}
      onCancel={onCancel}
      footer={false}
      destroyOnClose
    >
      <DataViewTable fmOrderCode={fmOrderCode} />
    </Modal>
  );
};

export default RecordModal;
