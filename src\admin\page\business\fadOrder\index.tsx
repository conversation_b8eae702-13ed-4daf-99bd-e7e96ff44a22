import { DatePicker, Input, Space, Spin } from 'antd';
import * as BfadO<PERSON>r<PERSON><PERSON> from 'common/api/factory/BfadOrder';
import * as FadOrderApi from 'common/api/factory/FadOrder';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import PartnerProSelect from 'common/components/Select/PartnerSelect';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import useTableSelection from 'common/hooks/useTableSelection';
import BusinessOrderBatchOperations from 'common/components/BusinessOrderBatchOperations';
import { usePermission } from 'common/utils/Permission';
import useConfirmOrCancel from 'common/hooks/useConfirmOrCancel';

const FadOrder: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [initialized, setInitialized] = useState<boolean>(false);
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const navigate = useNavigate();

  const [permission] = usePermission('A:F:FAD');
  const batchConfirmPermission = permission.codes.includes('BATCH_CONFIRM');

  const { selectedIds, rowSelection, clearAllSelection } = useTableSelection();
  const { executeConfirmOrCancel } = useConfirmOrCancel();

  let defaultPartnerValue: string[] = [];
  if (currentUser.type === 'FACTORY') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    async (params: Record<string, any>, tabActiveKey: string) => {
      params = { ...params, ...params.partnerValue };
      delete params.partnerValue;
      if (params.created) {
        params.createdStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      delete params.created;

      if (params.code) {
        params.code = `%${params.code}%`;
      }

      // if (tabActiveKey !== 'ALL') {
      //   params.status = [tabActiveKey];
      // } else {
      //   params.status = ['NEW', 'PASS', 'PART_PASS', 'NOT_PASS'];
      // }

      if (tabActiveKey === 'ALL') {
        params.status = ['NEW', 'PASS', 'PART_PASS', 'NOT_PASS'];
      } else if (tabActiveKey === 'AVAILABLE') {
        params.status = ['NEW', 'PART_PASS'];
      } else {
        params.status = [tabActiveKey];
      }
      try {
        let res: any;
        if (currentUser.mode === 'BINDING') {
          res = await BfadOrderApi.List({
            // orderByField: 'modified',
            // orderByMethod: 'DESCEND',
            ...params,
          });
        } else {
          res = await FadOrderApi.List({
            // orderByField: 'modified',
            // orderByMethod: 'DESCEND',
            ...params,
          });
        }
        return res;
      } catch (e) {
        return { data: [] };
      }
    },
    // eslint-disable-next-line
    [],
  );

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const batchConfirmOrCancelOnClick = (type: 'confirm' | 'cancel') => {
    const confirmApi = currentUser.mode === 'BINDING' ? BfadOrderApi.BatchConfirm : FadOrderApi.BatchConfirm;

    executeConfirmOrCancel(type, confirmApi, selectedIds, () => {
      clearAllSelection();
      powerTableRef.current?.load();
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.orderCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'fmOrderCode',
      label: i18n.t('global.fmOrderCode'),
      inputComponent: <Input />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'partnerIds',
      label: i18n.t('global.shipper'),
      inputComponent: <PartnerProSelect types={['FACTORY']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'toPartnerIds',
      label: i18n.t('global.receiver'),
      inputComponent: <PartnerProSelect types={['WAREHOUSE', 'SHOP']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      // render: (text, record) => <LinkWrapV5 to={`/app/fad/detail/${record.id}`}>{text}</LinkWrapV5>,
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.additionCode'),
      dataIndex: 'fmOrderCode',
      width: 250,
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      render: (text, record) => {
        const codes = record?.alias
          ?.filter((item) => item.source !== 'IC')
          ?.map((item) => ({
            label: item.sourceDesc,
            value: item.code,
          }));

        if (codes?.length > 0) {
          return <AdditionCodeViewer codes={codes} />;
        }
        return text;
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      valueType: 'text',
      width: 200,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      valueType: 'status',
      statusConfig: {
        NEW: { status: 'Red', desc: i18n.t('global.newOrder') },
        PART_PASS: { status: 'Blue', desc: i18n.t('global.partPass') },
        NOT_PASS: { status: 'Slate', desc: i18n.t('global.allNotPass') },
        PASS: { status: 'Green', desc: i18n.t('global.allPass') },
        // CANCEL: { status: 'Default', desc: cancelMsg },
      },
      width: 200,
    },
    {
      title: i18n.t('global.shipper'),
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.receiver'),
      valueType: 'text',
      dataIndex: 'toPartnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      valueType: 'text',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.bookingConfirmTime'),
      valueType: 'dateTime',
      dataIndex: 'created',
      sorter: true,
      width: 250,
    },
  ];

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues: any = {
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  // @ts-ignore
  // if (history.location.params?.fromFlag === 'dashboard') {
  //   delete searchPanelInitialValues.created;
  // }

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            <BusinessOrderBatchOperations
              selectedIds={selectedIds}
              clearAllSelection={clearAllSelection}
              confirmPermission={batchConfirmPermission}
              cancelPermission={false}
              btnOnClick={batchConfirmOrCancelOnClick}
            />
          </Space>
        }
      />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={searchPanelInitialValues}
          refreshBtnVisible
          searchPanelVisible={false}
          searchPanelCollapsible
          // @ts-ignore
          // tabDefaultActiveKey={history.location?.params?.status || 'ALL'}
          enableCache
          cacheKey="FAD_ORDER"
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            {
              code: 'NEW',
              name: i18n.t('global.newOrder'),
            },
            {
              code: 'PASS',
              name: i18n.t('global.allPass'),
            },
            {
              code: 'PART_PASS',
              name: i18n.t('global.partPass'),
            },
            {
              code: 'NOT_PASS',
              name: i18n.t('global.allNotPass'),
            },
            // {
            //   code: 'AVAILABLE',
            //   name: i18n.t('global.availableForSchedule'),
            // },
          ]}
          defaultPageSize={10}
          pagination
          autoLoad
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            rowSelection: {
              type: 'checkbox',
              ...rowSelection,
              getCheckboxProps: (record: Record<string, any>) => {
                return {
                  disabled: ['PASS', 'NOT_PASS'].includes(record.status),
                  name: record.code,
                };
              },
            },
            onRow: (record) => ({
              onClick: () => {
                navigate(`/app/fad/detail/${record.id}`);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default FadOrder;
