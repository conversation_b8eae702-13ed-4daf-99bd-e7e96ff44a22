import { DatePicker, Form, Input } from 'antd';
import { FormProps } from 'antd/lib/form';
import PartnerProSelect from 'common/components/Select/PartnerSelect';
import WarehouseSelect from 'common/components/Select/WarehouseSelect';
import TPartnerType from 'common/types/TPartnerType';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useState } from 'react';

interface IConfirmFormProps extends FormProps {
  onCancel?: () => void;
  orderRecord: { [key: string]: any };
  toPartnerId?: string;
}

const { TextArea } = Input;

const ConfirmForm: React.FC<IConfirmFormProps> = (props) => {
  const { onFinish, orderRecord, ...formProps } = props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const [receiverType, setReceiverType] = useState<TPartnerType>('WAREHOUSE');
  const [receiverId, setReceiverId] = useState<string>('');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  return (
    <Form
      form={form}
      name="prodConfirmForm"
      onFinish={(val) => {
        if (onFinish) {
          onFinish({ ...val });
        }
      }}
      {...formProps}
      initialValues={{
        toPartnerId: orderRecord.toPartnerId,
        toWarehouseId: orderRecord.toWarehouseId,
        actTime: moment(orderRecord.expTime?.split('~')[0], 'YYYY-MM-DD HH:mm'),
      }}
      layout="vertical"
    >
      <Form.Item name="toPartnerId" label={i18n.t('global.targetPlace')} rules={canNotBeNullRules}>
        <PartnerProSelect
          style={{ width: '60%' }}
          types={['WAREHOUSE', 'SHOP']}
          // sourceType="PERMISSION"
          onChange={(value, opts: any) => {
            setReceiverType(opts?.data.type);
            setReceiverId(opts?.data.id);
            form.setFieldValue('toWarehouseId', '');
          }}
        />
      </Form.Item>
      {receiverId && (
        <Form.Item label={i18n.t('global.receiverSubWarehouse')} name="toWarehouseId">
          {/* @ts-ignore */}
          <WarehouseSelect style={{ width: '50%' }} partnerId={receiverId} type={receiverType} />
        </Form.Item>
      )}
      <Form.Item name="actTime" label={i18n.t('global.relTime')}>
        <DatePicker style={{ width: '60%' }} showTime format="YYYY-MM-DD HH:mm" />
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <TextArea rows={3} style={{ width: '100%' }} />
      </Form.Item>
    </Form>
  );
};

export default ConfirmForm;
