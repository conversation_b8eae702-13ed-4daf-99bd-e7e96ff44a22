import { But<PERSON>, Tabs } from 'antd';
import * as SampleExtFieldApi from 'common/api/sample/SampleExtField';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import OperateDrawer from './components/OperateDrawer';

const SampleExtField: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [currentType, setCurrentType] = useState('SAMPLE');
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');
  const [current, setCurrent] = useState<Record<string, any> | undefined>(undefined);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [operationModalVisible, setOperationModalVisible] = useState(false);
  const { dispatch } = useContext<TGlobalContext>(GlobalContext);

  const editBtnOnClick = (record) => {
    setCurrent(record);
    setOperationModalVisible(true);
  };

  const fetchData = useCallback(
    async (params) => {
      const paylod: Record<string, any> = {
        enablePage: false,
        type: currentType,
      };
      if (typeof params.disabled === 'boolean') {
        paylod.disabled = params.disabled;
      }
      let result = { data: [] };
      try {
        result = await SampleExtFieldApi.List(paylod);
        dispatch({ type: 'setSampleExtFields', payload: result.data.filter((n: Record<string, any>) => !n.disabled) });
      } catch (e) {}
      return result;
    },
    [currentType, dispatch],
  );

  const addBtnOnClick = () => {
    setOperationModalVisible(true);
  };

  const handleSubmit = async (values) => {
    const id = current ? current.id : '';
    setConfirmLoading(true);
    try {
      if (id) {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await SampleExtFieldApi.Update({
          id: current?.id,
          ...values,
        });
      } else {
        values.code = values.code.trim();
        values.name = values.name.trim();
        await SampleExtFieldApi.Create({
          ...values,
          type: currentType,
        });
      }
      setOperationModalVisible(false);
      setCurrent(undefined);
      setConfirmLoading(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleExtFieldApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  useEffect(() => {
    if (currentType) {
      powerTableRef.current?.load();
    }
  }, [currentType]);

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.fieldType'),
      dataIndex: 'fieldTypeDesc',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.sortId'),
      dataIndex: 'sortId',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 150,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <>
      <Tabs
        defaultActiveKey="1"
        size="small"
        onChange={(key) => setCurrentType(key)}
        items={[
          {
            label: i18n.t('global.sample'),
            key: 'SAMPLE',
          },
        ]}
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        enableDisabledTrigger
        rightToolbar={[
          createPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={20}
        settingToolVisible
        enableCache
        cacheKey="SAMPLE_EXT_FIELD"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  editBtnOnClick(record);
                },
              })
            : undefined,
        }}
        request={fetchData}
      />
      <OperateDrawer
        visible={operationModalVisible}
        confirmLoading={confirmLoading}
        current={current}
        onSubmit={handleSubmit}
        onCancel={() => {
          setCurrent(undefined);
          setOperationModalVisible(false);
          powerTableRef.current?.load();
        }}
      />
    </>
  );
};

export default SampleExtField;
