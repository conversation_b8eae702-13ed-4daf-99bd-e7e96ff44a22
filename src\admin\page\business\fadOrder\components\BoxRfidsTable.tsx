import { Col, Input, Select, Space, Tooltip } from 'antd';
import * as BfadOrderApi from 'common/api/factory/BfadOrder';
import * as FadOrderApi from 'common/api/factory/FadOrder';
import QrScanLineIcon from 'common/assets/icons/icon-qr-scan-line.svg?react';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, { PowerTableColumnsType, SearchFieldsConfig } from 'common/components/PowerTable';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useCallback, useContext, useEffect, useState } from 'react';

const { Option } = Select;

interface IBoxRfidsTableProps {
  fmOrderId: string;
  onSelectBox: (boxIds: string[], rfidsCount: number) => void;
  powerTableRef: any;
  orderId: string;
  orderStatus: string;
}

const BoxRfidsTable: React.FC<IBoxRfidsTableProps> = (props) => {
  const { fmOrderId, powerTableRef, onSelectBox, orderId, orderStatus } = props;
  const [selectedRfidsCount, setSelectedRfidsCount] = useState<number>(0);
  const [boxesData, setBoxesData] = useState<any[]>([]);
  const [selectedFmOrderBoxIds, setSelectedFmOrderBoxIds] = useState<string[]>([]);
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewModalLoading, setBoxDetailsPreviewModalLoading] = useState<boolean>(false);
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [scanValue, setScanValue] = useState<string>();
  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const boxCodeOnClick = async (record) => {
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewModalLoading(true);
    setCurrentBoxCode(record.code);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BfadOrderApi.Rfids({
          fmOrderBoxId: record.fmOrderBoxId,
          fmOrderId,
          enablePage: false,
        });
      } else {
        result = await FadOrderApi.Rfids({
          fmOrderBoxId: record.fmOrderBoxId,
          fmOrderId,
          enablePage: false,
        });
      }
      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewModalLoading(false);
  };

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      let result = { data: [] };
      try {
        const payload = { ...params };
        if (payload.status) {
          payload.status = [payload.status];
        }
        if (currentUser.mode === 'BINDING') {
          result = await BfadOrderApi.Boxs({
            ...payload,
            fadOrderId: orderId,
          });
        } else {
          result = await FadOrderApi.Boxs({
            ...payload,
            fadOrderId: orderId,
          });
        }
      } catch (e) {}
      setBoxesData(result.data);
      return result;
    },
    // eslint-disable-next-line
    [orderId],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: (
        <Select placeholder={i18n.t('global.unlimited')} allowClear style={{ width: 160 }}>
          <Option value="NONE">{i18n.t('global.pendAppointment')}</Option>
          <Option value="PEND">{i18n.t('global.pend')}</Option>
          <Option value="PASS">{i18n.t('global.appointmentSuccess')}</Option>
          <Option value="NOT_PASS">{i18n.t('global.appointmentFail')}</Option>
        </Select>
      ),
    },
    {
      name: 'barcode',
      inputComponent: <Input placeholder={i18n.t('global.barcode')} />,
    },
    {
      name: 'prodCode',
      inputComponent: <Input placeholder={i18n.t('global.productCode')} />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.box'),
      dataIndex: 'code',
      valueType: 'text',
      width: 180,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCodes',
      width: 180,
      ellipsis: true,
      render(text) {
        const prodCodes = text?.split(',');
        if (prodCodes?.length > 0) {
          return (
            <Tooltip
              placement="topLeft"
              title={
                <div>
                  {prodCodes.map((item) => (
                    <div key={item}>{item}</div>
                  ))}
                </div>
              }
            >
              <span>{text}</span>
            </Tooltip>
          );
        }
        return text;
      },
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      valueType: 'status',
      statusConfig: {
        NONE: { status: 'Slate', desc: i18n.t('global.pendAppointment') },
        PEND: { status: 'Blue', desc: i18n.t('global.pend') },
        PASS: { status: 'Green', desc: i18n.t('global.appointmentSuccess') },
        NOT_PASS: { status: 'Red', desc: i18n.t('global.appointmentFail') },
      },
      minWidth: 120,
      auto: true,
    },
    // {
    //   title: i18n.t('global.inboundProgress'),
    //   width: 200,
    //   render: (text, record) => (
    //     <div className="flex gap-x-4">
    //       <Progress percent={(record.recQty / record.qty) * 100} strokeColor="#FD7557" showInfo={false} />
    //       <span>{Math.floor((record.recQty / record.qty) * 100)}%</span>
    //     </div>
    //   ),
    // },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    // {
    //   title: i18n.t('global.recQty'),
    //   dataIndex: 'recQty',
    //   sorter: true,
    //   valueType: 'number',
    //   width: 200,
    // },
  ];

  useEffect(() => {
    let count = 0;
    boxesData.forEach((item) => {
      if (selectedFmOrderBoxIds.indexOf(item.fmOrderBoxId) !== -1) {
        count += item.qty;
      }
    });
    setSelectedRfidsCount(count);
    // eslint-disable-next-line
  }, [selectedFmOrderBoxIds]);

  const onSelect = (record, selected) => {
    let selectedRecord: string[] = selectedFmOrderBoxIds;
    if (selected) {
      if (selectedRecord.indexOf(record.fmOrderBoxId) === -1) {
        setSelectedFmOrderBoxIds([...selectedRecord, record.fmOrderBoxId]);
      }
    } else {
      selectedRecord = selectedRecord.filter((item) => item !== record.fmOrderBoxId);
      setSelectedFmOrderBoxIds(selectedRecord);
    }
  };

  useEffect(() => {
    if (onSelectBox) {
      onSelectBox(selectedFmOrderBoxIds, selectedRfidsCount);
    }
    // eslint-disable-next-line
  }, [selectedRfidsCount]);

  const onSelectAll = (selected) => {
    if (selected) {
      setSelectedFmOrderBoxIds(boxesData.map((item) => item.fmOrderBoxId));
    } else {
      setSelectedFmOrderBoxIds([]);
    }
  };

  const scanInputOnPressEnter = (e) => {
    const str = e.currentTarget.value.trim();

    if (str) {
      const currentBox = boxesData.find((n) => n.code === str);
      if (currentBox) {
        setSelectedFmOrderBoxIds([...selectedFmOrderBoxIds, currentBox.fmOrderBoxId]);
        setScanValue('');
      } else {
        NoticeUtil.warn(i18n.t('global.boxCodeNotFound'));
      }
    }

    // let searchBoxId = '';
    // if (boxTableData.length > 0) {
    //   boxTableData.forEach((item) => {
    //     if (item.code === values) {
    //       searchBoxId = item.fmOrderBoxId;
    //     }
    //   });
    // }
    // if (searchBoxId) {
    //   if (selectedFmOrderBoxIds.indexOf(searchBoxId) === -1) {
    //     setSelectedFmOrderBoxIds([...selectedFmOrderBoxIds, searchBoxId]);
    //   }
    // }
  };

  const rowSelectionVal: any =
    orderStatus === 'NEW'
      ? {
          selectedRowKeys: selectedFmOrderBoxIds,
          type: 'checkbox',
          onSelect,
          onSelectAll,
        }
      : undefined;

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        defaultPageSize={10}
        request={fetchData}
        leftToolbar={
          orderStatus === 'NEW' && (
            <Space size="middle">
              <Col>
                {' '}
                {orderStatus === 'NEW' && (
                  <Input
                    value={scanValue}
                    placeholder={i18n.t('global.scanOrInputBoxCode')}
                    suffix={<QrScanLineIcon className="fill-lead-slate" />}
                    onPressEnter={scanInputOnPressEnter}
                    onChange={(e) => setScanValue(e.target.value)}
                  />
                )}
              </Col>
              <Col>
                {selectedFmOrderBoxIds.length > 0 && orderStatus === 'NEW' && (
                  <>
                    {i18n.t('global.selectCountTip', {
                      selectedBoxCount: selectedFmOrderBoxIds.length,
                      selectedRfidsCount,
                    })}
                  </>
                )}
              </Col>
            </Space>
          )
        }
        pagination
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={orderStatus === 'NEW' ? undefined : quickSearchFieldsConfig}
        refreshBtnVisible
        tableProps={{
          sticky: true,
          rowKey: 'fmOrderBoxId',
          rowSelection: rowSelectionVal,
          onRow: (record) => ({
            onClick: () => {
              boxCodeOnClick(record);
            },
          }),
        }}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewModalLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxRfidsTable;
