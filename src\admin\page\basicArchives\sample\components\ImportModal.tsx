import { ModalProps } from 'antd/es/modal';
import * as SampleSkuApi from 'common/api/sample/SampleSku';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useContext, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onFinish: (n) => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onFinish } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const { state } = useContext<TGlobalContext>(GlobalContext);

  const template: TemplateItem[] = [
    {
      dataIndex: 'smCode',
      display: i18n.t('global.productCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'smName',
      display: i18n.t('global.productName'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'smDisabled',
      display: i18n.t('global.sampleDisabled'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'disturbTag',
      display: i18n.t('global.disturbTag'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'source',
      display: i18n.t('global.source'),
      type: 'STRING',
    },
    {
      dataIndex: 'band',
      display: i18n.t('global.band'),
      type: 'STRING',
    },
    {
      dataIndex: 'smBrandCode',
      display: i18n.t('global.brandCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smBrandName',
      display: i18n.t('global.brandName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smColorGrpCode',
      display: i18n.t('global.colorGrpCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smColorGrpName',
      display: i18n.t('global.colorGrpName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smColorCode',
      display: i18n.t('global.colorCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smColorName',
      display: i18n.t('global.colorName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smGender',
      display: i18n.t('global.gender'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSizeGrpCode',
      display: i18n.t('global.sizeGrpCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSizeGrpName',
      display: i18n.t('global.sizeGrpName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSizeCode',
      display: i18n.t('global.sizeCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSizeName',
      display: i18n.t('global.sizeName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSeriesCode',
      display: i18n.t('global.seriesCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSeriesName',
      display: i18n.t('global.seriesName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smYearCode',
      display: i18n.t('global.yearCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smYearName',
      display: i18n.t('global.yearName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smPriCategoryCode',
      display: i18n.t('global.priCategoryCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smPriCategoryName',
      display: i18n.t('global.priCategoryName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSubCategoryCode',
      display: i18n.t('global.subCategoryCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smSubCategoryName',
      display: i18n.t('global.subCategoryName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smCostPrice',
      display: i18n.t('global.sampleCostPrice'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'smRetailPrice',
      display: i18n.t('global.sampleRetailPrice'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'smTagPrice',
      display: i18n.t('global.sampleTagPrice'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'listedDate',
      display: i18n.t('global.listedDate'),
      type: 'DATE',
      remark: 'YYYY-MM-DD',
    },
    {
      dataIndex: 'smRemark',
      display: i18n.t('global.sampleRemark'),
      type: 'STRING',
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.partnerCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'partnerType',
      display: i18n.t('global.partnerType'),
      type: 'STRING',
      remark: i18n.t('global.partnerTypeDesc'),
    },
    {
      dataIndex: 'smBarcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
    },
    {
      dataIndex: 'smBarcodeName',
      display: i18n.t('global.skuName'),
      type: 'STRING',
    },
    {
      dataIndex: 'smBarcodeDisabled',
      display: i18n.t('global.barcodeDisable'),
      type: 'BOOLEAN',
      // required: true,
    },
    {
      dataIndex: 'smBarcodeCostPrice',
      display: i18n.t('global.barcodeCostPrice'),
      type: 'NUMBER',
      // required: true,
    },
    {
      dataIndex: 'smBarcodeRetailPrice',
      display: i18n.t('global.smbarcodeRetailPrice'),
      type: 'NUMBER',
      // required: true,
    },
    {
      dataIndex: 'smBarcodeTagPrice',
      display: i18n.t('global.smbarcodeTagPrice'),
      type: 'NUMBER',
      // required: true,
    },
    {
      dataIndex: 'designer',
      display: i18n.t('global.designer'),
      type: 'STRING',
    },
  ];

  if (Array.isArray(state.sampleExtFields) && state.sampleExtFields.length > 0) {
    state.sampleExtFields.forEach((item: any) => {
      if (item.type === 'SAMPLE') {
        const newItem: any = {};
        newItem.dataIndex = `ext_${item.code}`;
        newItem.display = item.name;
        if (item.fieldType === 'TXT' || item.fieldType === 'URL' || item.fieldType === 'IMG') {
          newItem.type = 'STRING';
        } else if (item.fieldType === 'NUM' || item.fieldType === 'AMT') {
          newItem.type = 'NUMBER';
        }
        newItem.remark = `${item.remark || ''} ${i18n.t('global.ext')}`;
        template.push(newItem);
      }
    });
  }

  const onImport = async (data) => {
    setProgressStatus('active');
    const extFields = template.filter((n) => n.dataIndex.startsWith('ext_')).map((n) => n.dataIndex.slice(4));
    data.forEach((item) => {
      const ext: any = {};
      extFields.forEach((extItem) => {
        ext[extItem] = item[`ext_${extItem}`];
        delete item[`ext_${extItem}`];
      });
      item.smExt = ext;
      item.listedDate = moment(item.listedDate).format('YYYY-MM-DD HH:mm:ss');
    });
    try {
      setProgressPercent(0);
      const res: any = await SampleSkuApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          // @ts-ignore
          timeout: window.globalConfig.sampleImportTimeout.value,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      onFinish(res.id);
      setProgressPercent(100);
      setProgressStatus('success');
    } catch (e) {
      setProgressStatus('exception');
      throw e;
    }
  };

  return (
    <Modal width={960} title={i18n.t('global.import')} footer={false} destroyOnClose {...modalProps}>
      <Importer
        moduleName={i18n.t('global.sampleClothingFiles')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
