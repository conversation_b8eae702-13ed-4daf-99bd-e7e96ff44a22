import { DatePicker, Input, Radio, Space } from 'antd';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import * as StorageLocation<PERSON><PERSON> from 'common/api/core/StorageLocation';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import AliasViewer from 'common/components/AliasViewer';
import StorageLocationSelect from 'common/components/Select/StorageLocationSelect';
import { usePermission } from 'common/utils/Permission';
import ExportButton from 'common/components/Button/Export';
import { exportExcel } from 'common/utils/Excel';
import { templateItem } from 'common/components/Export';
import WarehouseSelect from 'common/components/Select/WarehouseSelect';
import * as Partner<PERSON><PERSON> from 'common/api/core/Partner';

const Location: React.FC = () => {
  const [permission] = usePermission('A:R:LOCATION');
  const exportPermission = permission.codes.includes('EXPORT');

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [radioActive, setRadioActive] = useState<'PROD' | 'SKU' | 'EPC'>('PROD');
  const [labelTotal, setLabelTotal] = useState<number>(0);
  const searchQueryType = useRef('PROD');
  const searchParams = useRef<Record<string, any>>({});
  const [partnerId, setPartnerId] = useState<string>();
  const [partnerType, setPartnerType] = useState<string>();
  const [warehouseId, setWarehouseId] = useState<string>();

  const typeOptions = useMemo(
    () => [
      {
        value: 'PROD',
        label: i18n.t('global.productCode'),
      },
      {
        value: 'SKU',
        label: i18n.t('global.barcode'),
      },
      {
        value: 'EPC',
        label: i18n.t('global.tag'),
      },
    ],
    [],
  );

  const radioOnChange = (e) => {
    setRadioActive(e.target.value);
    searchQueryType.current = e.target.value;
    powerTableRef.current?.reset(['SORTER']);
    powerTableRef.current?.load();
  };

  const fetchData = useCallback(async (params: Record<string, any>) => {
    if (params.partnerId) {
      setPartnerId(params.partnerId);
      PartnerApi.Get({ id: params.partnerId }).then((res: any) => {
        setPartnerType(res.type);
      });
    }
    if (params.warehouseId) setWarehouseId(params.warehouseId);

    if (params.startDateRange) {
      params.modifiedBegin = params.startDateRange[0].startOf('day');
      params.modifiedEnd = params.startDateRange[1].endOf('day');
    }
    delete params.startDateRange;
    if (params.epc) {
      params.epcList = [params.epc];
    }
    delete params.epc;

    params.queryDimension = searchQueryType.current;
    searchParams.current = params;
    const res: any = await StorageLocationApi.SumCount(params);
    if (res.data.length > 0) setLabelTotal(res.data[0].countQty);
    return res;
  }, []);

  const template: templateItem[] = useMemo(() => {
    let arr: templateItem[] = [];

    if (radioActive === 'PROD') {
      arr = [
        {
          display: i18n.t('global.warehouseShopCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseShopName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'storageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'storageLocationName',
        },
        {
          display: i18n.t('global.boxCode'),
          dataIndex: 'boxCode',
        },
        {
          display: i18n.t('global.productName'),
          dataIndex: 'prodName',
        },
        {
          display: i18n.t('global.productCode'),
          dataIndex: 'prodCode',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'brandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'brandName',
        },
        {
          display: i18n.t('global.colorGrpCode'),
          dataIndex: 'colorGrpCode',
        },
        {
          display: i18n.t('global.colorGrpName'),
          dataIndex: 'colorGrpName',
        },
        {
          display: i18n.t('global.sizeGrpCode'),
          dataIndex: 'sizeGrpCode',
        },
        {
          display: i18n.t('global.sizeGrpName'),
          dataIndex: 'sizeGrpName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'qty',
        },
      ];
    }

    if (radioActive === 'SKU') {
      arr = [
        {
          display: i18n.t('global.warehouseShopCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseShopName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'storageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'storageLocationName',
        },
        {
          display: i18n.t('global.boxCode'),
          dataIndex: 'boxCode',
        },
        {
          display: i18n.t('global.barcode'),
          dataIndex: 'barcode',
        },
        {
          display: i18n.t('global.productName'),
          dataIndex: 'prodName',
        },
        {
          display: i18n.t('global.productCode'),
          dataIndex: 'prodCode',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'brandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'brandName',
        },
        {
          display: i18n.t('global.colorCode'),
          dataIndex: 'colorCode',
        },
        {
          display: i18n.t('global.colorName'),
          dataIndex: 'colorName',
        },
        {
          display: i18n.t('global.sizeCode'),
          dataIndex: 'sizeCode',
        },
        {
          display: i18n.t('global.sizeName'),
          dataIndex: 'sizeName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'qty',
        },
      ];
    }

    if (radioActive === 'EPC') {
      arr = [
        {
          display: i18n.t('global.warehouseShopCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseShopName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'storageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'storageLocationName',
        },
        {
          display: i18n.t('global.boxCode'),
          dataIndex: 'boxCode',
        },
        {
          display: 'EPC',
          dataIndex: 'epc',
        },
        {
          display: i18n.t('global.barcode'),
          dataIndex: 'barcode',
        },
        {
          display: i18n.t('global.productName'),
          dataIndex: 'prodName',
        },
        {
          display: i18n.t('global.productCode'),
          dataIndex: 'prodCode',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'brandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'brandName',
        },
        {
          display: i18n.t('global.colorCode'),
          dataIndex: 'colorCode',
        },
        {
          display: i18n.t('global.colorName'),
          dataIndex: 'colorName',
        },
        {
          display: i18n.t('global.sizeCode'),
          dataIndex: 'sizeCode',
        },
        {
          display: i18n.t('global.sizeName'),
          dataIndex: 'sizeName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'qty',
        },
      ];
    }

    return arr;
  }, [radioActive]);

  const columns: PowerTableColumnsType = useMemo(() => {
    let arr: PowerTableColumnsType = [];

    if (radioActive === 'PROD') {
      arr = [
        {
          title: i18n.t('global.warehouseShop'),
          width: 200,
          dataIndex: 'partnerName',
          valueType: 'codeName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'storageLocationName',
          codeDataIndex: 'storageLocationCode',
          nameDataIndex: 'storageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.boxCode'),
          width: 200,
          dataIndex: 'boxCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.productName'),
          width: 200,
          dataIndex: 'prodName',
          valueType: 'text',
          tooltip: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.productCode'),
          width: 200,
          dataIndex: 'prodCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'text',
          dataIndex: 'brandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.colorGroup'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'colorGrpCode',
          nameDataIndex: 'colorGrpName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.sizeGroup'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'sizeGrpCode',
          nameDataIndex: 'sizeGrpName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'qty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    if (radioActive === 'SKU') {
      arr = [
        {
          title: i18n.t('global.warehouseShop'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'partnerName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'storageLocationName',
          codeDataIndex: 'storageLocationCode',
          nameDataIndex: 'storageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.boxCode'),
          width: 200,
          dataIndex: 'boxCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.barcode'),
          width: 200,
          dataIndex: 'barcode',
          valueType: 'text',
          sorter: true,
        },
        {
          title: i18n.t('global.productName'),
          width: 200,
          dataIndex: 'prodName',
          valueType: 'text',
          tooltip: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.productCode'),
          width: 200,
          dataIndex: 'prodCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'brandCode',
          dataIndex: 'brandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.color'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'colorCode',
          dataIndex: 'colorName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.size'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'sizeCode',
          dataIndex: 'sizeName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.alias'),
          dataIndex: 'alias',
          align: 'center',
          width: 100,
          render: (value, record) => <AliasViewer aliasData={record.alias} />,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'qty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    if (radioActive === 'EPC') {
      arr = [
        {
          title: i18n.t('global.warehouseShop'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'partnerName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'storageLocationName',
          codeDataIndex: 'storageLocationCode',
          nameDataIndex: 'storageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.boxCode'),
          width: 200,
          dataIndex: 'boxCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: 'EPC',
          width: 300,
          dataIndex: 'epc',
          valueType: 'text',
          tooltip: true,
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.productName'),
          width: 200,
          dataIndex: 'prodName',
          valueType: 'text',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.productCode'),
          width: 200,
          dataIndex: 'prodCode',
          valueType: 'text',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'brandCode',
          dataIndex: 'brandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.color'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'colorCode',
          dataIndex: 'colorName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.size'),
          width: 200,
          valueType: 'text',
          // codeDataIndex: 'sizeCode',
          dataIndex: 'sizeName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.barcode'),
          width: 200,
          dataIndex: 'barcode',
          valueType: 'text',
          sorter: true,
        },
        {
          title: i18n.t('global.alias'),
          dataIndex: 'alias',
          align: 'center',
          width: 100,
          render: (value, record) => <AliasViewer aliasData={record.alias} />,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'qty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    return arr;
  }, [radioActive]);

  const onPartnerChange = (_, option: any) => {
    setPartnerId(option.value);
    setPartnerType(option.data.type);
  };

  const onPartnerClear = () => {
    setPartnerId('');
    setPartnerType('');
    setWarehouseId('');

    powerTableRef.current?.setSearchPanelFieldsValue({
      partnerId: null,
      warehouseId: null,
      storageLocationId: null,
    });
  };

  const onWarehouseChange = (_, option: any) => {
    setWarehouseId(option.value);
  };

  const onWarehouseClear = () => {
    setWarehouseId('');
    powerTableRef.current?.setSearchPanelFieldsValue({
      warehouseId: null,
      storageLocationId: null,
    });
  };

  const searchFieldsConfig: SearchFieldsConfig = useMemo(() => {
    const arr: SearchFieldsConfig = [
      {
        name: 'partnerId',
        label: i18n.t('global.warehouseShop'),
        inputComponent: (
          <PartnerSelect
            types={['WAREHOUSE', 'SHOP']}
            sourceType="PERMISSION"
            className="w-60"
            onChange={onPartnerChange}
            onClear={onPartnerClear}
          />
        ),
      },
      {
        name: 'warehouseId',
        label: i18n.t('global.subWarehouse'),
        inputComponent: (
          <WarehouseSelect
            type={partnerType as 'WAREHOUSE' | 'SHOP'}
            partnerId={partnerId}
            className="w-60"
            onChange={onWarehouseChange}
            onClear={onWarehouseClear}
          />
        ),
      },
      {
        name: 'storageLocationId',
        label: i18n.t('global.storageLocation'),
        inputComponent: <StorageLocationSelect partnerId={partnerId} warehouseId={warehouseId} />,
      },
      {
        name: 'boxCode',
        label: i18n.t('global.boxCode'),
        inputComponent: <Input />,
      },
      {
        name: 'prodCode',
        label: i18n.t('global.productCode'),
        inputComponent: <Input />,
      },
      {
        name: 'barcode',
        label: i18n.t('global.barcode'),
        inputComponent: <Input />,
      },
      {
        name: 'startDateRange',
        label: i18n.t('global.date'),
        inputComponent: <DatePicker.RangePicker />,
      },
    ];

    if (radioActive === 'EPC') {
      arr.splice(6, 0, {
        name: 'epc',
        label: 'EPC',
        inputComponent: <Input />,
      });
    }
    return arr;
  }, [partnerId, partnerType, radioActive, warehouseId]);

  const exportInfo = useCallback(async () => {
    const payload: Record<string, any> = {
      ...searchParams.current,
      enablePage: false,
    };
    delete payload.currentPage;
    delete payload.pageSize;
    const res = await StorageLocationApi.SumCount(payload);
    exportExcel(
      template,
      res.data,
      `${i18n.t('global.inventoryAtLocations')}-${typeOptions.find((item) => item.value === radioActive)?.label}`,
    );
  }, [radioActive, template, typeOptions]);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {exportPermission && (
              <ExportButton
                onClick={() => {
                  exportInfo();
                }}
              />
            )}

            <Radio.Group
              options={typeOptions}
              onChange={radioOnChange}
              value={radioActive}
              optionType="button"
              buttonStyle="solid"
            />
          </Space>
        }
      />
      <PowerTable
        autoLoad
        initialized
        innerRef={powerTableRef}
        rowKey="index"
        columns={columns}
        searchFieldsConfig={searchFieldsConfig}
        searchPanelVisible
        pagination
        defaultPageSize={10}
        enableCache
        // cacheKey="REPORT_LOCATION"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        request={fetchData}
        paginationExtraContent={
          <div className="flex w-full items-center">
            {i18n.t('global.totalCount')}：{labelTotal}
          </div>
        }
      />
    </div>
  );
};

export default Location;
