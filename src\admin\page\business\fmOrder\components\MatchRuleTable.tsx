import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useImperativeHandle, useRef } from 'react';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import * as Maching<PERSON>ode<PERSON><PERSON> from 'common/api/core/MachingCode';

export interface MatchRuleTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

export interface MatchRuleTableProps {
  fmOrderId: string;
  innerRef?: React.MutableRefObject<MatchRuleTableInnerRef | undefined>;
}

const MatchRuleTable: React.FC<MatchRuleTableProps> = (props) => {
  const { fmOrderId, innerRef } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  // 获取单据明细，筛选出尺码ID
  const fetchOrderLines = useCallback(async () => {
    const payload = { fmOrderId, enablePage: false };
    let result: string[] = [];
    try {
      let resp: any;
      if (currentUser.mode === 'BINDING') {
        resp = await BfmOrderApi.Lines(payload);
      } else {
        resp = await FmOrderApi.Lines(payload);
      }
      if (resp?.data && resp.data.length > 0) {
        result = resp.data.filter((n) => n.sizeId).map((n) => n.sizeId);
        result = [...new Set(result)];
      }
    } catch {}
    return result;
  }, [currentUser, fmOrderId]);

  const fetchData = useCallback(async () => {
    let result: any = { data: [] };
    try {
      if (currentUser.mode === 'BINDING') {
        result = await BfmOrderApi.MatchRuleList({ id: fmOrderId });
      } else {
        result = await FmOrderApi.MatchRuleList({ id: fmOrderId });
      }
      // 如果单据找不到配比，则使用单据明细中尺码Ids去配比档案中查询对应的配比
      if (result?.data && result.data.length === 0) {
        const sizeIds = await fetchOrderLines();
        if (sizeIds.length > 0) {
          result = await MachingCodeApi.List({ enablePage: false, sizeId: sizeIds });
        }
      }
    } catch {}
    return result;
  }, [currentUser, fmOrderId, fetchOrderLines]);

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'prodName',
      sorter: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.color'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.type'),
      width: 130,
      valueType: 'text',
      dataIndex: 'type',
      ellipsis: true,
    },
    {
      title: i18n.t('global.ratioValue'),
      width: 200,
      valueType: 'text',
      dataIndex: 'ratio',
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  useImperativeHandle(innerRef, () => ({
    load: () => powerTableRef.current?.load(),
  }));

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="MACHING_CODE"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
    </div>
  );
};

export default MatchRuleTable;
