import AddDrawer from 'admin/page/basicArchives/prodAttributes/specGrp/components/AddDrawer';
import DetailDrawer from 'admin/page/basicArchives/prodAttributes/specGrp/components/DetailDrawer';
import { Button, Input } from 'antd';
import * as SpecGrpApi from 'common/api/core/SpecGrp';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import DisableSelect from 'common/components/Select/DisableSelect';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import { ISpecGrpItem } from './data';

const SpecGrp: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addModalConfirmLoading, setAddModalConfirmLoading] = useState(false);
  const [detailDrawerConfirmLoading, setDetailDrawerConfirmLoading] = useState(false);
  const [detailDrawerCurrent, setDetailDrawerCurrent] = useState<ISpecGrpItem | undefined>();
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);

  const [permission] = usePermission('A:BASE:ATTRIBUTE:SPEC_GRP');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchData = useCallback(async (params) => {
    return SpecGrpApi.List(params);
  }, []);

  const handleAddModalSubmit = async (values: ISpecGrpItem) => {
    setAddModalConfirmLoading(true);
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      await SpecGrpApi.Create(values);
      setAddModalConfirmLoading(false);
      setAddModalVisible(false);
      powerTableRef.current?.load();
      NoticeUtil.success();
    } catch (e) {
      setAddModalConfirmLoading(false);
    }
  };

  const handleAddModalCancel = () => {
    setAddModalVisible(false);
  };

  const handleEditModalSubmit = async (values: ISpecGrpItem) => {
    setDetailDrawerConfirmLoading(true);
    try {
      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
        delete values.status;
      }
      await SpecGrpApi.Update({
        ...values,
        id: detailDrawerCurrent?.id,
      });
      setDetailDrawerConfirmLoading(false);
      setDetailDrawerVisible(false);
      powerTableRef.current?.load();
      NoticeUtil.success();
    } catch (e) {
      setDetailDrawerConfirmLoading(false);
    }
  };

  const handleEditModalCancel = () => {
    setDetailDrawerCurrent(undefined);
    setDetailDrawerVisible(false);
    powerTableRef.current?.load();
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'disabled',
      label: i18n.t('global.status'),
      inputComponent: <DisableSelect />,
    },
  ];

  const addBtnOnClick = () => {
    setAddModalVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SpecGrpApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.sort'),
      dataIndex: 'sortId',
      valueType: 'number',
      sorter: true,
      width: 100,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        rightToolbar={[
          createPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="SPEC_GRP"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  setDetailDrawerCurrent(record);
                  setDetailDrawerVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <AddDrawer
        visible={addModalVisible}
        confirmLoading={addModalConfirmLoading}
        onSubmit={handleAddModalSubmit}
        onCancel={handleAddModalCancel}
      />
      <DetailDrawer
        visible={detailDrawerVisible}
        specId={detailDrawerCurrent ? detailDrawerCurrent.id : ''}
        confirmLoading={detailDrawerConfirmLoading}
        onSubmit={handleEditModalSubmit}
        onCancel={handleEditModalCancel}
      />
    </div>
  );
};

export default SpecGrp;
