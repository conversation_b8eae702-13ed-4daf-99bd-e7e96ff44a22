import { ModalProps } from 'antd/es/modal';
import * as SkuApi from 'common/api/core/Sku';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onFinish: (n) => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onFinish } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const { state } = useContext<TGlobalContext>(GlobalContext);

  const template: TemplateItem[] = [
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
    },
    {
      dataIndex: 'barcodeName',
      display: i18n.t('global.skuName'),
      type: 'STRING',
    },
    {
      dataIndex: 'skuImgUrl',
      display: i18n.t('global.barcodeUrl'),
      type: 'STRING',
    },
    {
      dataIndex: 'skuDefaultUrl',
      display: i18n.t('global.barcodeDefaultUrl'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'barcodeDisabled',
      display: i18n.t('global.barcodeDisable'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'prodCode',
      display: i18n.t('global.productCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'prodName',
      display: i18n.t('global.productName'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'prodImgUrl',
      display: i18n.t('global.productUrl'),
      type: 'STRING',
    },
    {
      dataIndex: 'prodDefaultUrl',
      display: i18n.t('global.productDefaultUrl'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'prodRemark',
      display: i18n.t('global.prodRemark'),
      type: 'STRING',
    },
    {
      dataIndex: 'disturbTag',
      display: i18n.t('global.disturbTag'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'rfidTag',
      display: i18n.t('global.isEnableRfidTag'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'prodDisabled',
      display: i18n.t('global.prodDisable'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'barcodeRetailPrice',
      display: i18n.t('global.barcodeRetailPrice'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'barcodeTagPrice',
      display: i18n.t('global.barcodeTagPrice'),
      type: 'NUMBER',
      required: true,
    },
    {
      dataIndex: 'brandCode',
      display: i18n.t('global.brandCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'brandName',
      display: i18n.t('global.brandName'),
      type: 'STRING',
    },
    {
      dataIndex: 'disableColor',
      display: i18n.t('global.colorDisable'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'colorGrpCode',
      display: i18n.t('global.colorGrpCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'colorGrpName',
      display: i18n.t('global.colorGrpName'),
      type: 'STRING',
    },
    {
      dataIndex: 'colorCode',
      display: i18n.t('global.colorCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'colorName',
      display: i18n.t('global.colorName'),
      type: 'STRING',
    },
    {
      dataIndex: 'disableSize',
      display: i18n.t('global.sizeDisable'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'sizeGrpCode',
      display: i18n.t('global.sizeGrpCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'sizeGrpName',
      display: i18n.t('global.sizeGrpName'),
      type: 'STRING',
    },
    {
      dataIndex: 'sizeCode',
      display: i18n.t('global.sizeCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'sizeName',
      display: i18n.t('global.sizeName'),
      type: 'STRING',
    },
    {
      dataIndex: 'disableSpec',
      display: i18n.t('global.specDisable'),
      type: 'BOOLEAN',
    },
    {
      dataIndex: 'specGrpCode',
      display: i18n.t('global.specGrpCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'specGrpName',
      display: i18n.t('global.specGrpName'),
      type: 'STRING',
    },
    {
      dataIndex: 'specCode',
      display: i18n.t('global.specCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'specName',
      display: i18n.t('global.specName'),
      type: 'STRING',
    },
    {
      dataIndex: 'genderCode',
      display: i18n.t('global.genderCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'genderName',
      display: i18n.t('global.genderName'),
      type: 'STRING',
    },
    {
      dataIndex: 'weight',
      display: i18n.t('global.weight'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'priCategoryCode',
      display: i18n.t('global.priCategoryCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'priCategoryName',
      display: i18n.t('global.priCategoryName'),
      type: 'STRING',
    },
    {
      dataIndex: 'subCategoryCode',
      display: i18n.t('global.subCategoryCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'subCategoryName',
      display: i18n.t('global.subCategoryName'),
      type: 'STRING',
    },
    {
      dataIndex: 'yearCode',
      display: i18n.t('global.yearCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'yearName',
      display: i18n.t('global.yearName'),
      type: 'STRING',
    },
    {
      dataIndex: 'gbCode',
      type: 'STRING',
      display: i18n.t('global.gbCode'),
    },
    {
      dataIndex: 'innerCode',
      type: 'STRING',
      display: i18n.t('global.innerCode'),
    },
    {
      dataIndex: 'otherCode',
      type: 'STRING',
      display: i18n.t('global.otherCode'),
    },
  ];

  if (Array.isArray(state.extFields) && state.extFields.length > 0) {
    state.extFields.forEach((item: any) => {
      if (item.type === 'PROD') {
        const newItem: any = {};
        newItem.dataIndex = `ext_${item.code}`;
        newItem.display = item.name;
        if (item.fieldType === 'TXT' || item.fieldType === 'URL' || item.fieldType === 'IMG') {
          newItem.type = 'STRING';
        } else if (item.fieldType === 'NUM' || item.fieldType === 'AMT') {
          newItem.type = 'NUMBER';
        }
        newItem.remark = `${item.remark || ''} ${i18n.t('global.ext')}`;
        template.push(newItem);
      }
    });
  }

  const onImport = async (data) => {
    setProgressStatus('active');
    const extFields = template.filter((n) => n.dataIndex.startsWith('ext_')).map((n) => n.dataIndex.slice(4));
    data.forEach((item) => {
      const ext: any = {};
      extFields.forEach((extItem) => {
        ext[extItem] = item[`ext_${extItem}`];
        delete item[`ext_${extItem}`];
      });
      item.ext = ext;
    });
    try {
      setProgressPercent(0);
      const res: any = await SkuApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          // @ts-ignore
          timeout: window.globalConfig.prodImportTimeout.value,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      onFinish(res.id);
      setProgressPercent(100);
      setProgressStatus('success');
    } catch (e) {
      setProgressStatus('exception');
      throw e;
    }
  };

  return (
    <Modal width={960} title={i18n.t('global.import')} footer={false} destroyOnClose {...modalProps}>
      <Importer
        moduleName={i18n.t('global.prodFile')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
