import { Button, DatePicker, Dropdown, Input, Menu } from 'antd';
import * as SampleStorageLocationApi from 'common/api/sample/sampleStorageLocation';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import moment from 'moment';
import useSetting from 'common/hooks/useSetting';
import StorageLocationDrawer from './StorageLocationDrawer';
import BatchLocationDrawer from './BatchLocationDrawer';

export interface SampleStorageLocationTableProps {
  warehouseId?: string;
  partnerId?: string;
}

type recordType = {
  code: string;
  name: string;
  disable?: boolean;
  [key: string]: any;
};

const SampleStorageLocationTable: React.FC<SampleStorageLocationTableProps> = (props) => {
  const { warehouseId, partnerId } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const recordItem = useRef<recordType>();
  const [storageLocationOpen, setStorageLocationOpen] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [batchLocationDrawerOpen, setBatchLocationDrawerOpen] = useState<boolean>(false);
  const [batchLocationDrawerLoading, setBatchLocationDrawerLoading] = useState<boolean>(false);

  const [permission] = usePermission('A:BASE:PARTNER');
  const createLPermission = permission.codes.includes('CREATE_LOCATION');
  const createBatchLPermission = permission.codes.includes('CREATE_BATCH_LOCATION');
  const editLPermission = permission.codes.includes('EDIT_LOCATION');
  const deleteLPermission = permission.codes.includes('DELETE_LOCATION');

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const fetchData = useCallback(
    async (payload: { [key: string]: any } = {}) => {
      const params: any = {
        partnerIds: [partnerId],
        warehouseId,
        ...payload,
      };
      return SampleStorageLocationApi.List(params);
    },
    [partnerId, warehouseId],
  );

  const handleCancel = () => {
    setStorageLocationOpen(false);
  };

  const deleteBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleStorageLocationApi.Delete({ id: record.id });
          NoticeUtil.success();
          setStorageLocationOpen(false);
          recordItem.current = undefined;
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input placeholder={i18n.t('global.name')} style={{ width: 280 }} />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      sorter: true,
      auto: true,
      minWidth: 200,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      valueType: 'text',
      sorter: true,
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.rowIndex'),
      dataIndex: 'rowIndex',
      valueType: 'text',
      sorter: true,
      width: 150,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.columnIndex'),
      dataIndex: 'columnIndex',
      valueType: 'text',
      sorter: true,
      width: 150,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.index'),
      dataIndex: 'no',
      valueType: 'text',
      sorter: true,
      width: 150,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deleteLPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  const searchPanelInitialValues = {
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
  };

  useEffect(() => {
    if (warehouseId && partnerId) powerTableRef.current?.load();
  }, [warehouseId, partnerId]);

  const storageLocationSubmit = async (values: any) => {
    setConfirmLoading(true);
    try {
      if (!recordItem.current) {
        await SampleStorageLocationApi.Create({
          warehouseId,
          ...values,
        });
      } else {
        await SampleStorageLocationApi.Update({
          ...values,
          id: recordItem.current.id,
          disable: recordItem.current.disable,
        });
      }
      setConfirmLoading(false);
      setStorageLocationOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const addBtnOnClick = () => {
    recordItem.current = undefined;
    setStorageLocationOpen(true);
  };
  const batchAddBtnOnClick = () => {
    setBatchLocationDrawerOpen(true);
  };

  const batchLocationHandleSubmit = async (values) => {
    setBatchLocationDrawerLoading(true);

    try {
      await SampleStorageLocationApi.BatchGenerate({
        warehouseId,
        ...values,
      });

      setBatchLocationDrawerLoading(false);
      setBatchLocationDrawerOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setBatchLocationDrawerLoading(false);
    }
  };

  const batchLocationHandleCancel = () => {
    setBatchLocationDrawerOpen(false);
  };

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        searchPanelInitialValues={searchPanelInitialValues}
        enableDisabledTrigger
        rightToolbar={[
          createLPermission && !createBatchLPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
          !createLPermission && createBatchLPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={batchAddBtnOnClick}>
              {i18n.t('global.batchAdd')}
            </Button>
          ),
          createLPermission && createBatchLPermission && (
            <Dropdown.Button
              type="primary"
              overlay={
                <Menu
                  items={[
                    {
                      label: i18n.t('global.batchAdd'),
                      key: 'BATCH',
                    },
                  ]}
                  onClick={batchAddBtnOnClick}
                />
              }
              onClick={addBtnOnClick}
            >
              <AddFillIcon className="fill-white" />
              {i18n.t('global.new')}
            </Dropdown.Button>
          ),
        ]}
        defaultPageSize={20}
        settingToolVisible
        pagination
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editLPermission
            ? (record) => ({
                onClick: () => {
                  recordItem.current = record;
                  setStorageLocationOpen(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <StorageLocationDrawer
        type="SAMPLE"
        current={recordItem.current}
        open={storageLocationOpen}
        confirmLoading={confirmLoading}
        onCancel={handleCancel}
        onSubmit={storageLocationSubmit}
        onDelete={deleteBtnOnClick}
      />

      <BatchLocationDrawer
        open={batchLocationDrawerOpen}
        loading={batchLocationDrawerLoading}
        onSubmit={batchLocationHandleSubmit}
        onCancel={batchLocationHandleCancel}
      />
    </>
  );
};

export default SampleStorageLocationTable;
