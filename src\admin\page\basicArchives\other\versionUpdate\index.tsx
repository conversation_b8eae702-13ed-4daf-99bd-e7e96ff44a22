/**
 * Create by codegen at 2022-07-26
 * 应用版本管理
 */
import AddDrawer from 'admin/page/basicArchives/other/versionUpdate/components/AddDrawer';
import DetailDrawer from 'admin/page/basicArchives/other/versionUpdate/components/DetailDrawer';
import { Button, Tooltip } from 'antd';
import * as VersionUpdateApi from 'common/api/core/VersionUpdate';
import * as FileApi from 'common/api/file/File';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import CheckFillIcon from 'common/assets/icons/icon-check-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import DownloadCloudLineIcon from 'common/assets/icons/icon-download-cloud-2-line.svg?react';
import ExternalLinkLineIcon from 'common/assets/icons/icon-external-link-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import { cachePrefix } from 'common/components/PowerTable/config';
import Spin from 'common/components/Spin';
import useDict from 'common/hooks/useDict';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import { saveAs } from 'file-saver';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import ReleaseModal from './components/ReleaseModal';

const cacheKey = 'VERSION_UPDATE';

const VersionUpdate: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [current, setCurrent] = useState<Record<string, any>>({});
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [adding, setAdding] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [releaseVisible, setReleaseVisible] = useState(false);
  const [releaseIng, setReleaseIng] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const softwareUpdateType = useDict(['SOFTWARE_UPDATE_TYPE']);
  const tabDefaultActiveKey = useRef<string>();
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [permission] = usePermission('A:BASE:OTHER:O:VU');
  const addPermission = permission.codes.includes('CREATE');
  const relPermission = permission.codes.includes('REL');
  const dowPermission = permission.codes.includes('DOW');
  const editPermission = permission.codes.includes('EDIT');
  const delPermission = permission.codes.includes('DEL');

  const fetchData = useCallback(
    async (params: Record<string, any>, actualTabKey: string) =>
      VersionUpdateApi.List({ client: actualTabKey, ...params }),
    [],
  );

  const addBtnOnClick = () => {
    setAddModalVisible(true);
  };

  const addModalOnOk = async (values: Record<string, any>) => {
    setAdding(true);
    try {
      await VersionUpdateApi.Create(values);
      setAddModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAdding(false);
  };

  const addModalOnCancel = () => {
    setAddModalVisible(false);
  };

  const editModalOnOk = async (values: Record<string, any>) => {
    values.code = current.code;
    setUpdating(true);
    try {
      await VersionUpdateApi.Update({ ...current, ...values });
      setEditModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setUpdating(false);
  };

  const editModalOnCancel = () => {
    setEditModalVisible(false);
    powerTableRef.current?.load();
  };

  const editBtnOnClick = (record: Record<string, any>) => {
    setCurrent(record);
    setEditModalVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.releaseVersion} - ${record.client}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await VersionUpdateApi.Delete({ ids: [record.id] });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const releaseBtnOnClick = (record: Record<string, any>) => {
    setCurrent(record);
    setReleaseVisible(true);
  };

  const releaseModalOnCancel = () => {
    setReleaseVisible(false);
  };

  const releaseModalOnOk = async (values: Record<string, any>) => {
    setReleaseIng(true);
    try {
      await VersionUpdateApi.Release(values);
      setReleaseVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setReleaseIng(false);
  };

  const onClickDownLoad = async (record: Record<string, any>) => {
    setDownloadLoading(true);
    try {
      const res: any = await FileApi.Download('file', record.source);
      // const lastIndex = record.source.lastIndexOf('.');
      // const suffix = record.source.slice(lastIndex, record.source.langth);
      // 下载文件名称 {程序名称}{客户}{版本号}
      saveAs(res, record.fileName);
      NoticeUtil.success();
    } catch (e) {}
    setDownloadLoading(false);
  };

  const tabsOnChange = (activeKey) => {
    tabDefaultActiveKey.current = activeKey;
  };

  useEffect(() => {
    if (softwareUpdateType.length > 0) {
      softwareUpdateType.forEach((item) => {
        item.name = <Tooltip title={item?.remark}>{item.name}</Tooltip>;
      });
      tabDefaultActiveKey.current = softwareUpdateType[0].code;
      try {
        const localCache = LocalStorageUtil.getItem(`${cachePrefix}${cacheKey}`);
        const { tabsActiveKey } = localCache;
        if (tabsActiveKey && softwareUpdateType.some((n) => n.code === tabsActiveKey)) {
          tabDefaultActiveKey.current = tabsActiveKey;
        }
      } catch {}
      setInitialized(true);
    }
  }, [softwareUpdateType]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.versionNumber'),
      dataIndex: 'releaseVersion',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.fileName'),
      dataIndex: 'fileName',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 300,
    },
    {
      title: i18n.t('global.uploadTime'),
      dataIndex: 'created',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.isDefault'),
      dataIndex: 'defaultTag',
      ellipsis: true,
      sorter: true,
      width: 120,
      render: (value, record) => {
        if (record.defaultTag) {
          return <CheckFillIcon className="fill-lead-green" />;
        }
        return '';
      },
    },
    {
      title: i18n.t('global.updateContent'),
      dataIndex: 'content',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 150,
    },
    {
      title: i18n.t('global.scheduleUpdate'),
      dataIndex: 'appointmentUpdateTag',
      valueType: 'boolean',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.updateTime'),
      dataIndex: 'appointmentUpdateDate',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 250,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      auto: true,
      minWidth: 150,
    },
    {
      title: i18n.t('global.downloadCount'),
      dataIndex: 'downloadCount',
      valueType: 'number',
      ellipsis: true,
      sorter: true,
      width: 150,
    },
    {
      title: i18n.t('global.lastModified'),
      dataIndex: 'modified',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (relPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.release'),
      icon: <ExternalLinkLineIcon className="fill-lead-orange" />,
      onClick: (record) => {
        releaseBtnOnClick(record);
      },
    });
  }

  if (dowPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.download'),
      icon: <DownloadCloudLineIcon className="fill-lead-orange" />,
      onClick: (record) => {
        onClickDownLoad(record);
      },
    });
  }

  if (delPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  return (
    <div>
      {initialized ? (
        <Spin
          spinning={downloadLoading}
          tip={i18n.t('global.downloading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        >
          <PowerTable
            initialized
            rowKey="id"
            columns={columns}
            innerRef={powerTableRef}
            tabDefaultActiveKey={tabDefaultActiveKey.current}
            tabStatus={softwareUpdateType}
            tabsOnChange={tabsOnChange}
            rightToolbar={[
              addPermission && (
                <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                  {i18n.t('global.new')}
                </Button>
              ),
            ]}
            defaultPageSize={20}
            settingToolVisible
            pagination
            autoLoad
            enableCache
            cacheKey={cacheKey}
            tableProps={{
              sticky: {
                offsetHeader: 0,
              },
              onRow: editPermission
                ? (record) => ({
                    onClick: () => {
                      if (!record.defaultTag) {
                        editBtnOnClick(record);
                      }
                    },
                  })
                : undefined,
            }}
            defaultSorter={{ field: 'created', order: 'DESCEND' }}
            request={fetchData}
          />
        </Spin>
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
      <AddDrawer
        visible={addModalVisible}
        clientDefault={tabDefaultActiveKey.current}
        confirmLoading={adding}
        onOk={addModalOnOk}
        onCancel={addModalOnCancel}
      />
      <DetailDrawer
        visible={editModalVisible}
        current={current}
        confirmLoading={updating}
        onOk={editModalOnOk}
        onCancel={editModalOnCancel}
      />
      <ReleaseModal
        visible={releaseVisible}
        current={current}
        confirmLoading={releaseIng}
        onOk={releaseModalOnOk}
        onCancel={releaseModalOnCancel}
      />
    </div>
  );
};

export default VersionUpdate;
