import { ITableDataLineItem, INormalDataItem } from './data';

export type TState = {
  loading: boolean;
  saving: boolean;
  weightValue: number;
  weightModalOpen: boolean;
  tableLineData: ITableDataLineItem[];
  normalData: INormalDataItem[];
  normalTotalQty: number;
  abnormalTotal: number;
};

export type TActionType =
  | {
      type: 'setLoading';
      payload: boolean;
    }
  | {
      type: 'setSaving';
      payload: boolean;
    }
  | {
      type: 'setWeightValue';
      payload: number;
    }
  | {
      type: 'setWeightModalOpen';
      payload: boolean;
    }
  | {
      type: 'setTableLineData';
      payload: ITableDataLineItem[];
    }
  | {
      type: 'setNormalTotalQty';
      payload: number;
    }
  | {
      type: 'setNormalData';
      payload: INormalDataItem[];
    }
  | {
      type: 'initData';
      payload: {
        // 单据/预装箱明细范围(未装箱)
        lineDataList: any[];
        // 装箱明细范围(已装箱)
        packedLineDataList: any[];
        isPreBox?: boolean;
        callback?: (data?: any) => void;
      };
    }
  | {
      type: 'setSuccessScanData';
      payload: {
        skuId: string;
        newNormalQty: number;
        tableIndex: number;
        operateSize: number;
        callback?: (data: any) => void;
      };
    }
  | {
      type: 'setErrorDataInTableLine';
      payload: {
        errorData: any[];
      };
    }
  | {
      type: 'resetModalData';
      payload: {
        isCloseModal?: boolean;
      };
    };

export const initialState: TState = {
  tableLineData: [],
  normalData: [],
  loading: false,
  saving: false,
  weightValue: 0,
  weightModalOpen: false,
  normalTotalQty: 0,
  abnormalTotal: 0,
};

export function reducer(state: TState, action: TActionType): TState {
  let newTableLineData: ITableDataLineItem[] = [];
  let newNormalData: INormalDataItem[] = [];
  let newNormalTotalQty = 0;
  let newAbnormalTotal = 0;

  const formatBaseInfo = (data?: any) => ({
    prodCode: data?.prodCode || '',
    colorCode: data?.colorCode || '',
    colorName: data?.colorName || '',
    sizeCode: data?.sizeCode || '',
    sizeName: data?.sizeName || '',
    skuName: data?.skuName || '',
    specCode: data?.specCode || '',
    specName: data?.specName || '',
  });

  switch (action.type) {
    case 'setLoading':
      return { ...state, loading: action.payload };
    case 'setSaving':
      return { ...state, saving: action.payload };
    case 'setWeightValue':
      return { ...state, weightValue: action.payload };
    case 'setWeightModalOpen':
      return { ...state, weightModalOpen: action.payload };
    case 'setTableLineData':
      return { ...state, tableLineData: action.payload };
    case 'setNormalData':
      return { ...state, normalData: action.payload };
    case 'setNormalTotalQty':
      return { ...state, normalTotalQty: action.payload };
    case 'initData':
      // eslint-disable-next-line no-case-declarations
      const { lineDataList, packedLineDataList, callback } = action.payload;
      if (lineDataList && lineDataList.length > 0) {
        // epc级别多条码汇总
        const collectBarcodeMap = new Map<string, any>();
        lineDataList.forEach((item) => {
          if (collectBarcodeMap.has(item.barcode)) {
            const collectBarcodeItem = collectBarcodeMap.get(item.barcode);
            collectBarcodeMap.set(item.barcode, {
              ...collectBarcodeItem,
              actQty: collectBarcodeItem.actQty + (item?.actQty || 0),
              qty: collectBarcodeItem.qty + item.qty,
            });
          } else {
            collectBarcodeMap.set(item.barcode, {
              ...item,
              actQty: item?.actQty || 0,
              qty: item.qty,
            });
          }
        });
        collectBarcodeMap.forEach((item) => {
          newTableLineData.push({
            barcode: item.barcode,
            qty: item.qty,
            // 预装箱的接口没有 actQty 已装箱的数量，兼容处理
            availableQty: item.qty ? item.qty - (item?.actQty || 0) : 0,
            operateQty: 0,
            actQty: 0,
            errorQty: 0,
            overQty: 0,
            alias: item.alias,
            source: item.source || '',
            brandName: item.brandName,
            brandCode: item.brandCode,
            skuId: item.skuId,
            rfidTag: item.rfidTag,
            ...formatBaseInfo(item),
          });
        });
      }

      if (packedLineDataList && packedLineDataList.length > 0) {
        packedLineDataList.forEach((item) => {
          const tableDataLineItem = newTableLineData.find((n) => n.barcode === item.barcode);
          if (tableDataLineItem) {
            tableDataLineItem.operateQty = item.qty;
            tableDataLineItem.actQty = item.qty;
            // 预装箱没有 actQty 字段，需要减去已装箱数量
            if (action.payload.isPreBox) {
              tableDataLineItem.availableQty -= item.qty;
            }
          }

          newNormalData.push({
            skuId: item.skuId,
            qty: item.qty,
            barcode: item.barcode,
          });

          newNormalTotalQty += item.qty;
        });
      }

      // 计算异常总数
      newAbnormalTotal = newTableLineData.reduce((total, item) => total + item.errorQty + item.overQty, 0);

      if (callback && newNormalTotalQty > 0) {
        callback();
      }

      return {
        ...state,
        normalData: newNormalData,
        tableLineData: newTableLineData,
        normalTotalQty: newNormalTotalQty,
        abnormalTotal: newAbnormalTotal,
      };
    case 'setSuccessScanData':
      // eslint-disable-next-line no-case-declarations
      const normalDataIndex = state.normalData.findIndex((n) => n.skuId === action.payload.skuId);
      if (normalDataIndex !== -1) {
        // 更新正常数据
        if (action.payload.newNormalQty > 0) {
          newNormalData = state.normalData.map((item, index) =>
            index === normalDataIndex ? { ...item, qty: action.payload.newNormalQty } : item,
          );
        } else {
          newNormalData = state.normalData.filter((_, index) => index !== normalDataIndex);
        }
      } else if (action.payload.newNormalQty > 0) {
        // 新增正常数据
        newNormalData = [...state.normalData, { skuId: action.payload.skuId, qty: action.payload.newNormalQty }];
      } else {
        newNormalData = [...state.normalData];
      }

      // eslint-disable-next-line no-case-declarations
      const tableDataLineItem = state.tableLineData[action.payload.tableIndex];
      if (tableDataLineItem) {
        // 将当前条码表格数据移动到最前面
        const filteredTableLineData = state.tableLineData.filter((_, index) => index !== action.payload.tableIndex);
        newTableLineData = [tableDataLineItem, ...filteredTableLineData].map((item) => {
          // 如果是减少操作且当前item有异常数，则减少异常数
          let updatedErrorQty = item.errorQty;
          let updatedOverQty = item.overQty;

          if (item.skuId === action.payload.skuId && action.payload.operateSize < 0) {
            // 减少操作时，优先减少errorQty，再减少overQty
            const reduceAmount = Math.abs(action.payload.operateSize);
            if (updatedErrorQty > 0) {
              // 拿最小值，防止减少的操作数大于异常数，导致减到负数
              const errorReduce = Math.min(updatedErrorQty, reduceAmount);
              updatedErrorQty -= errorReduce;
              // 有剩余的减少数的话，再拿去减超出量
              const remainingReduce = reduceAmount - errorReduce;
              if (remainingReduce > 0 && updatedOverQty > 0) {
                updatedOverQty = Math.max(0, updatedOverQty - remainingReduce);
              }
            } else if (updatedOverQty > 0) {
              updatedOverQty = Math.max(0, updatedOverQty - reduceAmount);
            }
          }

          return {
            ...item,
            operateQty: newNormalData.find((n) => n.skuId === item.skuId)?.qty || 0,
            availableQty:
              // 对应箱的剩余数减少
              item.skuId === action.payload.skuId
                ? Math.max(0, item.availableQty - action.payload.operateSize)
                : item.availableQty,
            errorQty: updatedErrorQty,
            overQty: updatedOverQty,
          };
        });
      }

      // 重新计算异常总数
      newAbnormalTotal = newTableLineData.reduce((total, item) => total + item.errorQty + item.overQty, 0);

      if (action.payload.callback) {
        action.payload.callback({ newNormalData });
      }

      return {
        ...state,
        normalData: newNormalData,
        tableLineData: newTableLineData,
        normalTotalQty: newNormalData.reduce((prev, next) => prev + next.qty, 0),
        abnormalTotal: newAbnormalTotal,
      };
    case 'setErrorDataInTableLine':
      newTableLineData = state.tableLineData;
      if (action.payload.errorData.length > 0) {
        newTableLineData = newTableLineData.map((item) => {
          const errorData = action.payload.errorData.find((n) => n.barcode === item.barcode);
          return {
            ...item,
            errorQty: errorData?.errQty || 0,
            overQty: errorData?.overQty || 0,
          };
        });
        // 重新计算异常总数
        newAbnormalTotal = newTableLineData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
      }
      return { ...state, tableLineData: newTableLineData, abnormalTotal: newAbnormalTotal };
    case 'resetModalData':
      return action.payload.isCloseModal
        ? initialState
        : {
            ...state,
            abnormalTotal: 0,
            normalData: [],
            normalTotalQty: 0,
            tableLineData: state.tableLineData.map((item) => ({
              ...item,
              operateQty: 0,
              // 重置时恢复原始剩余数量：已操作数为 0 时，不变，不为零时候把已操作数+剩余数
              availableQty: item.operateQty === 0 ? item.availableQty : item.operateQty + item.availableQty,
              errorQty: 0,
              overQty: 0,
            })),
          };
    default:
      return state;
  }
}
