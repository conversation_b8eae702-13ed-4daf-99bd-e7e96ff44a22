import { ReactNode } from 'react';
import * as NoticeUtil from 'common/utils/Notice';
import i18n from 'common/utils/I18n';

interface UseConfirmOrCancelReturn {
  /** 执行批量确认或取消操作 */
  executeConfirmOrCancel: (
    type: 'confirm' | 'cancel',
    confirmApi: any,
    selectedIds: string[],
    onSuccess: (errorIds: string[]) => void,
    cancelApi?: any,
    title?: ReactNode,
    content?: ReactNode,
  ) => void;
}

const useConfirmOrCancel = (): UseConfirmOrCancelReturn => {
  const executeConfirmOrCancel = (type, confirmApi, selectedIds, onSuccess, cancelApi, title, content) => {
    NoticeUtil.confirm({
      title: title || i18n.t('global.confirmConfirmOrder'),
      content: content || i18n.t('global.selectedDataNum', { num: selectedIds.length }),
      onOk: async () => {
        try {
          let resp: any;
          if (type === 'confirm') {
            resp = await confirmApi({ ids: selectedIds });
          } else if (type === 'cancel' && cancelApi) {
            resp = await cancelApi({ ids: selectedIds });
          }
          if (resp.results && resp.results.length > 0) {
            const errorData = resp.results.filter((n: any) => !n.success);
            if (errorData.length > 0) {
              NoticeUtil.error(
                <div>
                  <div>
                    {i18n.t('global.successOrErrorCountTips', {
                      count: resp.results.length - errorData.length,
                      num: errorData.length,
                    })}
                  </div>
                  {errorData.map((n: any) => (
                    <div key={n.orderId}>{n.message}</div>
                  ))}
                </div>,
              );
            } else {
              NoticeUtil.success(i18n.t('global.successTips', { count: resp.results.length }));
            }
          } else {
            NoticeUtil.success(i18n.t('global.successTips', { count: resp.results.length }));
          }
          if (onSuccess) onSuccess();
        } catch (e) {}
      },
    });
  };

  return {
    executeConfirmOrCancel,
  };
};

export default useConfirmOrCancel;
