import { request, RequestPayloadType, RequestReturnType } from '../Base';

const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/core/api/work-flow-task/list',
    method: 'post',
    data,
  });

const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/core/api/work-flow-task/reset',
    method: 'post',
    data,
  });

const LogList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/core/api/work-flow-task-log/list',
    method: 'post',
    data,
  });

export { List, LogList, Reset };
