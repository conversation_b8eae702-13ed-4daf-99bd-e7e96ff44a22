import { ModalProps } from 'antd/es/modal';
import * as EmployeeApi from 'common/api/core/Employee';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
  currentType: string;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onOk, currentType } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const partnerI18n: Record<string, any> = {
    WAREHOUSE: i18n.t('global.warehouseCode'),
    SHOP: i18n.t('global.shopCode'),
    FACTORY: i18n.t('global.factoryCode'),
  };

  const template: TemplateItem[] = [
    {
      dataIndex: 'code',
      display: i18n.t('global.codeOrAccount'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'name',
      display: i18n.t('global.name'),
      type: 'STRING',
      required: true,
    },
    // {
    //   dataIndex: 'password',
    //   display: passwordMsg,
    //   type: 'STRING',
    //   required: true,
    // },
    {
      dataIndex: 'email',
      display: i18n.t('global.email'),
      type: 'STRING',
    },
    {
      dataIndex: 'mobile',
      display: i18n.t('global.mobile'),
      type: 'STRING',
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.remark'),
      type: 'STRING',
    },
    {
      dataIndex: 'allowLogin',
      display: i18n.t('global.allowLogin'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'disabled',
      display: i18n.t('global.isDisabled'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'type',
      display: i18n.t('global.type'),
      type: 'STRING',
      required: true,
      remark: [i18n.t('global.warehouse'), i18n.t('global.shop'), i18n.t('global.factory')].join(', '),
    },
    {
      dataIndex: 'partnerCode',
      display: partnerI18n[currentType],
      type: 'STRING',
      remark: i18n.t('global.belongPartnerCode'),
    },
    {
      dataIndex: 'roleCode',
      display: i18n.t('global.role'),
      type: 'STRING',
      remark: i18n.t('global.roleCode'),
    },
  ];
  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item) => {
      switch (item.type) {
        case i18n.t('global.shop'):
          item.type = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.type = 'WAREHOUSE';
          break;
        case i18n.t('global.factory'):
          item.type = 'FACTORY';
          break;
        default:
          break;
      }
    });
    try {
      setProgressPercent(0);
      await EmployeeApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  useEffect(() => {
    if (!modalProps?.open) {
      setProgressStatus('normal');
      setProgressPercent(undefined);
    }
  }, [modalProps]);

  return (
    <Modal
      width={960}
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.employee')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
