import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/create',
    method: 'post',
    data,
    ...config,
  });

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/list',
    data,
    method: 'post',
  });

export const PreBoxes = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/pre-boxes',
    data,
    method: 'post',
  });

export const PreBoxRfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/pre-box-rfids',
    data,
    method: 'post',
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/get',
    data,
    method: 'post',
  });

export const Boxes = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/boxes',
    data,
    method: 'post',
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/lines',
    data,
    method: 'post',
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/rfids',
    data,
    method: 'post',
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/confirm',
    data,
    method: 'post',
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/cancel',
    data,
    method: 'post',
  });

export const BatchImports = (data: RequestPayloadType, config: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/batch-import',
    method: 'post',
    data,
    ...config,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/logs',
    method: 'post',
    data,
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/update',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/box-reset',
    method: 'post',
    data,
  });

export const BoxDelete = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/box-delete',
    method: 'post',
    data,
  });

export const BoxSave = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/box-save',
    method: 'post',
    data,
  });

export const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/reset',
    method: 'post',
    data,
  });

export const OutboundBoxData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/outbound-box-data',
    method: 'post',
    data,
  });

export const OutboundData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/outbound-data',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/batch-cancel',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sob-order/batch-confirm',
    method: 'post',
    data,
  });
