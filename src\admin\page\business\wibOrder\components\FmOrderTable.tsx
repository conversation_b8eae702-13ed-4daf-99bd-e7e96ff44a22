import { DatePicker, Input } from 'antd';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import ProdCodeParagraph from 'common/components/Paragraph/ProdCodeParagraph';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Tag from 'common/components/Tag';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useRef } from 'react';

import { AddDrawerContext, TAddDrawerContext } from './AddDrawerReducer';

const FmOrderTable: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: addDrawerState, dispatch } = useContext<TAddDrawerContext>(AddDrawerContext);
  const { fmOrder, currentStep } = addDrawerState;
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = async (params) => {
    const payload = {
      ...params,
    };
    payload.toPartnerIds = [currentUser.partnerId];
    if (Array.isArray(payload.created)) {
      payload.createdStart = payload.created[0];
      payload.createdEnd = payload.created[1];
    }
    delete payload.created;
    payload.status = ['NEW', 'PROCESSING'];
    try {
      let orderListResp: any;
      if (currentUser.mode === 'BINDING') {
        orderListResp = await BfmOrderApi.List(payload);
      } else {
        orderListResp = await FmOrderApi.List(payload);
      }

      orderListResp.data.forEach((item) => {
        item.prodList = [];
      });
      const prodListPayload = {
        fmOrderIds: orderListResp.data.map((n) => n.id),
      };
      if (prodListPayload.fmOrderIds.length > 0) {
        let prodList: any;
        if (currentUser.mode === 'BINDING') {
          prodList = await BfmOrderApi.FmProdList(prodListPayload);
        } else {
          prodList = await FmOrderApi.FmProdList(prodListPayload);
        }
        prodList.forEach((item) => {
          orderListResp.data.forEach((it) => {
            if (item.fmOrderId === it.id) {
              it.prodList.push(item);
            }
          });
        });
      }
      return orderListResp;
    } catch (e) {
      return { data: [] };
    }
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      labelHidden: true,
      inputComponent: <Input autoFocus placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} />,
    },
    {
      name: 'prodCode',
      labelHidden: true,
      inputComponent: <Input placeholder={i18n.t('global.productCode')} />,
    },
    {
      name: 'partnerIds',
      labelHidden: true,
      inputComponent: (
        <PartnerSelect
          sourceType="DEFAULT"
          types={['FACTORY']}
          multiple
          placeholder={i18n.t('global.factory')}
          style={{ width: 200 }}
        />
      ),
    },
    {
      name: 'created',
      labelHidden: true,
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      width: 150,
      dataIndex: 'code',
      fixed: 'left',
    },
    {
      title: i18n.t('global.sourceOrderCode'),
      dataIndex: 'sourceCode',
      valueType: 'text',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodList',
      width: 150,
      render: (value) => <ProdCodeParagraph prodList={value} />,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 120,
      render(text, record) {
        const colors = {
          NEW: 'red',
          PROCESSING: 'blue',
          FINISHED: 'green',
          CANCELED: 'slate',
        };
        return <Tag color={colors[record.status]}>{record.statusDesc}</Tag>;
      },
    },
    {
      title: i18n.t('global.from'),
      width: 180,
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.to'),
      width: 180,
      valueType: 'text',
      dataIndex: 'toPartnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.count'),
      width: 120,
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
    },
    {
      title: i18n.t('global.boxedCount'),
      width: 120,
      dataIndex: 'actQty',
      valueType: 'number',
      sorter: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.deliveryQty'),
      width: 120,
      dataIndex: 'deliveryQty',
      valueType: 'number',
      sorter: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  useEffect(() => {
    if (currentStep === 1) {
      powerTableRef.current?.load();
    }
  }, [currentStep]);

  return (
    <PowerTable
      columns={columns}
      quickSearchFieldsConfig={quickSearchFieldsConfig}
      quickSearchPanelSubmitButtonVisible
      request={fetchData}
      autoLoad
      pagination
      defaultPageSize={10}
      rowSelectionType="radio"
      defaultSorter={{ field: 'created', order: 'DESCEND' }}
      rowKey="id"
      selectedRowKeys={[fmOrder?.id]}
      onRowSelectionChange={(selectedRowKeys, selectedRows) => {
        if (selectedRows.length > 0) {
          dispatch({ type: 'setFmOrder', payload: selectedRows[0] });
        }
      }}
      tableProps={{
        onRow: (record) => ({
          onClick: () => {
            dispatch({ type: 'setFmOrder', payload: record });
          },
        }),
      }}
    />
  );
};

export default FmOrderTable;
