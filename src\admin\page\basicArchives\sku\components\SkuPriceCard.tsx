import { Tooltip } from 'antd';
import useSetting from 'common/hooks/useSetting';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

export interface SkuPriceCardProps {
  current: Record<string, any>;
}

const amountRenderFn =
  (minimumFractionDigits = 2, maximumFractionDigits = 2) =>
  (value) => {
    if (typeof value === 'number' || typeof Number(value) === 'number') {
      const formattedValue = new Intl.NumberFormat(undefined, {
        minimumFractionDigits,
        maximumFractionDigits,
      }).format(value);
      return formattedValue;
    }
    return <span>{value}</span>;
  };

const SkuPriceCard: React.FC<SkuPriceCardProps> = (props) => {
  const { current } = props;
  const [currencyData, setCurrencyData] = useState<any>();
  const { CURRENCY: currency } = useSetting([{ code: 'CURRENCY', valueType: 'STRING' }]);
  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }

  return (
    <div className="w-80 cursor-pointer rounded-md border px-4 py-2">
      <div className="truncate whitespace-nowrap text-lg font-semibold leading-6">
        <Tooltip
          title={
            <div>
              {current.partnerCode && (
                <div>
                  {i18n.t('global.code')}: {current.partnerCode}
                </div>
              )}
              {current.partnerName && (
                <div>
                  {i18n.t('global.name')}: {current.partnerName}
                </div>
              )}
              {current.partnerTypeDesc && (
                <div>
                  {i18n.t('global.type')}: {current.partnerTypeDesc}
                </div>
              )}
            </div>
          }
        >
          <span>{current.partnerName}</span>
        </Tooltip>
      </div>
      <div className="mt-2 flex items-center justify-between">
        <span>{i18n.t('global.barcodeTagPrice')}</span>
        <div className="flex items-center gap-x-1 text-xl font-bold text-lead-green">
          <Tooltip title={currencyData && currencyData.code} placement="leftTop">
            {currencyData && currencyData.symbol}
          </Tooltip>
          <span>{amountRenderFn(2, 2)(current.price || 0)}</span>
        </div>
      </div>
      <div className="mt-2 flex items-center justify-between">
        <span>{i18n.t('global.barcodeRetailPrice')}</span>
        <div className="flex items-center gap-x-1 text-xl font-bold text-lead-green">
          <Tooltip title={currencyData && currencyData.code} placement="leftTop">
            {currencyData && currencyData.symbol}
          </Tooltip>
          <span>{amountRenderFn(2, 2)(current.retailPrice || 0)}</span>
        </div>
      </div>
    </div>
  );
};

export default SkuPriceCard;
