import { DatePicker, Input, Radio, Space } from 'antd';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import * as SampleStorageLocationApi from 'common/api/sample/sampleStorageLocation';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import { usePermission } from 'common/utils/Permission';
import ExportButton from 'common/components/Button/Export';
import { exportExcel } from 'common/utils/Excel';
import { templateItem } from 'common/components/Export';
import SampleStorageLocationSelect from 'common/components/Select/SampleStorageLocationSelect';
import WarehouseSelect from 'common/components/Select/WarehouseSelect';
import SearchInput from 'common/components/SearchInput';
import * as PartnerApi from 'common/api/core/Partner';

const SampleLocation: React.FC = () => {
  const [permission] = usePermission('A:R:SAMPLE_LOCATION');
  const exportPermission = permission.codes.includes('EXPORT');

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [radioActive, setRadioActive] = useState<'SAMPLE' | 'SKU' | 'EPC'>('SAMPLE');
  const searchQueryType = useRef('SAMPLE');
  const searchParams = useRef<Record<string, any>>({});
  const [partnerId, setPartnerId] = useState<string>();
  const [partnerType, setPartnerType] = useState<string>();
  const [warehouseId, setWarehouseId] = useState<string>();

  const typeOptions = useMemo(
    () => [
      {
        value: 'SAMPLE',
        label: i18n.t('global.sample'),
      },
      {
        value: 'SKU',
        label: i18n.t('global.barcode'),
      },
      {
        value: 'EPC',
        label: i18n.t('global.tag'),
      },
    ],
    [],
  );

  const radioOnChange = (e) => {
    setRadioActive(e.target.value);
    searchQueryType.current = e.target.value;

    powerTableRef.current?.load();
  };

  const fetchData = useCallback(async (params: Record<string, any>) => {
    if (params.partnerId) {
      setPartnerId(params.partnerId);
      PartnerApi.Get({ id: params.partnerId }).then((res: any) => {
        setPartnerType(res.type);
      });
    }
    if (params.warehouseId) setWarehouseId(params.warehouseId);

    if (params.startDateRange) {
      params.modifiedBegin = params.startDateRange[0].startOf('day');
      params.modifiedEnd = params.startDateRange[1].endOf('day');
    }
    delete params.startDateRange;
    if (params.epc) {
      params.epcList = [params.epc];
    }
    delete params.epc;

    params.queryDimension = searchQueryType.current;
    searchParams.current = params;

    const res: any = await SampleStorageLocationApi.SumCount(params);
    return res;
  }, []);

  const template: templateItem[] = useMemo(() => {
    let arr: templateItem[] = [];

    if (radioActive === 'SAMPLE') {
      arr = [
        {
          display: i18n.t('global.warehouseCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'smStorageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'smStorageLocationName',
        },
        {
          display: i18n.t('global.sampleCode'),
          dataIndex: 'smCode',
        },
        {
          display: i18n.t('global.sampleName'),
          dataIndex: 'smName',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'smBrandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'smBrandName',
        },
        {
          display: i18n.t('global.colorGrpCode'),
          dataIndex: 'colorGrpCode',
        },
        {
          display: i18n.t('global.colorGrpName'),
          dataIndex: 'colorGrpName',
        },
        {
          display: i18n.t('global.sizeGrpCode'),
          dataIndex: 'sizeGrpCode',
        },
        {
          display: i18n.t('global.sizeGrpName'),
          dataIndex: 'sizeGrpName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'smQty',
        },
      ];
    }

    if (radioActive === 'SKU') {
      arr = [
        {
          display: i18n.t('global.warehouseCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'smStorageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'smStorageLocationName',
        },
        {
          display: i18n.t('global.sampleCode'),
          dataIndex: 'smCode',
        },
        {
          display: i18n.t('global.sampleName'),
          dataIndex: 'smName',
        },
        {
          display: i18n.t('global.barcode'),
          dataIndex: 'smBarcode',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'smBrandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'smBrandName',
        },
        {
          display: i18n.t('global.colorCode'),
          dataIndex: 'smColorCode',
        },
        {
          display: i18n.t('global.colorName'),
          dataIndex: 'smColorName',
        },
        {
          display: i18n.t('global.sizeCode'),
          dataIndex: 'smSizeCode',
        },
        {
          display: i18n.t('global.sizeName'),
          dataIndex: 'smSizeName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'smQty',
        },
      ];
    }

    if (radioActive === 'EPC') {
      arr = [
        {
          display: i18n.t('global.warehouseCode'),
          dataIndex: 'partnerCode',
        },
        {
          display: i18n.t('global.warehouseName'),
          dataIndex: 'partnerName',
        },
        {
          display: i18n.t('global.subWarehouseCode'),
          dataIndex: 'warehouseCode',
        },
        {
          display: i18n.t('global.subWarehouseName'),
          dataIndex: 'warehouseName',
        },
        {
          display: i18n.t('global.storageLocationCode'),
          dataIndex: 'smStorageLocationCode',
        },
        {
          display: i18n.t('global.storageLocationName'),
          dataIndex: 'smStorageLocationName',
        },
        {
          display: i18n.t('global.sampleCode'),
          dataIndex: 'smCode',
        },
        {
          display: i18n.t('global.sampleName'),
          dataIndex: 'smName',
        },
        {
          display: i18n.t('global.barcode'),
          dataIndex: 'smBarcode',
        },
        {
          display: 'EPC',
          dataIndex: 'smEpc',
        },
        {
          display: i18n.t('global.brandCode'),
          dataIndex: 'smBrandCode',
        },
        {
          display: i18n.t('global.brandName'),
          dataIndex: 'smBrandName',
        },
        {
          display: i18n.t('global.colorCode'),
          dataIndex: 'smColorCode',
        },
        {
          display: i18n.t('global.colorName'),
          dataIndex: 'smColorName',
        },
        {
          display: i18n.t('global.sizeCode'),
          dataIndex: 'smSizeCode',
        },
        {
          display: i18n.t('global.sizeName'),
          dataIndex: 'smSizeName',
        },
        {
          display: i18n.t('global.count'),
          dataIndex: 'smQty',
        },
      ];
    }

    return arr;
  }, [radioActive]);

  const columns: PowerTableColumnsType = useMemo(() => {
    let arr: PowerTableColumnsType = [];

    if (radioActive === 'SAMPLE') {
      arr = [
        {
          title: i18n.t('global.warehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'partnerName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'smStorageLocationName',
          codeDataIndex: 'smStorageLocationCode',
          nameDataIndex: 'smStorageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleCode'),
          width: 200,
          dataIndex: 'smCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleName'),
          width: 200,
          dataIndex: 'smName',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smBrandCode',
          nameDataIndex: 'smBrandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.colorGroup'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'colorGrpName',
          codeDataIndex: 'colorGrpCode',
          nameDataIndex: 'colorGrpName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sizeGroup'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'sizeGrpName',
          codeDataIndex: 'sizeGrpCode',
          nameDataIndex: 'sizeGrpName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'smQty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    if (radioActive === 'SKU') {
      arr = [
        {
          title: i18n.t('global.warehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'partnerName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'smStorageLocationName',
          codeDataIndex: 'smStorageLocationCode',
          nameDataIndex: 'smStorageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleCode'),
          width: 200,
          dataIndex: 'smCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleName'),
          width: 200,
          dataIndex: 'smName',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.barcode'),
          width: 200,
          dataIndex: 'smBarcode',
          valueType: 'text',
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smBrandCode',
          nameDataIndex: 'smBrandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.color'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smColorCode',
          nameDataIndex: 'smColorName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.size'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smSizeCode',
          nameDataIndex: 'smSizeName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'smQty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    if (radioActive === 'EPC') {
      arr = [
        {
          title: i18n.t('global.warehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'partnerName',
          codeDataIndex: 'partnerCode',
          nameDataIndex: 'partnerName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.subWarehouse'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'warehouseName',
          codeDataIndex: 'warehouseCode',
          nameDataIndex: 'warehouseName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.storageLocation'),
          width: 200,
          valueType: 'codeName',
          dataIndex: 'smStorageLocationName',
          codeDataIndex: 'smStorageLocationCode',
          nameDataIndex: 'smStorageLocationName',
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleCode'),
          width: 200,
          dataIndex: 'smCode',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: i18n.t('global.sampleName'),
          width: 200,
          dataIndex: 'smName',
          valueType: 'text',
          tooltip: true,
          sorter: true,
        },
        {
          title: 'EPC',
          width: 260,
          dataIndex: 'smEpc',
          valueType: 'text',
          tooltip: true,
          ellipsis: true,
          sorter: true,
        },
        {
          title: i18n.t('global.barcode'),
          width: 200,
          dataIndex: 'smBarcode',
          valueType: 'text',
          sorter: true,
        },
        {
          title: i18n.t('global.brand'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smBrandCode',
          nameDataIndex: 'smBrandName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.color'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smColorCode',
          nameDataIndex: 'smColorName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.size'),
          width: 200,
          valueType: 'codeName',
          codeDataIndex: 'smSizeCode',
          nameDataIndex: 'smSizeName',
          ellipsis: true,
          // sorter: true,
        },
        {
          title: i18n.t('global.count'),
          width: 200,
          dataIndex: 'smQty',
          valueType: 'number',
          sorter: true,
        },
      ];
    }

    return arr;
  }, [radioActive]);

  const onPartnerChange = (_, option: any) => {
    setPartnerId(option.value);
    setPartnerType(option.data.type);
  };

  const onPartnerClear = () => {
    setPartnerId('');
    setPartnerType('');
    setWarehouseId('');

    powerTableRef.current?.setSearchPanelFieldsValue({
      partnerId: null,
      warehouseId: null,
      smStorageLocationId: null,
    });
  };

  const onWarehouseChange = (_, option: any) => {
    setWarehouseId(option.value);
  };

  const onWarehouseClear = () => {
    setWarehouseId('');
    powerTableRef.current?.setSearchPanelFieldsValue({
      warehouseId: null,
      smStorageLocationId: null,
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smCode',
      label: i18n.t('global.sampleCode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.sampleCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = useMemo(() => {
    const arr: SearchFieldsConfig = [
      {
        name: 'partnerId',
        label: i18n.t('global.warehouse'),
        inputComponent: (
          <PartnerSelect
            types={['WAREHOUSE', 'SHOP']}
            sourceType="PERMISSION"
            className="w-60"
            onChange={onPartnerChange}
            onClear={onPartnerClear}
          />
        ),
      },
      {
        name: 'warehouseId',
        label: i18n.t('global.subWarehouse'),
        inputComponent: (
          <WarehouseSelect
            type={partnerType as 'WAREHOUSE' | 'SHOP'}
            partnerId={partnerId}
            className="w-60"
            onChange={onWarehouseChange}
            onClear={onWarehouseClear}
          />
        ),
      },
      {
        name: 'smStorageLocationId',
        label: i18n.t('global.storageLocation'),
        inputComponent: <SampleStorageLocationSelect partnerId={partnerId} warehouseId={warehouseId} />,
      },
      {
        name: 'smBarcode',
        label: i18n.t('global.barcode'),
        inputComponent: <Input />,
      },
      {
        name: 'startDateRange',
        label: i18n.t('global.date'),
        inputComponent: <DatePicker.RangePicker />,
      },
    ];
    if (radioActive === 'EPC') {
      arr.splice(5, 0, {
        name: 'epc',
        label: 'EPC',
        inputComponent: <Input />,
      });
    }
    return arr;
  }, [partnerId, partnerType, radioActive, warehouseId]);

  const exportInfo = useCallback(async () => {
    const payload: Record<string, any> = {
      ...searchParams.current,
      enablePage: false,
    };
    delete payload.currentPage;
    delete payload.pageSize;
    const res = await SampleStorageLocationApi.SumCount(payload);
    exportExcel(
      template,
      res.data,
      `${i18n.t('menu.admin.sampleLocation')}-${typeOptions.find((item) => item.value === radioActive)?.label}`,
    );
  }, [radioActive, template, typeOptions]);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {exportPermission && (
              <ExportButton
                onClick={() => {
                  exportInfo();
                }}
              />
            )}

            <Radio.Group
              options={typeOptions}
              onChange={radioOnChange}
              value={radioActive}
              optionType="button"
              buttonStyle="solid"
            />
          </Space>
        }
      />
      <PowerTable
        autoLoad
        initialized
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        innerRef={powerTableRef}
        rowKey="index"
        columns={columns}
        searchFieldsConfig={searchFieldsConfig}
        searchPanelVisible
        pagination
        defaultPageSize={10}
        enableCache
        cacheKey="REPORT_SAMPLE_LOCATION"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        request={fetchData}
      />
    </div>
  );
};

export default SampleLocation;
