import { Space } from 'antd';
import * as Maching<PERSON><PERSON><PERSON>pi from 'common/api/core/MachingCode';
// import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import ImportButton from 'common/components/Button/Import';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  // SearchFieldsConfig,
} from 'common/components/PowerTable';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';
import ImportModal from './components/ImportModal';

import OperateDrawer from './components/OperateDrawer';
import { IMachingCodeItem } from './data';

const MachingCode: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IMachingCodeItem> | undefined>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const [permission] = usePermission('A:BASE:MACHING');
  // const createPermission = permission.codes.includes('CREATE');
  // const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');
  const importPermission = permission.codes.includes('IMPORT');

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.prodCode} - ${record.colorCode} - ${record.sizeCode}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await MachingCodeApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  // const addBtnOnClick = () => {
  //   setVisible(true);
  //   setCurrent(undefined);
  // };

  const fetchData = useCallback(async (params) => {
    return MachingCodeApi.List(params);
  }, []);

  const handleSubmit = async (values: IMachingCodeItem) => {
    const id = current ? current.id : '';

    setConfirmLoading(true);
    try {
      if (id) {
        // if (typeof values.status === 'boolean') {
        //   values.disabled = !values.status;
        //   delete values.status;
        // }
        await MachingCodeApi.Update({
          ...current,
          ...values,
        });
      } else {
        // values.disabled = false;
        // values.code = values.code.trim();
        // values.name = values.name.trim();
        await MachingCodeApi.Create(values);
      }
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  // const searchFieldsConfig: SearchFieldsConfig = [
  //   {
  //     name: 'name',
  //     label: i18n.t('global.name'),
  //     inputComponent: <Input />,
  //   },
  // ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'prodName',
      sorter: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.color'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.type'),
      width: 130,
      valueType: 'text',
      dataIndex: 'type',
      ellipsis: true,
    },
    {
      title: i18n.t('global.ratioValue'),
      width: 200,
      valueType: 'text',
      dataIndex: 'ratio',
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {importPermission && <ImportButton onClick={() => setImportModalVisible(true)} />}
            {/* {createPermission && (
              <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )} */}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        // searchFieldsConfig={searchFieldsConfig}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="MACHING_CODE"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          // onRow: editPermission
          //   ? (record) => ({
          //       onClick: () => {
          //         setCurrent(record);
          //         setVisible(true);
          //       },
          //     })
          //   : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />

      <OperateDrawer
        visible={visible}
        confirmLoading={confirmLoading}
        machingId={current ? current.id : ''}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: () => {
            setImportModalVisible(false);
          },
          maskClosable: false,
        }}
        onOk={() => {
          powerTableRef.current?.load();
        }}
      />
    </div>
  );
};

export default MachingCode;
