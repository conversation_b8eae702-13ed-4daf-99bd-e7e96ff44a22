/**
 * Create by codegen at 2022-07-13
 * 数据字典 - 编辑
 */
import { Col, Form, Input, InputNumber, Row, Switch } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef, useState } from 'react';

interface EditDictDataModalProps {
  visible?: boolean;
  current?: Record<string, any>;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
  onDelete?: (values: Record<string, any> | undefined) => void;
}

const EditDictDataDrawer: React.FC<EditDictDataModalProps> = (props) => {
  const { visible, current, confirmLoading, onOk, onCancel, onDelete } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);
  const [currentSource, setCurrentSource] = useState<Record<string, any>>();

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  const deleteBtnOnClick = () => {
    if (onDelete) onDelete(current);
  };

  const onRecover = () => {
    form.setFieldsValue(currentSource);
  };

  useEffect(() => {
    let timerId: any = null;
    if (visible) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    if (visible && current) {
      const values = {
        status: !current.disabled,
        ...current,
      };
      form.setFieldsValue(values);
      setCurrentSource(values);
    }

    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
      form.resetFields();
    };
  }, [visible, form, current]);

  return (
    <Drawer
      title={
        <>
          {i18n.t('global.editDictData')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      open={visible}
      onOk={modalOnOk}
      onClose={modalOnCancel}
      destroyOnClose
      width={700}
      maskClosable={false}
      keyboard={false}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={modalOnOk}
          deletePermission
          onDelete={deleteBtnOnClick}
          onRecover={onRecover}
        />
      }
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label={i18n.t('global.code')}>{current?.code}</Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="name" label={i18n.t('global.name')} rules={canNotBeNullRules}>
              <Input maxLength={50} ref={inputRef} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="i18nName" label={i18n.t('global.internationalizedName')}>
              <Input maxLength={100} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="sort" label={i18n.t('global.sort')}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="remark" label={i18n.t('global.remark')}>
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.default')} name="defaultTag" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.status')} name="status" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default EditDictDataDrawer;
