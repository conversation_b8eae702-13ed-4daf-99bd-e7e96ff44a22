import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';
import ConditionForm from './ConditionForm';

interface AddDrawerProps {
  open: boolean;
  loading?: boolean;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: (e: React.MouseEvent<HTMLElement>) => void;
}

const AddConditionDrawer: React.FC<AddDrawerProps> = (props) => {
  const { onSubmit, loading, open, onCancel } = props;
  const [form] = Form.useForm();
  const codeInputRef = useRef<any>(null);

  const handleOk = () => {
    form.validateFields().then((values) => {
      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
      }
      onSubmit(values);
    });
  };

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      setTimeout(() => {
        codeInputRef.current?.focus({ cursor: 'end' });
      }, 300);
    }
  }, [open, form]);

  return (
    <Drawer
      title={i18n.t('global.addTagFilterRule')}
      okButtonProps={{
        loading,
      }}
      okText={i18n.t('global.apply')}
      onOk={handleOk}
      onClose={onCancel}
      open={open}
    >
      <ConditionForm codeInputRef={codeInputRef} form={form} layout="vertical" name="basic" />
    </Drawer>
  );
};

export default AddConditionDrawer;
