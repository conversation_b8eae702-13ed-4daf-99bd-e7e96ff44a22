import TabsMenuItem from 'common/components/TabsMenuItem';
import React, { useEffect, useState } from 'react';
import { faPenToSquare, faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Space } from 'antd';
import { usePermission } from 'common/utils/Permission';
import classNames from 'classnames';

export interface TabsMenuProps {
  code?: string;
  menuList: Record<string, any>[];
  onClick: (path: string) => void;
  onChangeClick: (values: any) => void;
  onDelClick: (values: any) => void;
}
const TabsMenu: React.FC<TabsMenuProps> = (props) => {
  const { menuList, code, onClick, onChangeClick, onDelClick } = props;

  const [permission] = usePermission('A:BASE:ISSUES');
  const groupEditPermission = permission.codes.includes('GROUPEDIT');
  const groupDeletePermission = permission.codes.includes('GROUPDELETE');

  const [activeCode, setActiveCode] = useState<string>('');

  useEffect(() => {
    if (code) setActiveCode(code);
  }, [code]);

  const menuOnClick = (value) => {
    onClick(value);
  };

  const menuItemOnChangeClick = (value) => {
    onChangeClick(value);
  };
  const menuItemOnDelClick = (value) => {
    onDelClick(value);
  };

  return (
    <>
      {menuList.map((item: Record<string, any>) => {
        return (
          <TabsMenuItem
            key={item.code}
            code={item.code}
            name={
              <div className="group flex w-full">
                <span style={{ flex: 1 }}>{item.name}</span>
                <Space
                  className={classNames('pl-2 group-hover:flex', {
                    flex: activeCode === item.code,
                    hidden: activeCode !== item.code,
                  })}
                >
                  {groupEditPermission && (
                    <FontAwesomeIcon icon={faPenToSquare} onClick={() => menuItemOnChangeClick(item)} />
                  )}
                  {groupDeletePermission && (
                    <FontAwesomeIcon icon={faTrashCan} onClick={() => menuItemOnDelClick(item)} />
                  )}
                </Space>
              </div>
            }
            activeCode={activeCode}
            onClick={menuOnClick}
          />
        );
      })}
    </>
  );
};

export default TabsMenu;
