import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/create',
    method: 'post',
    data,
    ...config,
  });

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/list',
    method: 'post',
    data,
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/get',
    method: 'post',
    data,
  });

export const Boxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/boxes',
    method: 'post',
    data,
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/rfids',
    method: 'post',
    data,
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/confirm',
    method: 'post',
    data,
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/lines',
    method: 'post',
    data,
  });

export const BatchImports = (data: RequestPayloadType, config: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/batch-imports',
    method: 'post',
    data,
    ...config,
  });

export const BoxSave = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/box-save',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/box-reset',
    method: 'post',
    data,
  });

export const BoxDelete = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/box-delete',
    method: 'post',
    data,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/logs',
    method: 'post',
    data,
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/cancel',
    method: 'post',
    data,
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/update',
    method: 'post',
    data,
  });

export const AbnormalLines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/abnormal-lines',
    method: 'post',
    data,
  });

export const AbnormalTags = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/abnormal-tags',
    method: 'post',
    data,
  });

export const MatchRuleList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/match-rule/list',
    method: 'post',
    data,
  });

export const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/reset',
    method: 'post',
    data,
  });

export const OutboundBoxData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/outbound-box-data',
    method: 'post',
    data,
  });

export const OutboundData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/outbound-data',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/batch-confirm',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/bwarehouse/wob-order/batch-cancel',
    method: 'post',
    data,
  });
