import { useCallback, useState } from 'react';

interface UseTableSelectionOptions {
  /** 默认选中的ID列表 */
  defaultSelectedIds?: string[];
  /** 选择变化时的回调 */
  onChange?: (selectedIds: string[]) => void;
}

interface UseTableSelectionReturn {
  /** 当前选中的ID列表 */
  selectedIds: string[];
  /** 设置选中的ID列表 */
  setSelectedIds: (ids: string[]) => void;
  /** 单个选择处理函数 */
  onSelect: (record: { id: string }, selected: boolean) => void;
  /** 全选处理函数 */
  onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => void;
  /** 清空所有选择 */
  clearAllSelection: () => void;
  /** 表格rowSelection配置对象 */
  rowSelection: {
    selectedRowKeys: string[];
    onSelect: (record: { id: string }, selected: boolean) => void;
    onSelectAll: (selected: boolean, selectedRows: any[], changeRows: any[]) => void;
  };
}

/**
 * 表格选择逻辑的通用hooks
 * @param options 配置选项
 * @returns 选择相关的状态和方法
 */
export const useTableSelection = (options: UseTableSelectionOptions = {}): UseTableSelectionReturn => {
  const { defaultSelectedIds = [], onChange } = options;

  const [selectedIds, setSelectedIdsState] = useState<string[]>(defaultSelectedIds);

  const setSelectedIds = useCallback(
    (ids: string[]) => {
      setSelectedIdsState(ids);
      onChange?.(ids);
    },
    [onChange],
  );

  const onSelect = useCallback(
    (record: { id: string }, selected: boolean) => {
      if (selected) {
        setSelectedIds([...new Set([...selectedIds, record.id])]);
      } else {
        setSelectedIds(selectedIds.filter((n) => n !== record.id));
      }
    },
    [selectedIds, setSelectedIds],
  );

  const onSelectAll = useCallback(
    (selected: boolean, selectedRows: any[], changeRows: any[]) => {
      let newSelectIds = JSON.parse(JSON.stringify(selectedIds));
      if (selected) {
        changeRows.forEach((item) => {
          if (!newSelectIds.includes(item.id)) {
            newSelectIds.push(item.id);
          }
        });
      } else {
        newSelectIds = newSelectIds.filter((id) => !changeRows.some((n) => id === n.id));
      }
      setSelectedIds(newSelectIds);
    },
    [selectedIds, setSelectedIds],
  );

  const clearAllSelection = useCallback(() => {
    setSelectedIds([]);
  }, [setSelectedIds]);

  // 提供便捷的rowSelection配置对象
  const rowSelection = {
    selectedRowKeys: selectedIds,
    onSelect,
    onSelectAll,
  };

  return {
    selectedIds,
    setSelectedIds,
    onSelect,
    onSelectAll,
    clearAllSelection,
    rowSelection,
  };
};

export default useTableSelection;
