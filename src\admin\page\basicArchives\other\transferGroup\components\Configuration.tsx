import { faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Card, Input, Spin, Typography } from 'antd';
import * as TransferGroupApi from 'common/api/core/TransferGroup';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import PartnerSelectorModal from 'common/components/PartnerSelectorModal';
import PowerTable from 'common/components/PowerTable';
import Tag from 'common/components/Tag';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';

export interface ConfigurationPropsRef {
  recover: () => void;
}
interface ConfigurationProps {
  current: Record<string, any> | undefined;
  onChange: (values: Record<string, any>) => void;
  innerRef?: React.MutableRefObject<ConfigurationPropsRef | undefined>;
}
const { Text } = Typography;

const Configuration: React.FC<ConfigurationProps> = (props) => {
  const { current, onChange, innerRef } = props;
  const [partnerType, setPartnerType] = useState<any>('');
  const [partnerSelectorModalVisible, setPartnerSelectorModalVisible] = useState<boolean>(false);

  const [shipData, setShipData] = useState<any[]>([]);
  const [receiptData, setReceiptData] = useState<any[]>([]);
  const [operator, setOperator] = useState<'SHIPPER' | 'RECEIVER'>('SHIPPER');
  const [shipDataSource, setShipDataSource] = useState<any>([]);
  const [receiptDataSource, setReceiptDataSource] = useState<any>([]);
  const [modalLoading, setModalLoading] = useState<boolean>(false);
  const [shipSearchValue, setShipSearchValue] = useState<any>();
  const [receiptSearchValue, setReceiptSearchValue] = useState<any>();
  const [defaultSelected, setDefaultSelected] = useState<string[]>([]);

  const shipAddClick = (type) => {
    setPartnerType(type);
    setOperator('SHIPPER');
    setDefaultSelected(shipDataSource.map((n) => n.id));
    setPartnerSelectorModalVisible(true);
  };
  const receiptAddClick = (type) => {
    setPartnerType(type);
    setOperator('RECEIVER');
    setPartnerSelectorModalVisible(true);
    setDefaultSelected(receiptDataSource.map((n) => n.id));
  };
  const partnerSelectorModalOnOk = (values) => {
    if (operator === 'SHIPPER') {
      setShipData(values);
      setShipDataSource(values);
    } else if (operator === 'RECEIVER') {
      setReceiptData(values);
      setReceiptDataSource(values);
    }
    setPartnerSelectorModalVisible(false);
  };
  const partnerSelectorModalOnCancel = () => {
    setPartnerSelectorModalVisible(false);
    setPartnerType('');
  };

  const deleteShipClick = (record) => {
    setShipData(shipData.filter((n) => n.id !== record.id));
    setShipDataSource(shipDataSource.filter((n) => n.id !== record.id));
  };
  const deleteReceiptClick = (record) => {
    setReceiptData(receiptData.filter((n) => n.id !== record.id));
    setReceiptDataSource(receiptDataSource.filter((n) => n.id !== record.id));
  };

  const feactSettingData = async () => {
    setModalLoading(true);
    try {
      const shipData: any = await TransferGroupApi.ShipList({
        transferGroupId: current?.id,
        enablePage: false,
      });
      const ReceiptData: any = await TransferGroupApi.ReceiptList({
        transferGroupId: current?.id,
        enablePage: false,
      });
      setShipData(shipData.data);
      setShipDataSource(shipData.data);
      setReceiptData(ReceiptData.data);
      setReceiptDataSource(ReceiptData.data);
    } catch (e) {}
    setModalLoading(false);
  };

  useEffect(() => {
    if (current) {
      feactSettingData();
      setShipSearchValue('');
      setReceiptSearchValue('');
    }
    // eslint-disable-next-line
  }, [current]);

  const shipTableColumns: any = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      width: 100,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      ellipsis: true,
      minWidth: 100,
      auto: true,
    },
    {
      title: i18n.t('global.operation'),
      dataIndex: 'actions',
      width: 80,
      align: 'center',
      render: (_, record) => (
        <Button
          danger
          onClick={() => {
            deleteShipClick(record);
          }}
          type="link"
        >
          <FontAwesomeIcon icon={faTrashCan} />
        </Button>
      ),
    },
  ];
  const receiptTableColumns: any = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      width: 100,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      minWidth: 100,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.operation'),
      dataIndex: 'actions',
      width: 80,
      align: 'center',
      render: (_, record) => (
        <Button
          danger
          onClick={() => {
            deleteReceiptClick(record);
          }}
          type="link"
        >
          <FontAwesomeIcon icon={faTrashCan} />
        </Button>
      ),
    },
  ];

  const onShipSearch = (value: string) => {
    let searchShipData;
    if (value) {
      value = value.trim().toUpperCase();

      searchShipData = shipDataSource.filter(
        (item) => item.code?.toUpperCase()?.indexOf(value) >= 0 || item.name?.toUpperCase()?.indexOf(value) >= 0,
      );
      setShipData(searchShipData);
    } else {
      setShipData(shipDataSource);
    }
  };

  const onReceiptSearch = (value: string) => {
    let searchReceiptData;
    if (value) {
      value = value.trim().toUpperCase();
      searchReceiptData = receiptDataSource.filter(
        (item) => item.code?.toUpperCase()?.indexOf(value) >= 0 || item.name?.toUpperCase()?.indexOf(value) >= 0,
      );
      setReceiptData(searchReceiptData);
    } else {
      setReceiptData(receiptDataSource);
    }
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      feactSettingData();
    },
  }));

  useEffect(() => {
    if (onChange) {
      onChange({ shipData: shipDataSource, receiptData: receiptDataSource });
    }
    // eslint-disable-next-line
  }, [shipDataSource, receiptDataSource]);

  const tagNode = useCallback(
    (type, desc) => (
      <Tag
        type="solid"
        color={
          {
            SHOP: 'purple',
            WAREHOUSE: 'blue',
            FACTORY: 'green',
          }[type]
        }
      >
        {type === 'SHOP' ? i18n.t('global.shop') : desc}
      </Tag>
    ),
    [],
  );

  return (
    <>
      <Spin spinning={modalLoading}>
        <div className="flex gap-x-4">
          <Card className="flex-1" bodyStyle={{ padding: 0 }}>
            <div className="mb-3 flex items-center justify-between bg-lead-light-bg px-3 py-2">
              <span>
                <Text style={{ fontSize: 16 }}>
                  {i18n.t('global.delivery')} {current ? tagNode(current?.shipType, current?.shipTypeDesc) : ''}
                </Text>
              </span>
              <Button
                icon={<AddFillIcon className="fill-lead-slate" />}
                onClick={() => {
                  shipAddClick(current?.shipType);
                }}
              >
                {i18n.t('global.new')}
              </Button>
            </div>
            <Input.Search
              placeholder={i18n.t('global.inputCodeThenEnterToSearch')}
              onSearch={onShipSearch}
              onChange={(e) => {
                setShipSearchValue(e.target.value);
              }}
              value={shipSearchValue}
              className="mb-3 px-3"
              allowClear
            />
            <div className="px-3">
              <PowerTable
                initialized
                rowKey="id"
                pagination={false}
                refreshBtnVisible={false}
                columns={shipTableColumns}
                tableProps={{
                  dataSource: shipData,
                  bordered: false,
                  pagination: {
                    defaultCurrent: 1,
                    defaultPageSize: 10,
                    total: shipData.length,
                  },
                  size: 'small',
                }}
              />
            </div>
          </Card>
          <Card className="flex-1" bodyStyle={{ padding: 0 }}>
            <div className="mb-3 flex items-center justify-between bg-lead-light-bg px-3 py-2">
              <span>
                <Text style={{ fontSize: 16 }}>
                  {i18n.t('global.receiver')} {current ? tagNode(current?.receiptType, current?.receiptTypeDesc) : ''}
                </Text>
              </span>
              <Button
                icon={<AddFillIcon className="fill-lead-slate" />}
                onClick={() => {
                  receiptAddClick(current?.receiptType);
                }}
              >
                {i18n.t('global.new')}
              </Button>
            </div>
            <Input.Search
              placeholder={i18n.t('global.inputCodeThenEnterToSearch')}
              onSearch={onReceiptSearch}
              className="mb-3 px-3"
              allowClear
              onChange={(e) => {
                setReceiptSearchValue(e.target.value);
              }}
              value={receiptSearchValue}
            />
            <div className="px-3">
              <PowerTable
                initialized
                rowKey="id"
                pagination={false}
                refreshBtnVisible={false}
                columns={receiptTableColumns}
                tableProps={{
                  dataSource: receiptData,
                  bordered: false,
                  pagination: {
                    defaultCurrent: 1,
                    defaultPageSize: 10,
                    total: receiptData.length,
                  },
                  size: 'small',
                }}
              />
            </div>
          </Card>
        </div>
      </Spin>
      <PartnerSelectorModal
        partnerType={partnerType}
        onOk={partnerSelectorModalOnOk}
        modalProps={{
          open: partnerSelectorModalVisible,
          onCancel: () => {
            partnerSelectorModalOnCancel();
          },
        }}
        multiple
        defaultSelected={defaultSelected}
      />
    </>
  );
};

export default Configuration;
