import { Button, DatePicker, Form, Input } from 'antd';
import classNames from 'classnames';
import * as Bsib<PERSON>rder<PERSON><PERSON> from 'common/api/shop/BsibOrder';
import * as SibOrderApi from 'common/api/shop/SibOrder';
import Drawer from 'common/components/Drawer';
import Result from 'common/components/Result';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Spin from 'common/components/Spin';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import useSetting from 'common/hooks/useSetting';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useReducer } from 'react';
import { useNavigate } from 'react-router-dom';

import { AddDrawerContext, initialState, reducer } from './AddDrawerReducer';

interface AddDrawerProps {
  open?: boolean;
  onRefresh?: () => void;
  onClose?: () => void;
}

const AddDrawer: React.FC<AddDrawerProps> = (props) => {
  const { open, onClose } = props;
  const onRefresh = useMemo(() => props.onRefresh || (() => {}), [props.onRefresh]);
  const [form] = Form.useForm();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { currentStep, success, errorMsg, saving, result } = state;
  const navigate = useNavigate();
  const { SHOP_NO_ORDER_TYPE_REQUIRED } = useSetting([{ code: 'SHOP_NO_ORDER_TYPE_REQUIRED', valueType: 'BOOLEAN' }]);

  const submit = useCallback(async () => {
    await form.validateFields();
    const payload = { ...form.getFieldsValue() };
    if (payload.billingDate) {
      payload.billingDate = payload.billingDate.startOf('day').format('YYYY-MM-DD hh:mm:ss');
    }

    if (payload.fromPartnerId === currentUser.partnerId) {
      NoticeUtil.warn(i18n.t('global.notAllowSameReceiveShipper'));
      return;
    }
    payload.partnerId = currentUser.partnerId;
    payload.mode = 'BY_ORDER';

    dispatch({ type: 'setSaving', payload: true });

    // console.log('payload is: ', payload);

    // function timer(time: number, success = true): Promise<void> {
    //   return new Promise((resolve, reject) => {
    //     setTimeout(() => {
    //       if (success) {
    //         resolve();
    //       } else {
    //         reject();
    //       }
    //     }, time);
    //   });
    // }

    // Test Code
    // try {
    //   await timer(1000, false);
    //   dispatch({
    //     type: 'setResult',
    //     payload: {
    //       id: 'ID_123456',
    //       code: 'CODE_123456',
    //     },
    //   });
    //   dispatch({ type: 'setSuccess', payload: true });
    // } catch (e) {
    //   dispatch({ type: 'setErrorMsg', payload: '这是测试错误信息,123456789 123456789 123456789 123456789 123456789' });
    //   dispatch({ type: 'setSuccess', payload: false });
    // }

    // dispatch({ type: 'setCurrentStep', payload: 2 });
    // dispatch({ type: 'setSaving', payload: false });

    try {
      let res: any;
      if (currentUser.mode === 'BINDING') {
        res = await BsibOrderApi.Create(payload, {
          throwError: false,
        });
      } else {
        res = await SibOrderApi.Create(payload, {
          throwError: false,
        });
      }
      dispatch({
        type: 'setResult',
        payload: {
          id: res.id,
          code: res.code,
        },
      });
      dispatch({ type: 'setSuccess', payload: true });
      onRefresh();
    } catch (e: any) {
      dispatch({ type: 'setErrorMsg', payload: e?.response?.data?.message || '' });
      dispatch({ type: 'setSuccess', payload: false });
    }
    dispatch({ type: 'setCurrentStep', payload: 1 });
    dispatch({ type: 'setSaving', payload: false });
  }, [dispatch, onRefresh, currentUser.mode, currentUser.partnerId, form]);

  const onOpenOrder = useCallback(() => {
    if (result?.id) {
      navigate(`/app/sib/${result?.id}`);
    }
  }, [result?.id, navigate]);

  const onRetry = useCallback(() => {
    dispatch({ type: 'setCurrentStep', payload: 0 });
  }, []);

  useEffect(() => {
    if (!open) {
      dispatch({ type: 'reset' });
      form.resetFields();
    }
  }, [open, form]);

  return (
    <AddDrawerContext.Provider value={{ state, dispatch }}>
      <Drawer
        title={i18n.t('global.newInboundOrder')}
        subTitle={
          {
            0: i18n.t('global.writeBasicInfo'),
            1: i18n.t('global.result'),
          }[currentStep]
        }
        open={open}
        onClose={onClose}
        destroyOnClose
        footer={
          currentStep !== 1 ? (
            <div className="flex gap-x-2 px-6 pb-6">
              {currentStep === 0 && (
                <Button type="primary" loading={saving} onClick={submit}>
                  {i18n.t('global.ok')}
                </Button>
              )}
              <Button onClick={props.onClose}>{i18n.t('global.cancel')}</Button>
            </div>
          ) : (
            false
          )
        }
      >
        <Spin spinning={saving}>
          <Form
            form={form}
            name="basic"
            layout="vertical"
            // onFinish={formOnFinish}
            style={{
              display: currentStep === 0 ? 'block' : 'none',
            }}
            className={classNames({
              hidden: currentStep !== 0,
            })}
            initialValues={{
              billingDate: moment(),
            }}
          >
            <Form.Item
              label={i18n.t('global.orderType')}
              name="orderTypeCode"
              rules={[SHOP_NO_ORDER_TYPE_REQUIRED && { required: true, message: i18n.t('global.fieldCanNotBeNull') }]}
            >
              <OrderTypeSelect type="IB" categoryCode="SHOP" />
            </Form.Item>
            <Form.Item
              label={i18n.t('global.shipper')}
              name="fromPartnerId"
              rules={[
                {
                  required: true,
                  message: i18n.t('global.fieldCanNotBeNull'),
                },
              ]}
            >
              <PartnerSelect
                types={['WAREHOUSE', 'SHOP', 'FACTORY']}
                sourceType="PERMISSION"
                onChange={(value, opts: any) => {
                  dispatch({ type: 'setShipperType', payload: opts?.data.type });
                  dispatch({ type: 'setShipperId', payload: opts?.data.id });
                  form.setFieldValue('fromWarehouseId', '');
                }}
              />
            </Form.Item>
            {/* 注：创建入库单时不需要选择发货方的仓库，通常也不会知道 */}
            {/* {shipperId && ( */}
            {/*   <Form.Item label={i18n.t('global.shipperSubWarehouse')} name="fromWarehouseId"> */}
            {/*     <WarehouseSelect partnerId={shipperId} type={shipperType} /> */}
            {/*   </Form.Item> */}
            {/* )} */}
            <Form.Item label={i18n.t('global.receiver')}>
              <span>{currentUser.partnerName}</span>
            </Form.Item>
            <Form.Item label={i18n.t('global.issueDate')} name="billingDate">
              <DatePicker />
            </Form.Item>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} maxLength={100} />
            </Form.Item>
          </Form>
          <div
            className={classNames({
              hidden: currentStep !== 1,
            })}
          >
            {success && (
              <Result
                status="success"
                title={i18n.t('global.createdDone')}
                subTitle={`${i18n.t('global.orderCode')}: ${result?.code}`}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onOpenOrder}>
                    {i18n.t('global.openOrder')}
                  </Button>,
                ]}
              />
            )}
            {!success && (
              <Result
                status="error"
                title={i18n.t('global.createdFail')}
                subTitle={errorMsg}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onRetry}>
                    {i18n.t('global.retry')}
                  </Button>,
                ]}
              />
            )}
          </div>
        </Spin>
      </Drawer>
    </AddDrawerContext.Provider>
  );
};

export default AddDrawer;
