import { ModalProps } from 'antd/es/modal';
import * as SpTsOrderApi from 'common/api/sample/SpTsOrder';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface ISnapImportModalProps {
  wTsOrderId?: string;
  onOk?: () => void;
  modalProps?: ModalProps;
}

const SnapImportModal: React.FC<ISnapImportModalProps> = (props) => {
  const { wTsOrderId, onOk, modalProps } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const template: TemplateItem[] = [
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'snap',
      display: i18n.t('global.bankStorageAmount'),
      type: 'NUMBER',
      required: true,
    },
  ];

  const onImport = async (data) => {
    setProgressStatus('active');
    try {
      setProgressPercent(0);
      await SpTsOrderApi.ImportSnap(
        {
          data,
          id: wTsOrderId,
        },
        {
          throwError: false,
          // @ts-ignore
          timeout: window.globalConfig.sptsImportSnapTimeout.value,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );

      setProgressPercent(100);
      setProgressStatus('success');
      if (onOk) onOk();
    } catch (e) {
      setProgressStatus('exception');
      throw e;
    }
  };

  return (
    <Modal
      title={i18n.t('global.snapImport')}
      width={1000}
      destroyOnClose
      keyboard={false}
      maskClosable={false}
      footer={false}
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.stockSnap')}
        template={template}
        onImport={onImport}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default SnapImportModal;
