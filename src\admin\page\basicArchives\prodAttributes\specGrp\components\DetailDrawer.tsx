import SpecGrpOperateForm from 'admin/page/basicArchives/prodAttributes/specGrp/components/SpecGrpOperateForm';
import SpecOperateForm from 'admin/page/basicArchives/prodAttributes/specGrp/components/SpecOperateForm';
import { Form, Tabs } from 'antd';
import * as SpecGrpApi from 'common/api/core/SpecGrp';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useState } from 'react';

import { ISpecGrpItem } from '../data';

interface EditModalProps {
  visible: boolean;
  confirmLoading: boolean;
  specId: string | undefined;
  onSubmit: (values: ISpecGrpItem) => void;
  onCancel: () => void;
}

const EditModal: React.FC<EditModalProps> = (props) => {
  const [form] = Form.useForm();
  const [grpid, setGrpid] = useState('');
  const { visible, confirmLoading, specId, onSubmit, onCancel } = props;
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState('INFO');
  const [permission] = usePermission('A:BASE:ATTRIBUTE:SPEC_GRP');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchSpec = useCallback(async () => {
    setLoading(true);
    try {
      const specGrp: any = await SpecGrpApi.Get({
        id: specId,
      });
      setGrpid(specGrp.id);
      setCurrent(specGrp);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [specId]);

  useEffect(() => {
    if (visible && specId) {
      fetchSpec();
    }
    return () => {
      setActiveKey('INFO');
    };
  }, [visible, specId, fetchSpec]);

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISpecGrpItem);
    }
  };
  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SpecGrpApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchSpec();
    setActiveKey('INFO');
  };

  return (
    <Drawer
      title={
        <>
          {i18n.t('global.editSpecGrp')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      width={680}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          deletePermission={deletePermission}
          onDelete={deleteBtnOnClick}
          onApply={handleSubmit}
          onRecover={onRecover}
        />
      }
      onOk={handleSubmit}
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="1"
          animated={false}
          activeKey={activeKey}
          onChange={(e) => setActiveKey(e)}
          items={[
            {
              label: i18n.t('global.information'),
              key: 'INFO',
              children: <SpecGrpOperateForm current={current} form={form} onFinish={handleFinish} />,
            },
            {
              label: i18n.t('global.specs'),
              key: 'SPEC',
              children: <SpecOperateForm grpId={grpid} />,
            },
          ]}
        />
      </Spin>
    </Drawer>
  );
};

export default EditModal;
