import * as BwcbOrderApi from 'common/api/warehouse/BwcbOrder';
import * as WcbOrderApi from 'common/api/warehouse/WcbOrder';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback, useContext } from 'react';

export const useCommonFn = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const confirm = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BwcbOrderApi.Confirm({
                  id: orderId,
                });
              } else {
                await WcbOrderApi.Confirm({
                  id: orderId,
                });
              }

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  return { confirm };
};
