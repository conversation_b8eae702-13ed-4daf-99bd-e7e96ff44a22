import { createContext } from 'react';

import { CurrentTypePartnerListItem, CurrentTypeWarehouseListItem, PartnerPermissionListItem } from './data';

export const initialState: {
  empId: string;
  type: string;
  /** 接口返回是否拥有全部权限 */
  defaultAllPermissionFlag: boolean;
  /** 是否拥有全部权限 */
  allPermissionFlag: boolean;
  warehouseRowKeys: string[];
  shopRowKeys: string[];
  factoryRowKeys: string[];
  tabActiveKey: string;
  /** 当前tabActiveKey选中的数据 */
  currentTypeSelectRowKeys: string[];
  /** 当前tabActiveKey的往来单位 */
  currentTypePartnerList: CurrentTypePartnerListItem[];
  /** 当前tabActiveKey的仓店数据 */
  currentTypeWarehouseList: CurrentTypeWarehouseListItem[];
  /** warehouse, shop, factory类型选中的所有仓店 */
  allSelectedRows: CurrentTypeWarehouseListItem[];
  /** 接口返回的所有选中的仓店 */
  partnerPermissionList: Record<string, any>[];
} = {
  empId: '',
  type: '',
  defaultAllPermissionFlag: true,
  allPermissionFlag: true,
  warehouseRowKeys: [],
  shopRowKeys: [],
  factoryRowKeys: [],
  tabActiveKey: '',
  currentTypeSelectRowKeys: [],
  currentTypePartnerList: [],
  currentTypeWarehouseList: [],
  allSelectedRows: [],
  partnerPermissionList: [],
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'setEmpId'; payload: string }
  | { type: 'setType'; payload: string }
  | { type: 'reset'; ignoreType?: boolean }
  | { type: 'setDefaultAllPermissionFlag'; payload: boolean }
  | { type: 'setAllPermissionFlag'; payload: boolean }
  | { type: 'setWarehouseRowKeys'; payload: string[] }
  | { type: 'setShopRowKeys'; payload: string[] }
  | { type: 'setFactoryRowKeys'; payload: string[] }
  | { type: 'setTabActiveKey'; payload: string }
  | { type: 'setCurrentTypeSelectRowKeys'; payload: string[] }
  | { type: 'setCurrentTypePartnerList'; payload: CurrentTypePartnerListItem[] }
  | { type: 'setCurrentTypeWarehouseList'; payload: CurrentTypeWarehouseListItem[] }
  | { type: 'setTypeSelectRowKeys'; payload: PartnerPermissionListItem[]; tabActiveKey: string }
  | { type: 'setAllSelectedRows'; payload: CurrentTypeWarehouseListItem[] };

export function reducer(state: TStateType, action: TActionType): TStateType {
  const newCurrentTypeSelectRowsKeys: string[] = [];
  const newWarehouseRowKeys: string[] = [];
  const newShopRowKeys: string[] = [];
  const newFactoryRowKeys: string[] = [];
  let newState = state;
  switch (action.type) {
    case 'reset':
      newState = { ...initialState };
      if (action.ignoreType) {
        newState.type = state.type;
        newState.tabActiveKey = state.type;
      }
      return newState;
    case 'setEmpId':
      return { ...state, empId: action.payload };
    case 'setType':
      return { ...state, type: action.payload };
    case 'setDefaultAllPermissionFlag':
      return { ...state, defaultAllPermissionFlag: action.payload };
    case 'setAllPermissionFlag':
      return { ...state, allPermissionFlag: action.payload };
    case 'setWarehouseRowKeys':
      return { ...state, warehouseRowKeys: action.payload };
    case 'setShopRowKeys':
      return { ...state, shopRowKeys: action.payload };
    case 'setFactoryRowKeys':
      return { ...state, factoryRowKeys: action.payload };
    case 'setTabActiveKey':
      return { ...state, tabActiveKey: action.payload };
    case 'setCurrentTypeSelectRowKeys':
      return { ...state, currentTypeSelectRowKeys: action.payload };
    case 'setCurrentTypePartnerList':
      return { ...state, currentTypePartnerList: action.payload };
    case 'setCurrentTypeWarehouseList':
      return { ...state, currentTypeWarehouseList: action.payload };
    case 'setTypeSelectRowKeys':
      action.payload.forEach((item) => {
        if (!item.warehouseId) return;
        if (item.partnerType === 'WAREHOUSE') {
          newWarehouseRowKeys.push(item.warehouseId);
        } else if (item.partnerType === 'SHOP') {
          newShopRowKeys.push(item.warehouseId);
        } else if (item.partnerType === 'FACTORY') {
          newFactoryRowKeys.push(item.warehouseId);
        }
        if (item.partnerType === action.tabActiveKey) {
          newCurrentTypeSelectRowsKeys.push(item.warehouseId);
        }
      });
      return {
        ...state,
        currentTypeSelectRowKeys: newCurrentTypeSelectRowsKeys,
        warehouseRowKeys: newWarehouseRowKeys,
        shopRowKeys: newShopRowKeys,
        factoryRowKeys: newFactoryRowKeys,
        partnerPermissionList: action.payload,
      };
    case 'setAllSelectedRows':
      return { ...state, allSelectedRows: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TDetailDrawerContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const DetailDrawerContext = createContext<TDetailDrawerContext>({
  state: initialState,
  dispatch: () => undefined,
});
