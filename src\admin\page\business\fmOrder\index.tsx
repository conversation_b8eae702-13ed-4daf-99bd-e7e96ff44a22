import { Button, DatePicker, Input, Space, Spin } from 'antd';
import * as BfmOrder<PERSON>pi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ScheduleIcon from 'common/assets/icons/icon-schedule.svg?react';
import ImportButton from 'common/components/Button/Import';
import ProdCodeParagraph from 'common/components/Paragraph/ProdCodeParagraph';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ImportModal from 'common/components/FmOrderImportModal';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import useTableSelection from 'common/hooks/useTableSelection';
import BusinessOrderBatchOperations from 'common/components/BusinessOrderBatchOperations';
import useConfirmOrCancel from 'common/hooks/useConfirmOrCancel';
import { useCommonFn } from './common';
import AddModal from './components/AddModal';
import RecordModal from './components/RecordModal';

const FmOrder: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [fmOrderCode, setFmOrderCode] = useState<string>('');
  const [fadOrderRecordModalVisible, setFadOrderRecordModalVisible] = useState<boolean>(false);
  const [addModalVisible, setAddModalVisible] = useState<boolean>(false);
  const [fmOrderImportModalVisible, setFmOrderImportModalVisible] = useState<boolean>(false);
  const [permission] = usePermission('A:F:FM');
  const createPermission = permission.codes.includes('CREATE');
  const importPermission = permission.codes.includes('IMPORT');
  const cancelPermission = permission.codes.includes('CANCEL');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const batchConfirmPermission = permission.codes.includes('BATCH_CONFIRM');
  const batchCancelPermission = permission.codes.includes('BATCH_CANCEL');
  const scheduleRecordPermission = permission.codes.includes('SCHEDULE_RECORD');
  const [initialized, setInitialized] = useState<boolean>(false);
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const navigate = useNavigate();
  const { confirm, cancel } = useCommonFn();
  const { selectedIds, rowSelection, clearAllSelection } = useTableSelection();
  const { executeConfirmOrCancel } = useConfirmOrCancel();

  let defaultPartnerValue: string[] = [];

  if (currentUser.type === 'FACTORY') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    async (params: Record<string, any>, tabActiveKey: string) => {
      if (params.created) {
        params.createdStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      delete params.created;
      if (params.code) {
        params.code = `%${params.code}%`;
      }

      if (tabActiveKey === 'ALL') {
        params.status = ['NEW', 'PROCESSING', 'FINISHED'];
      } else if (tabActiveKey === 'WAITING_BOXING') {
        params.status = ['NEW', 'PROCESSING'];
      } else {
        params.status = [tabActiveKey];
      }

      try {
        let orderListResp;
        if (currentUser.mode === 'BINDING') {
          orderListResp = await BfmOrderApi.List({
            ...params,
          });
        } else {
          orderListResp = await FmOrderApi.List({
            ...params,
          });
        }
        orderListResp.data.forEach((item) => {
          item.prodList = [];
        });
        const prodListPayload = {
          fmOrderIds: orderListResp.data.map((n) => n.id),
        };
        if (prodListPayload.fmOrderIds.length > 0) {
          let prodList;
          if (currentUser.mode === 'BINDING') {
            prodList = await BfmOrderApi.FmProdList(prodListPayload);
          } else {
            prodList = await FmOrderApi.FmProdList(prodListPayload);
          }
          prodList.forEach((item) => {
            orderListResp.data.forEach((it) => {
              if (item.fmOrderId === it.id) {
                it.prodList.push(item);
              }
            });
          });
        }
        return orderListResp;
      } catch (e) {
        return { data: [] };
      }
    },
    // eslint-disable-next-line
    [],
  );

  const cancelBtnOnClick = async (record: Record<string, any>) => {
    try {
      await cancel(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    try {
      await confirm(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const batchConfirmOrCancelOnClick = (type: 'confirm' | 'cancel') => {
    const confirmApi = currentUser.mode === 'BINDING' ? BfmOrderApi.BatchConfirm : FmOrderApi.BatchConfirm;
    const cancelApi = currentUser.mode === 'BINDING' ? BfmOrderApi.BatchCancel : FmOrderApi.BatchCancel;

    executeConfirmOrCancel(
      type,
      confirmApi,
      selectedIds,
      () => {
        clearAllSelection();
        powerTableRef.current?.load();
      },
      cancelApi,
    );
  };

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'partnerIds',
      label: i18n.t('global.shipper'),
      inputComponent: <PartnerSelect types={['FACTORY']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'toPartnerIds',
      label: i18n.t('global.to'),
      inputComponent: <PartnerSelect types={['WAREHOUSE', 'SHOP', 'FACTORY']} sourceType="DEFAULT" multiple />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.additionCode'),
      width: 250,
      ellipsis: true,
      dataIndex: 'sourceCode',
      tooltip: true,
      render: (text, record) => {
        const codes = record?.alias
          ?.filter((item) => item.source !== 'IC')
          ?.map((item) => ({
            label: item.sourceDesc,
            value: item.code,
          }));
        if (codes?.length > 0) {
          return <AdditionCodeViewer codes={codes} />;
        }
        return text;
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodList',
      width: 160,
      render: (value) => <ProdCodeParagraph prodList={value} />,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PROCESSING: 'blue',
                FINISHED: 'green',
                CANCELED: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.shipper'),
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.receiver'),
      valueType: 'text',
      dataIndex: 'toPartnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.boxedCount'),
      dataIndex: 'actQty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.deliveryQty'),
      dataIndex: 'deliveryQty',
      valueType: 'number',
      sorter: true,
      width: 230,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      valueType: 'dateTime',
      dataIndex: 'created',
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (confirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-blue" />,
      isDisabled: (record) => !(record.status === 'PROCESSING'),
      onClick: (record) => {
        confirmBtnOnClick(record);
      },
    });
  }

  if (cancelPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.cancel'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      isDisabled: (record) => !(record.status === 'PROCESSING' || record.status === 'NEW'),
      onClick: (record) => {
        cancelBtnOnClick(record);
      },
    });
  }

  if (scheduleRecordPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.fadOrderRecord'),
      icon: <ScheduleIcon className="fill-lead-yellow" />,
      onClick: (record) => {
        setFadOrderRecordModalVisible(true);
        setFmOrderCode(record.code);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues = {
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            <BusinessOrderBatchOperations
              selectedIds={selectedIds}
              clearAllSelection={clearAllSelection}
              confirmPermission={batchConfirmPermission}
              cancelPermission={batchCancelPermission}
              btnOnClick={batchConfirmOrCancelOnClick}
            />
            {importPermission && (
              <ImportButton
                onClick={() => {
                  setFmOrderImportModalVisible(true);
                }}
              />
            )}
            {createPermission && (
              <Button
                type="primary"
                icon={<AddFillIcon className="fill-white" />}
                onClick={() => {
                  setAddModalVisible(true);
                }}
              >
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={searchPanelInitialValues}
          refreshBtnVisible
          searchPanelVisible={false}
          searchPanelCollapsible
          enableCache
          cacheKey="FM_ORDER"
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            {
              code: 'NEW',
              name: i18n.t('global.newOrder'),
            },
            {
              code: 'PROCESSING',
              name: i18n.t('global.processing'),
            },
            {
              code: 'FINISHED',
              name: i18n.t('global.finished'),
            },
            {
              code: 'CANCELED',
              name: i18n.t('global.cancelled'),
            },
            // {
            //   code: 'WAITING_BOXING',
            //   name: i18n.t('global.waitingForBoxing'),
            // },
          ]}
          defaultPageSize={10}
          pagination
          autoLoad
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            rowSelection: {
              type: 'checkbox',
              ...rowSelection,
              getCheckboxProps: (record: Record<string, any>) => {
                return {
                  disabled: ['FINISHED', 'CANCELED'].includes(record.status),
                  name: record.code,
                };
              },
            },
            onRow: (record) => ({
              onClick: () => {
                navigate(`/app/fm/detail/${record.id}`);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
      <AddModal
        visible={addModalVisible}
        onOk={() => powerTableRef.current?.load()}
        onCancel={() => {
          setAddModalVisible(false);
        }}
      />

      <ImportModal
        modalProps={{
          open: fmOrderImportModalVisible,
          onCancel: () => {
            setFmOrderImportModalVisible(false);
          },
          destroyOnClose: true,
          maskClosable: false,
          keyboard: false,
        }}
        onOk={() => {
          powerTableRef.current?.load();
        }}
      />
      <RecordModal
        visible={fadOrderRecordModalVisible}
        onCancel={() => {
          setFadOrderRecordModalVisible(false);
        }}
        fmOrderCode={fmOrderCode}
      />
    </div>
  );
};

export default FmOrder;
