import { Form, Input, Switch } from 'antd';
import * as PartnerA<PERSON> from 'common/api/core/Partner';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { ILogisticsItem } from '../data';

interface EditDrawerProps {
  open: boolean;
  saving?: boolean;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  current: Partial<ILogisticsItem> | undefined;
}

const EditDrawer: React.FC<EditDrawerProps> = (props) => {
  const { onSubmit, saving, open, onCancel, current } = props;
  const [form] = Form.useForm();
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [currentRecord, setCurrentRecord] = useState<Record<string, any>>();
  const [loading, setLoading] = useState(false);

  const [permission] = usePermission('A:BASE:LOGISTICS');
  const delPermission = permission.codes.includes('DEL');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const fetchGet = useCallback(async () => {
    if (!current?.id) return;
    setLoading(true);
    try {
      const rec = await PartnerApi.Get({
        id: current?.id,
      });
      setCurrentRecord(rec);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  }, [current]);

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
        }
        onSubmit(values);
      })
      .catch(() => {});
  };

  const deleteLogistics = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: current?.name,
      okType: 'danger',
      onOk: async () => {
        try {
          await PartnerApi.Delete({ id: current?.id });
          NoticeUtil.success();
          // @ts-ignore
          if (onCancel) onCancel();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchGet();
  };

  useEffect(() => {
    if (current) fetchGet();
  }, [current, fetchGet]);

  useEffect(() => {
    if (currentRecord) {
      const values = {
        status: !currentRecord?.disabled,
        ...currentRecord,
      };
      form.setFieldsValue(values);
    }
  }, [currentRecord, form]);

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      setTimeout(() => {
        nameInputRef.current?.focus({ cursor: 'end' });
      }, 300);
    }
  }, [open, form]);

  return (
    <Drawer
      title={`${i18n.t('global.editLogistics')} [${current?.code}]`}
      onClose={onCancel}
      open={open}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: saving,
          }}
          onApply={handleOk}
          deletePermission={delPermission}
          onDelete={deleteLogistics}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical" name="basic">
          <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
            <Input ref={codeInputRef} readOnly maxLength={20} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} maxLength={50} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default EditDrawer;
