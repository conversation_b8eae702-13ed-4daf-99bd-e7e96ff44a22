import { ModalProps } from 'antd/es/modal';
import * as PartnerPermissionApi from 'common/api/core/PartnerPermission';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
}

const PartnerImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onOk } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const template: TemplateItem[] = [
    {
      dataIndex: 'empCode',
      display: i18n.t('global.employeeCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'empType',
      display: i18n.t('global.employeeType'),
      type: 'STRING',
      required: true,
      remark: [
        i18n.t('global.warehouse'),
        i18n.t('global.shop'),
        i18n.t('global.factory'),
        i18n.t('global.supplier'),
        // i18n.t('global.tenant'),
      ].join(', '),
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.partnerCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'partnerType',
      display: i18n.t('global.partnerType'),
      type: 'STRING',
      required: true,
      remark: [
        i18n.t('global.warehouse'),
        i18n.t('global.shop'),
        i18n.t('global.factory'),
        i18n.t('global.supplier'),
        i18n.t('global.logistics'),
      ].join(', '),
    },
    {
      dataIndex: 'warehouseCode',
      display: i18n.t('global.warehouseCode'),
      type: 'STRING',
    },
  ];
  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item) => {
      switch (item.empType) {
        case i18n.t('global.shop'):
          item.empType = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.empType = 'WAREHOUSE';
          break;
        case i18n.t('global.factory'):
          item.empType = 'FACTORY';
          break;
        case i18n.t('global.supplier'):
          item.empType = 'SUPPLIER';
          break;
        // case i18n.t('global.tenant'):
        //   item.empType = 'TENANT';
        //   break;
        default:
          break;
      }

      switch (item.partnerType) {
        case i18n.t('global.shop'):
          item.partnerType = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.partnerType = 'WAREHOUSE';
          break;
        case i18n.t('global.factory'):
          item.partnerType = 'FACTORY';
          break;
        case i18n.t('global.supplier'):
          item.partnerType = 'SUPPLIER';
          break;
        case i18n.t('global.logistics'):
          item.partnerType = 'LOGISTICS';
          break;
        default:
          break;
      }
    });
    try {
      setProgressPercent(0);
      await PartnerPermissionApi.Imports(data, {
        throwError: false,
        timeout: 300000,
        onUploadProgress: (progressEvent: any) => {
          const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
          setProgressPercent(percentCompleted);
        },
      });
      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  useEffect(() => {
    if (!modalProps?.open) {
      setProgressStatus('normal');
      setProgressPercent(undefined);
    }
  }, [modalProps]);

  return (
    <Modal
      width={960}
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        moduleName={`${i18n.t('global.employee')}-${i18n.t('global.permission')}`}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default PartnerImportModal;
