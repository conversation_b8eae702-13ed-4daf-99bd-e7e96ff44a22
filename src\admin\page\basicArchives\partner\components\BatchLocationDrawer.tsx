import { Form, InputNumber } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { IWarehouseItem } from '../data';

interface BatchLocationDrawerProps {
  open: boolean;
  loading: boolean;
  onSubmit: (values: IWarehouseItem) => void;
  onCancel: () => void;
}

const BatchLocationDrawer: React.FC<BatchLocationDrawerProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, open, loading } = props;
  const qtyInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    const times = setTimeout(() => {
      if (!open) return;
      qtyInputRef.current.focus();
    }, 200);

    return () => {
      clearTimeout(times);
      form.resetFields();
    };
  }, [open, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values) => {
    if (onSubmit) onSubmit(values);
  };
  const onClose = () => {
    if (onCancel) onCancel();
  };

  const onRecover = () => {
    form.resetFields();
  };

  return (
    <Drawer
      title={i18n.t('global.batchAddStorageLocation')}
      open={open}
      destroyOnClose
      maskClosable={false}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          onApply={handleSubmit}
          onRecover={onRecover}
        />
      }
    >
      <Form layout="vertical" form={form} onFinish={handleFinish}>
        <Form.Item label={i18n.t('global.count')} name="qty" rules={canNotBeNullRules}>
          <InputNumber ref={qtyInputRef} min={1} style={{ width: '100%' }} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default BatchLocationDrawer;
