import { ModalProps } from 'antd/es/modal';
import * as UniqueApi from 'common/api/core/Unique';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onOk?: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onOk } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    try {
      setProgressPercent(0);
      await UniqueApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );

      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  const template: TemplateItem[] = [
    {
      dataIndex: 'barcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'uniqueCode',
      display: i18n.t('global.uniqueCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'truncatedUniCode',
      display: i18n.t('global.extractUniqueCode'),
      type: 'STRING',
    },
  ];

  return (
    <Modal
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      destroyOnClose
      footer={false}
      width={1000}
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.uniqueCode')}
        template={template}
        onImport={onImport}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
