import * as Bsob<PERSON>rder<PERSON><PERSON> from 'common/api/shop/BsobOrder';
import * as SobOrder<PERSON>pi from 'common/api/shop/SobOrder';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback, useContext } from 'react';

export const useCommonFn = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const confirm = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BsobOrderApi.Confirm({ id: orderId });
              } else {
                await SobOrderApi.Confirm({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  const cancel = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BsobOrderApi.Cancel({ id: orderId });
              } else {
                await SobOrderApi.Cancel({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
              // powerTableRef.current?.load();
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  const reset = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmResetOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BsobOrderApi.Reset({ id: orderId });
              } else {
                await SobOrderApi.Reset({ id: orderId });
              }

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  return { confirm, cancel, reset };
};
