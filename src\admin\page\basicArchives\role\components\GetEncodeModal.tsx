import React, { useCallback, useEffect, useState } from 'react';
import { Typography } from 'antd';
import Modal from 'common/components/Modal';
import * as RoleApi from 'common/api/core/Permission';
import i18n from 'common/utils/I18n';
import CloseButton from 'common/components/Button/Close';
import { IRoleItem } from '../data';

const { Paragraph } = Typography;

interface IOperationModalProps {
  visible: boolean;
  current: Partial<IRoleItem> | undefined;
  onClose: () => void;
}

const GetEncodeModal: React.FC<IOperationModalProps> = (props) => {
  const { visible: open, current, onClose } = props;
  const [encode, setEncode] = useState('');
  const fetchEncode = useCallback(async () => {
    try {
      const res: any = await RoleApi.GetRoleEncode({ roleId: current?.id });
      setEncode(res.encodeStr);
    } catch (e) {}
  }, [current?.id]);

  useEffect(() => {
    if (open && current?.id) {
      fetchEncode();
    }
  }, [current, fetchEncode, open]);
  return (
    <Modal
      title={i18n.t('global.encryptedEncode')}
      open={open}
      footer={
        <CloseButton
          onClick={() => {
            if (onClose) {
              onClose();
            }
          }}
        />
      }
      onCancel={onClose}
      destroyOnClose
    >
      <Paragraph copyable>{encode}</Paragraph>
    </Modal>
  );
};

export default GetEncodeModal;
