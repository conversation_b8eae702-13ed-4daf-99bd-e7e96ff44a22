import OperateDrawer from 'admin/page/basicArchives/other/boxSpec/components/OperateDrawer';
import { Button, Input } from 'antd';
import * as BoxSpecApi from 'common/api/core/BoxSpec';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import { IBoxSpecItem } from './data';

const BoxSpec: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IBoxSpecItem> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [permission] = usePermission('A:BASE:OTHER:BOX_SPEC');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await BoxSpecApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const fetchData = useCallback(async (params: Record<string, any>) => {
    return BoxSpecApi.List(params);
  }, []);

  const handleSubmit = async (values: IBoxSpecItem) => {
    const id = current ? current.id : '';

    setConfirmLoading(true);
    try {
      if (id) {
        await BoxSpecApi.Update({
          ...current,
          ...values,
        });
      } else {
        values.code = values.code.trim();
        values.name = values.name.trim();
        await BoxSpecApi.Create(values);
      }
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.alias'),
      dataIndex: 'alias',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.capacity'),
      dataIndex: 'capacity',
      valueType: 'number',
      sorter: true,
      width: 160,
    },
    {
      title: i18n.t('global.length'),
      dataIndex: 'length',
      valueType: 'number',
      sorter: true,
      width: 130,
    },
    {
      title: i18n.t('global.width'),
      dataIndex: 'width',
      valueType: 'number',
      sorter: true,
      width: 130,
    },
    {
      title: i18n.t('global.height'),
      dataIndex: 'height',
      valueType: 'number',
      sorter: true,
      width: 130,
    },
    {
      title: i18n.t('global.volume'),
      dataIndex: 'volume',
      valueType: 'number',
      sorter: true,
      width: 150,
    },
    {
      title: i18n.t('global.weightUnit'),
      dataIndex: 'weight',
      valueType: 'number',
      sorter: true,
      width: 150,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        rightToolbar={[
          createPermission && (
            <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="BOX_SPEC"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  setCurrent(record);
                  setVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <OperateDrawer
        visible={visible}
        confirmLoading={confirmLoading}
        boxSpecId={current ? current.id : ''}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default BoxSpec;
