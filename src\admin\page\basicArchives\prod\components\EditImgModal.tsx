import { ProdContext, TProdContext } from 'admin/page/basicArchives/prod/reducer';
import { Button } from 'antd';
import * as SkuImgApi from 'common/api/core/SkuImg';
import ImgUpload from 'common/components/ImgUpload';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useState } from 'react';

interface IEditImgProps {
  visible: boolean;
  onCancel: () => void;
}

const EditImgModal: React.FC<IEditImgProps> = (props) => {
  const { onCancel, visible } = props;
  const { state } = useContext<TProdContext>(ProdContext);
  const [defaultImgId, setDefaultImgId] = useState<string>();
  const [imgList, setImgList] = useState<any[]>([]);

  const handleDefaultImg = async (id) => {
    try {
      await SkuImgApi.SetDefault({ id });
      setDefaultImgId(id);
    } catch (e) {}
  };

  const fetchImgList = useCallback(async () => {
    const res = await SkuImgApi.List({
      prodIds: [state.prodRecord?.id],
      enablePage: false,
    });
    let imgList: any[] = [];
    imgList = res.data.filter((n) => !n.skuId);
    setImgList(imgList);
  }, [state.prodRecord]);

  const handleSaveImg = async (source) => {
    try {
      const data: any = [
        {
          source,
          prodId: state.prodRecord?.id,
        },
      ];
      await SkuImgApi.Save({ data });
      fetchImgList();
    } catch (e) {}
  };

  const handleDeleteImg = async (id) => {
    try {
      await SkuImgApi.Delete({ ids: [id] });
      fetchImgList();
    } catch (e) {}
  };

  useEffect(() => {
    if (!visible) {
      setImgList([]);
    } else {
      fetchImgList();
    }
  }, [visible, fetchImgList]);

  return (
    <Modal
      title={i18n.t('global.editProdImg')}
      width={1000}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button
          key="back"
          onClick={() => {
            if (onCancel) {
              onCancel();
            }
          }}
        >
          {i18n.t('global.close')}
        </Button>,
      ]}
    >
      <ImgUpload
        defaultImg
        handleDefaultImg={handleDefaultImg}
        handleSaveImg={handleSaveImg}
        handleDeleteImg={handleDeleteImg}
        defaultImgId={defaultImgId}
        value={imgList}
      />
    </Modal>
  );
};

export default EditImgModal;
