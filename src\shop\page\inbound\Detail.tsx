/**
 * 门店入库单（后台及仓库端口通用页面）
 */
import { Descriptions, Statistic, Tabs } from 'antd';
import * as <PERSON>sib<PERSON><PERSON>r<PERSON><PERSON> from 'common/api/shop/BsibOrder';
import * as SibOrder<PERSON>pi from 'common/api/shop/SibOrder';
import CheckIcon from 'common/assets/icons/icon-check.svg?react';
import CloseCircleLineIcon from 'common/assets/icons/icon-close-circle-line.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import InboxArchiveLineIcon from 'common/assets/icons/icon-inbox-archive-line.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import Button from 'common/components/Button';
import LogDrawer from 'common/components/LogDrawer';
import OperateModeSelectorModal from 'common/components/OperateModeSelectorModal';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import RemarkEditModal from 'common/components/RemarkEditModal';
import Tag from 'common/components/Tag';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useMemo, useReducer, useRef } from 'react';
import { useParams } from 'react-router-dom';

import { useCommonFn } from './common';
import BarcodeInboundModal from './components/BarcodeInboundModal';
import BoxTable, { BoxTableInnerRef } from './components/BoxTable';
import LineTable, { LineTableInnerRef } from './components/LineTable';
import PreBoxTable, { PreBoxTableInnerRef } from './components/PreBoxTable';
import RfidInboundModal from './components/RfidInboundModal';
import { DetailContext, initialState, reducer } from './DetailReducer';

const Detail: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const {
    orderRecord: _orderRecord,
    remarkModalOpen,
    currentBox,
    barcodeInboundModalOpen,
    rfidInboundModalOpen,
    operateModeModalOpen,
    logDrawerOpen,
    logs,
  } = state;

  const orderRecord = _orderRecord || {};

  const params = useParams();

  const [permission] = usePermission('S_C:IB');
  const inboundPermission = permission.codes.includes('INBOUND');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');
  const resetPermission = permission.codes.includes('RESET');
  const fastReceiptPermission = permission.codes.includes('FAST_RECEIPT');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');

  const { confirm, fastReceipt, cancel, reset } = useCommonFn();
  const preBoxTableRef = useRef<PreBoxTableInnerRef>();
  const boxTableRef = useRef<BoxTableInnerRef>();
  const lineTableRef = useRef<LineTableInnerRef>();
  const barcodeInboundModalRef = useRef<any>();
  const rfidInboundModalRef = useRef<any>();

  const setting = useSetting([{ code: 'RFID_RULES_DEPEND_ON_FIELD', valueType: 'STRING' }]);

  const { RFID_RULES_DEPEND_ON_FIELD: dependField } = setting;

  const fetchOrderData = useCallback(async () => {
    dispatch({ type: 'setLoading', payload: true });
    try {
      let rec: any;
      if (currentUser.mode === 'BINDING') {
        rec = await BsibOrderApi.Get({ id: params.id });
      } else {
        rec = await SibOrderApi.Get({ id: params.id });
      }
      dispatch({ type: 'setOrderRecord', payload: rec });
    } catch (err) {}
    dispatch({ type: 'setLoading', payload: false });
    return {};
  }, [params.id, currentUser.mode]);

  const refresh = async () => {
    await fetchOrderData();
    preBoxTableRef.current?.load();
    boxTableRef.current?.load();
    lineTableRef.current?.load();
  };

  const confirmBtnOnClick = async () => {
    try {
      await confirm(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const inboundBtnOnClick = (record?) => {
    let inboundType = LocalStorageUtil.getItem('INBOUND-TYPE');
    dispatch({ type: 'setCurrentBox', payload: record });
    if (record.operateMode) {
      inboundType = record.operateMode;
    }
    if (inboundType === 'RFID') {
      dispatch({ type: 'setRfidInboundModalOpen', payload: true });
    } else if (inboundType === 'SKU') {
      dispatch({ type: 'setBarcodeInboundModalOpen', payload: true });
    } else if (inboundType === 'UNIQUE_CODE') {
      NoticeUtil.error(i18n.t('global.uniqueCodeBoxingNotSupport'));
    } else if (inboundType === 'MIX') {
      NoticeUtil.error(i18n.t('global.mixPackingNotSupport'));
    } else {
      dispatch({ type: 'setOperateModeModalOpen', payload: true });
    }
  };

  const operateModeModalOnClick = (menuItem) => {
    dispatch({ type: 'setOperateModeModalOpen', payload: false });
    const change = () => {
      if (menuItem.path === 'SKU') {
        dispatch({ type: 'setBarcodeInboundModalOpen', payload: true });
        dispatch({ type: 'setRfidInboundModalOpen', payload: false });
      } else if (menuItem.path === 'RFID') {
        dispatch({ type: 'setBarcodeInboundModalOpen', payload: false });
        dispatch({ type: 'setRfidInboundModalOpen', payload: true });
      }
      LocalStorageUtil.setItem('INBOUND-TYPE', menuItem.path);
    };
    const isOperate = barcodeInboundModalRef.current?.getIsOperate() || rfidInboundModalRef.current?.getIsOperate();
    if (menuItem.path !== LocalStorageUtil.getItem('INBOUND-TYPE') && isOperate) {
      NoticeUtil.confirm({
        content: i18n.t('global.confirmDiscard'),
        onOk: () => {
          change();
        },
      });
    } else {
      change();
    }
  };

  const fastReceiptBtnOnClick = async () => {
    try {
      await fastReceipt(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const resetBtnOnclick = async () => {
    try {
      await reset(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const cancelBtnOnClick = async () => {
    try {
      await cancel(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const remarkEditModalOnSubmit = async (values) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BsibOrderApi.Update({
          id: params.id,
          mode: orderRecord.mode,
          ...values,
        });
      } else {
        await SibOrderApi.Update({
          id: params.id,
          mode: orderRecord.mode,
          ...values,
        });
      }

      NoticeUtil.success();
      refresh();
    } catch {}
    dispatch({ type: 'setRemarkEditModalOpen', payload: false });
  };

  const fetchLogs = useCallback(async () => {
    try {
      let res: any;
      if (currentUser.mode === 'BINDING') {
        res = await BsibOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      } else {
        res = await SibOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      }

      dispatch({ type: 'setLogs', payload: res.data });
    } catch (e) {}
  }, [currentUser.mode, orderRecord.id]);

  const logBtnOnClick = useCallback(() => {
    dispatch({ type: 'setLogDrawerOpen', payload: true });
    fetchLogs();
  }, [fetchLogs]);

  const subTitle = useMemo(() => {
    const color: any = {
      NEW: 'red',
      PROCESSING: 'blue',
      FINISHED: 'green',
      CANCELED: 'slate',
    }[orderRecord.status];

    return <Tag color={color}>{orderRecord.statusDesc}</Tag>;
  }, [orderRecord.status, orderRecord.statusDesc]);

  useEffect(() => {
    LocalStorageUtil.setItem('RFID_RULES_DEPEND_ON_FIELD', dependField);
  }, [dependField]);

  useEffect(() => {
    fetchOrderData();
    // eslint-disable-next-line
  }, [params.id]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <DetailContext.Provider value={detailContextValue}>
      <AppHeader
        title={orderRecord.code}
        subTitle={subTitle}
        toolbar={
          <div className="flex gap-x-2">
            <Button icon={<RefreshIcon className="fill-lead-dark" />} loading={state.loading} onClick={refresh} />
            <Button type="default" icon={<ListIcon className="fill-lead-dark" />} onClick={logBtnOnClick}>
              {i18n.t('global.log')}
            </Button>
            {/* 取消按钮是否可见：有权限且状态不是已完成和已取消 */}
            {cancelPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
              <Button type="danger" icon={<CloseCircleLineIcon className="fill-white" />} onClick={cancelBtnOnClick}>
                {i18n.t('global.cancel')}
              </Button>
            )}
            {/* 重置按钮是否可见：有权限且状态为处理中 */}
            {resetPermission && orderRecord.status === 'PROCESSING' && (
              <Button type="warning" icon={<ResetIcon className="fill-white" />} onClick={resetBtnOnclick}>
                {i18n.t('global.reset')}
              </Button>
            )}
            {/* 一键验收按钮是否可见：状态为新单或者处理中，非本地单（本地单没有预装箱），单据数大于0且操作数小于0 */}
            {fastReceiptPermission &&
              (orderRecord.status === 'NEW' || orderRecord.status === 'PROCESSING') &&
              !orderRecord.localTag &&
              orderRecord.qty > 0 &&
              orderRecord.actQty === 0 && (
                <Button type="success" icon={<CheckIcon className="fill-white" />} onClick={fastReceiptBtnOnClick}>
                  {i18n.t('global.fastReceipt')}
                </Button>
              )}
            {/* 入库按钮是否可见：有权限且状态不是已完成和处理中 */}
            {inboundPermission && (orderRecord.status === 'NEW' || orderRecord.status === 'PROCESSING') && (
              <Button type="primary" icon={<InboxArchiveLineIcon className="fill-white" />} onClick={inboundBtnOnClick}>
                {i18n.t('global.inbound')}
              </Button>
            )}
            {confirmPermission && orderRecord.status === 'PROCESSING' && (
              <Button
                type="primary"
                icon={<DoubleCheckIcon className="fill-white" />}
                className="global-blue"
                onClick={confirmBtnOnClick}
              >
                {i18n.t('global.confirm')}
              </Button>
            )}
          </div>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <SendReceiveLayout
            className="flex-auto"
            left={
              <PartnerViewer
                partnerType={orderRecord.fromPartnerType}
                partnerCode={orderRecord.fromPartnerCode}
                partnerName={orderRecord.fromPartnerName}
                warehouseCode={orderRecord.fromWarehouseCode}
                warehouseName={orderRecord.fromWarehouseName}
                label={i18n.t('global.from')}
              />
            }
            right={
              <PartnerViewer
                partnerType="SHOP"
                partnerCode={orderRecord.partnerCode}
                partnerName={orderRecord.partnerName}
                warehouseCode={orderRecord.warehouseCode}
                warehouseName={orderRecord.warehouseName}
                label={i18n.t('global.to')}
              />
            }
          />
          <div className="flex flex-initial items-center gap-x-6">
            {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
            <Statistic title={i18n.t('global.inboundQty')} value={orderRecord.actQty} />
            <Statistic
              title={i18n.t('global.diffQty')}
              value={orderRecord.qty > 0 ? orderRecord.qty - orderRecord.actQty : 0}
            />
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
            {/* <OrderStatusStatistic status={orderRecord.status} /> */}
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {(orderRecord.sourceCode || orderRecord.fmOrderCode) && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>
                <AdditionCodeViewer codes={orderRecord.sourceCode} />
              </Descriptions.Item>
            )}
            {orderRecord.prodCode && (
              <Descriptions.Item label={i18n.t('global.productCode')}>
                <ProdCodeRowText prodCode={orderRecord.prodCode} />
              </Descriptions.Item>
            )}
            {orderRecord.orderTypeName && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.orderTypeName}</Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>{orderRecord.created}</Descriptions.Item>
            )}
            {((!orderRecord.remark && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED') ||
              orderRecord.remark) && (
              <Descriptions.Item label={i18n.t('global.remark')} contentStyle={{ alignItems: 'center' }}>
                {orderRecord.remark}
                {modifyRemarkPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
                  <>
                    <Button
                      type="link"
                      size="small"
                      icon={<EditBoxLineIcon className="fill-lead-orange" />}
                      onClick={() => dispatch({ type: 'setRemarkEditModalOpen', payload: true })}
                      className="-my-px ml-2"
                    />
                    <RemarkEditModal
                      current={orderRecord}
                      onSubmit={remarkEditModalOnSubmit}
                      onCancel={() => dispatch({ type: 'setRemarkEditModalOpen', payload: false })}
                      open={remarkModalOpen}
                      title={i18n.t('global.modifyRemark')}
                    />
                  </>
                )}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          defaultActiveKey="0"
          size="small"
          items={(orderRecord.localTag === false
            ? [
                {
                  key: '1',
                  label: `${i18n.t('global.preBox')} (${orderRecord.preboxCount || 0})`,
                  children: <PreBoxTable innerRef={preBoxTableRef} onInbound={inboundBtnOnClick} />,
                },
              ]
            : []
          )
            .concat([
              {
                key: '1',
                label: `${i18n.t('global.inboundBox')} (${orderRecord.boxCount || 0})`,
                children: <BoxTable innerRef={boxTableRef} onInbound={inboundBtnOnClick} onRefresh={refresh} />,
              },
              {
                key: '2',
                label: i18n.t('global.details'),
                children: <LineTable innerRef={lineTableRef} />,
              },
            ])
            .map((n, i) => ({
              ...n,
              key: i.toString(),
            }))}
        />
      </div>
      <RfidInboundModal
        // 编辑箱时，当boxes接口返回的boxId和id相同时该箱是按预装箱入库（boxId为预装箱id）；不相同时是按单入库
        sibOrderPreBoxId={currentBox.boxId !== currentBox.id && currentBox.boxId}
        sibOrderBoxId={currentBox.id}
        sibOrderId={params.id}
        orderRecord={orderRecord}
        sibOrderBoxCode={currentBox.boxCode}
        fmOrderCode={orderRecord.fmOrderCode}
        localTag={orderRecord.localTag}
        sibOrderBoxStatus={currentBox.status || 'NEW'}
        boxOperateMode={currentBox.operateMode}
        innerRef={rfidInboundModalRef}
        onOperateModeChange={() => {
          dispatch({ type: 'setOperateModeModalOpen', payload: true });
        }}
        onOk={() => {
          refresh();
          dispatch({ type: 'setRfidInboundModalOpen', payload: false });
        }}
        modalProps={{
          open: rfidInboundModalOpen,
          destroyOnClose: true,
          keyboard: false,
          onCancel: () => {
            dispatch({ type: 'setRfidInboundModalOpen', payload: false });
          },
        }}
      />
      <BarcodeInboundModal
        sibOrderPreBoxId={currentBox.boxId !== currentBox.id && currentBox.boxId}
        sibOrderBoxId={currentBox.id}
        sibOrderId={params.id}
        sibOrderBoxCode={currentBox.boxCode}
        sibOrderBoxStatus={currentBox.status || 'NEW'}
        localTag={orderRecord.localTag}
        innerRef={barcodeInboundModalRef}
        // setting={setting}
        onOk={() => {
          refresh();
          dispatch({ type: 'setBarcodeInboundModalOpen', payload: false });
        }}
        onOperateModeChange={() => dispatch({ type: 'setOperateModeModalOpen', payload: true })}
        modalProps={{
          open: barcodeInboundModalOpen,
          destroyOnClose: true,
          keyboard: false,
          onCancel: () => {
            dispatch({ type: 'setBarcodeInboundModalOpen', payload: false });
          },
        }}
      />
      <OperateModeSelectorModal
        title={i18n.t('global.selectOperateMode')}
        open={operateModeModalOpen}
        onCancel={() => {
          dispatch({ type: 'setOperateModeModalOpen', payload: false });
        }}
        onItemClick={operateModeModalOnClick}
      />
      <LogDrawer
        open={logDrawerOpen}
        orderId={orderRecord.id}
        onClose={() => dispatch({ type: 'setLogDrawerOpen', payload: false })}
        data={logs}
      />
    </DetailContext.Provider>
  );
};

export default Detail;
