# 仓库端条码入库

灵创RFID管理系统里的条码入库收货业务逻辑说明

## 文档信息

- **创建时间**: 2025年7月23日
- **版本**: v1.0
- **作者**: 陈俊强
- **文档类型**: 业务逻辑说明

## 入库单结构

```mermaid
graph TD
    A[入库单] --> B[入库单单头]
    B --> B1[是否本地单]
    A --> C[单据明细]
    A --> D[预装箱]
    A --> E[物理箱]
    
    C --> C1[当前单据的明细<br/>SKU维度]
    
    D --> D1[箱列表]
    D1 --> D2[箱明细<br/>（EPC维度）]
    
    E --> E1[箱列表]
    E1 --> E2[箱明细<br/>（EPC维度）]

    style A fill:#e1f5fe,color:#000000
    style B fill:#fff3e0,color:#000000
    style C fill:#f3e5f5,color:#000000
    style D fill:#e1f5fe,color:#000000
    style E fill:#e1f5fe,color:#000000
```

1. 单据明细
  - 解释：当前单据的明细
2. 预装箱
  - 解释：预装箱即从其他地方发货的发货箱数据，分为箱列表及箱明细两个维度的数据，界面默认显示箱列表，点击某个箱打开后查看箱明细列表，EPC维度
3. 物理箱
  - 解释：物理箱即当前入库单进行入库操作所创建的箱列表，EPC维度

## 操作模式

1. 按单入库
  - 解释：按单入库时，入库范围是当前单据的明细
2. 按预装箱入库
  - 解释：按预装箱入库时，入库范围是当前预装箱的明细

## 相关单据参数

1. 是否本地单：
  - 解释：
    - 本地单据，即在系统界面中创建的单据，此类单据装箱范围是当前系统的任意合法SKU或者EPC，只允许按单入库；
    - 非本地单据，即在系统业务流转中生成的单据或者从其他系统导入的单据，此类单据有可能存在预装箱，允许按单入库或者按预装箱入库；

## 相关系统设置
### 超量

- 超量开关：是、否
  - 解释：是否开启超量规则，为了支持将额外数量的货物收进来所设置的比例
- 超量方式：单据、条码
  - 解释：按单据总数进行超出或者按条码进行超出
- 超出比例：百分比
  - 解释：按超量方式计算允许超量的百分比

### 串货

- 串货开关：是、否
  - 解释：是否开启串货规则，为了支持将额外货物收进来所设置的规则
- 串货规则：串码、串色、串款
  - 解释：
    - 串色：开启串货且规则是“串色”时，装箱时允许不在范围内但款号及尺码相同，颜色不同的条码进行入库
    - 串码：开启串货且规则是“串码”时，装箱时允许不在范围内但款号及颜色相同，尺码不同的条码进行入库
    - 串色、串码：开启串货且规则是“串色、串码”时，装箱时允许不在范围内但款号相同，颜色、尺码不同的条码进行入库

### 特殊说明

- 按预装箱入库时，不考虑超量及串货规则，只能按范围数据进行入库

## 业务流程

1. 打开入库操作界面
2. 加载范围数据（按单时，范围数据为当前单据明细；按预装箱时，范围数据为当前预装箱明细的SKU级明细；另外本地单不需要加载范围数据）
3. 加载物理箱数据并填充到范围数据里（仅编辑箱时需要）
4. 计算出可入库数量（可入库数量=范围数据中数量-操作数），需考虑超量规则（按条码）
5. 用户扫描或输入条码回车，获取条码信息（存在范围数据则从SKU级别范围数据中匹配条码信息，否则请求接口获取条码信息）
6. 判断条码是否在范围数据中，如果不在则提示条码不存在（如果启用串货则根据串货规则进行判断）
7. 判断本次增减操作是否在合法范围内（0~可入库数量，需要考虑按单超量），如果不在则提示操作数超出范围
8. 点击保存，保存入库操作数据

## 示例场景

#### 场景1：按单超量10%

> 整单的可入库总数量：单据总数量+单据总数量的10%；只要保证总数量不超过整单的可入库总数量，超量可产生在装箱范围内的任意SKU；

```
超量开关：开启
超量方式：单据
超量比例：10%
串货开关：关闭
```

📄 单据明细：
| 条码 | 数量 |
|-----|-----:|
| SKU01 | 30 |
| SKU02 | 60 |
| SKU03 | 10 |
| **合计** | **100** |

✅ 合法入库（按单入库）：

📦 物理箱01：
| 条码 | 数量 | 操作数 |
|-----|-----:|-----:|
| SKU01 | 30 | 40 |
| SKU02 | 60 | 60 |
| SKU03 | 10 | 10 |
| **合计** | **100** | **110** |

✅ 合法入库（按单入库）：

📦 物理箱01：
| 条码 | 数量 | 操作数 |
|-----|-----:|-----:|
| SKU01 | 30 | 30 |
| SKU02 | 60 | 65 |
| SKU03 | 10 | 15 |
| **合计** | **100** | **110** |

✅ 合法入库（按单入库）：

📦 物理箱01：
| 条码 | 数量 | 操作数 |
|-----|-----:|-----:|
| SKU01 | 30 | 0 |
| SKU02 | 60 | 110 |
| SKU03 | 10 | 0 |
| **合计** | **100** | **110** |

#### 场景2：按条码超量10%

> 开启超量开关、超量方式是“条码”、比例是10%，则装箱范围内的每个SKU都允许超出单据数的10%

```
超量开关：开启
超量方式：条码
超量比例：10%
串货开关：关闭
```

📄 单据明细：

| 条码 | 数量 |
|-----|-----:|
| SKU01 | 30 |
| SKU02 | 60 |
| SKU03 | 10 |
| **合计** | **100** |

✅ 合法入库（按单入库）：

📦 物理箱01：

| 条码 | 数量 | 剩余数 | 操作数 |
|-----|-----:|-----:|-----:|
| SKU01 | 30 | 33 | 33 |
| SKU02 | 60 | 66 | 66 |
| SKU03 | 10 | 11 | 11 |
| **合计** | **100** | **110** |

#### 场景3：串货（串色）

> 开启串货开关、串货规则是“串色”，则装箱时允许不在范围内但款号及尺码相同，颜色不同的条码进行入库，此时界面应按款号+尺码聚合，可展开查看明细

```
超量开关：关闭
串货开关：开启
串货规则：串色
```

📄 单据明细：

| 条码 | 款号 | 颜色 | 尺码 | 数量 |
|-----|------|------|------|-----:|
| SKU01 | P001 | 🔴红色 | XL | 20 |
| SKU02 | P001 | 🔵蓝色 | XL | 10 |
| SKU03 | P002 | ⚫黑色 | L | 30 |
| SKU04 | P003 | 🟢绿色 | M | 40 |
| **合计** | | | | **100** |

✅ 合法入库（按单入库）：

📦 物理箱01：

| 操作 | 款号 | 尺码 | 计划总数 | 剩余数 | 入库数 |
|-----|-----|-----|-----:|-----:|-----:|
| 👉展开 | P001 | XL | 30 | 30 | 20 |
| 👉展开 | P002 | L | 30 | 30 | 10 |
| 👉展开 | P003 | M | 40 | 40 | 30 |
| 合计 | | | **100** | **100** | **60** |

展开P001+XL：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU01 | P001 | 🔴红色 | XL | 20 | 5 | 计划内 |
| **SKU_EXT_01** | **P001** | **🟣紫色** | **XL** | **0** | **5** | **串色** |
| **SKU_EXT_02** | **P001** | **⚪白色** | **XL** | **0** | **5** | **串色** |
| **SKU_EXT_03** | **P001** | **🟡黄色** | **XL** | **0** | **5** | **串色** |

展开P002+L：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU03 | P002 | ⚫黑色 | L | 30 | 10 | 计划内 |

展开P003+M：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU04 | P003 | 🟢绿色 | M | 40 | 30 | 计划内 |


📦 物理箱02：

| 操作 | 款号 | 尺码 | 计划总数 | 剩余数 | 入库数 |
|-----|-----|-----|-----:|-----:|-----:|
| 👉展开 | P001 | XL | 30 | 10 | 10 |
| 👉展开 | P002 | L | 30 | 20 | 20 |
| 👉展开 | P003 | M | 40 | 10 | 10 |
| 合计 | | | **100** | **100** | **40** |

展开P001+XL：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU01 | P001 | 🔴红色 | XL | 20 | 2 | 计划内 |
| SKU02 | P001 | 🔵蓝色 | XL | 10 | 2 | 计划内 |
| **SKU_EXT_01** | **P001** | **🟣紫色** | **XL** | **0** | **2** | **串色** |
| **SKU_EXT_02** | **P001** | **⚪白色** | **XL** | **0** | **2** | **串色** |
| **SKU_EXT_03** | **P001** | **🟡黄色** | **XL** | **0** | **2** | **串色** |

展开P002+L：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU03 | P002 | ⚫黑色 | L | 30 | 3 | 计划内 |
| **SKU_EXT_04** | **P002** | **⚪白色** | **L** | **0** | **3** | **串色** |
| **SKU_EXT_05** | **P002** | **🟡黄色** | **L** | **0** | **4** | **串色** |
| **SKU_EXT_06** | **P002** | **🟢绿色** | **L** | **0** | **10** | **串色** |

展开P003+M：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU04 | P003 | 🟢绿色 | M | 40 | 10 | 计划内 |

#### 场景4：串货（串款）

> 开启串货开关、串货规则是“串款”，则装箱时允许不在范围内任意SKU进行入库，此时界面不应显示入库范围明细，而是显示当前入库范围的总数、剩余数、入库数及入库明细

```
超量开关：关闭
串货开关：开启
串货规则：串款
```

📄 单据明细：

| 条码 | 款号 | 颜色 | 尺码 | 数量 |
|-----|------|------|------|-----:|
| SKU01 | P001 | 🔴红色 | XL | 20 |
| SKU02 | P001 | 🔵蓝色 | XL | 10 |
| SKU03 | P002 | ⚫黑色 | L | 30 |
| SKU04 | P003 | 🟢绿色 | M | 40 |
| **合计** | | | | **100** |

✅ 合法入库（按单入库）：

📦 物理箱01：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU01 | P001 | 🔴红色 | XL | 20 | 5 | 计划内 |
| **SKU_EXT_01** | **P001** | **🟣紫色** | **XL** | **0** | **5** | **串款** |
| **SKU_EXT_02** | **P001** | **⚪白色** | **XL** | **0** | **5** | **串款** |
| **SKU_EXT_03** | **P001** | **🟡黄色** | **XL** | **0** | **5** | **串款** |

```
|-------------------------------------------------------------------|
|                                            |         |            |
|                       进度                  |  剩余数  |   入库数   |
|        [=====                       ] 20%  |   80    |  20 / 100  |
|                                            |         |            |
---------------------------------------------------------------------
```

📦 物理箱02：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU02 | P001 | 🔵蓝色 | XL | 10 | 5 | 计划内 |
| **SKU_EXT_04** | **P002** | **⚪白色** | **L** | **0** | **10** | **串款** |
| **SKU_EXT_05** | **P002** | **🟡黄色** | **L** | **0** | **10** | **串款** |
| **SKU_EXT_06** | **P002** | **🟢绿色** | **L** | **0** | **10** | **串款** |

```
|-------------------------------------------------------------------|
|                                            |         |            |
|                       进度                  |  剩余数  |   入库数   |
|        [===============             ] 55%  |   45    |  55 / 100  |
|                                            |         |            |
---------------------------------------------------------------------
```

📦 物理箱03：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU04 | P003 | 🟢绿色 | M | 40 | 40 | 计划内 |
| **SKU_EXT_01** | **P001** | **🟣紫色** | **XL** | **0** | **5** | **串款** |

```
|-------------------------------------------------------------------|
|                                            |         |            |
|                       进度                  |  剩余数  |   入库数   |
|        [============================] 100% |   0     | 100 / 100  |
|                                            |         |            |
---------------------------------------------------------------------
```

#### 场景5：按单超量10%，串货（串色）

> 开启超量开关、超量方式是“单据”、比例是10%，开启串货开关、串货规则是“串色”，则装箱时允许不在范围内但颜色相同的条码进行入库，最大装箱数量为范围数据中数量+（范围数据中数量*10%）

```
超量开关：开启
超量类型：按单
超量比例：10%
串货开关：开启
串货规则：串色
```

📄 单据明细：

| 条码 | 款号 | 颜色 | 尺码 | 数量 |
|-----|------|------|------|-----:|
| SKU01 | P001 | 🔴红色 | XL | 20 |
| SKU02 | P001 | 🔵蓝色 | XL | 10 |
| SKU03 | P002 | ⚫黑色 | L | 30 |
| SKU04 | P003 | 🟢绿色 | M | 40 |
| **合计** | | | | **100** |

✅ 合法入库（按单入库）：

📦 物理箱01：

| 操作 | 款号 | 尺码 | 计划总数 | 剩余数 | 入库数 |
|-----|-----|-----|-----:|-----:|-----:|
| 👉展开 | P001 | XL | 30 | 33 | 33 |
| 👉展开 | P002 | L | 30 | 33 | 33 |
| 👉展开 | P003 | M | 40 | 44 | 40 |
| 合计 | | | **100** | **110** | **106** |

展开P001+XL：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU01 | P001 | 🔴红色 | XL | 20 | 5 | 计划内 |
| SKU02 | P001 | 🔵蓝色 | XL | 10 | 5 | 计划内 |
| **SKU_EXT_01** | **P001** | **🟣紫色** | **XL** | **0** | **10** | **串色** |
| **SKU_EXT_02** | **P001** | **⚪白色** | **XL** | **0** | **10** | **串色** |
| **SKU_EXT_03** | **P001** | **🟡黄色** | **XL** | **0** | **3** | **串色** |

展开P002+L：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU03 | P002 | ⚫黑色 | L | 30 | 33 | 计划内 |

展开P003+M：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU04 | P003 | 🟢绿色 | M | 40 | 40 | 计划内 |

📦 物理箱02：

| 操作 | 款号 | 尺码 | 计划总数 | 剩余数 | 入库数 |
|-----|-----|-----|-----:|-----:|-----:|
| 👉展开 | P003 | M | 40 | 4 | 4 |
| 合计 | | | **100** | **4** | **4** |

展开P003+M：

| 条码  | 款号 | 颜色 | 尺码 | 原始计划数 | 入库数 | 类型 |
|-----|------|------|------|-----:|-----:|------|
| SKU04 | P003 | 🟢绿色 | M | 40 | 4 | 计划内 |