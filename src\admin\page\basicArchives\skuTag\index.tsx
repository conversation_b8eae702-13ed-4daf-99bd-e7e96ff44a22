import { DatePicker, Input, Space } from 'antd';
import * as RfidApi from 'common/api/core/Rfid';
import ExportButton from 'common/components/Button/Export';
import ImportButton from 'common/components/Button/Import';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import moment from 'moment';
import useSetting from 'common/hooks/useSetting';
import Tag from 'common/components/Tag';
import ExportModal from './components/ExportModal';
import ImportModal from './components/ImportModal';

const searchCheckKeys = ['createStart', 'createEnd', 'modifiedStart', 'modifiedEnd', 'partnerIdList', 'barcode', 'epc'];

const SkuTag: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [total, setTotal] = useState(0);
  const searchParams = useRef({});
  const [exportParams, setExportParams] = useState({});
  const [initialized, setInitialized] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);

  const [permission] = usePermission('A:LABEL:ST');
  const importPermission = permission.codes.includes('IMPORT');
  const ExportPermission = permission.codes.includes('EXPORT');

  const { DISABLE_RFID_IMPORT: disableRfidImport } = useSetting([
    { code: 'DISABLE_RFID_IMPORT', valueType: 'BOOLEAN', defaultValue: false },
  ]);

  const fetchTotal = async (params) => {
    try {
      const count: any = await RfidApi.ListCount(params);
      setTotal(count);
    } catch {}
  };

  const fetchData = useCallback(
    (params) => {
      if (params.created) {
        params.createStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      if (params.modified) {
        params.modifiedStart = params.modified[0].startOf('day');
        params.modifiedEnd = params.modified[1].endOf('day');
      }
      delete params.created;

      if (params.partnerIdList) {
        params.partnerIdList = [params.partnerIdList];
      }

      if (params.barcode) {
        params.barcode = params.barcode.trim();
      }
      if (params.epc) {
        params.epc = params.epc.trim();
      }

      const obj: Record<string, any> = {};
      Object.keys(params).forEach((key) => {
        if (searchCheckKeys.includes(key) && params[key]) {
          obj[key] = params[key];
        }
      });
      if (
        JSON.stringify(searchParams.current) !== JSON.stringify(obj) ||
        (initialized && Object.keys(obj).length === 0)
      ) {
        fetchTotal(obj);
        searchParams.current = obj;
        setInitialized(Object.keys(obj).length !== 0);
      }
      setExportParams(params);
      return RfidApi.FindByPage(params);
    },
    [initialized],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'epc',
      label: 'EPC',
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'barcode',
      label: i18n.t('global.barcode'),
      inputComponent: <Input />,
    },
    {
      name: 'partnerIdList',
      label: i18n.t('global.operatingPosition'),
      inputComponent: <PartnerSelect types={['SHOP', 'WAREHOUSE', 'FACTORY']} sourceType="PERMISSION" />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
    {
      name: 'modified',
      label: i18n.t('global.modified'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: 'EPC',
      dataIndex: 'epc',
      valueType: 'text',
      ellipsis: true,
      tooltip: true,
      sorter: true,
      auto: true,
      minWidth: 330,
    },
    {
      title: 'TID',
      dataIndex: 'tid',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 260,
      tooltip: true,
    },
    {
      title: i18n.t('global.uniqueCode'),
      dataIndex: 'unicode',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 180,
    },
    {
      title: i18n.t('global.gbCode'),
      dataIndex: 'gbCode',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 240,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 150,
    },
    {
      title: i18n.t('global.prodName'),
      dataIndex: 'prodName',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 180,
    },
    {
      title: i18n.t('global.positionStatus'),
      dataIndex: 'status',
      ellipsis: true,
      sorter: false,
      width: 150,
      render: (text, record) => {
        const statusMap = {
          INSTOCK: 'green',
          ONWAY: 'blue',
          SOLD: 'purple',
          LOST: 'red',
        };
        const curStatus = statusMap[record.status];
        return curStatus ? <Tag color={curStatus}>{record.statusDesc}</Tag> : '--';
      },
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 180,
    },
    {
      title: i18n.t('global.color'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.size'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.brand'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'brandCode',
      nameDataIndex: 'brandName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.operationSourceType'),
      dataIndex: 'sourcePartnerTypeDesc',
      valueType: 'text',
      ellipsis: true,
      // sorter: true,
      width: 350,
    },
    {
      title: i18n.t('global.operationSource'),
      ellipsis: true,
      sorter: true,
      width: 300,
      dataIndex: 'sourcePartnerCode',
      codeDataIndex: 'sourcePartnerCode',
      nameDataIndex: 'sourcePartnerName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.operatingPositionType'),
      dataIndex: 'partnerTypeDesc',
      valueType: 'text',
      ellipsis: true,
      // sorter: true,
      width: 250,
    },
    {
      title: i18n.t('global.operatingPosition'),
      ellipsis: true,
      sorter: true,
      width: 250,
      codeDataIndex: 'partnerCode',
      nameDataIndex: 'partnerName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.lastModified'),
      dataIndex: 'modified',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
  ];

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const searchPanelInitialValues = {
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    modified: [defaultSelectDate.startDate, defaultSelectDate.endDate],
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {importPermission && !disableRfidImport && (
              <ImportButton
                type="primary"
                onClick={() => {
                  setImportModalVisible(true);
                }}
              />
            )}
            {ExportPermission && <ExportButton onClick={() => setExportModalOpen(true)} />}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        searchPanelInitialValues={searchPanelInitialValues}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        simplePaginationRightToolBar={<span className="mr-2">{i18n.t('global.totalDataNum', { num: total })}</span>}
        autoLoad
        enableCache
        cacheKey="SKU_TAG"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: () => {
            setImportModalVisible(false);
          },
          maskClosable: false,
        }}
        onFinish={() => {
          powerTableRef.current?.load();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
      <ExportModal
        title={i18n.t('global.tag')}
        visible={exportModalOpen}
        total={total}
        paramsRecord={exportParams}
        onCancel={() => setExportModalOpen(false)}
      />
    </div>
  );
};
export default SkuTag;
