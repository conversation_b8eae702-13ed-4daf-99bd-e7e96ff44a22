export interface CurrentTypeWarehouseListItem {
  id: string;
  created: string;
  modified: string;
  code: string;
  name: string;
  disabled: boolean;
  remark: string;
  type: string;
  allowTakeStock: boolean;
  defaulted: boolean;
  partnerId: string;
  partnerCode: string;
  partnerName: string;
  permissionDataId?: string;
}
export interface CurrentTypePartnerListItem {
  id: string;
  created: string;
  modified: string;
  code: string;
  name: string;
  disabled: boolean;
  remark: string;
  type: string;
  typeDesc: string;
  contact: string;
  address: string;
  tel: string;
  mobile: string;
  enableRfid: boolean;
  returnTag: string;
  defReturnPartnerId: string;
  defReturnPartnerName: string;
  province: string;
  provinceName: string;
  city: string;
  cityName: string;
  town: string;
  townName: string;
  businessModelId: string;
  businessModelName: string;
  businessModelCode: string;
  enableAntiTheft: string;
  enableAntiTheftDesc: string;
  payConfig: string;
  payConfigDesc: string;
  children: CurrentTypeWarehouseListItem[];
}

export interface PartnerPermissionListItem {
  id: string;
  partnerCode: string;
  partnerDisabled: boolean;
  partnerId: string;
  partnerName: string;
  partnerType: string;
  partnerTypeDesc: string;
  warehouseCode: string;
  warehouseDisabled: boolean;
  warehouseId: string;
  warehouseName: string;
  warehouseType: string;
  warehouseTypeDesc: string;
}
