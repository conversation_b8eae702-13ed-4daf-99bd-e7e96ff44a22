import { DatePicker, Input } from 'antd';
import * as UniqueApi from 'common/api/core/Unique';
import ImportButton from 'common/components/Button/Import';
import PowerTable, { IPowerTableInnerRef, SearchFieldsConfig } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';
import ImportModal from './components/ImportModal';

const UniqueCode: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [permission] = usePermission('A:LABEL:UNIQUE');
  const importPermission = permission.codes.includes('IMPORT');

  const fetchData = useCallback(async (params: Record<string, any>) => {
    if (params.uniqueCode) {
      params.uniqueCode = params.uniqueCode.trim();
    }
    if (params.createDateRange) {
      params.createdStart = params.createDateRange[0].startOf('day');
      params.createdEnd = params.createDateRange[1].endOf('day');
    }
    delete params.createDateRange;
    let data: any = [];
    try {
      data = await UniqueApi.List(params);
    } catch (e) {}
    return data;
  }, []);

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'uniqueCode',
      label: i18n.t('global.uniqueCode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'barcode',
      label: i18n.t('global.barcode'),
      inputComponent: <Input />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const columns: any = [
    {
      title: i18n.t('global.uniqueCode'),
      dataIndex: 'uniqueCode',
      sorter: 'true',
      minWidth: 360,
      ellipsis: true,
    },
    {
      title: i18n.t('global.extractUniqueCode'),
      dataIndex: 'truncatedUniCode',
      sorter: 'true',
      minWidth: 360,
      ellipsis: true,
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      sorter: 'true',
      sort: true,
      minWidth: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      width: 200,
    },
  ];

  return (
    <div>
      <AppHeader toolbar={importPermission && <ImportButton onClick={() => setImportModalVisible(true)} />} />
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        // enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="SKU"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: () => {
            setImportModalVisible(false);
          },
          maskClosable: false,
        }}
        onOk={() => {
          powerTableRef.current?.load();
        }}
      />
    </div>
  );
};

export default UniqueCode;
