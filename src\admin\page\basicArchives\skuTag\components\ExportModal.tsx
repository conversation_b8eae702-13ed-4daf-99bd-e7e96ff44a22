import * as RfidApi from 'common/api/core/Rfid';
import Export, { templateItem } from 'common/components/Export';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';
import Modal from 'common/components/Modal';

interface ExportModalProps {
  title?: string;
  visible?: boolean;
  onCancel?: () => void;
  paramsRecord?: Record<string, any>;
  total?: number;
}

const ExportModal: React.FC<ExportModalProps> = (props) => {
  const { visible, onCancel, title, paramsRecord, total } = props;
  const [data, setData] = useState<Record<string, any>>();
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const template: templateItem[] = [
    { display: 'EPC', dataIndex: 'epc' },
    { display: 'TID', dataIndex: 'tid' },
    { display: i18n.t('global.uniqueCode'), dataIndex: 'unicode' },
    { display: i18n.t('global.gbCode'), dataIndex: 'gbCode' },
    { display: i18n.t('global.productCode'), dataIndex: 'productCode' },
    { display: i18n.t('global.prodName'), dataIndex: 'productName' },
    { display: i18n.t('global.barcode'), dataIndex: 'barcode' },
    { display: i18n.t('global.colorCode'), dataIndex: 'productColorCode' },
    { display: i18n.t('global.color'), dataIndex: 'productColorName' },
    { display: i18n.t('global.sizeCode'), dataIndex: 'productSizeCode' },
    { display: i18n.t('global.size'), dataIndex: 'productSizeName' },
    { display: i18n.t('global.brandCode'), dataIndex: 'brandCode' },
    { display: i18n.t('global.brand'), dataIndex: 'brandName' },
    { display: i18n.t('global.operationSourceType'), dataIndex: 'sourcePartnerTypeDesc' },
    { display: i18n.t('global.operationSource'), dataIndex: 'sourcePartnerName' },
    { display: i18n.t('global.operatingPositionType'), dataIndex: 'partnerTypeDesc' },
    { display: i18n.t('global.operatingPosition'), dataIndex: 'partnerName' },
    { display: i18n.t('global.isDisabled'), dataIndex: 'disabled' },
    { display: i18n.t('global.created'), dataIndex: 'created' },
    { display: i18n.t('global.lastModified'), dataIndex: 'modified' },
  ];

  const fetchData = (pageOrCursor?: number | string) =>
    new Promise((resolve, reject) => {
      if (!paramsRecord) return;
      if (abortController?.signal.aborted) {
        reject(new Error('Request cancelled'));
        return;
      }
      const params: any = { ...paramsRecord, currentPage: 1, pageSize: 1000 };
      if (typeof pageOrCursor === 'number') {
        params.currentPage = pageOrCursor;
      } else if (typeof pageOrCursor === 'string') {
        params.cursor = pageOrCursor;
      }
      // 兼容 total/hasNext
      let totalPages = 1;
      if (typeof total === 'number' && total > 0) {
        totalPages = Math.ceil(total / params.pageSize);
      }
      RfidApi.List(params)
        .then((res: any) => {
          if (abortController?.signal.aborted) {
            reject(new Error('Request cancelled'));
            return;
          }
          resolve({
            ...res,
            totalRecords: total,
            totalPages,
            hasNext: res.hasNext,
            nextCursor: res.nextCursor,
          });
          setData({
            ...res,
            currentPage: Number(res.currentPage),
            totalRecords: total,
            totalPages,
            hasNext: res.hasNext,
            nextCursor: res.nextCursor,
          });
        })
        .catch((err) => reject(err));
    });

  const modalOnCancel = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    if (onCancel) onCancel();
  };

  useEffect(() => {
    if (visible) {
      const controller = new AbortController();
      setAbortController(controller);
      fetchData();
    } else {
      if (abortController) {
        abortController.abort();
        setAbortController(null);
      }
      setData(undefined);
    }
    // eslint-disable-next-line
  }, [visible]);

  return (
    <Modal
      title={
        <>
          {i18n.t('global.export')} {' - '}
          {title}
        </>
      }
      open={visible}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      footer={null}
      transitionName=""
      maskTransitionName=""
    >
      <div className="flex h-80 items-center justify-center">
        <Export fileName={title} template={template} data={data} request={fetchData} />
      </div>
    </Modal>
  );
};

export default ExportModal;
