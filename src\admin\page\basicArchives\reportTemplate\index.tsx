import * as TemplateApi from 'common/api/report/Template';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import DataViewDrawer, { ITableParam } from './components/DataViewDrawer';

const permissionRootCodeMap = {
  factory: 'A:F:REPORT_TMP',
  warehouse: 'A:W:REPORT_TMP',
  shop: 'A:S:REPORT_TMP',
  sample: 'A:P:REPORT_TMP',
};

const ReportTemplate: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [businessData, setBusinessData] = useState([]);
  const [tableParam, setTableParam] = useState<ITableParam>({
    id: 0,
    barTenderTag: false,
  });
  const { type } = useParams();
  const [permission] = usePermission(permissionRootCodeMap[type as keyof typeof permissionRootCodeMap]);
  const managementPermission = permission.codes.includes('MANAGEMENT');

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      params.isPageable = false;
      if (type) {
        params.module = type.toUpperCase();
      }
      let result: any = { data: [] };
      try {
        result = await TemplateApi.BusinessList(params);
        setBusinessData(result.data);
      } catch (e) {}
      return result;
    },
    [type],
  );

  useEffect(() => {
    if (type) {
      powerTableRef.current?.load();
    }
  }, [type, fetchData]);

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.module'),
      // dataIndex: 'moduleDesc',
      width: 200,
      render: (record) => {
        if (record.module === 'SHOP') {
          return i18n.t('global.shop');
        }
        return record.moduleDesc;
      },
    },
    {
      title: i18n.t('global.businessType'),
      valueType: 'codeName',
      codeDataIndex: 'businessCode',
      nameDataIndex: 'businessName',
      sorter: (a, b) => {
        const aCode = a.businessCode;
        const bCode = b.businessCode;
        if (aCode < bCode) {
          return -1;
        }
        if (aCode > bCode) {
          return 1;
        }

        // names must be equal
        return 0;
      },
      width: 200,
    },
    {
      title: i18n.t('global.templateType'),
      dataIndex: 'businessTypeName',
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
    },
  ];

  return (
    <div>
      <AppHeader />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        defaultPageSize={20}
        refreshBtnVisible={false}
        pagination={false}
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
          dataSource: businessData,
          onRow: managementPermission
            ? (record) => ({
                onClick: () => {
                  setTableParam({
                    ...tableParam,
                    id: record.id,
                    businessCode: record.businessCode,
                    module: record.module,
                    businessTypeCode: record.businessTypeCode,
                  });
                  setDrawerOpen(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <DataViewDrawer
        drawerProps={{
          open: drawerOpen,
          width: 700,
          onClose: () => {
            setDrawerOpen(false);
          },
        }}
        tableParam={tableParam}
      />
    </div>
  );
};

export default ReportTemplate;
