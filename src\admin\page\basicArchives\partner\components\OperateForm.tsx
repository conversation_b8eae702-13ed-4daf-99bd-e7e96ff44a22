import { Col, Form, FormProps, Input, InputNumber, Row, Switch } from 'antd';
import BusinessModelSelect from 'common/components/Select/BusinessModelSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
// import ShopAntiTheftSelect from 'common/components/Select/ShopAntiTheftSelect';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useImperativeHandle, useRef } from 'react';
// import AreaCascader from 'common/components/Select/areaCascader';

export interface OperateFormInnerRef {
  /**
   * Focus code input
   */
  focusCodeInput(): void;
  /**
   * Focus name input
   */
  focusNameInput(): void;
}

interface OperateFormProps extends FormProps {
  /**
   * 类型
   */
  type: string;
  /**
   * “编码”是否只读
   */
  codeReadOnly?: boolean;
  /**
   * “允许盘点”是否可见
   */
  allowTakeStockVisible?: boolean;
  /**
   * “状态”是否可见
   */
  statusVisible?: boolean;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<OperateFormInnerRef | undefined>;
}

const extFieldFormFatherName = 'ext';

const OperateForm: React.FC<OperateFormProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { enableSampleServe } = globalState;
  const { type, codeReadOnly, statusVisible, allowTakeStockVisible, innerRef } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const extFields = globalState.extFields.filter((n) => n.type === 'PARTNER' && n.subType === type && !n.disabled);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useImperativeHandle(innerRef, () => ({
    focusCodeInput: () => {
      codeInputRef.current?.focus({ cursor: 'end' });
    },
    focusNameInput: () => {
      nameInputRef.current?.focus({ cursor: 'end' });
    },
  }));

  return (
    <Form
      layout="vertical"
      form={props.form}
      name={props.name}
      initialValues={{
        allowTakeStock: true,
        returnTag: false,
      }}
      onFinish={props.onFinish}
      onFinishFailed={props.onFinishFailed}
    >
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
            <Input ref={codeInputRef} maxLength={20} readOnly={codeReadOnly} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} maxLength={50} />
          </Form.Item>
        </Col>

        {(type === 'SHOP' || type === 'WAREHOUSE') && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.businessModel')} name="businessModelId">
              <BusinessModelSelect />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item label={i18n.t('global.contact')} name="contact">
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={i18n.t('global.tel')}>
            <Input.Group compact>
              <Form.Item
                noStyle
                name="telArea"
                rules={[
                  {
                    pattern: /^\d{1,3}$/,
                    message: i18n.t('global.areaError'),
                  },
                ]}
              >
                <Input prefix="+" style={{ width: '20%' }} maxLength={3} />
              </Form.Item>
              <Form.Item
                noStyle
                name="telNumber"
                rules={[
                  {
                    pattern: /^[\d-]{7,11}$/,
                    message: i18n.t('global.telError'),
                  },
                ]}
              >
                <Input style={{ width: '80%' }} />
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={i18n.t('global.mobile')}>
            <Input.Group compact>
              <Form.Item
                noStyle
                name="mobileArea"
                rules={[
                  {
                    pattern: /^\d{1,3}$/,
                    message: i18n.t('global.areaError'),
                  },
                ]}
              >
                <Input prefix="+" style={{ width: '20%' }} />
              </Form.Item>
              <Form.Item
                noStyle
                name="mobileNumber"
                rules={[
                  {
                    pattern: /^[\d-]{7,11}$/,
                    message: i18n.t('global.mobileError'),
                  },
                ]}
              >
                <Input style={{ width: '80%' }} />
              </Form.Item>
            </Input.Group>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label={i18n.t('global.longitude')}
                name="longitude"
                rules={[
                  {
                    pattern: /^[-]?((([0-9]|[1-9][0-9]|1[0-7][0-9])(\.\d+)?|180(\.0+)?))$/,
                    message: i18n.t('global.longitudeError'),
                  },
                ]}
              >
                <InputNumber className="w-full" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={i18n.t('global.latitude')}
                name="latitude"
                rules={[
                  {
                    pattern: /^[-]?([0-8]?\d(\.\d+)?|90(\.0+)?)$/,
                    message: i18n.t('global.latitudeError'),
                  },
                ]}
              >
                <InputNumber className="w-full" />
              </Form.Item>
            </Col>
          </Row>
        </Col>
        {/* <Col span={12}> */}
        {/*   <Form.Item label={i18n.t('global.area')} name="area"> */}
        {/*     <AreaCascader onChange={AreaCascaderOnChange} loadData={AreaCascaderLoadData} /> */}
        {/*   </Form.Item> */}
        {/* </Col> */}
        <Col span={24}>
          <Form.Item label={i18n.t('global.detailAddress')} name="address">
            <Input />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Col>
        {/* {(type === 'SHOP' || type === 'WAREHOUSE') && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.antiTheft')} name="enableAntiTheft">
              <ShopAntiTheftSelect />
            </Form.Item>
          </Col>
        )} */}
        {/* {(type === 'SHOP' || type === 'WAREHOUSE') && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.antiTheftSharedWarehouseStoreCode')} name="combinationCode">
              <Input />
            </Form.Item>
          </Col>
        )} */}
        {type === 'WAREHOUSE' && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.returnWarehouse')} valuePropName="checked" name="returnTag">
              <Switch />
            </Form.Item>
          </Col>
        )}
        {type === 'WAREHOUSE' && enableSampleServe && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.sampleWarehouse')} valuePropName="checked" name="sampleFlag">
              <Switch />
            </Form.Item>
          </Col>
        )}
        {type === 'SHOP' && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.defaultReturnWarehouse')} name="defReturnPartnerId">
              <PartnerSelect types={['WAREHOUSE']} params={{ returnTag: true }} />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item label={i18n.t('global.rfidTag')} valuePropName="checked" name="enableRfid">
            <Switch />
          </Form.Item>
        </Col>
        {/* 提示：是否允许盘点字段为“仓位”的字段，该值会传递至自动生成的仓位里 */}
        {allowTakeStockVisible && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.allowStocktaking')} valuePropName="checked" name="allowTakeStock">
              <Switch />
            </Form.Item>
          </Col>
        )}
        {statusVisible && (
          <Col span={12}>
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          </Col>
        )}

        {type !== 'SUPPLIER' && (
          <Col span={24}>
            <div className="rounded-lg border border-lead-light-bg">
              <div className="mb-4 inline-flex h-9 w-full items-start justify-start gap-2.5 rounded-tl-lg rounded-tr-lg bg-lead-light-bg px-3 py-2">
                <div className="shrink grow basis-0 text-sm font-semibold leading-tight text-slate-700">
                  {i18n.t('global.extendedProperties')} ({extFields.length})
                </div>
              </div>
              <Row gutter={24} className="px-3">
                {extFields.map((item: any) => {
                  if (item.fieldType === 'TXT' || item.fieldType === 'URL' || item.fieldType === 'IMG') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={[extFieldFormFatherName, item.code]}>
                          <Input />
                        </Form.Item>
                      </Col>
                    );
                  }
                  if (item.fieldType === 'NUM' || item.fieldType === 'AMT') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={[extFieldFormFatherName, item.code]}>
                          <InputNumber style={{ width: 150 }} />
                        </Form.Item>
                      </Col>
                    );
                  }
                  return null;
                })}
              </Row>
            </div>
          </Col>
        )}
      </Row>
    </Form>
  );
};

export default OperateForm;
