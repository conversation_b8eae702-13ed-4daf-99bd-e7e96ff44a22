import { Button, Space } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/file/File';
import * as Template<PERSON><PERSON> from 'common/api/report/Template';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import DownloadCloudLineIcon from 'common/assets/icons/icon-download-cloud-line.svg?react';
import InformationLineIcon from 'common/assets/icons/icon-information-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import ReportHelpModal from 'common/components/ReportHelpModal';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import { saveAs } from 'file-saver';
import React, { useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

// import DataViewModal from './components/DataViewModal';
import OperationDrawer from './components/OperationDrawer';
import { ITemplateItem } from './data';

const permissionRootCodeMap = {
  factory: 'A:F:LP_TMP',
  warehouse: 'A:W:LP_TMP',
  shop: 'A:S:LP_TMP',
  sample: 'A:P:LP_TMP',
};

const TemplateList: React.FC = () => {
  const { type } = useParams();
  const module = type?.toUpperCase();
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [operationDrawerOpen, setOperationDrawerOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [dataViewModalOpen, setDataViewModalOpen] = useState(false);
  const [current, setCurrent] = useState<Record<string, any> | undefined>(undefined);
  const [permission] = usePermission(permissionRootCodeMap[type as keyof typeof permissionRootCodeMap]);
  const managementPermission = permission.codes.includes('MANAGEMENT');

  const helpModalModule = useMemo(() => {
    if (module === 'SAMPLE') {
      return 'SAMPLE';
    }
    return 'CORE';
  }, [module]);

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await TemplateApi.Delete({ id: record.id });
          NoticeUtil.success();
          setOperationDrawerOpen(false);
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const addBtnOnClick = () => {
    setOperationDrawerOpen(true);
    setCurrent(undefined);
  };

  const fetchData = (params) => {
    const mod = module ? module.toUpperCase() : '';
    return TemplateApi.List({
      ...params,
      module: mod,
      barTenderTag: true,
    });
  };

  const operationDrawerOnSubmit = async (values: ITemplateItem) => {
    const id = current ? current.id : '';
    values.barTenderTag = true;
    if (module) {
      values.module = module.toUpperCase();
    }
    setConfirmLoading(true);
    try {
      if (id) {
        await TemplateApi.Update({
          ...current,
          ...values,
          templateId: id,
        });
      } else {
        values.name = values.name.trim();
        await TemplateApi.Create(values);
      }
      setConfirmLoading(false);
      setOperationDrawerOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const downBtnOnClick = async (record: Record<string, any>) => {
    const hide = NoticeUtil.processing();
    try {
      const res: any = await FileApi.Download('template', record.source);
      saveAs(res, record.source);
      hide();
    } catch (err) {
      hide();
    }
  };

  const operationDrawerOnClose = () => {
    setOperationDrawerOpen(false);
    setCurrent(undefined);
  };

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      width: 200,
    },
    {
      title: i18n.t('global.language'),
      dataIndex: 'language',
      width: 120,
      render: (text) => {
        if (text) {
          return text.includes('zh-CN') ? (
            <span title={text}>{i18n.t('global.chinese')}</span>
          ) : (
            <span title={text}>English</span>
          );
        }
        return <span>--</span>;
      },
    },
    {
      title: i18n.t('global.spec'),
      dataIndex: 'spec',
      width: 150,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (managementPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.download'),
      icon: <DownloadCloudLineIcon className="fill-lead-dark" />,
      onClick: (record) => {
        downBtnOnClick(record);
      },
    });

    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {managementPermission && (
              <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        rightToolbar={
          <Button
            icon={<InformationLineIcon className="fill-lead-dark" />}
            onClick={() => {
              setDataViewModalOpen(true);
            }}
          />
        }
        columns={tableColumns}
        innerRef={powerTableRef}
        defaultPageSize={20}
        settingToolVisible
        pagination={false}
        autoLoad
        tableProps={{
          sticky: true,
          onRow: managementPermission
            ? (record) => ({
                onClick: () => {
                  setCurrent(record);
                  setOperationDrawerOpen(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <OperationDrawer
        open={operationDrawerOpen}
        confirmLoading={confirmLoading}
        current={current}
        onSubmit={operationDrawerOnSubmit}
        onDelete={deleteBtnOnClick}
        onClose={operationDrawerOnClose}
      />
      <ReportHelpModal
        open={dataViewModalOpen}
        tableParam={{
          businessType: 'TP',
          module: helpModalModule,
          templateType: 'TAG',
        }}
        onCancel={() => {
          setDataViewModalOpen(false);
        }}
      />
    </div>
  );
};

export default TemplateList;
