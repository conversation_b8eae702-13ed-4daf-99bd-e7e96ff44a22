import { faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AddDictDataDrawer from 'admin/page/basicArchives/other/dict/components/AddDictDataDrawer';
import EditDictDataDrawer from 'admin/page/basicArchives/other/dict/components/EditDictDataDrawer';
import { Button } from 'antd';
import * as DictDataApi from 'common/api/core/DictData';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import RefreshButton from 'common/components/Button/Refresh';
import DisabledTrigger from 'common/components/DisabledTrigger';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

export interface DictDataModalPropsRef {
  recover: () => void;
}
interface DictDataModalProps {
  current: Record<string, any>;
  innerRef?: React.MutableRefObject<DictDataModalPropsRef | undefined>;
}

const DictDataModal: React.FC<DictDataModalProps> = (props) => {
  const { current, innerRef } = props;
  const [currentDictData, setCurrentDictData] = useState<Record<string, any>>({});
  const [addDictDataModalVisible, setAddDictDataModalVisible] = useState(false);
  const [editDictDataModalVisible, setEditDictDataModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<Array<any>>([]);
  const [permission] = usePermission('A:BASE:OTHER:B:DICT');
  const detPermission = permission.codes.includes('DET');
  const [language] = useState<string | null>(LocalStorageUtil.getItem('LOCALE'));
  const [disabled, setDisabled] = useState<boolean | null>(null);

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(async () => {
    let result: Array<any> = [];
    setLoading(true);
    try {
      const params: Record<string, any> = {
        dictId: current?.id,
      };
      if (typeof disabled === 'boolean') {
        params.disabled = disabled;
      }
      const resp = await DictDataApi.List(params);

      result = resp.data;
      setTableData(result);
    } catch (e) {}
    setLoading(false);
  }, [current?.id, disabled]);

  useEffect(() => {
    fetchData();
  }, [fetchData, current?.id]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line
  }, [disabled]);

  const getDataLanguage = (values: Record<string, any>) => {
    const obj = {
      'zh-CN': values.name,
      'en-US': values.i18nName,
    };
    return JSON.stringify(obj);
  };

  const editDictDataModalOnOk = async (values: Record<string, any>) => {
    values.code = currentDictData.code;
    if (values.i18nName) {
      values.i18nName = getDataLanguage(values);
    }
    if (typeof values.status === 'boolean') {
      values.disabled = !values.status;
      delete values.status;
    }
    setUpdating(true);
    try {
      await DictDataApi.Update(values);
      setEditDictDataModalVisible(false);
      NoticeUtil.success();
      await fetchData();
    } catch (e) {}
    setUpdating(false);
  };

  const editDictDataModalOnCancel = () => {
    setEditDictDataModalVisible(false);
  };

  const editBtnOnClick = (record: Record<string, any>) => {
    const values = { ...record };
    if (language && record.i18nName) {
      const obj = JSON.parse(values.i18nName);
      values.i18nName = obj[language];
    }
    setCurrentDictData(values);
    setEditDictDataModalVisible(true);
  };

  const addDictDataModalOnOk = async (values: Record<string, any>) => {
    setUpdating(true);
    if (values.i18nName) {
      values.i18nName = getDataLanguage(values);
    }
    if (typeof values.status === 'boolean') {
      values.disabled = !values.status;
      delete values.status;
    }
    values.dictId = current?.id;
    try {
      await DictDataApi.Create(values);
      setAddDictDataModalVisible(false);
      NoticeUtil.success();
      await fetchData();
    } catch (e) {}
    setUpdating(false);
  };

  const addDictDataModalOnCancel = () => {
    setAddDictDataModalVisible(false);
  };

  const addDictDataBtnOnClick = () => {
    setAddDictDataModalVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await DictDataApi.Delete({ ids: [record.id] });
          NoticeUtil.success();
          setEditDictDataModalVisible(false);
          await fetchData();
        } catch (e) {}
      },
    });
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchData();
    },
  }));

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      ellipsis: true,
      defaultSortOrder: 'ascend',
      sorter: (a, b) => {
        const aVal = a.code.toUpperCase();
        const bVal = b.code.toUpperCase();
        if (aVal < bVal) {
          return -1;
        }
        if (aVal > bVal) {
          return 1;
        }
        return 0;
      },
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      ellipsis: true,
      width: 200,
      sorter: (a, b) => {
        const aVal = a.name.toUpperCase();
        const bVal = b.name.toUpperCase();
        if (aVal < bVal) {
          return -1;
        }
        if (aVal > bVal) {
          return 1;
        }
        return 0;
      },
      render: (value, record) => {
        if (language && record.i18nName) {
          const i18nName = JSON.parse(record.i18nName);
          return i18nName[language];
        }
        return record.name;
      },
    },
    {
      title: i18n.t('global.sort'),
      dataIndex: 'sort',
      valueType: 'number',
      width: 100,
      sorter: (a, b) => a.sort - b.sort,
    },
    {
      title: i18n.t('global.default'),
      dataIndex: 'defaultTag',
      sorter: (a, b) => a.defaultTag - b.defaultTag,
      width: 100,
      valueType: 'boolean',
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (detPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      className: 'text-lead-red hover:border-lead-red focus:border-lead-red',
      icon: <FontAwesomeIcon icon={faTrashCan} />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        defaultPageSize={20}
        refreshBtnVisible={false}
        autoLoad
        tableProps={{
          dataSource: tableData,
          loading,
          onRow: (record) => ({
            onClick: () => {
              editBtnOnClick(record);
            },
          }),
        }}
        leftToolbar={[
          detPermission && (
            <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addDictDataBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        rightToolbar={[
          <DisabledTrigger onChange={(val) => setDisabled(val)} />,
          <RefreshButton onClick={() => fetchData()} />,
        ]}
      />
      <EditDictDataDrawer
        visible={editDictDataModalVisible}
        current={currentDictData}
        confirmLoading={updating}
        onOk={editDictDataModalOnOk}
        onDelete={deleteBtnOnClick}
        onCancel={editDictDataModalOnCancel}
      />
      <AddDictDataDrawer
        visible={addDictDataModalVisible}
        confirmLoading={updating}
        onOk={addDictDataModalOnOk}
        onCancel={addDictDataModalOnCancel}
      />
    </>
  );
};

export default DictDataModal;
