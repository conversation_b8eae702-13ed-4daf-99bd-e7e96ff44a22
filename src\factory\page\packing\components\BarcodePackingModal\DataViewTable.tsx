import { ColDef } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { Row, Tooltip } from 'antd';
import classNames from 'classnames';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import i18n from 'common/utils/I18n';
import React, { useCallback, useMemo } from 'react';
import InformationFillIcon from 'common/assets/icons/icon-information-fill.svg?react';

import { ITableDataLineItem } from './data';

interface IDataViewTableProps {
  leftTBar?: React.ReactNode;
  data?: any[];
  className?: string;
  /** 是否显示错误数和超数列 */
  showErrorColumns?: boolean;
}

const DataViewTable: React.FC<IDataViewTableProps> = (props) => {
  const { data, leftTBar, className, showErrorColumns } = props;

  const wrapRef = React.useRef<HTMLDivElement>(null);

  const colDefs = useMemo(() => {
    const result: ColDef<ITableDataLineItem>[] = [
      {
        field: 'barcode',
        headerName: i18n.t('global.barcode'),
        minWidth: 150,
        flex: 1,
      },
      {
        field: 'prodCode',
        headerName: i18n.t('global.productCode'),
        width: 160,
        cellRenderer: (params) => (
          <ProdCodeRowText prodCode={params.value} rfidTag={params.data.rfidTag} disturbTag={params.data.disturbTag} />
        ),
      },
      {
        field: 'skuName',
        headerName: i18n.t('global.name'),
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'colorName',
        headerName: i18n.t('global.colors'),
        width: 130,
      },
      {
        field: 'sizeName',
        headerName: i18n.t('global.sizes'),
        width: 130,
      },
      {
        field: 'specName',
        headerName: i18n.t('global.spec'),
        width: 150,
      },
      {
        field: 'qty',
        headerName: i18n.t('global.orderCount'),
        width: 100,
        headerClass: 'text-right',
        cellClass: 'text-right',
        type: 'numericColumn',
      },
      {
        field: 'availableQty',
        headerName: i18n.t('global.availableQty'),
        width: 100,
        headerClass: 'text-right',
        cellClass: 'text-right text-lead-green',
        type: 'numericColumn',
      },
      {
        field: 'operateQty',
        headerName: i18n.t('global.operateQty'),
        width: 100,
        headerClass: 'text-right',
        cellClass: (params) => {
          const baseClass = 'text-right';
          return params.value > 0 ? `${baseClass} text-lead-green` : baseClass;
        },
        type: 'numericColumn',
      },
    ];

    // 条件性添加错误数和超数列
    if (showErrorColumns) {
      result.push(
        {
          field: 'overQty',
          headerName: i18n.t('global.excessQty'),
          width: 100,
          cellClass: 'flex justify-end items-center',
          cellRenderer: (record) => {
            if (record.value > 0) {
              return (
                <Tooltip placement="topRight" title={i18n.t('global.exceptDealTip')}>
                  <div className="flex items-center gap-1">
                    <InformationFillIcon className="mt-[-2px] h-6 w-6 cursor-pointer fill-lead-orange" />
                    <span className="text-lead-orange">{record.value}</span>
                  </div>
                </Tooltip>
              );
            }
            return null;
          },
        },
        {
          field: 'errorQty',
          headerName: i18n.t('global.exceptQty'),
          width: 100,
          cellClass: 'flex justify-end items-center',
          cellRenderer: (record) => {
            if (record.value > 0) {
              return (
                <Tooltip placement="topRight" title={i18n.t('global.exceptDealTip')}>
                  <div className="flex items-center gap-1">
                    <InformationFillIcon className="mt-[-2px] h-6 w-6 cursor-pointer fill-lead-red" />
                    <span className="text-lead-red">{record.value}</span>
                  </div>
                </Tooltip>
              );
            }
            return null;
          },
        },
      );
    }

    return result;
  }, [showErrorColumns]);

  const getRowId = useCallback((params) => params.data.barcode, []);

  return (
    <div className={classNames(className, 'flex flex-col')}>
      <Row justify="space-between" align="middle" className="mb-4">
        {leftTBar}
      </Row>
      <div ref={wrapRef} className={classNames('ag-theme-quartz h-full w-full flex-auto')}>
        <AgGridReact
          enableCellTextSelection
          getRowId={getRowId}
          ensureDomOrder
          rowData={data}
          columnDefs={colDefs}
          defaultColDef={{
            sortable: true,
            resizable: true,
          }}
        />
      </div>
    </div>
  );
};

export default DataViewTable;
