import { DatePicker } from 'antd';
import * as BwcbOrderApi from 'common/api/warehouse/BwcbOrder';
import * as WcbOrderApi from 'common/api/warehouse/WcbOrder';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import Spin from 'common/components/Spin';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment/moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePermission } from 'common/utils/Permission';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import { useCommonFn } from './common';

const WcbOrder: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [initialized, setInitialized] = useState(false);
  const navigate = useNavigate();

  const [permission] = usePermission('A:W:CB');
  const confirmPermission = permission.codes.includes('CONFIRM');

  const { confirm } = useCommonFn();

  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([
    { code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER', defaultValue: 7 },
  ]);

  const fetchData = useCallback(
    async (params: any, tabActiveKey: string) => {
      const payload = {
        ...params,
      };

      if (payload.createDateRange) {
        payload.createdStart = payload.createDateRange[0].startOf('day');
        payload.createdEnd = payload.createDateRange[1].endOf('day');
      }
      delete payload.createDateRange;

      if (!payload.code) delete payload.code;

      let result: any;
      switch (tabActiveKey) {
        case 'ALL':
          payload.status = ['NEW', 'PROCESSING', 'FINISHED'];
          break;
        case 'NEW':
          payload.status = ['NEW'];
          break;
        case 'PROCESSING':
          payload.status = ['PROCESSING'];
          break;
        case 'FINISHED':
          payload.status = ['FINISHED'];
          break;
        default:
          break;
      }

      if (currentUser.mode === 'BINDING') {
        result = await BwcbOrderApi.List(payload);
      } else {
        result = await WcbOrderApi.List(payload);
      }
      return result;
    },
    [currentUser],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      inputComponent: <SearchInput placeholder={i18n.t('global.orderCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    try {
      await confirm(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      width: 200,
      fixed: 'left',
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PROCESSING: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.reviewStatus'),
      dataIndex: 'reviewStatus',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NOT_REVIEWED: 'slate',
                REVIEWED: 'green',
              }[record.reviewStatus]
            }
          >
            {record.reviewStatusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.syncStatus'),
      dataIndex: 'syncStatus',
      valueType: 'codeName',
      codeDataIndex: 'syncStatus',
      nameDataIndex: 'syncStatusDesc',
      width: 150,
    },
    {
      title: i18n.t('global.syncVersion'),
      dataIndex: 'syncVer',
      valueType: 'text',
      sorter: true,
      width: 150,
    },
    {
      title: i18n.t('global.archived'),
      dataIndex: 'archived',
      valueType: 'boolean',
      sorter: true,
      width: 150,
    },
    {
      title: i18n.t('global.boxQty'),
      dataIndex: 'boxQty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.remark'),
      valueType: 'text',
      dataIndex: 'remark',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      auto: true,
      minWidth: 150,
    },
    {
      title: i18n.t('global.makeDate'),
      dataIndex: 'makeDate',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (confirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-orange" />,
      isDisabled: (record) => !(record.status === 'PROCESSING'),
      onClick: (record) => {
        confirmBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  return (
    <div>
      {initialized ? (
        <>
          <AppHeader />
          <PowerTable
            innerRef={powerTableRef}
            rowKey="id"
            columns={columns}
            quickSearchFieldsConfig={quickSearchFieldsConfig}
            searchFieldsConfig={searchFieldsConfig}
            refreshBtnVisible
            searchPanelVisible={false}
            searchPanelInitialValues={{
              createDateRange: [defaultSelectDate.startDate, defaultSelectDate.endDate],
            }}
            searchPanelCollapsible
            defaultPageSize={10}
            pagination
            autoLoad
            enableCache
            cacheKey="WCB_ORDER"
            tableProps={{
              sticky: {
                offsetHeader: 96,
              },
              onRow: (record) => ({
                onClick: () => {
                  navigate(`/app/wcb/${record.id}`);
                },
              }),
            }}
            tabStatus={[
              {
                code: 'ALL',
                name: i18n.t('global.all'),
              },
              { code: 'NEW', name: i18n.t('global.newOrder') },
              {
                code: 'PROCESSING',
                name: i18n.t('global.processing'),
              },
              {
                code: 'FINISHED',
                name: i18n.t('global.finished'),
              },
            ]}
            defaultSorter={{ field: 'created', order: 'DESCEND' }}
            request={fetchData}
          />
        </>
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default WcbOrder;
