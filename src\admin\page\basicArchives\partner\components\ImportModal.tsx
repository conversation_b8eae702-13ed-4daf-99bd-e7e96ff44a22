import { ModalProps } from 'antd/es/modal';
import * as PartnerApi from 'common/api/core/Partner';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useMemo, useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onOk } = props;
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { enableSampleServe } = globalState;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [customErrorMessage, setCustomErrorMessage] = useState<string>('');

  const template: TemplateItem[] = useMemo(() => {
    const result: TemplateItem[] = [
      {
        dataIndex: 'code',
        display: i18n.t('global.code'),
        type: 'STRING',
        required: true,
      },
      {
        dataIndex: 'name',
        display: i18n.t('global.name'),
        type: 'STRING',
        required: true,
      },
      {
        dataIndex: 'contact',
        display: i18n.t('global.contact'),
        type: 'STRING',
      },
      {
        dataIndex: 'tel',
        display: i18n.t('global.tel'),
        type: 'STRING',
      },
      {
        dataIndex: 'mobile',
        display: i18n.t('global.mobile'),
        type: 'STRING',
      },
      {
        dataIndex: 'combinationCode',
        display: i18n.t('global.antiTheftSharedWarehouseStoreCode'),
        type: 'STRING',
      },
      // {
      //   dataIndex: 'province',
      //   display: i18n.t('global.province'),
      //   type: 'STRING',
      //   remark: <Link to="/app/area">{i18n.t('global.queryArea')}</Link>,
      // },
      // {
      //   dataIndex: 'city',
      //   display: i18n.t('global.city'),
      //   type: 'STRING',
      //   remark: <Link to="/app/area">{i18n.t('global.queryArea')}</Link>,
      // },
      // {
      //   dataIndex: 'town',
      //   display: i18n.t('global.town'),
      //   type: 'STRING',
      //   remark: <Link to="/app/area">{i18n.t('global.queryArea')}</Link>,
      // },
      {
        dataIndex: 'address',
        display: i18n.t('global.detailAddress'),
        type: 'STRING',
      },
      {
        dataIndex: 'longitude',
        display: i18n.t('global.longitude'),
        type: 'STRING',
      },
      {
        dataIndex: 'latitude',
        display: i18n.t('global.latitude'),
        type: 'STRING',
      },
      {
        dataIndex: 'remark',
        display: i18n.t('global.remark'),
        type: 'STRING',
      },
      {
        dataIndex: 'returnTag',
        display: i18n.t('global.returnWarehouse'),
        type: 'BOOLEAN',
      },
      {
        dataIndex: 'disabled',
        display: i18n.t('global.isDisabled'),
        type: 'BOOLEAN',
        required: true,
      },
      {
        dataIndex: 'type',
        display: i18n.t('global.partnerType'),
        type: 'STRING',
        required: true,
        remark: [i18n.t('global.warehouse'), i18n.t('global.shop'), i18n.t('global.factory')].join(', '),
      },
    ];
    if (enableSampleServe) {
      result.splice(9, 0, {
        dataIndex: 'sampleFlag',
        display: i18n.t('global.sampleWarehouse'),
        type: 'BOOLEAN',
      });
    }
    return result;
  }, [enableSampleServe]);

  const onCheckLongitude = (value) => {
    const parsedValue = parseFloat(value);
    if (Number.isNaN(parsedValue)) {
      return true;
    }
    if (parsedValue < -180 || parsedValue > 180) {
      return true;
    }
    return false;
  };

  const onCheckLatitude = (value) => {
    const parsedValue = parseFloat(value);
    if (Number.isNaN(parsedValue)) {
      return true;
    }
    if (parsedValue < -90 || parsedValue > 90) {
      return true;
    }
    return false;
  };

  const onImport = async (data) => {
    setCustomErrorMessage('');

    const errorList: string[] = [];
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item, index) => {
      switch (item.type) {
        case i18n.t('global.shop'):
          item.type = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.type = 'WAREHOUSE';
          break;
        case i18n.t('global.factory'):
          item.type = 'FACTORY';
          break;
        case i18n.t('global.supplier'):
          item.type = 'SUPPLIER';
          break;
        case i18n.t('global.logistics'):
          item.type = 'LOGISTICS';
          break;
        default:
          break;
      }

      if (onCheckLongitude(item.longitude)) {
        errorList.push(
          `${i18n.t('global.importRowError', { num: index + 1, errNum: 1 })}, ${i18n.t('global.longitudeError')}`,
        );
      }
      if (onCheckLatitude(item.latitude)) {
        errorList.push(
          `${i18n.t('global.importRowError', { num: index + 1, errNum: 1 })}, ${i18n.t('global.latitudeError')}`,
        );
      }
    });

    if (errorList.length > 0) {
      setCustomErrorMessage(errorList.join('\n'));
      setProgressStatus('exception');
      return;
    }

    try {
      setProgressPercent(0);
      await PartnerApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e: any) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  return (
    <Modal
      width={960}
      title={i18n.t('global.importPartner')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        customErrorMessage={customErrorMessage}
        moduleName={i18n.t('global.partner')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
