import { ProdContext, TProdContext } from 'admin/page/basicArchives/prod/reducer';
import { Col, Form, Input, InputNumber, Row, Switch } from 'antd';
import { FormProps } from 'antd/lib/form';
import * as <PERSON>ku<PERSON><PERSON> from 'common/api/core/Sku';
import * as SkuImgApi from 'common/api/core/SkuImg';
import ImgUpload from 'common/components/ImgUpload/index';
import ColorSelect from 'common/components/Select/ColorSelect';
import SizeSelect from 'common/components/Select/SizeSelect';
import SpecSelect from 'common/components/Select/SpecSelect';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useState } from 'react';

import { ISkuItem } from '../data';

export interface OperationFormPropsRef {
  recover: () => void;
}
interface OperationFormProps extends FormProps {
  current?: Partial<ISkuItem> | undefined;
  statusVisible?: boolean;
  innerRef?: React.MutableRefObject<OperationFormPropsRef | undefined>;
}

const SkuOperateForm: React.FC<OperationFormProps> = (props) => {
  const { statusVisible, current, innerRef, ...formProps } = props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const { state } = useContext<TProdContext>(ProdContext);
  const [defaultImgId, setDefaultImgId] = useState<string>();
  const [imgList, setImgList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Record<string, any>>({});

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const fetchSku = useCallback(async () => {
    if (!current) return;
    setLoading(true);
    try {
      const res: any = await SkuApi.Get({ id: current.id });
      setCurrentRecord(res);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [current]);

  const handleDefaultImg = async (id) => {
    try {
      await SkuImgApi.SetDefault({ id });
      setDefaultImgId(id);
    } catch (e) {}
  };

  const fetchImgList = useCallback(async () => {
    const res = await SkuImgApi.List({
      prodIds: [state.prodRecord?.id],
    });
    setImgList(res.data.filter((n) => n.skuId === current?.id));
  }, [state.prodRecord, current?.id]);

  const handleSaveImg = async (source) => {
    try {
      const data: any = [
        {
          source,
          prodId: state.prodRecord?.id,
          skuId: current?.id,
        },
      ];
      await SkuImgApi.Save({ data });
      fetchImgList();
    } catch (e) {}
  };

  const handleDeleteImg = async (id) => {
    try {
      await SkuImgApi.Delete({ ids: [id] });
      fetchImgList();
    } catch (e) {}
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchSku();
      fetchImgList();
    },
  }));

  useEffect(() => {
    fetchImgList();
  }, [fetchImgList]);

  useEffect(() => {
    if (typeof currentRecord.disabled === 'boolean') {
      currentRecord.status = !currentRecord.disabled;
    }
    form.setFieldsValue(currentRecord);
  }, [currentRecord, form]);

  useEffect(() => {
    if (current) {
      fetchSku();
    }
  }, [current, fetchSku]);

  return (
    <Spin spinning={loading}>
      <Form form={form} {...formProps} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="barcode" label={i18n.t('global.barcode')} rules={canNotBeNullRules}>
              <Input readOnly={!!current} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
              {/* <Input ref={(input) => setNameInputRef(input)} /> */}
              <Input />
            </Form.Item>
          </Col>
          {!state.prodRecord?.disableColor && (
            <Col span={12}>
              <Form.Item label={i18n.t('global.color')} name="colorId" rules={canNotBeNullRules}>
                <ColorSelect grpid={state.prodRecord ? state.prodRecord.colorGrpId : undefined} />
              </Form.Item>
            </Col>
          )}
          {!state.prodRecord?.disableSize && (
            <Col span={12}>
              <Form.Item label={i18n.t('global.size')} name="sizeId" rules={canNotBeNullRules}>
                <SizeSelect grpid={state.prodRecord ? state.prodRecord.sizeGrpId : undefined} />
              </Form.Item>
            </Col>
          )}
          {!state.prodRecord?.disableSpec && (
            <Col span={12}>
              <Form.Item label={i18n.t('global.spec')} name="specId">
                <SpecSelect grpid={state.prodRecord ? state.prodRecord.specGrpId : undefined} />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item label={i18n.t('global.barcodeRetailPrice')} name="retailPrice" rules={canNotBeNullRules}>
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.barcodeTagPrice')} name="tagPrice" rules={canNotBeNullRules}>
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.weight')} name="weight">
              <InputNumber addonAfter={i18n.t('global.gram')} min={0} />
            </Form.Item>
          </Col>
          {statusVisible && (
            <Col span={12}>
              <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
                <Switch />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          {current && (
            <Col span={24}>
              <Form.Item label={i18n.t('global.skuImage')}>
                <ImgUpload
                  defaultImg
                  handleDefaultImg={handleDefaultImg}
                  handleSaveImg={handleSaveImg}
                  handleDeleteImg={handleDeleteImg}
                  value={imgList}
                  defaultImgId={defaultImgId}
                />
              </Form.Item>
            </Col>
          )}
        </Row>
      </Form>
    </Spin>
  );
};

export default SkuOperateForm;
