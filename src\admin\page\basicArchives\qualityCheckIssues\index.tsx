import AppHeader from 'common/layout/AppHeader';
import { Button, Input } from 'antd';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import * as FqcIssuesApi from 'common/api/factory/FqcIssues';
import * as BfqcIssuesApi from 'common/api/factory/BfqcIssues';

import ImportButton from 'common/components/Button/Import';
import AddGroupDrawer from './components/AddGroupDrawer';
import AddIssuesDrawer from './components/AddIssuesDrawer';
import GroupMenu from './components/GroupMenu';
import EditGroupDrawer from './components/EditGroupDrawer';
import EditIssuesDrawer from './components/EditIssuesDrawer';
import ImportModal from './components/ImportModal';

const QualityCheckIssues: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:BASE:ISSUES');
  const groupCreatePermission = permission.codes.includes('GROUPCREATE');
  const issuesCreatePermission = permission.codes.includes('ISSUESCREATE');
  const issuesEditPermission = permission.codes.includes('ISSUESEDIT');
  const issuesDeletePermission = permission.codes.includes('ISSUESDELETE');
  const importPermission = permission.codes.includes('IMPORT');

  const [groupList, setGroupList] = useState([]);
  const [groupCode, setGroupCode] = useState('');

  const [importModalVisible, setImportModalVisible] = useState(false);

  const [addGroupDrawerOpen, setAddGroupDrawerOpen] = useState(false);
  const [addGroupDrawerSaving, setAddGroupDrawerSaving] = useState(false);

  const [addIssuesDrawerOpen, setAddIssuesDrawerOpen] = useState(false);
  const [addIssuesDrawerSaving, setAddIssuesDrawerSaving] = useState(false);

  const selectGroupItem = useRef<any>();
  const [editGroupDrawerVisible, setEditGroupDrawerVisible] = useState(false);

  const selectIssuseItem = useRef<any>();
  const [editIssuesDrawerVisible, setEditIssuesDrawerVisible] = useState(false);

  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const getGroupData = async () => {
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfqcIssuesApi.GroupList({ enablePage: false });
      } else {
        res = await FqcIssuesApi.GroupList({ enablePage: false });
      }

      setGroupList(res?.data);
      if (groupCode && groupCode === res?.data?.[0]?.code) {
        powerTableRef.current?.load();
      } else setGroupCode(res?.data?.[0]?.code);
    } catch (err) {}
  };

  const deleteIssuesBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        const payload = {
          ids: [record.id],
        };
        try {
          if (currentUser.mode === 'BINDING') {
            await BfqcIssuesApi.Del(payload);
          } else {
            await FqcIssuesApi.Del(payload);
          }

          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const fetchIssuesData = useCallback(
    (params) => {
      if (!groupCode) {
        return new Promise<any>((resolve) => {
          resolve({ data: [] });
        });
      }

      const item: any = groupList.find((item: any) => item.code === groupCode);

      if (currentUser.mode === 'BINDING') {
        return BfqcIssuesApi.List({ ...params, fqcProblemGroupId: item.id });
      }

      return FqcIssuesApi.List({ ...params, fqcProblemGroupId: item.id });
    },
    [currentUser.mode, groupCode, groupList],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      width: 200,
      ellipsis: true,
      sorter: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (issuesDeletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteIssuesBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);
  const addBtnOnClick = () => {
    setAddGroupDrawerOpen(true);
  };

  const addIssuesDrawerOnSubmit = async (values) => {
    if (!groupCode) return;

    const item: any = groupList.find((item: any) => item.code === groupCode);

    try {
      setAddGroupDrawerSaving(true);
      const payload = {
        ...values,
        fqcProblemGroupId: item.id,
      };

      if (currentUser.mode === 'BINDING') {
        await BfqcIssuesApi.Create(payload);
      } else {
        await FqcIssuesApi.Create(payload);
      }

      setAddIssuesDrawerOpen(false);
      setAddIssuesDrawerSaving(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAddGroupDrawerSaving(false);
  };
  const addIssuesDrawerOnCancel = () => {
    setAddIssuesDrawerOpen(false);
  };

  const onGroupMenuDelClick = (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BfqcIssuesApi.GroupDel({ ids: [record.id] });
          } else {
            await FqcIssuesApi.GroupDel({ ids: [record.id] });
          }

          NoticeUtil.success();
          getGroupData();
        } catch (err) {}
      },
    });
  };

  const onGroupMenuChangeClick = (value) => {
    selectGroupItem.current = value;
    setEditGroupDrawerVisible(true);
  };

  const addGroupDrawerOnSubmit = async (values) => {
    try {
      setAddGroupDrawerSaving(true);
      const payload = values;
      if (typeof payload.disabled !== 'boolean') {
        payload.disabled = false;
      }

      if (currentUser.mode === 'BINDING') {
        await BfqcIssuesApi.GroupCreate(payload);
      } else {
        await FqcIssuesApi.GroupCreate(payload);
      }

      setAddGroupDrawerSaving(false);
      setAddGroupDrawerOpen(false);
      NoticeUtil.success();

      getGroupData();
    } catch (e) {}
    setAddGroupDrawerSaving(false);
  };
  const addGroupDrawerOnCancel = () => {
    setAddGroupDrawerOpen(false);
  };

  const addIssuesBtnOnClick = () => {
    setAddIssuesDrawerOpen(true);
  };

  const editGroupDrawerOnOk = () => {
    setEditGroupDrawerVisible(false);
    setGroupCode('');
    getGroupData();
  };

  const editIssuesDrawerOnOk = () => {
    setEditIssuesDrawerVisible(false);
    selectIssuseItem.current = {};
    powerTableRef.current?.load();
  };

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  useEffect(() => {
    powerTableRef.current?.load();
  }, [groupCode]);

  useEffect(() => {
    getGroupData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <AppHeader />

      <div>
        <div className="flex h-[calc(100vh_-_160px)] gap-x-6">
          <div className="sticky top-24 box-border h-full w-[200px] shrink-0 overflow-y-auto">
            {groupCreatePermission && (
              <Button className="mb-4" block icon={<AddFillIcon className="fill-black" />} onClick={addBtnOnClick}>
                {i18n.t('global.addGroup')}
              </Button>
            )}
            <GroupMenu
              code={groupCode}
              menuList={groupList}
              onClick={setGroupCode}
              onChangeClick={onGroupMenuChangeClick}
              onDelClick={onGroupMenuDelClick}
            />
          </div>
          <div className="h-full flex-1 overflow-y-auto">
            <PowerTable
              initialized
              rowKey="id"
              columns={tableColumns}
              innerRef={powerTableRef}
              quickSearchFieldsConfig={quickSearchFieldsConfig}
              searchFieldsConfig={searchFieldsConfig}
              defaultPageSize={10}
              settingToolVisible
              pagination
              autoLoad
              enableCache
              cacheKey="FQC_ISSUES"
              rightToolbar={[
                importPermission && <ImportButton onClick={importBtnOnClick} />,
                issuesCreatePermission && (
                  <Button
                    type="primary"
                    icon={<AddFillIcon className="fill-white" />}
                    onClick={addIssuesBtnOnClick}
                    disabled={!groupCode}
                  >
                    {i18n.t('global.new')}
                  </Button>
                ),
              ]}
              tableProps={{
                onRow: issuesEditPermission
                  ? (record) => ({
                      onClick: () => {
                        selectIssuseItem.current = record;
                        setEditIssuesDrawerVisible(true);
                      },
                    })
                  : undefined,
              }}
              defaultSorter={{ field: 'created', order: 'DESCEND' }}
              request={fetchIssuesData}
            />
          </div>
        </div>
        <AddGroupDrawer
          onSubmit={addGroupDrawerOnSubmit}
          open={addGroupDrawerOpen}
          onCancel={addGroupDrawerOnCancel}
          saving={addGroupDrawerSaving}
        />
        <EditGroupDrawer
          visible={editGroupDrawerVisible}
          current={selectGroupItem.current}
          onOk={editGroupDrawerOnOk}
          onClose={() => setEditGroupDrawerVisible(false)}
        />
        <AddIssuesDrawer
          onSubmit={addIssuesDrawerOnSubmit}
          open={addIssuesDrawerOpen}
          onCancel={addIssuesDrawerOnCancel}
          saving={addIssuesDrawerSaving}
        />
        <EditIssuesDrawer
          visible={editIssuesDrawerVisible}
          current={selectIssuseItem.current}
          onOk={editIssuesDrawerOnOk}
          onClose={() => setEditIssuesDrawerVisible(false)}
        />
        <ImportModal
          modalProps={{
            open: importModalVisible,
            onCancel: importModalOnCancel,
            maskClosable: false,
          }}
          onOk={() => {
            getGroupData();
          }}
          onGoBack={() => setImportModalVisible(false)}
        />
      </div>
    </div>
  );
};

export default QualityCheckIssues;
