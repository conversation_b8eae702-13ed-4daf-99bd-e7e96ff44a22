import React, { useCallback, useContext, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from 'react';
import { ModalProps } from 'antd/es/modal';

import * as BfmOrder<PERSON>pi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import ExchangeIcon from 'common/assets/icons/icon-exchange.svg?react';
import BarcodeAliasModal from 'common/components/BarcodeAliasModal';
import useSearchAlias from 'common/components/BarcodeAliasModal/hook/useSearchAlias';
import BarcodeInput from 'common/components/BarcodeInput';
import Button from 'common/components/Button';
import FlashStatistic from 'common/components/FlashStatistic';
import Modal from 'common/components/Modal';
import useSetting from 'common/hooks/useSetting';
import useSpeech from 'common/hooks/useSpeech';
import * as NoticeUtil from 'common/utils/Notice';
import i18n from 'common/utils/I18n';
import { Col, Row, Space, Spin } from 'antd';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { offWindowBeforeUnload, onWindowBeforeUnload } from 'common/utils';
import { reducer, initialState } from './reducer';
import DataViewTable from './DataViewTable';
import WeightModal from './WeightModal';

interface IBarcodePackingModalProps {
  orderId: string;
  boxInfo: {
    status: string;
    id: string;
    code: string;
  };
  /** 是否是预装箱 */
  isPreBox?: boolean;
  /** 是否从预装箱tab进入 */
  fromPreboxTab?: boolean;
  innerRef: any;
  modalProps?: ModalProps;
  onOk?: () => void;
  onOperateModeChange: () => void;
}

const BarcodePackingModal: React.FC<IBarcodePackingModalProps> = (props) => {
  const { orderId, boxInfo, isPreBox, fromPreboxTab, modalProps, innerRef, onOperateModeChange, onOk } = props;
  const {
    ENABLE_BARCODE_SCAN_CUTOFF: enableBarcodeScanCutoff,
    BARCODE_CUTOFF_BEFORE_DIGITS: barcodeCutoffBeforeDigits,
    BARCODE_CUTOFF_AFTER_DIGITS: barcodeCutoffAfterDigits,
    FM_BARCODE_PACKING_THRESHOLD: fmBarcodePackingThreshold,
    FACTORY_PACKING_WEIGHT_CALCULATE_METHOD: factoryPackingWeightCalculateMethod,
  } = useSetting([
    { code: 'ENABLE_BARCODE_SCAN_CUTOFF', valueType: 'BOOLEAN' },
    { code: 'BARCODE_CUTOFF_BEFORE_DIGITS', valueType: 'NUMBER' },
    { code: 'BARCODE_CUTOFF_AFTER_DIGITS', valueType: 'NUMBER' },
    { code: 'FM_BARCODE_PACKING_THRESHOLD', valueType: 'NUMBER' },
    { code: 'FACTORY_PACKING_WEIGHT_CALCULATE_METHOD', valueType: 'STRING' },
  ]);

  const {
    barcodeAliasModalOpen,
    barcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  } = useSearchAlias();

  const enableWeight = useMemo(
    () => factoryPackingWeightCalculateMethod === 'MANUAL',
    [factoryPackingWeightCalculateMethod],
  );

  // 全局状态
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const [state, dispatch] = useReducer(reducer, initialState);
  const { loading, saving, tableLineData, normalData, normalTotalQty, weightValue, weightModalOpen, abnormalTotal } =
    state;

  const { playSound, stopSound } = useSpeech();

  const playAndNotify = useCallback(
    (text: string, type: 'success' | 'error' | 'warn' | 'info', val?: string) => {
      NoticeUtil[type](val ? `${text} ${val}` : text);
      playSound({ text });
    },
    [playSound],
  );

  const isOperate = useRef(false);
  const barcodeInputRef = useRef<any>(null);
  const normalQtyRef = useRef<any>(null);

  useImperativeHandle(innerRef, () => ({
    getIsOperate: () => isOperate.current,
  }));

  /** 请求单据所有明细 */
  const fetchOrderLineData = async () => {
    try {
      let result: any;
      if (currentUser.mode === 'BINDING') {
        result = await BfmOrderApi.Lines({
          enablePage: false,
          fmOrderId: orderId,
        });
      } else {
        result = await FmOrderApi.Lines({
          enablePage: false,
          fmOrderId: orderId,
        });
      }

      return result.data;
    } catch {}
    return [];
  };

  /** 请求预装箱所有明细 */
  const fetchPreBoxLineData = async () => {
    try {
      let result: any;
      if (currentUser.mode === 'BINDING') {
        result = await BfmOrderApi.PreBoxRfids({
          // boxId: orderInfo.boxId,
          orderId,
          enablePage: false,
          boxCode: boxInfo.code,
        });
      } else {
        result = await FmOrderApi.PreBoxRfids({
          // boxId: orderInfo.boxId,
          orderId,
          enablePage: false,
          boxCode: boxInfo.code,
        });
      }

      if (result.data.length > 0) {
        return result.data;
      }
    } catch {}
    return [];
  };

  /** 请求箱所有明细 */
  const fetchBoxLineData = async () => {
    try {
      let result: any;
      if (currentUser.mode === 'BINDING') {
        result = await BfmOrderApi.Rfids({
          fmOrderBoxId: boxInfo.id,
          fmOrderId: orderId,
          enablePage: false,
        });
      } else {
        result = await FmOrderApi.Rfids({
          fmOrderBoxId: boxInfo.id,
          fmOrderId: orderId,
          enablePage: false,
        });
      }

      if (result.data.length > 0) {
        return result.data;
      }
    } catch {}
    return [];
  };

  const initData = async () => {
    try {
      dispatch({ type: 'setLoading', payload: true });
      const promiseList: Promise<any>[] = [];

      // 需要获取预装箱数据的情况：从预装箱列表进入 或 装箱列表中的预装箱
      if (fromPreboxTab || isPreBox) {
        promiseList.push(fetchPreBoxLineData());
      } else {
        promiseList.push(fetchOrderLineData());
      }

      // 是否是编辑箱
      if (boxInfo.id) {
        // 从装箱列表进入时还需要获取装箱数据
        if (!fromPreboxTab) {
          promiseList.push(fetchBoxLineData());
        }
      }
      const [lineDataList, packedLineDataList] = await Promise.all(promiseList);
      dispatch({
        type: 'initData',
        payload: {
          lineDataList,
          packedLineDataList,
          isPreBox,
          callback: () => {
            normalQtyRef.current?.remind();
          },
        },
      });
    } catch {
    } finally {
      dispatch({ type: 'setLoading', payload: false });
    }
  };

  const handleModalCancel = () => {
    const commonLogic = () => {
      if (modalProps?.onCancel) {
        const fakeMouseEvent: any = {};
        modalProps.onCancel(fakeMouseEvent);
      }
    };
    if (isOperate.current) {
      const text = i18n.t('global.confirmDiscard');
      playSound({ text });
      NoticeUtil.confirm({
        content: text,
        onOk: () => {
          commonLogic();
        },
      });
    } else {
      commonLogic();
    }
  };

  const scanLineDataByRules = async (barcode: string, size: number, tableIndex: number) => {
    if (tableIndex === -1) {
      const text = i18n.t('global.barcodeNotInOrder');
      playAndNotify(text, 'warn', `[${barcode}]`);
      return;
    }

    const lineData = tableLineData[tableIndex];
    if (lineData.rfidTag) {
      const text = i18n.t('global.prodOnlyForRFID');
      playAndNotify(text, 'warn');
      return;
    }

    // 当前条码操作数
    const currentNormalQty = normalData.find((n) => n.skuId === lineData.skuId)?.qty || 0;
    // 当前条码新的操作数
    const newNormalQty = currentNormalQty + size;

    // 因为 availableQty 会实时减少，所以直接判断本次操作量是否超过当前剩余数
    if (size > lineData.availableQty) {
      const text = i18n.t('global.readedQtyExcessRemainingQty');
      playAndNotify(text, 'warn', `[${barcode}]`);
      return;
    }

    if (newNormalQty < 0) {
      const text = i18n.t('global.deleteQtyExcess');
      playAndNotify(text, 'warn', `[${barcode}]`);
      return;
    }

    dispatch({
      type: 'setSuccessScanData',
      payload: {
        skuId: lineData.skuId,
        newNormalQty,
        operateSize: size,
        tableIndex,
        callback: ({ newNormalData }) => {
          // 等待组件更新完成后才调用提示callback
          requestAnimationFrame(() => {
            // 正常数据变化时，正常数量组件UI提醒
            if (newNormalData.length > 0) {
              normalQtyRef.current?.remind();
            }
            isOperate.current = true;
            const text = `${i18n.t('global.scanSuccess')} [${barcode}]`;
            playAndNotify(text, 'success');
          });
        },
      },
    });
  };

  const handleBarcodeInputEnter = (barcode: string, operateSize: number) => {
    if (!operateSize) {
      const text = i18n.t('global.pleaseInputOperateQty');
      playAndNotify(text, 'warn');
      return;
    }
    if (operateSize === 0) {
      const text = i18n.t('global.operateQtyError');
      playAndNotify(text, 'warn');
      return;
    }
    if (barcode) {
      searchAliasByBarcode({ barcode, searchData: tableLineData, operateSize, callback: scanLineDataByRules });
    }
  };

  const handleSaveBtnClick = async () => {
    try {
      dispatch({ type: 'setSaving', payload: true });
      const payload: any = {
        data: normalData.map((item) => ({ skuId: item.skuId, qty: item.qty })),
        fmOrderId: orderId,
        operateMode: 'SKU',
        boxCode: boxInfo.code,
        boxMode: 'MIX_PROD',
        lastFlag: false,
      };
      if (weightValue) {
        payload.weight = weightValue;
      }
      if (currentUser.mode === 'BINDING') {
        await BfmOrderApi.BoxSkuSave(payload);
      } else {
        await FmOrderApi.BoxSkuSave(payload);
      }

      const text = i18n.t('global.saveSuccess');
      playAndNotify(text, 'success');
      if (onOk) onOk();
    } catch (err: any) {
      let responseData: any[] = [];
      if (err.response?.data?.detailMsg) {
        responseData = JSON.parse(err.response.data.detailMsg);
      }
      if (responseData.length > 0) {
        dispatch({
          type: 'setErrorDataInTableLine',
          payload: {
            errorData: responseData,
          },
        });
      }
    } finally {
      dispatch({ type: 'setSaving', payload: false });
    }
  };

  const handleResetBtnClick = () => {
    barcodeInputRef.current?.reset();
    const text = i18n.t('global.confirmDiscard');
    playSound({ text });
    NoticeUtil.confirm({
      content: text,
      onOk: () => {
        dispatch({ type: 'resetModalData', payload: { isCloseModal: false } });
        stopSound();
      },
    });
  };

  const handleBarcodeAliasModalOnOk = (selectedRows: any) => {
    barcodeAliasModalOnOk({ selectedRows, searchData: tableLineData, callback: scanLineDataByRules });
  };

  // 弹窗打开/关闭相关事件处理
  useEffect(() => {
    if (modalProps?.open === true) {
      initData();
      onWindowBeforeUnload();
    } else {
      offWindowBeforeUnload();

      isOperate.current = false;
      dispatch({ type: 'resetModalData', payload: { isCloseModal: true } });
      stopSound();
    }
    // eslint-disable-next-line
  }, [modalProps?.open]);

  const modalTitle = (
    <div className="flex h-6">
      <span>
        {i18n.t('global.barcodePacking')}
        {boxInfo.code ? ` [${boxInfo.code}]` : ''}
      </span>
      {boxInfo.status === 'NEW' && (
        <Button
          type="ghost"
          style={{ marginLeft: 20 }}
          icon={<ExchangeIcon className="fill-lead-slate" />}
          onClick={onOperateModeChange}
        />
      )}
    </div>
  );

  return (
    <Modal
      fullScreen
      centered
      title={modalTitle}
      footer={false}
      maskClosable={false}
      {...modalProps}
      onCancel={handleModalCancel}
      className="operate-modal"
    >
      <Spin spinning={loading}>
        <div className="flex h-full flex-col gap-y-4">
          <DataViewTable
            className="flex-auto"
            data={tableLineData}
            showErrorColumns={abnormalTotal > 0}
            leftTBar={
              <div className="flex w-full items-center justify-between">
                <BarcodeInput
                  range={
                    fmBarcodePackingThreshold ? [-fmBarcodePackingThreshold, fmBarcodePackingThreshold] : undefined
                  }
                  enableBarcodeScanCutoff={enableBarcodeScanCutoff}
                  barcodeCutoffBeforeDigits={barcodeCutoffBeforeDigits}
                  barcodeCutoffAfterDigits={barcodeCutoffAfterDigits}
                  innerRef={barcodeInputRef}
                  onPressEnter={handleBarcodeInputEnter}
                />
                <div className="flex gap-x-2">
                  {enableWeight && (
                    <Button onClick={() => dispatch({ type: 'setWeightModalOpen', payload: true })}>
                      {weightValue
                        ? `${i18n.t('global.weight')}: ${weightValue}${i18n.t('global.gram')}`
                        : i18n.t('global.inputWeight')}
                    </Button>
                  )}
                </div>
              </div>
            }
          />
          <Row justify="space-between" align="bottom">
            <Col>
              <Space>
                <Button
                  type="success"
                  size="large"
                  loading={saving}
                  onClick={handleSaveBtnClick}
                  disabled={!(normalData.length > 0) || abnormalTotal > 0}
                >
                  {i18n.t('global.save')}
                </Button>
                {normalData.length > 0 && (
                  <Button danger size="large" onClick={handleResetBtnClick}>
                    {i18n.t('global.reset')}
                  </Button>
                )}
              </Space>
            </Col>
            <Col>
              <Space>
                <FlashStatistic
                  type="success"
                  innerRef={normalQtyRef}
                  title={i18n.t('global.normal')}
                  value={normalTotalQty}
                />
              </Space>
            </Col>
          </Row>
        </div>
      </Spin>
      <WeightModal
        open={weightModalOpen}
        onClose={() => dispatch({ type: 'setWeightModalOpen', payload: false })}
        onOk={(values) => {
          dispatch({ type: 'setWeightValue', payload: values.weight });
          dispatch({ type: 'setWeightModalOpen', payload: false });
        }}
      />
      <BarcodeAliasModal
        selectRowFiledId="skuId"
        open={barcodeAliasModalOpen}
        onCancel={barcodeAliasModalOnCancel}
        onOk={handleBarcodeAliasModalOnOk}
        data={barcodeAliasData}
      />
    </Modal>
  );
};

export default BarcodePackingModal;
