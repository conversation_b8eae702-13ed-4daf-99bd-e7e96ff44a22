import { Button, DatePicker, Form, Input } from 'antd';
import classNames from 'classnames';
import * as SpibOrderApi from 'common/api/sample/SpibOrder';
import Drawer from 'common/components/Drawer';
import Result from 'common/components/Result';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Spin from 'common/components/Spin';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useReducer } from 'react';
import { useNavigate } from 'react-router-dom';

import { AddDrawerContext, initialState, reducer } from './AddDrawerReducer';

interface AddDrawerProps {
  open?: boolean;
  onRefresh?: () => void;
  onClose?: () => void;
}

const AddDrawer: React.FC<AddDrawerProps> = (props) => {
  const { open, onClose } = props;
  const onRefresh = useMemo(() => props.onRefresh || (() => {}), [props.onRefresh]);
  const [form] = Form.useForm();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { currentStep, success, errorMsg, saving, result } = state;
  const navigate = useNavigate();

  const submit = useCallback(async () => {
    await form.validateFields();
    const payload = { ...form.getFieldsValue() };
    if (payload.billingDate) {
      payload.billingDate = payload.billingDate.startOf('day').format('YYYY-MM-DD hh:mm:ss');
    }

    if (payload.fromPartnerId === currentUser.partnerId) {
      NoticeUtil.warn(i18n.t('global.notAllowSameReceiveShipper'));
      return;
    }
    payload.partnerId = currentUser.partnerId;
    payload.mode = 'BY_ORDER';

    dispatch({ type: 'setSaving', payload: true });

    try {
      const res: any = await SpibOrderApi.Create(payload, {
        throwError: false,
      });
      dispatch({
        type: 'setResult',
        payload: {
          id: res.id,
          code: res.code,
        },
      });
      dispatch({ type: 'setSuccess', payload: true });
      onRefresh();
    } catch (e: any) {
      dispatch({ type: 'setErrorMsg', payload: e?.response?.data?.message || '' });
      dispatch({ type: 'setSuccess', payload: false });
    }
    dispatch({ type: 'setCurrentStep', payload: 1 });
    dispatch({ type: 'setSaving', payload: false });
  }, [dispatch, onRefresh, currentUser.partnerId, form]);

  const onOpenOrder = useCallback(() => {
    if (result?.id) {
      navigate(`/app/spib/${result?.id}`);
    }
  }, [result?.id, navigate]);

  const onRetry = useCallback(() => {
    dispatch({ type: 'setCurrentStep', payload: 0 });
  }, []);

  useEffect(() => {
    if (!open) {
      dispatch({ type: 'reset' });
      form.resetFields();
    }
  }, [open, form]);

  return (
    <AddDrawerContext.Provider value={{ state, dispatch }}>
      <Drawer
        title={i18n.t('global.newInboundOrder')}
        subTitle={
          {
            0: i18n.t('global.writeBasicInfo'),
            1: i18n.t('global.result'),
          }[currentStep]
        }
        open={open}
        onClose={onClose}
        width={
          {
            0: 378,
            1: 378,
          }[currentStep]
        }
        destroyOnClose
        footer={
          currentStep !== 1 ? (
            <div className="flex gap-x-2 px-6 pb-6">
              {currentStep === 0 && (
                <Button type="primary" loading={saving} onClick={submit}>
                  {i18n.t('global.ok')}
                </Button>
              )}
              <Button onClick={props.onClose}>{i18n.t('global.cancel')}</Button>
            </div>
          ) : (
            false
          )
        }
      >
        <Spin spinning={saving}>
          <Form
            form={form}
            name="basic"
            layout="vertical"
            style={{
              display: currentStep === 0 ? 'block' : 'none',
            }}
            className={classNames({
              hidden: currentStep !== 0,
            })}
            initialValues={{
              billingDate: moment(),
              fromPartnerType: 'WAREHOUSE',
            }}
          >
            <Form.Item label={i18n.t('global.orderType')} name="orderTypeCode">
              <OrderTypeSelect type="IB" categoryCode="WAREHOUSE" />
            </Form.Item>
            <Form.Item
              label={i18n.t('global.shipper')}
              name="fromPartnerId"
              rules={[
                {
                  required: true,
                  message: i18n.t('global.fieldCanNotBeNull'),
                },
              ]}
            >
              <PartnerSelect types={['WAREHOUSE']} sourceType="PERMISSION" />
            </Form.Item>
            <Form.Item label={i18n.t('global.receiver')}>
              <span>{currentUser.partnerName}</span>
            </Form.Item>
            <Form.Item label={i18n.t('global.issueDate')} name="billingDate">
              <DatePicker />
            </Form.Item>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} maxLength={100} />
            </Form.Item>
          </Form>
          <div
            className={classNames({
              hidden: currentStep !== 1,
            })}
          >
            {success && (
              <Result
                status="success"
                title={i18n.t('global.createdDone')}
                subTitle={`${i18n.t('global.orderCode')}: ${result?.code}`}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onOpenOrder}>
                    {i18n.t('global.openOrder')}
                  </Button>,
                ]}
              />
            )}
            {!success && (
              <Result
                status="error"
                title={i18n.t('global.createdFail')}
                subTitle={errorMsg}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onRetry}>
                    {i18n.t('global.retry')}
                  </Button>,
                ]}
              />
            )}
          </div>
        </Spin>
      </Drawer>
    </AddDrawerContext.Provider>
  );
};

export default AddDrawer;
