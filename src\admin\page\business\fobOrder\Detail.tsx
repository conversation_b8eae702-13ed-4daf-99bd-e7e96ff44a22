import { EditOutlined } from '@ant-design/icons';
import { Button, Descriptions, Space, Statistic, Tabs } from 'antd';
import * as BfobOrderApi from 'common/api/factory/BfobOrder';
import * as FobOrderApi from 'common/api/factory/FobOrder';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import ConfirmButton from 'common/components/Button/Confirm';
import RefreshButton from 'common/components/Button/Refresh';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import { IPowerTableInnerRef } from 'common/components/PowerTable';
import RemarkEditModal from 'common/components/RemarkEditModal';
import Tag from 'common/components/Tag';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import { useCommonFn } from './common';
import BoxTable from './components/BoxTable';
import LineRfidsTable from './components/LineRfidsTable';

const Detail: React.FC = () => {
  const params: any = useParams();
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [loading, setLoading] = useState(false);
  const [orderRecord, setOrderRecord] = useState<{
    [key: string]: any;
  }>({});
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [permission] = usePermission('A:F:OB');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const { confirm } = useCommonFn();

  const [remarkEditModalVisible, setRemarkEditModalVisible] = useState<boolean>(false);
  const [logs, setLogs] = useState<any[]>([]);

  const boxTableRef = useRef<IPowerTableInnerRef>();

  const fetchOrderData = useCallback(async () => {
    if (!params || !params.id) return;
    setLoading(true);
    try {
      let rec;
      if (currentUser.mode === 'BINDING') {
        rec = await BfobOrderApi.Get({ id: params.id });
      } else {
        rec = await FobOrderApi.Get({ id: params.id });
      }
      setOrderRecord(rec);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
    // eslint-disable-next-line
  }, [params]);

  const fetchLogs = async () => {
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfobOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      } else {
        res = await FobOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      }
      setLogs(res.data);
    } catch (e) {}
  };

  const refresh = () => {
    fetchOrderData();
    boxTableRef.current?.load();
    powerTableRef.current?.load();
  };

  const confirmBtnOnClick = async () => {
    try {
      await confirm(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const modifyRemark = () => {
    setRemarkEditModalVisible(true);
  };
  const remarkOnCancel = () => {
    setRemarkEditModalVisible(false);
  };

  const remarkEditModalOnSubmit = async (values) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BfobOrderApi.Update({
          id: params.id,
          ...values,
        });
      } else {
        await FobOrderApi.Update({
          id: params.id,
          ...values,
        });
      }

      NoticeUtil.success();
      refresh();
    } catch {}
    setRemarkEditModalVisible(false);
  };

  useEffect(() => {
    if (logModalVisible) {
      fetchLogs();
    }
    // eslint-disable-next-line
  }, [logModalVisible]);

  useEffect(() => {
    if (!params || !params.id) return;
    fetchOrderData();
    boxTableRef.current?.load();
    // eslint-disable-next-line
  }, [params]);

  const subTitle = useMemo(() => {
    return (
      <Tag
        color={
          {
            NEW: 'red',
            PROCESSING: 'blue',
            FINISHED: 'green',
            CANCELED: 'slate',
          }[orderRecord.status]
        }
      >
        {orderRecord.statusDesc}
      </Tag>
    );
  }, [orderRecord.status, orderRecord.statusDesc]);

  return (
    <div>
      <AppHeader
        title={loading ? i18n.t('global.loading') : orderRecord.code}
        subTitle={subTitle}
        toolbar={
          <Space>
            <RefreshButton size="middle" onClick={refresh} />
            <Button
              type="default"
              icon={<ListIcon className="fill-lead-dark" />}
              onClick={() => setLogModalVisible(true)}
            >
              {i18n.t('global.log')}
            </Button>
            {confirmPermission && orderRecord.status === 'PROCESSING' && <ConfirmButton onClick={confirmBtnOnClick} />}
          </Space>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <SendReceiveLayout
            className="flex-auto"
            left={
              <PartnerViewer
                label={i18n.t('global.delivery')}
                partnerType="FACTORY"
                partnerCode={orderRecord.partnerCode}
                partnerName={orderRecord.partnerName}
                warehouseCode={orderRecord.warehouseCode}
                warehouseName={orderRecord.warehouseName}
              />
            }
            right={
              <PartnerViewer
                label={i18n.t('global.receiver')}
                partnerType={orderRecord.toPartnerType}
                partnerCode={orderRecord.toPartnerCode}
                partnerName={orderRecord.toPartnerName}
                warehouseCode={orderRecord.toWarehouseCode}
                warehouseName={orderRecord.toWarehouseName}
              />
            }
          />
          <div className="flex flex-initial items-center gap-x-6">
            {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
            <Statistic title={i18n.t('global.boxedCount')} value={orderRecord.actQty} />
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {orderRecord.prodCode && (
              <Descriptions.Item label={i18n.t('global.productCode')}>{orderRecord.prodCode}</Descriptions.Item>
            )}
            {orderRecord.sourceCode && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>{orderRecord.sourceCode}</Descriptions.Item>
            )}
            {orderRecord.fmOrderCode && (
              <Descriptions.Item label={i18n.t('global.fmOrderCode')}>{orderRecord.fmOrderCode}</Descriptions.Item>
            )}
            {orderRecord.typeDesc && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.typeDesc}</Descriptions.Item>
            )}
            {orderRecord.amt && (
              <Descriptions.Item label={i18n.t('global.orderAmount')}>{orderRecord.amt}</Descriptions.Item>
            )}
            {orderRecord.actAmt && (
              <Descriptions.Item label={i18n.t('global.realAmount')}>{orderRecord.actAmt}</Descriptions.Item>
            )}
            {orderRecord.typeDesc && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.typeDesc}</Descriptions.Item>
            )}
            {orderRecord.modified && (
              <Descriptions.Item label={i18n.t('global.modified')}>
                {moment(orderRecord.modified).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>
                {moment(orderRecord.created).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {((orderRecord.remark && orderRecord.status === 'FINISHED') || orderRecord.status !== 'FINISHED') && (
              <Descriptions.Item label={i18n.t('global.remark')}>
                {orderRecord.remark}
                {/* eslint-disable-next-line max-len */}
                {modifyRemarkPermission && orderRecord.status !== 'FINISHED' && (
                  <>
                    <Button type="link" size="small" icon={<EditOutlined />} onClick={() => modifyRemark()} />
                    <RemarkEditModal
                      current={orderRecord}
                      onSubmit={remarkEditModalOnSubmit}
                      onCancel={remarkOnCancel}
                      open={remarkEditModalVisible}
                      title={i18n.t('global.modifyRemark')}
                    />
                  </>
                )}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          size="small"
          defaultActiveKey="BOX"
          items={[
            {
              label: (
                <>
                  {i18n.t('global.outboundBox')} ({orderRecord.boxCount || 0})
                </>
              ),
              key: 'BOX',
              children: <BoxTable powerTableRef={boxTableRef} orderId={params && params.id ? params.id : undefined} />,
            },
            {
              label: i18n.t('global.line'),
              key: 'RFIDS',
              children: (
                <LineRfidsTable orderId={params && params.id ? params.id : undefined} powerTableRef={powerTableRef} />
              ),
            },
          ]}
        />
      </div>
      <LogDrawer
        open={logModalVisible}
        orderId={orderRecord.id}
        onClose={() => setLogModalVisible(false)}
        data={logs}
      />
    </div>
  );
};

export default Detail;
