import { <PERSON><PERSON>, Spin } from 'antd';
import * as PermissionApi from 'common/api/core/Permission';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import EmployeeModalSelector from 'common/components/ModalSelector/EmployeeModalSelector';
import PowerTable, { PowerTableColumnsType, PowerTableColumnType } from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';

interface EmployeePropsRef {
  recover: () => void;
}
export interface EmployeeProps {
  current?: Record<string, any>;
  onChange?: (value: Record<string, any>) => void;
  innerRef?: React.MutableRefObject<EmployeePropsRef | undefined>;
}
const Employee: React.FC<EmployeeProps> = (props) => {
  const { current, onChange, innerRef } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentEmp, setCurrentEmp] = useState<string[]>([]);
  const [employeeModalSelectorVisible, setEmployeeModalSelectorVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const resp: any = await PermissionApi.RoleEmpList({
        enablePage: false,
        roleId: current?.id,
      });
      setCurrentEmp(resp.data.map((item) => item.empId));
      setDataSource(resp.data);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [current]);

  const employeeSelectorModalOnOk = (value) => {
    const emp: any = [];
    value.forEach((item) => {
      emp.push({
        empId: item.id,
        empName: item.name,
        empCode: item.code,
        empTypeDesc: item.typeDesc,
      });
    });
    setDataSource(emp);
    setCurrentEmp(value.map((n) => n.id));
    setEmployeeModalSelectorVisible(false);
  };

  const deleteBtnOnClick = (record) => {
    setDataSource(dataSource.filter((item) => item.empId !== record.empId));
    setCurrentEmp(currentEmp.filter((item) => item !== record.empId));
  };

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'empCode',
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'empName',
    },
    {
      title: i18n.t('global.type'),
      dataIndex: 'empTypeDesc',
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.delete'),
    icon: <DeleteBinLineIcon className="fill-lead-red" />,
    onClick: (record) => {
      deleteBtnOnClick(record);
    },
  });
  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  useEffect(() => {
    if (onChange) onChange(dataSource);
    // eslint-disable-next-line
  }, [dataSource]);

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchData();
    },
  }));

  useEffect(() => {
    if (current) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [current]);

  return (
    <Spin spinning={loading}>
      <Button type="primary" style={{ marginBottom: 16 }} onClick={() => setEmployeeModalSelectorVisible(true)}>
        {i18n.t('global.selectEmp')}
      </Button>
      <PowerTable
        rowKey="empId"
        columns={columns}
        refreshBtnVisible={false}
        tableProps={{
          dataSource,
          size: 'small',
          pagination: {
            defaultPageSize: 15,
            position: ['bottomRight'],
            style: { marginBottom: 8 },
          },
        }}
      />
      <EmployeeModalSelector
        modalProps={{ open: employeeModalSelectorVisible }}
        onOk={employeeSelectorModalOnOk}
        onCancel={() => {
          setEmployeeModalSelectorVisible(false);
        }}
        defaultSelected={currentEmp}
        multiple
      />
    </Spin>
  );
};

export default Employee;
