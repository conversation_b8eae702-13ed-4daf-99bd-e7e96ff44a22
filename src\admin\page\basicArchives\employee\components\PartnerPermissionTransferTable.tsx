import { <PERSON><PERSON>, <PERSON> } from 'antd';
import PowerTable, { PowerTableColumnsType } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
// import useWindowSize from 'common/hooks/useWindowSize';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useMemo, useState } from 'react';

import { CurrentTypeWarehouseListItem } from './data';
import { DetailDrawerContext, TDetailDrawerContext } from './DetailDrawerReducer';

import styles from './PartnerPermissionTransferTable.module.css';

export interface PartnerPermissionTransferTablePropsRef {
  recover: () => void;
}

export interface PartnerPermissionTransferTableProps {
  leftColumns: PowerTableColumnsType;
  rightColumns: PowerTableColumnsType;
  innerRef: React.MutableRefObject<PartnerPermissionTransferTablePropsRef | undefined>;
}
const PartnerPermissionTransferTable: React.FC<PartnerPermissionTransferTableProps> = (props) => {
  const { leftColumns, rightColumns, innerRef } = props;
  const { state, dispatch } = useContext<TDetailDrawerContext>(DetailDrawerContext);
  const { currentTypePartnerList, currentTypeWarehouseList, currentTypeSelectRowKeys, tabActiveKey } = state;
  const [leftDataSource, setLeftDataSource] = useState<Record<string, any>[]>([]);
  const [rightDataSource, setRightDataSource] = useState<Record<string, any>[]>([]);
  const [leftTableData, setLeftTableData] = useState<Record<string, any>[]>([]);
  const [rightTableData, setRightTableData] = useState<Record<string, any>[]>([]);
  const [leftBtnDisable, setLeftBtnDisable] = useState(true);
  const [rightBtnDisable, setRightBtnDisable] = useState(true);
  const [leftSelectedRowKeys, setLeftSelectedRowKeys] = useState<any>([]);
  const [rightSelectedRowKeys, setRightSelectedRowKeys] = useState<any>([]);
  // const { height } = useWindowSize();

  const setDataSource = useCallback((left: Record<string, any>[], right: Record<string, any>[]) => {
    const leftData = JSON.parse(JSON.stringify(left));
    const rightData = JSON.parse(JSON.stringify(right));

    setLeftDataSource(leftData);
    setLeftTableData(leftData);
    setRightTableData(rightData);
    setRightDataSource(rightData);
  }, []);

  const dispatchMap = useMemo(() => {
    return {
      WAREHOUSE: 'setWarehouseRowKeys',
      SHOP: 'setShopRowKeys',
      FACTORY: 'setFactoryRowKeys',
    };
  }, []);
  /** 左边onSelect */
  const leftOnSelect = (record, selected) => {
    if (selected) {
      // 选中
      const selectedKeys: string[] = [record.id];
      if (record.children && record.children.length > 0) {
        // 选中的是父级时，同时选中对应所有子级
        selectedKeys.push(...record.children.map((n) => n.id));
      }
      setRightBtnDisable(false);
      setLeftSelectedRowKeys([...leftSelectedRowKeys, ...selectedKeys]);
    } else {
      // 取消当前选中项
      let selectedKeys = leftSelectedRowKeys.filter((n) => n !== record.id);
      if (record.children && record.children.length > 0) {
        // 当取消的是父级时，同时取消对应的所有的子级
        record.children.forEach((item) => {
          selectedKeys = selectedKeys.filter((n) => n !== item.id);
        });
      } else {
        // 当前取消的是子级时，也同时取消对应的父级
        selectedKeys = selectedKeys.filter((n) => n !== record.partnerId);
      }
      setRightBtnDisable(selectedKeys.length === 0);
      setLeftSelectedRowKeys(selectedKeys);
    }
  };

  /** 全选/反选 */
  const leftOnSelectAll = (selected, selectedRows) => {
    setRightBtnDisable(!selected);
    setLeftSelectedRowKeys(selectedRows.map((n) => n.id));
  };
  const rightSelect = (record, selected) => {
    if (selected) {
      // 选中
      const selectedKeys: string[] = [record.id];
      if (record.children && record.children.length > 0) {
        // 选中的是父级时，同时选中对应所有子级
        selectedKeys.push(...record.children.map((n) => n.id));
      }
      setLeftBtnDisable(false);
      setRightSelectedRowKeys([...rightSelectedRowKeys, ...selectedKeys]);
    } else {
      // 取消当前选中项
      let selectedKeys = rightSelectedRowKeys.filter((n) => n !== record.id);
      if (record.children && record.children.length > 0) {
        // 当取消的是父级时，同时取消对应的所有的子级
        record.children.forEach((item) => {
          selectedKeys = selectedKeys.filter((n) => n !== item.id);
        });
      } else {
        // 当前取消的是子级时，也同时取消对应的父级
        selectedKeys = selectedKeys.filter((n) => n !== record.partnerId);
      }
      setLeftBtnDisable(selectedKeys.length === 0);
      setRightSelectedRowKeys(selectedKeys);
    }
  };

  /** 全选/反选 */
  const rightOnSelectAll = (selected, selectedRows) => {
    setLeftBtnDisable(!selected);
    setRightSelectedRowKeys(selectedRows.map((n) => n.id));
  };

  const leftBtnOnClick = useCallback(() => {
    let rightList = rightDataSource.filter((n) => !rightSelectedRowKeys.includes(n.id));
    const leftList: Record<string, any>[] = [
      ...currentTypePartnerList.filter((n) => rightSelectedRowKeys.includes(n.id)),
      ...leftDataSource.filter((n) => !rightSelectedRowKeys.includes(n.id)),
    ];

    const wList = currentTypeWarehouseList.filter((n) => rightSelectedRowKeys.includes(n.id));
    wList.forEach((item) => {
      // 选中项是子级，右边找出对应的父级
      const rightParentItem = rightList.find((n) => n.id === item.partnerId);
      if (!rightParentItem) return;
      // 找出父级取消选中的子级
      rightParentItem.children = rightParentItem.children.filter((n) => n.id !== item.id);
      if (rightParentItem.children.length === 0) {
        // 父级的子级为空时，去除父级
        rightList = rightList.filter((n) => n.id !== rightParentItem.id);
      }
      // 找出右边对应父级
      const leftParentItem = leftList.find((n) => n.id === rightParentItem.id);
      if (leftParentItem) {
        leftParentItem.children.unshift(item);
      } else {
        const sourceItem = currentTypePartnerList.find((n) => n.id === rightParentItem.id);
        if (!sourceItem) return;
        leftList.unshift({
          ...sourceItem,
          children: [item],
        });
      }
    });

    const selectRowKeys: string[] = [];
    rightList.forEach((item) => {
      if (item.children && item.children.length > 0) {
        selectRowKeys.push(...item.children.map((n) => n.id));
      }
    });

    dispatch({ type: dispatchMap[tabActiveKey], payload: selectRowKeys });

    setDataSource(leftList, rightList);
    setRightSelectedRowKeys([]);
    setLeftBtnDisable(true);
    // eslint-disable-next-line
  }, [
    currentTypePartnerList,
    currentTypeWarehouseList,
    leftSelectedRowKeys,
    rightSelectedRowKeys,
    leftDataSource,
    rightDataSource,
    setDataSource,
    tabActiveKey,
    dispatch,
  ]);

  const rightBtnOnClick = useCallback(() => {
    let leftList = leftDataSource.filter((n) => !leftSelectedRowKeys.includes(n.id));
    const rightList: Record<string, any>[] = [
      ...currentTypePartnerList.filter((n) => leftSelectedRowKeys.includes(n.id)),
      ...rightDataSource.filter((n) => !leftSelectedRowKeys.includes(n.id)),
    ];

    const wList = currentTypeWarehouseList.filter((n) => leftSelectedRowKeys.includes(n.id));
    wList.forEach((item) => {
      // 选中项是子级，左边找出对应的父级
      const leftParentItem = leftList.find((n) => n.id === item.partnerId);
      if (!leftParentItem) return;
      // 找出父级取消选中的子级
      leftParentItem.children = leftParentItem.children.filter((n) => n.id !== item.id);
      if (leftParentItem.children.length === 0) {
        // 父级的子级为空时，去除父级
        leftList = leftList.filter((n) => n.id !== leftParentItem.id);
      }
      // 找出右边对应父级
      const rightParentItem = rightList.find((n) => n.id === leftParentItem.id);
      if (rightParentItem) {
        // 有父级，在父级children中添加子级
        rightParentItem.children.unshift(item);
      } else {
        // 无父级，从源数据中找父级
        const sourceItem = currentTypePartnerList.find((n) => n.id === leftParentItem.id);
        if (!sourceItem) return;
        rightList.unshift({
          ...sourceItem,
          children: [item],
        });
      }
    });

    const selectRowKeys: string[] = [];
    rightList.forEach((item) => {
      if (item.children && item.children.length > 0) {
        selectRowKeys.push(...item.children.map((n) => n.id));
      }
    });

    dispatch({ type: dispatchMap[tabActiveKey], payload: selectRowKeys });

    setDataSource(leftList, rightList);

    setDataSource(leftList, rightList);
    setLeftSelectedRowKeys([]);
    setRightBtnDisable(true);
    // eslint-disable-next-line
  }, [
    currentTypePartnerList,
    currentTypeWarehouseList,
    leftSelectedRowKeys,
    rightSelectedRowKeys,
    leftDataSource,
    rightDataSource,
    setDataSource,
    tabActiveKey,
    dispatch,
  ]);

  const leftSearch = (value: string) => {
    if (!value) {
      setLeftTableData(JSON.parse(JSON.stringify(leftDataSource)));
      return;
    }
    const val = value.trim();
    const searchData = leftDataSource.filter((item) => item.code.includes(val.toUpperCase()));
    setLeftTableData(JSON.parse(JSON.stringify(searchData)));
  };

  const rightSearch = (value: string) => {
    if (!value) {
      setRightTableData(JSON.parse(JSON.stringify(rightDataSource)));
      return;
    }
    const val = value.trim();
    const searchData = rightDataSource.filter((item) => item.code.includes(val.toUpperCase()));
    setRightTableData(JSON.parse(JSON.stringify(searchData)));
  };

  const recover = () => {
    setLeftSelectedRowKeys([]);
    setRightSelectedRowKeys([]);
    setLeftBtnDisable(true);
    setRightBtnDisable(true);
    setLeftTableData(leftDataSource);
    setRightTableData(rightDataSource);
  };

  useImperativeHandle(innerRef, () => ({
    recover,
  }));

  useEffect(() => {
    // 回显已选中的数据
    if (currentTypePartnerList.length > 0 && currentTypeSelectRowKeys.length > 0) {
      const selectedWarehouseData: CurrentTypeWarehouseListItem[] = [];
      // 为了回显时的顺序和添加的循序一致，使用双重循环
      currentTypeSelectRowKeys.forEach((id) => {
        currentTypeWarehouseList.forEach((n) => {
          if (id === n.id) {
            selectedWarehouseData.push(n);
          }
        });
      });
      const obj = {};
      selectedWarehouseData.forEach((item) => {
        if (obj[item.partnerId]) {
          obj[item.partnerId].children.unshift(item);
          return;
        }
        const partnerItem = currentTypePartnerList.find((n) => n.id === item.partnerId);
        if (!partnerItem) return;
        obj[item.partnerId] = {
          ...partnerItem,
          children: [item],
        };
      });
      const leftList: Record<string, any>[] = [];
      currentTypePartnerList.forEach((item) => {
        if (!(Object.keys(obj).includes(item.id) && item.children.length === obj[item.id].children.length)) {
          leftList.unshift({
            ...item,
            children: item.children.filter((n) => !currentTypeSelectRowKeys.includes(n.id)),
          });
        }
      });

      const rightList: Record<string, any>[] = Object.values(obj);
      setDataSource(leftList, rightList);
    }

    if (currentTypePartnerList && currentTypeSelectRowKeys.length === 0) {
      const data = JSON.parse(JSON.stringify(currentTypePartnerList));
      setLeftTableData(data);
      setLeftDataSource(data);
      setRightTableData([]);
      setRightDataSource([]);
    }
    return () => {
      recover();
    };
    // eslint-disable-next-line
  }, [
    currentTypePartnerList,
    currentTypeSelectRowKeys,
    currentTypeWarehouseList,
    setDataSource,
    tabActiveKey,
  ]);

  return (
    <div className={`flex justify-between gap-x-2 ${styles.transfer}`}>
      <Card bodyStyle={{ paddingTop: 0, paddingBottom: 0, paddingLeft: 0, paddingRight: 0 }}>
        <div className="px-4">
          <SearchInput className="mb-4 mt-4" placeholder={i18n.t('global.inputCode')} onSearch={leftSearch} />
        </div>
        <PowerTable
          rowKey="id"
          columns={leftColumns}
          refreshBtnVisible={false}
          settingToolVisible={false}
          tableProps={{
            rowSelection: {
              type: 'checkbox',
              checkStrictly: false,
              selectedRowKeys: leftSelectedRowKeys,
              onSelect: leftOnSelect,
              onSelectAll: leftOnSelectAll,
            },
            dataSource: leftTableData,
            size: 'small',
            // scroll: { y: height - 412 },
            pagination: {
              defaultCurrent: 1,
              defaultPageSize: 10,
              total: leftTableData.length,
            },
          }}
        />
      </Card>
      <div className="flex flex-col items-center justify-center gap-y-1">
        <Button
          className="flex h-6 w-6 items-center justify-center"
          disabled={rightBtnDisable}
          onClick={rightBtnOnClick}
        >
          {'>'}
        </Button>
        <Button className="flex h-6 w-6 items-center justify-center" disabled={leftBtnDisable} onClick={leftBtnOnClick}>
          {'<'}
        </Button>
      </div>
      <Card bodyStyle={{ paddingTop: 0, paddingBottom: 0, paddingLeft: 0, paddingRight: 0 }}>
        <div className="px-4">
          <SearchInput className="mb-4 mt-4" placeholder={i18n.t('global.inputCode')} onSearch={rightSearch} />
        </div>
        <PowerTable
          rowKey="id"
          columns={rightColumns}
          refreshBtnVisible={false}
          settingToolVisible={false}
          tableProps={{
            rowSelection: {
              type: 'checkbox',
              checkStrictly: false,
              selectedRowKeys: rightSelectedRowKeys,
              onSelect: rightSelect,
              onSelectAll: rightOnSelectAll,
            },
            dataSource: rightTableData,
            size: 'small',
            // scroll: { y: height - 412 },
            pagination: {
              defaultCurrent: 1,
              defaultPageSize: 10,
              total: rightTableData.length,
            },
          }}
        />
      </Card>
    </div>
  );
};

export default PartnerPermissionTransferTable;
