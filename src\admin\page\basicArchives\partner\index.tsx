import AddDrawer from 'admin/page/basicArchives/partner/components/AddDrawer';
import DetailDrawer from 'admin/page/basicArchives/partner/components/DetailDrawer';
import ImportModal from 'admin/page/basicArchives/partner/components/ImportModal';
import { Button, DatePicker, Dropdown, Input, MenuProps, Space } from 'antd';
import * as PartnerApi from 'common/api/core/Partner';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import { cachePrefix } from 'common/components/PowerTable/config';
import SearchInput from 'common/components/SearchInput';
import BusinessModelSelect from 'common/components/Select/BusinessModelSelect';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useMemo, useRef, useState } from 'react';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { DownOutlined } from '@ant-design/icons';
import ImportStorageLocationModal from './components/ImportStorageLocationModal';
import ImportSampleLocationModal from './components/ImportSampleLocationModal';

export type WarehouseType = 'WAREHOUSE' | 'SHOP' | 'FACTORY' | 'SUPPLIER';

const cacheKey = 'PARTNER';

const Partner: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { enableSampleServe } = globalState;
  const [current, setCurrent] = useState<any>({});
  const [addModalVisible, setAddDrawerVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [importSampleLocationModalVisible, setImportSampleLocationModalVisible] = useState(false);
  const [permission] = usePermission('A:BASE:PARTNER');
  const addPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const delPermission = permission.codes.includes('DEL');
  const importPermission = permission.codes.includes('CREATE');
  const importLocationPermission = permission.codes.includes('IMPORT_LOCATION');
  const importSampleLocationPermission = permission.codes.includes('IMPORT_SAMPLE_LOCATION') && enableSampleServe; // 有样品权限，且样品服务开启
  const [currentType, setCurrentType] = useState<WarehouseType>(
    LocalStorageUtil.getItem(`${cachePrefix}${cacheKey}`)?.tabsActiveKey || 'WAREHOUSE',
  );
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importStorageLocationModalVisible, setImportStorageLocationModalVisible] = useState(false);

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const importStorageLocationOnCancel = () => {
    setImportStorageLocationModalVisible(false);
  };

  const importStorageLocationOnClick = () => {
    setImportStorageLocationModalVisible(true);
  };

  const importSampleLocationOnClick = () => {
    setImportSampleLocationModalVisible(true);
  };

  const importSampleLocationOnCancel = () => {
    setImportSampleLocationModalVisible(false);
  };

  const tabsOnChange = (activeKey) => {
    setCurrentType(activeKey);
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.codeOrAccount'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'businessModelId',
      label: i18n.t('global.businessModel'),
      inputComponent: <BusinessModelSelect />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.create'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await PartnerApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      sorter: true,
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      valueType: 'text',
      sorter: true,
      width: 230,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.longitude'),
      dataIndex: 'longitude',
      valueType: 'text',
      width: 230,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.latitude'),
      dataIndex: 'latitude',
      valueType: 'text',
      width: 230,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      ellipsis: true,
      minWidth: 150,
      auto: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (delPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      className: 'text-lead-red hover:border-lead-red focus:border-lead-red',
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  if (currentType === 'SHOP') {
    const item = {
      title: i18n.t('global.defaultReturnWarehouse'),
      dataIndex: 'defReturnPartnerName',
      ellipsis: true,
      width: 300,
    };
    tableColumns.splice(4, 0, item as any);
  }

  if (currentType === 'WAREHOUSE') {
    const returnWarehouseItem = {
      title: i18n.t('global.returnWarehouse'),
      dataIndex: 'returnTag',
      valueType: 'boolean',
      sorter: true,
      ellipsis: true,
      width: 220,
    };
    tableColumns.splice(3, 0, returnWarehouseItem as any);
    if (enableSampleServe) {
      const sampleWarehouseItem = {
        title: i18n.t('global.sampleWarehouse'),
        dataIndex: 'sampleFlag',
        valueType: 'boolean',
        sorter: true,
        ellipsis: true,
        width: 220,
      };
      tableColumns.splice(4, 0, sampleWarehouseItem as any);
    }
  }

  if (currentType === 'SHOP' || currentType === 'WAREHOUSE') {
    tableColumns.splice(
      3,
      0,
      {
        title: i18n.t('global.businessModel'),
        dataIndex: 'businessModelName',
        ellipsis: true,
        width: 160,
        valueType: 'text',
      },
      {
        title: i18n.t('global.antiTheft'),
        dataIndex: 'enableAntiTheft',
        codeDataIndex: 'enableAntiTheft',
        nameDataIndex: 'enableAntiTheftDesc',
        valueType: 'codeName',
        ellipsis: true,
        width: 160,
      },
      {
        title: i18n.t('global.antiTheftSharedWarehouseStoreCode'),
        dataIndex: 'combinationCode',
        valueType: 'text',
        ellipsis: true,
        width: 220,
      },
    );
  }

  const fetchData = useCallback((params: Record<string, any>, actualTabKey: string) => {
    const payload = { ...params };
    payload.type = [actualTabKey];
    // if (typeof disabled === 'boolean') {
    //   payload.disabled = disabled;
    // }
    if (payload.createDateRange) {
      payload.createdStart = payload.createDateRange[0].startOf('day');
      payload.createdEnd = payload.createDateRange[1].endOf('day');
      delete payload.createDateRange;
    }
    return PartnerApi.List(payload);
  }, []);

  const addBtnOnClick = () => {
    setAddDrawerVisible(true);
    // history.push(`/app/${lowerCaseType}/add`);
  };

  const addModalOnCancel = () => {
    setAddDrawerVisible(false);
  };

  const PartnerDetailDrawerOnClose = () => {
    setDetailDrawerVisible(false);
    powerTableRef.current?.load();
    setCurrent({});
  };

  const addModalOnSubmit = async (values) => {
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      await PartnerApi.Create({
        type: currentType,
        disabled: false,
        ...values,
      });
      setAddDrawerVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const menuList: MenuProps['items'] = useMemo(() => {
    const items: MenuProps['items'] = [];

    if (importPermission) {
      items.push({
        label: i18n.t('global.importPartner'),
        key: 'IMPORT',
        onClick: importBtnOnClick,
      });
    }

    if (importLocationPermission) {
      items.push({
        label: i18n.t('global.importStorageLocation'),
        key: 'IMPORT_LOCATION',
        onClick: importStorageLocationOnClick,
      });
    }

    if (importSampleLocationPermission) {
      items.push({
        label: i18n.t('global.importSampleLocation'),
        key: 'IMPORT_SAMPLE_LOCATION',
        onClick: importSampleLocationOnClick,
      });
    }

    return items;
  }, [importLocationPermission, importPermission, importSampleLocationPermission]);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {(importPermission || importLocationPermission || importSampleLocationPermission) && (
              <Dropdown menu={{ items: menuList }}>
                <Button>
                  <Space>
                    {i18n.t('global.import')}
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            )}
            {addPermission && (
              <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        tabDefaultActiveKey="WAREHOUSE"
        tabStatus={[
          {
            code: 'WAREHOUSE',
            name: i18n.t('global.warehouse'),
          },
          {
            code: 'SHOP',
            name: i18n.t('global.shop'),
          },
          {
            code: 'FACTORY',
            name: i18n.t('global.factory'),
          },
          {
            code: 'SUPPLIER',
            name: i18n.t('global.supplier'),
          },
        ]}
        tabsOnChange={tabsOnChange}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey={cacheKey}
        tableProps={{
          // bordered: true,
          sticky: {
            offsetHeader: 96,
          },
          onRow: editPermission
            ? (record: any) => ({
                onClick: () => {
                  setCurrent(record);
                  setDetailDrawerVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <AddDrawer type={currentType} open={addModalVisible} onCancel={addModalOnCancel} onSubmit={addModalOnSubmit} />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: importModalOnCancel,
          maskClosable: false,
        }}
        onOk={() => {
          powerTableRef.current?.load();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
      <ImportStorageLocationModal
        modalProps={{
          open: importStorageLocationModalVisible,
          onCancel: importStorageLocationOnCancel,
          maskClosable: false,
        }}
        onOk={() => {}}
        onGoBack={() => setImportStorageLocationModalVisible(false)}
      />
      <ImportSampleLocationModal
        modalProps={{
          open: importSampleLocationModalVisible,
          onCancel: importSampleLocationOnCancel,
          maskClosable: false,
        }}
        onOk={() => {}}
        onGoBack={() => setImportSampleLocationModalVisible(false)}
      />
      <DetailDrawer
        type={currentType}
        partnerId={current?.id}
        open={detailDrawerVisible}
        onCancel={PartnerDetailDrawerOnClose}
      />
    </div>
  );
};

export default Partner;
