const fs = require('fs');
const CryptoJS = require('crypto-js');
const axios = require('axios');
const querystring = require('querystring');

// 同步读取文件数据
const zhData = fs.readFileSync('src/translations/zh-CN.json', 'UTF-8');
const enData = fs.readFileSync('src/translations/en-US.json', 'UTF-8');

const handleData = () => {
  // 判断是否有读取到数据
  if (!(zhData && enData)) return;
  const zh_CN = JSON.parse(zhData);
  const en_US = JSON.parse(enData);
  let successNum = 0;
  let errorNum = 0;
  let accountStatus = true;
  const requst = async (query, item) => {
    if (!query) return;
    const appKey = '0816b0a46fbd3d38';
    const key = 'M51BhubfawzZo1YhzuaPfty0W0mMrHdn'; // 注意：暴露appSecret，有被盗用造成损失的风险
    const salt = new Date().getTime();
    const curtime = Math.round(new Date().getTime() / 1000);
    const str1 = appKey + truncate(query) + salt + curtime + key;
    const from = 'zh-CHS';
    const to = 'en';
    const sign = CryptoJS.SHA256(str1).toString(CryptoJS.enc.Hex);
    function truncate(q) {
      const len = q.length;
      if (len <= 20) return q;
      return q.substring(0, 10) + len + q.substring(len - 10, len);
    }
    await axios
      .post(
        'http://openapi.youdao.com/api',
        querystring.stringify({
          q: query,
          appKey,
          salt,
          from,
          to,
          sign,
          signType: 'v3',
          curtime,
        }),
      )
      .then(({ data }) => {
        if (data.translation) {
          let str = data.translation[0];
          if (!str.includes(' ')) {
            str = str.slice(0, 1).toLowerCase() + str.slice(1, str.length);
          }
          en_US[item] = str;
          console.log(`${query}--->${en_US[item]}`);
          successNum++;
        } else if (data.errorCode === '401') {
          accountStatus = false;
          console.log('账户已经欠费，请进行账户充值!');
        }
      })
      .catch((error) => {
        errorNum++;
        console.log('失败', error);
      });
  };
  const handle = (arr) => {
    if (!accountStatus) return en_US; // 判断账户状态是否欠费，是的话，返回读取的文件信息
    return new Promise(async (resolve) => {
      await Promise.all(
        arr
          .filter((key) => !en_US[key] && zh_CN[key])
          .map((key) => requst(zh_CN[key], key)),
      );
      const result = arr.filter((key) => !en_US[key] && zh_CN[key]);
      if (result.length) {
        resolve(await handle(result));
      } else {
        resolve(en_US);
      }
    });
  };

  const arr = Object.keys(en_US);
  handle(arr).then((res) => {
    fs.writeFileSync(
      'src/translations/en-US.json',
      JSON.stringify(res, null, 2),
      'UTF-8',
      (err) => {
        console.log('写入失败!', err);
      },
    );
    console.log(`写入成功!  翻译成功${successNum}次，失败${errorNum}次`);
  });
};
handleData();
