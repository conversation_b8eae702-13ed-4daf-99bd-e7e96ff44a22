import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    method: 'post',
    data,
    url: '/api/warehouse/wib-order/create',
    ...config,
  });
export const Save = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/save',
    method: 'post',
    data,
  });
export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/list',
    method: 'post',
    data,
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/get',
    method: 'post',
    data,
  });

export const Boxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/boxes',
    method: 'post',
    data,
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/rfids',
    method: 'post',
    data,
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/confirm',
    method: 'post',
    data,
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/lines',
    method: 'post',
    data,
  });

export const ConfigUpdate = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/config/update',
    method: 'post',
    data,
  });

export const ConfigList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/config/list',
    method: 'post',
    data,
  });

export const PreBoxRfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/pre-box-rfids',
    method: 'post',
    data,
  });

export const PreBoxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/pre-boxes',
    method: 'post',
    data,
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/cancel',
    method: 'post',
    data,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/logs',
    method: 'post',
    data,
  });

export const FastReceipt = (data: RequestPayloadType): RequestReturnType =>
  request({
    method: 'post',
    data,
    url: '/api/warehouse/wib-order/fast-receipt',
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/update',
    method: 'post',
    data,
  });

export const AbnormalLines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/abnormal-lines',
    method: 'post',
    data,
  });

export const AbnormalTags = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/abnormal-tags',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/box-reset',
    method: 'post',
    data,
  });

export const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/reset',
    method: 'post',
    data,
  });

export const ConfirmBox = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/confirmBox',
    method: 'post',
    data,
  });

export const InboundData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/inbound-data',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/batch-confirm',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wib-order/batch-cancel',
    method: 'post',
    data,
  });
