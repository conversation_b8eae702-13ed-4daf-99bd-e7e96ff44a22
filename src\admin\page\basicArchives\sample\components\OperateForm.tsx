import moment from 'moment';
import { Col, DatePicker, Form, Image, Input, InputNumber, Row, Switch, Tooltip } from 'antd';
import { Store } from 'antd/es/form/interface';
import { FormProps } from 'antd/lib/form';
import * as FileApi from 'common/api/file/Img';
import CopperCoinFillRedIcon from 'common/assets/icons/icon-copper-coin-fill-red.svg?react';
import EditFillIcon from 'common/assets/icons/icon-edit-fill.svg?react';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import './OperateForm.css';
import SampleBrandSelect from 'common/components/Select/SampleBrandSelect';
import SamplePriCategorySelect from 'common/components/Select/SamplePriCategorySelect';
import SampleSubCategorySelect from 'common/components/Select/SampleSubCategorySelect';
import SampleYearSelect from 'common/components/Select/SampleYearSelect';
import SampleColorGrpSelect from 'common/components/Select/SampleColorGrpSelect';
import SampleSizeGrpSelect from 'common/components/Select/SampleSizeGrpSelect';
import SampleSeriesSelect from 'common/components/Select/SampleSeriesSelect';
import { SampleContext, TSampleContext } from '../reducer';
import EditImgModal from './EditImgModal';

export interface OperateFormInnerRef {
  /**
   * Focus code input
   */
  focusCodeInput(): void;
  /**
   * Focus name input
   */
  focusNameInput(): void;
}

interface OperationFormProps extends FormProps {
  codeReadOnly?: boolean;
  statusVisible?: boolean;
  imageVisible?: boolean;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<OperateFormInnerRef | undefined>;
  refresh?: () => void;
}

export const extFieldFormItemNamePreFix = 'EXT_FIELD_';

const OperateForm: React.FC<OperationFormProps> = (props) => {
  const { onFinish, statusVisible, refresh, imageVisible, innerRef, codeReadOnly, ...formProps } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [imgEditModalVisible, setImgEditModalVisible] = useState(false);
  const { state: sampleState } = useContext<TSampleContext>(SampleContext);

  const { state } = useContext<TGlobalContext>(GlobalContext);
  const extFields = state.sampleExtFields.filter((n) => n.type === 'SAMPLE');
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const showEditImgModal = () => {
    setImgEditModalVisible(true);
  };
  const handleImgEditModalCancel = () => {
    if (refresh) refresh();
    setImgEditModalVisible(false);
  };
  const onFormFinish = (values: Store) => {
    const { listedDate, ...otherValues } = values;
    if (onFinish) {
      onFinish({
        listedDate: listedDate ? moment(listedDate).format('YYYY-MM-DD HH:mm:ss') : undefined,
        ...otherValues,
      });
    }
  };

  useImperativeHandle(innerRef, () => ({
    focusCodeInput: () => {
      codeInputRef.current?.focus({ cursor: 'end' });
    },
    focusNameInput: () => {
      nameInputRef.current?.focus({ cursor: 'end' });
    },
  }));

  useEffect(() => {
    if (form && sampleState.sampleRecord) {
      const init: Record<string, any> = { ...sampleState.sampleRecord };
      if (init.listedDate) {
        init.listedDate = moment(init.listedDate);
      }
      form.setFieldsValue(init);
    } else {
      form.resetFields();
      form.setFieldsValue({
        costPrice: 0,
        retailPrice: 0,
        tagPrice: 0,
      });
    }
  }, [form, sampleState.sampleRecord]);

  return (
    <>
      <Form
        form={form}
        name="sampleOperationForm"
        className="sample-operation-form"
        onFinish={onFormFinish}
        layout="vertical"
        {...formProps}
      >
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
              <Input ref={codeInputRef} readOnly={codeReadOnly} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
              <Input ref={nameInputRef} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.source')} name="source">
              <Input />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.band')} name="band">
              <Input />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.gender')} name="gender">
              <Input />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.brand')} name="smBrandId">
              <SampleBrandSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.priCategory')} name="smPriCategoryId">
              <SamplePriCategorySelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.subCategory')} name="smSubCategoryId">
              <SampleSubCategorySelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.year')} name="smYearId">
              <SampleYearSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="smColorGrpId" label={i18n.t('global.colorGroup')}>
              <SampleColorGrpSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="smSizeGrpId" label={i18n.t('global.sizeGroup')}>
              <SampleSizeGrpSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="smSeriesId" label={i18n.t('global.series')}>
              <SampleSeriesSelect />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="listedDate" label={i18n.t('global.listedDate')}>
              <DatePicker format="YYYY-MM-DD" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.designer')} name="designer">
              <Input />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={
                <>
                  <span>{i18n.t('global.disturb')}</span>
                  <Tooltip title={i18n.t('global.disturbTip')}>
                    <CopperCoinFillRedIcon className="ml-1 fill-lead-red" />
                  </Tooltip>
                </>
              }
              valuePropName="checked"
              name="disturbTag"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="costPrice" label={i18n.t('global.costPrice')}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="retailPrice" label={i18n.t('global.smRetailPrice')}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="tagPrice" label={i18n.t('global.barcodeTagPrice')}>
              <InputNumber min={0} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} showCount maxLength={500} />
            </Form.Item>
          </Col>
          {statusVisible && (
            <Col span={8}>
              <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
                <Switch />
              </Form.Item>
            </Col>
          )}
          {imageVisible && (
            <Col span={24} className="mb-6">
              <div className="flex">
                <span>{i18n.t('global.image')}：</span>
                <div aria-label="edit" tabIndex={0} onKeyDown={() => {}} role="button" onClick={showEditImgModal}>
                  <EditFillIcon className="bottom-0 ml-2 fill-lead-slate" />
                </div>
              </div>
              <Image
                preview={
                  sampleState.sampleRecord?.defaultImg
                    ? {
                        src: FileApi.Get(sampleState.sampleRecord?.defaultImg),
                      }
                    : false
                }
                alt=""
                src={
                  sampleState.sampleRecord?.defaultImg
                    ? FileApi.Get(sampleState.sampleRecord?.defaultImg, 100, 100)
                    : 'img/noImage.svg'
                }
                style={{
                  width: '100px',
                  height: '100px',
                }}
              />
            </Col>
          )}
          <Col span={24}>
            <div className="rounded-lg border border-lead-light-bg">
              <div className="mb-4 inline-flex h-9 w-full items-start justify-start gap-2.5 rounded-tl-lg rounded-tr-lg bg-lead-light-bg px-3 py-2">
                <div className="shrink grow basis-0 text-sm font-semibold leading-tight text-slate-700">
                  {i18n.t('global.extendedProperties')} ({extFields.length})
                </div>
              </div>
              <Row gutter={24} className="px-3">
                {extFields.map((item: any) => {
                  if (item.fieldType === 'TXT' || item.fieldType === 'URL' || item.fieldType === 'IMG') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={extFieldFormItemNamePreFix + item.code}>
                          <Input />
                        </Form.Item>
                      </Col>
                    );
                  }
                  if (item.fieldType === 'NUM' || item.fieldType === 'AMT') {
                    return (
                      <Col key={item.name} span={8}>
                        <Form.Item label={item.name} name={extFieldFormItemNamePreFix + item.code}>
                          <InputNumber style={{ width: 150 }} />
                        </Form.Item>
                      </Col>
                    );
                  }
                  return null;
                })}
              </Row>
            </div>
          </Col>
        </Row>
      </Form>
      <EditImgModal visible={imgEditModalVisible} onCancel={handleImgEditModalCancel} />
    </>
  );
};

export default OperateForm;
