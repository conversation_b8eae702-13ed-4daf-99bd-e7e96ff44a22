/**
 * Create by codegen at 2022-07-13
 * 数据字典 - 编辑
 */
import DictDataTable from 'admin/page/basicArchives/other/dict/components/DictDataTable';
import { Form, Input, Switch, Tabs } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useRef, useState } from 'react';

interface DetailDrawerProps {
  visible?: boolean;
  current: Record<string, any>;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

const DetailDrawer: React.FC<DetailDrawerProps> = (props) => {
  const { visible, current, confirmLoading, onOk, onCancel } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);
  const [currentSource, setCurrentSource] = useState<Record<string, any>>();
  const [activeKey, setActiveKey] = useState('INFO');
  const [permission] = usePermission('A:BASE:OTHER:B:DICT');
  const editPermission = permission.codes.includes('EDIT');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) {
      onCancel();
      form.resetFields();
    }
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  useEffect(() => {
    let timerId: any = null;
    if (visible && current) {
      const values = {
        status: !current.disabled,
        ...current,
      };
      form.setFieldsValue(values);
      setCurrentSource(values);
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    if (!visible) setActiveKey('INFO');
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [current, visible, form]);

  const onRecover = () => {
    form.setFieldsValue(currentSource);
    setActiveKey('INFO');
  };

  const items: any[] = [];

  if (editPermission) {
    items.push({
      label: i18n.t('global.information'),
      key: 'INFO',
      children: (
        <Form layout="vertical" form={form} onFinish={formOnFinish}>
          <Form.Item label={i18n.t('global.code')}>{current?.code}</Form.Item>
          <Form.Item name="name" label={i18n.t('global.name')} rules={canNotBeNullRules}>
            <Input ref={inputRef} maxLength={50} />
          </Form.Item>
          <Form.Item name="remark" label={i18n.t('global.remark')}>
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="status" label={i18n.t('global.status')} valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      ),
    });
  }

  items.push({
    label: i18n.t('global.configuration'),
    key: 'CUSTOM',
    children: <DictDataTable current={current} />,
  });

  return (
    <Drawer
      title={
        <>
          {i18n.t('global.editDict')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      open={visible}
      width={720}
      onClose={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          applyPermission={editPermission}
          onApply={modalOnOk}
          onRecover={onRecover}
        />
      }
    >
      <Tabs size="small" items={items} activeKey={activeKey} onChange={(e) => setActiveKey(e)} />
    </Drawer>
  );
};

export default DetailDrawer;
