import { initialState, ProdContext, reducer } from 'admin/page/basicArchives/prod/reducer';
import { Button, Dropdown, Input, message, Space } from 'antd';
import * as AsyncTask<PERSON>pi from 'common/api/core/AsyncTask';
import * as ProdApi from 'common/api/core/Prod';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import BrandSelect from 'common/components/Select/BrandSelect';
import DisableSelect from 'common/components/Select/DisableSelect';
import GenderSelect from 'common/components/Select/GenderSelect';
import PriCategorySelect from 'common/components/Select/PriCategorySelect';
import SubCategorySelect from 'common/components/Select/SubCategorySelect';
import YearSelect from 'common/components/Select/YearSelect';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useMemo, useReducer, useRef, useState } from 'react';

import { makeID } from 'common/utils';
import AddDrawer from './components/AddDrawer';
import DetailDrawer from './components/DetailDrawer';
import ImportImgModal from './components/ImportImgModal';
import ImportModal from './components/ImportModal';

const Prod = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [current, setCurrent] = useState<any>({});
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importImgModalVisible, setImportImgModalVisible] = useState(false);
  const [addModalConfirmLoading, setAddModalConfirmLoading] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const importAsynvMessageKey = useRef<string>();

  const [permission] = usePermission('A:BASE:PRODUCT');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const importPermission = permission.codes.includes('IMPORT');
  const deletePermission = permission.codes.includes('DELETE');

  const editBtnOnClick = async (record: Record<string, any>) => {
    dispatch({ type: 'setProdId', payload: record.id });
    setCurrent(record);
    setDetailDrawerVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await ProdApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'disabled',
      label: i18n.t('global.status'),
      inputComponent: <DisableSelect />,
    },
    {
      name: 'brandIds',
      label: i18n.t('global.brand'),
      inputComponent: <BrandSelect mode="multiple" />,
    },
    {
      name: 'priCategoryIds',
      label: i18n.t('global.priCategory'),
      inputComponent: <PriCategorySelect mode="multiple" />,
    },
    {
      name: 'subCategoryIds',
      label: i18n.t('global.subCategory'),
      inputComponent: <SubCategorySelect mode="multiple" />,
    },
    {
      name: 'yearIds',
      label: i18n.t('global.yearCode'),
      inputComponent: <YearSelect mode="multiple" />,
    },
    {
      name: 'genderIds',
      label: i18n.t('global.gender'),
      inputComponent: <GenderSelect mode="multiple" />,
    },
  ];

  const tableColumns: any = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 200,
      ellipsis: true,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} disturbTag={record.disturbTag} rfidTag={record.rfidTag} />
      ),
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      valueType: 'text',
      minWidth: 200,
      auto: true,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.sourceNumber'),
      dataIndex: 'sourceCode',
      width: 200,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.brand'),
      width: 150,
      valueType: 'codeName',
      codeDataIndex: 'brandCode',
      nameDataIndex: 'brandName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.colorGroup'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'colorGrpCode',
      nameDataIndex: 'colorGrpName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.gender'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'genderName',
      nameDataIndex: 'genderCode',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.sizeGroup'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'sizeGrpCode',
      nameDataIndex: 'sizeGrpName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.specGroup'),
      width: 220,
      valueType: 'codeName',
      codeDataIndex: 'specGrpCode',
      nameDataIndex: 'specGrpName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.year'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'yearCode',
      nameDataIndex: 'yearName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.priCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'priCategoryCode',
      nameDataIndex: 'priCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.subCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'subCategoryCode',
      nameDataIndex: 'subCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const timeRef = useRef();

  const fetSyncTaskApiInfo = async (id) => {
    try {
      const res: any = await AsyncTaskApi.Get({ id });
      if (res.status === 'PROCESSING') {
        return;
      }
      if (res.status === 'SUCCESS') {
        message.success(i18n.t('global.prodImportSuccess'));
        powerTableRef.current?.load();
      }
      if (res.status === 'FAIL') {
        message.error(
          <>
            {i18n.t('global.prodImportFail')}
            <br />
            {res.logStack}
          </>,
        );
      }
      message.destroy(importAsynvMessageKey.current);
      clearInterval(timeRef.current);
    } catch (e) {}
  };

  const onImportantFinish = (id) => {
    const key = makeID();
    message.loading({
      content: i18n.t('global.importProcessing'),
      duration: 0,
      key,
    });
    importAsynvMessageKey.current = key;
    setImportModalVisible(false);

    // @ts-ignore
    timeRef.current = setInterval(() => {
      fetSyncTaskApiInfo(id);
    }, 5000);
  };

  const onImportantImgFinish = () => {};

  useEffect(
    () => () => {
      clearInterval(timeRef.current);
      message.destroy(importAsynvMessageKey.current);
    },
    [],
  );

  const fetchData = useCallback(async (params: Record<string, any>) => {
    return ProdApi.List(params);
  }, []);

  const addBtnOnClick = () => {
    setAddModalVisible(true);
  };

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const importImgBtnOnClick = () => {
    setImportImgModalVisible(true);
  };

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  const importImgModalOnCancel = () => {
    setImportImgModalVisible(false);
    powerTableRef.current?.load();
  };

  const addModalOnCancel = () => {
    setAddModalVisible(false);
  };

  const addModalOnSubmit = async (values) => {
    setAddModalConfirmLoading(true);
    values.code = values.code.trim();
    values.name = values.name.trim();
    values.disabled = false;
    try {
      await ProdApi.Create(values);
      setAddModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAddModalConfirmLoading(false);
  };

  const detailDrawerOnClose = () => {
    setDetailDrawerVisible(false);
    dispatch({ type: 'setSkuList', payload: [] });
    dispatch({ type: 'setProdId', payload: '' });
    dispatch({ type: 'setDisabled', payload: null });
    dispatch({ type: 'setProdRecord', payload: null });
    powerTableRef.current?.load();
  };

  const prodContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <ProdContext.Provider value={prodContextValue}>
      <div>
        <AppHeader
          toolbar={
            <Space>
              {importPermission && (
                <Dropdown.Button
                  menu={{
                    items: [
                      {
                        label: i18n.t('global.importImages'),
                        key: 'IMAGES',
                        disabled: !importPermission,
                      },
                    ],
                    onClick: () => importImgBtnOnClick(),
                  }}
                  onClick={importBtnOnClick}
                  trigger={['click']}
                >
                  {i18n.t('global.import')}
                </Dropdown.Button>
              )}
              {createPermission && (
                <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                  {i18n.t('global.new')}
                </Button>
              )}
            </Space>
          }
        />
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          enableDisabledTrigger
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          enableCache
          cacheKey="PRODUCT"
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: editPermission
              ? (record) => ({
                  onClick: () => {
                    editBtnOnClick(record);
                  },
                })
              : undefined,
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
        <AddDrawer
          modalProps={{
            open: addModalVisible,
            maskClosable: false,
          }}
          conformLoading={addModalConfirmLoading}
          onCancel={addModalOnCancel}
          onSubmit={addModalOnSubmit}
        />
        <ImportModal
          modalProps={{
            open: importModalVisible,
            onCancel: importModalOnCancel,
            maskClosable: false,
          }}
          onFinish={onImportantFinish}
          onGoBack={() => setImportModalVisible(false)}
        />
        <ImportImgModal
          modalProps={{
            open: importImgModalVisible,
            onCancel: importImgModalOnCancel,
            maskClosable: false,
          }}
          onFinish={onImportantImgFinish}
        />
        <DetailDrawer
          open={detailDrawerVisible}
          title={
            <>
              {i18n.t('global.productDetails')}
              {current ? ` [${current.code}]` : ''}
            </>
          }
          onClose={detailDrawerOnClose}
        />
      </div>
    </ProdContext.Provider>
  );
};

export default Prod;
