import { SwapRightOutlined } from '@ant-design/icons';
import { Form, FormProps, Input, Select, Switch } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useImperativeHandle, useRef } from 'react';

import styles from './OperateForm.module.css';

export interface OperateFormInnerRef {
  /**
   * Focus code input
   */
  focusCodeInput(): void;
  /**
   * Focus name input
   */
  focusNameInput(): void;
}

interface OperateFormProps extends FormProps {
  /**
   * “编码”是否只读
   */
  codeReadOnly?: boolean;
  /**
   * “状态”是否可见
   */
  statusVisible?: boolean;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<OperateFormInnerRef | undefined>;
}

const OperateForm: React.FC<OperateFormProps> = (props) => {
  const { codeReadOnly, statusVisible, innerRef } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useImperativeHandle(innerRef, () => ({
    focusCodeInput: () => {
      codeInputRef.current?.focus({ cursor: 'end' });
    },
    focusNameInput: () => {
      nameInputRef.current?.focus({ cursor: 'end' });
    },
  }));

  return (
    <Form
      layout="vertical"
      form={props.form}
      name={props.name}
      initialValues={{
        status: false,
      }}
      onFinish={props.onFinish}
      onFinishFailed={props.onFinishFailed}
    >
      <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
        <Input ref={codeInputRef} readOnly={codeReadOnly} maxLength={30} />
      </Form.Item>
      <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
        <Input maxLength={30} ref={nameInputRef} />
      </Form.Item>
      <Form.Item label={i18n.t('global.type')} style={{ marginBottom: 0 }} required className={styles.inputGroup}>
        <Input.Group compact style={{ display: 'flex', marginBottom: 0 }}>
          <Form.Item name="shipType" rules={canNotBeNullRules} style={{ display: 'inline-block', width: '50%' }}>
            <Select
              disabled={statusVisible}
              placeholder={i18n.t('global.delivery')}
              style={{
                width: '100%',
              }}
            >
              <Select.Option value="WAREHOUSE">{i18n.t('global.warehouse')}</Select.Option>
              <Select.Option value="SHOP">{i18n.t('global.shop')}</Select.Option>
            </Select>
          </Form.Item>
          <SwapRightOutlined
            style={{
              fontSize: 30,
              marginLeft: 6,
              color: '#108ee9',
            }}
          />
          <Form.Item
            name="receiptType"
            rules={canNotBeNullRules}
            style={{
              display: 'inline-block',
              width: '50%',
              marginLeft: 8,
            }}
          >
            <Select
              disabled={statusVisible}
              placeholder={i18n.t('global.receiver')}
              style={{
                width: '100%',
              }}
            >
              <Select.Option value="WAREHOUSE">{i18n.t('global.warehouse')}</Select.Option>
              <Select.Option value="SHOP">{i18n.t('global.shop')}</Select.Option>
            </Select>
          </Form.Item>
        </Input.Group>
      </Form.Item>

      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={3} />
      </Form.Item>
      {statusVisible && (
        <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
          <Switch />
        </Form.Item>
      )}
    </Form>
  );
};

export default OperateForm;
