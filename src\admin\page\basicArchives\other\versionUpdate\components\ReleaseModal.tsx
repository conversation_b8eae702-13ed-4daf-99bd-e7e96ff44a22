/**
 * Create by codegen at 2022-07-26
 * 应用版本管理 - 发布
 */
import { DatePicker, Form, Radio } from 'antd';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

interface ReleaseModalProps {
  visible?: boolean;
  current?: Record<string, any>;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

const ReleaseModal: React.FC<ReleaseModalProps> = (props) => {
  const { visible, confirmLoading, current, onOk, onCancel } = props;
  const [form] = Form.useForm();
  const [radioKey, setRadioKey] = useState(1);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      values.appointmentUpdateTag = radioKey === 3;
      values.defaultTag = radioKey === 2;
      values.id = current?.id;
      onOk(values);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setRadioKey(1);
    }
  }, [form, visible]);

  useEffect(() => {
    if (visible && current) {
      if (current?.appointmentUpdateTag) {
        current.appointmentUpdateDate = moment(current.appointmentUpdateDate);
        setRadioKey(3);
      } else if (current.defaultTag) {
        setRadioKey(2);
      } else {
        current.appointmentUpdateDate = moment();
        setRadioKey(1);
      }
      form.setFieldsValue(current);
    }
  }, [visible, current, form]);

  return (
    <Modal
      title={i18n.t('global.release')}
      width={600}
      open={visible}
      confirmLoading={confirmLoading}
      onOk={modalOnOk}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Form.Item label={i18n.t('global.release')}>
          <Radio.Group onChange={(e) => setRadioKey(e.target.value)} value={radioKey}>
            <Radio value={1}>{i18n.t('global.notYetReleased')}</Radio>
            <Radio value={2}>{i18n.t('global.publishNow')}</Radio>
            <Radio value={3}>{i18n.t('global.bookAnAppointment')}</Radio>
          </Radio.Group>
        </Form.Item>
        {radioKey === 3 && (
          <Form.Item name="appointmentUpdateDate" label={i18n.t('global.updateTime')} rules={canNotBeNullRules}>
            <DatePicker showTime />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default ReleaseModal;
