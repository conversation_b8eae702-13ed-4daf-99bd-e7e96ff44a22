import { Input, Radio } from 'antd';
import * as Bwob<PERSON>rder<PERSON><PERSON> from 'common/api/warehouse/BwobOrder';
import * as WobOrder<PERSON><PERSON> from 'common/api/warehouse/WobOrder';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import BoxAbnormalDetailsPreviewModal from 'common/components/BoxAbnormalDetailsPreviewModal';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import BoxStatusSelect from 'common/components/Select/BoxStatusSelect';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  onRefresh?: () => void;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId, localTag, normalBoxCount, abnormalBoxCount } = orderRecord;
  const { onRefresh, innerRef } = props;
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [currentBoxId, setCurrentBoxId] = useState<Record<string, any>[]>([]);
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [dataType, setDataType] = useState<'normal' | 'error'>('normal');
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);
  const [currentBoxMessage, setCurrentBoxMessage] = useState('');
  const [boxAbnormalDetailsPreviewModalVisible, setBoxAbnormalDetailsPreviewModalVisible] = useState<boolean>(false);

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:W:OB');
  const boxResetPermission = permission.codes.includes('BOX_RESET');
  const boxDeletePermission = permission.codes.includes('DELETE_BOX');

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);
  const { SHOW_RFID_EXT_FLAG: showRfidExt } = useSetting([{ code: 'SHOW_RFID_EXT_FLAG', valueType: 'BOOLEAN' }]);

  const fetchData = useCallback(
    async (params) => {
      const payload: any = {
        ...params,
        wobOrderId: orderRecord.id,
        correctTag: dataType === 'normal',
      };
      let rec;
      if (currentUser.mode === 'BINDING') {
        rec = await BwobOrderApi.Boxs(payload);
      } else if (currentUser.mode === 'WRITE') {
        rec = await WobOrderApi.Boxs(payload);
      }
      return rec;
    },
    [currentUser.mode, dataType, orderRecord.id],
  );

  const _onRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const resetBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmResetBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BwobOrderApi.BoxReset({
              boxCode: record.boxCode,
              wobOrderId: orderId,
            });
          } else {
            await WobOrderApi.BoxReset({
              boxCode: record.boxCode,
              wobOrderId: orderId,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const deleteBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDeleteBox'),
      content: record.code,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BwobOrderApi.BoxDelete({
              id: record.id,
            });
          } else {
            await WobOrderApi.BoxDelete({
              id: record.id,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const openDetail = async (record) => {
    setCurrentBoxMessage(record.msg);
    setCurrentBoxCode(record.boxCode);
    setCurrentBoxId(record.boxId);
    if (record.correctTag) {
      setBoxDetailsPreviewDrawerOpen(true);
      setBoxDetailsPreviewDrawerLoading(true);
      try {
        let result;
        const payload = {
          wobBoxId: record.id,
          wobOrderId: orderId,
          enablePage: false,
          showRfidExtFlag: showRfidExt,
        };
        if (currentUser.mode === 'BINDING') {
          result = await BwobOrderApi.Rfids(payload);
        } else {
          result = await WobOrderApi.Rfids(payload);
        }

        // setCurrentBoxDetailRfidData(result.data.filter((n) => n.epc !== null));
        setCurrentBoxDetailRfidData(result.data);
      } catch (e) {}
      setBoxDetailsPreviewDrawerLoading(false);
    } else {
      setBoxAbnormalDetailsPreviewModalVisible(true);
    }
  };

  const abnormalSkuListRequest = useCallback(
    (params: Record<string, any>) => {
      params.wobOrderId = orderId;
      params.wobBoxId = currentBoxId;
      let result;
      if (currentUser.mode === 'BINDING') {
        result = BwobOrderApi.AbnormalLines(params);
      } else {
        result = WobOrderApi.AbnormalLines(params);
      }
      return result;
    },
    // eslint-disable-next-line
    [orderId, currentBoxId],
  );

  const abnormalEpcListRequest = useCallback(
    (params: Record<string, any>) => {
      params.wobOrderId = orderId;
      params.wobBoxId = currentBoxId;
      let result;
      if (currentUser.mode === 'BINDING') {
        result = BwobOrderApi.AbnormalTags(params);
      } else {
        result = WobOrderApi.AbnormalTags(params);
      }
      return result;
    },
    // eslint-disable-next-line
    [orderId, currentBoxId],
  );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId, dataType]);

  let columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      sorter: true,
      ellipsis: true,
      minWidth: 180,
      auto: true,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      dataIndex: 'operateMode',
      valueType: 'codeName',
      codeDataIndex: 'operateMode',
      nameDataIndex: 'operateModeDesc',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.packedQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.reason'),
      dataIndex: 'msg',
      width: 150,
    },
  ];

  if (dataType === 'normal') {
    columns = columns.filter((n) => n.dataIndex !== 'msg');
  }

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: <BoxStatusSelect placeholder={i18n.t('global.status')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    valueType: 'action',
    fixed: 'right',
    align: 'center',
    actionConfig: [],
  };

  if (boxResetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        record.wholeTag ||
        (orderRecord.status !== 'NEW' && orderRecord.status !== 'PROCESSING') ||
        record.status !== 'UPLOADED',
      onClick: resetBtnOnClick,
    });
  }

  if (boxDeletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        record.status === 'FINISHED' || orderRecord.status === 'FINISHED' || orderRecord.status === 'CANCELED',
      onClick: deleteBtnOnClick,
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  if (localTag) {
    columns = columns.filter((n) => n.dataIndex !== 'qty');
  }

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={20}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        pagination
        request={fetchData}
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        quickSearchPanelFieldsWrap
        refreshBtnVisible
        leftToolbar={[
          <Radio.Group
            value={dataType}
            optionType="button"
            onChange={({ target: { value } }) => {
              setDataType(value);
            }}
            options={[
              {
                label: `${i18n.t('global.normal')} (${normalBoxCount})`,
                value: 'normal',
              },
              {
                label: `${i18n.t('global.abnormal')} (${abnormalBoxCount})`,
                value: 'error',
              },
            ]}
          />,
        ]}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
      <BoxAbnormalDetailsPreviewModal
        title={
          <>
            {i18n.t('global.boxLine')} - {currentBoxCode}
          </>
        }
        message={currentBoxMessage}
        skuListRequest={abnormalSkuListRequest}
        epcListRequest={abnormalEpcListRequest}
        open={boxAbnormalDetailsPreviewModalVisible}
        onCancel={() => setBoxAbnormalDetailsPreviewModalVisible(false)}
      />
    </>
  );
};

export default BoxTable;
