import { Form, Input, InputNumber, Select, Switch } from 'antd';
import * as ExtFieldApi from 'common/api/core/ExtField';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const { Option } = Select;

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  currentType: string;
  current: Partial<Record<string, any>> | undefined;
  onSubmit: (values: Record<string, any>) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, currentType, current, visible, confirmLoading } = props;
  const addInputRef = useRef<any>(null);
  const editInputRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  const [permission] = usePermission('A:BASE:OTHER:EXT_FIELD');
  const deletePermission = permission.codes.includes('DELETE');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const fetchExtField = useCallback(async () => {
    if (!current) return;
    setLoading(true);
    try {
      const res: any = await ExtFieldApi.Get({
        id: current?.id,
      });
      if (typeof res.disabled === 'boolean') {
        res.status = !res.disabled;
      }
      form.setFieldsValue(res);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [current, form]);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
    if (current && visible) {
      fetchExtField();
    }
    setTimeout(() => {
      if (current) {
        editInputRef.current?.focus({ cursor: 'end' });
      } else {
        addInputRef.current?.focus({ cursor: 'end' });
      }
    }, 500);
  }, [current, visible, form, fetchExtField]);

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        onSubmit(values);
      })
      .catch(() => {});
  };

  const title: React.ReactNode = current?.code ? (
    <>
      {i18n.t('global.editExtField')}
      {` [${current?.code}]`}
    </>
  ) : (
    i18n.t('global.newExtField')
  );

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await ExtFieldApi.Delete({ id: current?.id });
          NoticeUtil.success();
          if (onCancel) onCancel();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchExtField();
  };

  return (
    <Drawer
      title={title}
      destroyOnClose
      open={visible}
      onClose={onCancel}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleOk}
          deletePermission={deletePermission && !!current}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onCancel}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={addInputRef} readOnly={!!current} maxLength={20} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={editInputRef} maxLength={50} />
          </Form.Item>
          {currentType === 'PARTNER' && (
            <Form.Item label={i18n.t('global.partnerType')} name="subType" rules={canNotBeNullRules}>
              <Select style={{ width: 120 }}>
                <Option value="FACTORY">{i18n.t('global.factory')}</Option>
                <Option value="SHOP">{i18n.t('global.shop')}</Option>
                <Option value="WAREHOUSE">{i18n.t('global.warehouse')}</Option>
              </Select>
            </Form.Item>
          )}
          <Form.Item label={i18n.t('global.fieldType')} name="fieldType" rules={canNotBeNullRules}>
            <Select style={{ width: 120 }}>
              <Option value="NUM">{i18n.t('global.num')}</Option>
              <Option value="TXT">{i18n.t('global.txt')}</Option>
              <Option value="URL">{i18n.t('global.link')}</Option>
              <Option value="IMG">{i18n.t('global.image')}</Option>
              <Option value="AMT">{i18n.t('global.amount')}</Option>
            </Select>
          </Form.Item>
          <Form.Item name="sortId" rules={canNotBeNullRules} label={i18n.t('global.sortId')}>
            <InputNumber min={0} step={1} precision={0} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
