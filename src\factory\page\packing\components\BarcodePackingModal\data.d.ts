import { ITableBaseItem } from '../../data';

interface ITableDataLineItem extends ITableBaseItem {
  barcode: string;
  /** 单据数 */
  qty: number;
  /** 剩余数 */
  availableQty: number;
  /** 操作数 */
  operateQty: number;
  /** 已装箱数 */
  actQty: number;
  /** 异常：错误数 */
  errorQty: number;
  /** 异常：超量数 */
  overQty: number;
  /** SKU ID */
  skuId: string;
  /** 条码别名 */
  alias?: any;
  /** 来源 */
  source?: string;
  /** 品牌名称 */
  brandName?: string;
  /** 品牌编码 */
  brandCode?: string;
}

interface INormalDataItem {
  /** 条码 */
  barcode?: string;
  /** 操作数量 */
  qty: number;
  /** SKU ID */
  skuId: string;
}

export type { INormalDataItem, ITableDataLineItem };
