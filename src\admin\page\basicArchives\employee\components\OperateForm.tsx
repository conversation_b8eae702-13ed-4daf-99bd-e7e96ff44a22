import { Form, FormProps, Input, Switch } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useImperativeHandle, useRef } from 'react';

export interface OperateFormInnerRef {
  /**
   * Focus code input
   */
  focusCodeInput(): void;
  /**
   * Focus name input
   */
  focusNameInput(): void;
}

interface OperateFormProps extends FormProps {
  /**
   * “编码”是否只读
   */
  codeReadOnly?: boolean;
  /**
   * “状态”是否可见
   */
  statusVisible?: boolean;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<OperateFormInnerRef | undefined>;
}

const OperateForm: React.FC<OperateFormProps> = (props) => {
  const { codeReadOnly, statusVisible, innerRef } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useImperativeHandle(innerRef, () => ({
    focusCodeInput: () => {
      codeInputRef.current?.focus({ cursor: 'end' });
    },
    focusNameInput: () => {
      nameInputRef.current?.focus({ cursor: 'end' });
    },
  }));

  return (
    <Form
      layout="vertical"
      form={props.form}
      name={props.name}
      initialValues={{
        allowLogin: true,
        status: false,
      }}
      onFinish={props.onFinish}
      onFinishFailed={props.onFinishFailed}
    >
      <Form.Item
        name="code"
        rules={canNotBeNullRules}
        label={
          <>
            {i18n.t('global.code')}/{i18n.t('global.account')}
          </>
        }
      >
        <Input ref={codeInputRef} readOnly={codeReadOnly} maxLength={20} />
      </Form.Item>
      <Form.Item label={i18n.t('global.accountName')} name="name" rules={canNotBeNullRules}>
        <Input ref={nameInputRef} maxLength={50} />
      </Form.Item>
      <Form.Item label={i18n.t('global.email')} name="email">
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.mobile')} name="mobile">
        <Input.Group compact>
          <Form.Item
            noStyle
            name="mobileArea"
            rules={[
              {
                pattern: /^\d{1,3}$/,
                message: i18n.t('global.areaError'),
              },
            ]}
          >
            <Input prefix="+" style={{ width: '20%' }} />
          </Form.Item>
          <Form.Item
            noStyle
            name="mobile"
            rules={[
              {
                pattern: /^[\d-]{7,11}$/,
                message: i18n.t('global.mobileError'),
              },
            ]}
          >
            <Input style={{ width: '80%' }} />
          </Form.Item>
        </Input.Group>
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item label={i18n.t('global.allowLogin')} valuePropName="checked" name="allowLogin">
        <Switch />
      </Form.Item>
      {statusVisible && (
        <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
          <Switch />
        </Form.Item>
      )}
    </Form>
  );
};

export default OperateForm;
