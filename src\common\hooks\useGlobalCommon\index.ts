import { removeToken, setToken } from 'common/api/Base';
import * as PermissionApi from 'common/api/core/Permission';
import { getTenantToken, getToken } from 'common/api/core/Security';
import * as Ser<PERSON>ist<PERSON>pi from 'common/api/core/SerList';
import * as UserApi from 'common/api/core/User';
import * as TenantSecurityApi from 'common/api/tenant/Security';
import * as ConfigApi from 'common/api/core/Config';
import { buildTargetConfig } from 'common/app/GlobalVars';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { useCallback, useContext } from 'react';

let userVar: any;
const useGlobalCommon = () => {
  const { state, dispatch } = useContext<TGlobalContext>(GlobalContext);
  const { loginType } = state;

  const login = useCallback(
    async (
      payload: Record<string, any>,
      callbacks: {
        setErrorOpen: (open: boolean) => void;
        setErrorMsg: (msg: string) => void;
        setLoggingIn: (logging: boolean) => void;
      } = {
        setErrorOpen: () => {},
        setErrorMsg: () => {},
        setLoggingIn: () => {},
      },
    ) => {
      dispatch({ type: 'setIsAuthenticating', payload: true });
      callbacks.setErrorOpen(false);
      try {
        callbacks.setLoggingIn(true);
        let tokenData: any = {};
        if (payload.userType === 'TENANT') {
          // 云后台登录
          tokenData = await getTenantToken(payload, false);
        } else {
          // 后台，仓库，门店，工厂登录
          tokenData = await getToken(payload, false);
        }
        callbacks.setLoggingIn(false);
        setToken(tokenData);
        dispatch({ type: 'setPreChecked', payload: true });
        dispatch({ type: 'setIsAuthenticated', payload: true });
        dispatch({ type: 'setIsAuthenticating', payload: false });
        return true;
      } catch (e: any) {
        if (e.code === 'ECONNABORTED' || e.code === 'ERR_NETWORK') {
          callbacks.setErrorMsg(i18n.t('global.networkError'));
        } else if (e.response?.data?.message) {
          callbacks.setErrorMsg(e.response?.data?.message);
        } else {
          callbacks.setErrorMsg(i18n.t('global.loginIncorrect'));
        }
        callbacks.setErrorOpen(true);

        callbacks.setLoggingIn(false);
        dispatch({ type: 'setIsAuthenticating', payload: false });
        throw e;
      }
    },
    [dispatch],
  );

  const fetchUserData = useCallback(async () => {
    dispatch({ type: 'setIsCurrentUserFetching', payload: true });
    try {
      let user: any = {};
      if (loginType === 'TENANT') {
        user = await TenantSecurityApi.GetByLogin(false);
      } else {
        user = await UserApi.GetByLogin(false);
      }
      dispatch({ type: 'setCurrentUser', payload: user });
      userVar = user;
      dispatch({ type: 'setIsCurrentUserFetching', payload: false });
      return user;
    } catch (e) {
      dispatch({ type: 'setIsCurrentUserFetching', payload: false });
      throw e;
    }
  }, [dispatch, loginType]);

  const preCheck = useCallback(
    async (
      callbacks: {
        setErrorOpen?: (open: boolean) => void;
        setErrorMsg?: (msg: string) => void;
      } = {},
    ) => {
      try {
        dispatch({ type: 'setPreChecking', payload: true });
        await fetchUserData();
        dispatch({ type: 'setPreChecking', payload: false });
        dispatch({ type: 'setIsAuthenticated', payload: true });
        dispatch({ type: 'setPreChecked', payload: true });
      } catch (e: any) {
        dispatch({ type: 'setPreChecking', payload: false });
        if (e.response && e.response.status && (e.response.status === 401 || e.response.status === 403)) {
          removeToken();
          // 没权限的提示信息
          if (callbacks.setErrorMsg) {
            callbacks.setErrorMsg(e.response?.data?.message || i18n.t('global.noPermission'));
          }
          if (callbacks.setErrorOpen) {
            callbacks.setErrorOpen(true);
          }
        }
        dispatch({ type: 'setIsAuthenticated', payload: false });
        dispatch({ type: 'setPreChecked', payload: true });
        throw e;
      }
    },
    [dispatch, fetchUserData],
  );

  const fetchPermission = useCallback(async () => {
    dispatch({ type: 'setPermissionFetching', payload: true });
    if (!userVar) return false;
    try {
      const permissionListResp = await PermissionApi.GetPermissionByLogin({
        enablePage: false,
        client: 'WEB',
        subClient: buildTargetConfig.menuSubClientCode,
        mode: userVar.mode === 'BINDING' ? 'BIND' : 'WRITE',
      });

      const settings = await ConfigApi.List({ codes: ['FACTORY_FQC_ENABLE'], enablePage: false });
      const factoryFqcEnable = settings.data.find((n) => n.code === 'FACTORY_FQC_ENABLE')?.value === 'true';

      // add depth field to items.
      const rootItem = permissionListResp.data.find((n) => !n.parentCode);
      if (rootItem) {
        (function loop(item, lastDepth) {
          const depth = lastDepth + 1;
          item.depth = depth;
          const childs = permissionListResp.data.filter((n) => n.parentCode === item.code);
          if (childs.length > 0) {
            childs.forEach((n) => {
              loop(n, depth);
            });
          }
        })(rootItem, 0);
      }

      if (!factoryFqcEnable) {
        permissionListResp.data = permissionListResp.data.filter(
          (n) => !(n.code.startsWith('A:F:FQC') || n.code.startsWith('F_C:INSPECT')),
        );
      }

      dispatch({ type: 'setPermissionList', payload: permissionListResp.data });
      dispatch({ type: 'setPermissionFetching', payload: false });
      dispatch({ type: 'setPermissionFetched', payload: true });
    } catch (e) {
      dispatch({ type: 'setPermissionFetching', payload: false });
      throw e;
    }
    return true;
  }, [dispatch]);

  /**
   * 查询已启用的服务
   */
  const fetchServices = useCallback(async () => {
    dispatch({ type: 'setServicesFetching', payload: true });
    if (!userVar) return false;
    try {
      const servicesResp: any = await SerListApi.SerList();
      dispatch({ type: 'setServices', payload: servicesResp });
      dispatch({ type: 'setServicesFetching', payload: false });
    } catch (e) {
      dispatch({ type: 'setServicesFetching', payload: false });
      throw e;
    }
    return true;
  }, [dispatch]);

  return { buildTargetConfig, login, preCheck, fetchPermission, fetchServices };
};

export default useGlobalCommon;
