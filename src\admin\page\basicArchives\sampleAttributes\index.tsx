import SampleBrand from 'admin/page/basicArchives/sampleAttributes/sampleBrand';
import ColorGrp from 'admin/page/basicArchives/sampleAttributes/sampleColorGrp';
import PriCategory from 'admin/page/basicArchives/sampleAttributes/samplePriCategory';
import SizeGrp from 'admin/page/basicArchives/sampleAttributes/sampleSizeGrp';
import SubCategory from 'admin/page/basicArchives/sampleAttributes/samlpleSubCategory';
import Year from 'admin/page/basicArchives/sampleAttributes/sampleYear';
import Series from 'admin/page/basicArchives/sampleAttributes/sampleSeries';
import TabsMenu from 'common/components/TabsMenu';
import AppHeader from 'common/layout/AppHeader';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useState } from 'react';
import SampleExtField from './sampleExtField';

const currentMenuCode = 'A:BASE:SAMPLE_ATTRIBUTE';

const SampleAttributes: React.FC = () => {
  // 获取当前菜单编码的所有权限
  const [permission] = usePermission(currentMenuCode);
  // 筛选当前编码的有权限的子集菜单
  const permissionMenu = permission.allChild.filter((item: Record<string, any>) => item.depth === 4);
  // const navigate = useNavigate();
  const [activeMenuCode, setActiveMenuCode] = useState('');
  const tabsMenuOnChange = (record) => {
    setActiveMenuCode(record.code);
    // navigate(path);
  };

  useEffect(() => {
    if (permissionMenu.length > 0 && !activeMenuCode) {
      setActiveMenuCode(permissionMenu[0].code);
    }
    // eslint-disable-next-line
  }, [permissionMenu, activeMenuCode]);

  const elementMap: Record<string, any> = {
    'A:BASE:SAMPLE_ATTRIBUTE:BRAND': <SampleBrand />,
    'A:BASE:SAMPLE_ATTRIBUTE:YEAR': <Year />,
    'A:BASE:SAMPLE_ATTRIBUTE:SERIES': <Series />,
    'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY': <PriCategory />,
    'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY': <SubCategory />,
    'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP': <ColorGrp />,
    'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP': <SizeGrp />,
    'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD': <SampleExtField />,
  };

  return (
    <div>
      <AppHeader />
      <div className="flex h-[calc(100vh_-_160px)] gap-x-6">
        <div className="sticky top-24 box-border h-full w-[200px] shrink-0 cursor-pointer overflow-y-auto">
          <TabsMenu code={currentMenuCode} menuList={permissionMenu} onClick={tabsMenuOnChange} />
        </div>
        <div className="h-full flex-1 overflow-y-auto">{elementMap[activeMenuCode]}</div>
      </div>
    </div>
  );
};

export default SampleAttributes;
