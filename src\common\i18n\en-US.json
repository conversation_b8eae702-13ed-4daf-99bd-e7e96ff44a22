{"global.identityCheck": "Identity verification", "global.loginOtherAccount": "Log in to other account", "global.identityExpiredHelp": "Your identity information has expired, please enter your password to recheck it.", "global.profitRatio": "Inventory accuracy rate", "global.DEC": "DEC", "global.HEX": "HEX", "global.abnormal": "Abnormal", "global.abnormalBoxLine": "Exception carton details", "global.acceptFileTips": "Click or drag the file here and parse", "global.account": "Account", "global.accountName": "Username", "global.actQty": "Completed", "global.add": "Add", "global.addAllStockOrder": "Add whole inventory order", "global.addBarcode": "Add barcode", "global.additionCode": "Additional Order Number", "global.address": "Address", "global.adjustField": "Adjust the field", "global.adjustFieldDetailed": "Adjust the field mapping", "global.admin": "admin", "global.agreementCode": "Contract No", "global.aisle": "Aisle", "global.alias": "<PERSON><PERSON>", "global.all": "All", "global.allLoaded": "All loaded", "global.allNotPass": "All failed", "global.allPass": "All passed", "global.allPermission": "All permission", "global.allocatingGroupCode": "Transfer group code, only the first row needs to be filled in for each group of data\n", "global.allocatingGroupDeliverType": "Transfer group shipper type, only the first row needs to be filled in for each group of data\n", "global.allocatingGroupName": "Transfer group name, only the first row needs to be filled in for each group of data\n", "global.allocatingGroupRemark": "Transfer group remark, only the first row needs to be filled in for each group of data\n", "global.allocatingGroupTakeType": "Transfer group recipient type, only the first row needs to be filled in for each group of data\n", "global.allowLogin": "Allow log in", "global.allowStocktaking": "Allow Stocktaking", "global.amount": "Amount", "global.antennaCode": "Antenna code", "global.apiUrl": "API Address", "global.appVersionManagement": "Application Version Upgrade", "global.applicationFile": "Application file", "global.applyTsOrderConfirm": "Confirm audit selected inventory order?", "global.appointmentFail": "Reservation failed", "global.appointmentSuccess": "Reservation succeed", "global.area": "Area", "global.areaError": "Please enter a 1-3 digit area code!", "global.areaFile": "Area achive", "global.assignEmp": "Assign staffs", "global.assignPermission": "Assign permission", "global.asyncTask": "Async task", "global.autoRefresh": "Auto refresh", "global.availableForSchedule": "Available For Schedule", "global.availableQty": "Remaining <PERSON>ty", "global.avgRSSI": "RSSI avg.", "global.bankStorageAmount": "Accounted inventory", "global.barTenderTemplate": "Printing template", "global.barcode": "Barcode", "global.barcodes": "Barcodes", "global.barcodeDisabled": "Barcode disabled", "global.barcodePureNumber": "Barcode is purely numeric", "global.barcodeRecord": "Barcode Record", "global.barcodeRetailPrice": "Retail Price", "global.barcodeTagPrice": "Listed price", "global.information": "Information", "global.batchCreateAllModeTsOrder": "Batch Creating Stocktaking Order", "global.beOffline": "Go offline before  ", "global.issueDate": "Issue date", "global.block": "Data block", "global.bookAnAppointment": "Scheduled release", "global.bookingConfirmTime": "Time of submission", "global.boolean": "Boolean", "global.booleanDescribeTips": "Example: {{true}} for yes, {{false}} for no (case supported)", "global.box": "Box", "global.boxes": "Boxes", "global.boxCode": "Box No.", "global.boxConfirm": "Box Audit", "global.boxLine": "Box Details", "global.boxSpec": "Box Specification", "global.packedQty": "Packed Quantity", "global.boxSticker": "Box sticker", "global.brand": "Brand", "global.brandCode": "Brand Code", "global.brandName": "Brand Name", "global.brandRate": "Brand Ratio", "global.businessModel": "Business Model", "global.businessParameters": "Business parameters", "global.businessType": "Business Type", "global.byBarcodeTotal": "Summarize by barcode", "global.byBox": "By Box", "global.byBrand": "By brand", "global.byColor": "By color", "global.byColorProd": "By Model", "global.byDateRange": "By date range", "global.byPriCategory": "By primary category", "global.byPriceTotal": "Summarize by Model", "global.byProd": "By Model", "global.bySize": "By size", "global.bySku": "By barcode", "global.bySubCategory": "By sub-category", "global.byTag": "By label", "global.byTagTotal": "Summarize by label", "global.cancel": "Cancel", "global.cancelled": "Canceled", "global.reject": "Reject", "global.cannotBeEmpty": "Cannot be empty", "global.capacity": "Unit capacity", "global.categoryCodeList": "Category", "global.checkHandleTip": "Data is being processed, do you want to discard it?", "global.chinese": "Simplified Chinese", "global.chooseFile": "Select the file", "global.chooseFileDetailed": "Select a data source file", "global.city": "City", "global.cityCode": "City code", "global.clear": "Clear", "global.client": "Client", "global.close": "Close", "global.code": "Code", "global.codeLenRule": "Code length up to 20 characters", "global.codeOrAccount": "Code/Account", "global.codeOrSourceCode": "Code or source code", "global.codeOrAdditionCode": "Coding or appending tracking number", "global.orderCodeOrSourceOrderCode": "Order No. or source order code", "global.col": "Column", "global.collapseFilter": "Collapse filter", "global.color": "Color", "global.colorCode": "Color code", "global.colorGroup": "Color set", "global.colorGrpCode": "Color set code", "global.colorGrpName": "Color set name", "global.colorName": "Color name", "global.colorRate": "Color ratio", "global.colors": "Color", "global.columnN": "Column {{num}}", "global.companyInfo": "Company information", "global.companyName": "Company Name", "global.confirm": "Audit", "global.affirm": "Confirm", "global.confirmCancelOrder": "Confirm cancel the order?", "global.confirmConfirmOrder": "Confirm audit the order?", "global.confirmDelete": "Confirm delete?", "global.confirmDeleteBox": "Confirm delete box?", "global.confirmDialogTitle": "Confirm operation", "global.confirmFastReceipt": "Confirm fast receiving?", "global.confirmResetBox": "Confirm reset the box?", "global.confirmResetOrder": "Confirm reset the order?", "global.confirmResetData": "Confirm reset the data?", "global.confirmStatus": "Audit status", "global.confirmSuccess": "Audit success", "global.confirmTime": "Audit time", "global.confirmTip": "Confirm audit?", "global.confirmSetDefault": "Are you sure you want to setting as default?", "global.confirmConfirmBox": "Cofirm  audit carton?", "global.confirmCancel": "Confirm cancel?", "global.contact": "Contact", "global.continueImportImg": "Continue to import images", "global.contractNumber": "Contract No", "global.count": "Quantity", "global.create": "Create", "global.createPrintTask": "Create Print Task", "global.createPrintTaskFailTip": "Failed to create print task, please choose barcode", "global.createPrintTaskSuccess": "Print task creation succeed", "global.created": "Creation time", "global.createdDone": "Creation success", "global.createdFail": "Failed to create", "global.currentLogin": "Current log in", "global.currentPosition": "Current position", "global.customize": "Customize", "global.date": "Date", "global.dateTime": "Date & Time", "global.dayFitting": "Average number of daily fittings", "global.default": "<PERSON><PERSON><PERSON>", "global.defaultImg": "Default image", "global.defaultReturnWarehouse": "Default Return Warehouse", "global.delete": "Delete", "global.deliveryDate": "Delivery date", "global.deliveryQty": "Delivered quantity", "global.shipperSubWarehouse": "Shipper's storage position", "global.designCode": "Design code", "global.detail": "Detail", "global.details": "Details", "global.detailAddress": "Detail Address", "global.detectedFileHasNColAndRow": "The content of the data source file parsed totals {{colNum}} columns and {{rowNum}} rows.", "global.device": "Devices", "global.deviceCode": "Device code", "global.deviceDashboard": "Device dashboard", "global.deviceManagement": "Device profile", "global.dict": "Data dict", "global.dictData": "Dict data", "global.diff": "Discrepancy", "global.diffCost": "Discrepancy value", "global.diffQty": "Discrepant Quantity", "global.disableColor": "Disabled color", "global.disableSize": "Disabled size", "global.disableSpec": "Disabled spec", "global.disabled": "Inactive", "global.disturb": "Interference material", "global.disturbTip": "The product has materials that interfere with RFID signal (metal wire, laser tag, polyester fiber)", "global.doubleGroupCodeError": "Duplicate group code detected", "global.download": "Download", "global.downloadCount": "Visit count", "global.downloadDataTemplate": "Download data template", "global.downloadSnap": "Download stock", "global.downloadSnapSuccess": "Task executed successful, please wait...", "global.downloading": "Downloading", "global.edit": "Edit", "global.editBarcode": "Edit barcode", "global.editProdImg": "Edit product images", "global.email": "Email", "global.employee": "Employee", "global.emptyTip1": "No normal data, click to continue", "global.emptyTip2": "Press button to start reading", "global.enableColor": "Enable color", "global.enableRfid": "RFID Only", "global.enableSize": "Enable size", "global.enableSpec": "Enable spec", "global.enabled": "Active", "global.enum": "Enumeration", "global.equalColorCode": "Represents color code", "global.equalProduct": "Represents product", "global.equalProductCode": "Represents Model", "global.equalSort": "Represents the sorting,where 0 indicates the default sorting", "global.error": "Exception", "global.errorData": "Data exception", "global.example": "Example", "global.excelCol": "Excel column", "global.expTime": "Expected delivery time", "global.expandFilter": "Show filters", "global.explain": "Instructions", "global.expressNumber": "Tracking number", "global.export": "Export", "global.extField": "Extended fields", "global.extendedProperties": "Extended properties", "global.factory": "Factory", "global.factoryCode": "Factory code", "global.factoryEmployee": "Factory Employee", "global.fadBox": "Scheduling Cartons", "global.fadOrder": "Scheduling delivery order", "global.fadOrderRecord": "Scheduled Delivery Record", "global.failed": "Failed", "global.fastReceipt": "Fast receiving", "global.field": "Fields", "global.fieldCanNotBeNull": "This field is required", "global.fieldName": "Field name", "global.fieldType": "Field type", "global.fieldsExplanation": "Field description", "global.fileName": "File name", "global.fillOrderBasicInfo": "Fill in the basic information of the order", "global.done": "Done", "global.finish": "Finish", "global.finished": "Completed", "global.firstRowIsHeader": "First row is column header", "global.fittingAnalysis": "Fitting room data analysis", "global.fittingCount": "Fitting counts", "global.fittingDashboard": "Fitting room dashboard", "global.fittingNumber": "Distribution of fitting counts", "global.fittingRecord": "Raw fitting data", "global.fittingRoom": "Fitting room", "global.fixedLength": "Fixed Length", "global.fm": "Factory production", "global.fmOrder": "Production order", "global.fmOrderCode": "Production order No.", "global.fob": "Factory Outbound", "global.fromPartner": "Source warehouse", "global.gender": "Gender", "global.genderCode": "Gender code", "global.genderName": "Gender name", "global.goodAfternoon": "Good Afternoon", "global.goodEarlyMorning": "Good Dawning", "global.goodEvening": "Good Evening", "global.goodMidnight": "Good Night", "global.goodMorning": "Good Morning", "global.goodNoon": "Good Noon", "global.groupCode": "Group code", "global.groupName": "Group name", "global.groupRemark": "Group remark", "global.grpInfo": "Group info", "global.handset": "Handheld", "global.hasAllPermission": "All permission", "global.height": "Height(mm)", "global.help": "Help", "global.historyInboundOutbound": "Historical Inbound/Outbound", "global.historyInventory": "Historical Stocktaking", "global.historyOperation": "Historical operation", "global.homePage": "Home", "global.hotProd": "Hot product(Top 10)", "global.hotProdColor": "Hop product color(Top 10)", "global.identityExpiredMessage": "Your identity has expired, please log in again", "global.identityVerificationSuccessful": "Identity verification successful", "global.image": "Images", "global.import": "Import", "global.importDataEmptyWarning": "The content of your data source file is detected to be empty. Please select again ", "global.importDetailed": "Import your data into the system", "global.importError": "Import the errors", "global.importErrorMsg": "Import error message", "global.importFieldsDetail": "Import field details", "global.importImages": "Import images", "global.importProcessing": "Products are importing, please wait...", "global.importProd": "Products import", "global.importProdImg": "Import product images", "global.importProdImgRule": "Rules of product images", "global.importProduct": "Import product", "global.importRowError": "Row {{num}}, found {{errNum}} errors", "global.importSkipAdjustTips": "If no adjustment is needed, simply click Next to skip this step.", "global.importSkuImgRule": "Rules of Model images", "global.importSnap": "Import stock", "global.importSuccess": "<PERSON><PERSON><PERSON> succeeded", "global.importSuccessTips": "Import task successfully executed. Total of {{dataLength}} data are imported in {{totalTime} seconds", "global.importTemplate": "Import template", "global.importTip": "Click or drag the file here and parse", "global.importingData": "Importing data for you...", "global.inProcessing": "Processing...", "global.inbound": "Inbound", "global.inboundBox": "Inbound Boxes", "global.inboundQty": "Inbound Quantity", "global.inboundOrder": "Inbound Order", "global.inboundProgress": "Inbound Progress", "global.informationCheck": "Information confirm", "global.init": "Initialization", "global.initializing": "Initializing", "global.innerCode": "Internal code", "global.inputBarcodeThenEnterToSearch": "Input the barcode and press Enter to search...", "global.inputCode": "Please input code", "global.inputCodeThenEnterToSearch": "Input the code and press Enter to search...", "global.inputEPCToSource": "Input EPC to query traceability information", "global.inputKey": "Please input keywords", "global.inputNameThenEnterToSearch": "Input the name and press Enter to search", "global.inputOrderCodeThenEnterToSearch": "Input order No. and press Enter to search", "global.inputSortId": "Please input sorted ID", "global.inputUniqueCodeToSource": "Input the unique code to query the traceability information", "global.interface": "View", "global.internationalizedName": "Internationalized name", "global.inventory": "Stocktaking", "global.inventoryAmountRatio": "Discrepancy rate", "global.inventoryCoverage": "Inventory accuracy", "global.ipPosition": "IP Address", "global.isDefault": "<PERSON><PERSON><PERSON>", "global.isDisabled": "Disabled", "global.isNormal": "Normal", "global.item": "Control", "global.keepImport": "Continue to import", "global.keyword": "Keyword", "global.language": "Language", "global.lastModified": "Last modified", "global.lastSevenDays": "Last 7 days", "global.lastTenTimes": "Last 10 inventory check", "global.lastThirtyDays": "Last 30 days", "global.lastTwentyTimes": "Last 20 inventory check", "global.latitude": "Latitude", "global.length": "Length(mm)", "global.line": "Details", "global.lineRange": "Detailed range", "global.link": "Link", "global.listDevice": "Device list", "global.loading": "Loading...", "global.localOrder": "Local order", "global.log": "Logs", "global.login": "Log In", "global.logistics": "Logistics company", "global.logisticsCode": "Logistics company code", "global.longitude": "Longitude", "global.loss": "Inventory Loss", "global.manualCount": "Manual count", "global.mapEquipment": "Device map", "global.matchedDataCount": "Matched {{num}} piece of data", "global.maxRSSI": "Max RSSI", "global.menu": "<PERSON><PERSON>", "global.minRSSI": "Minimum RSSI", "global.minValueValidMsg": "Minimum value is {num}", "global.minute": "Minute", "global.mixBox": "Mixed Packing", "global.mixColorBoxingMode": "Mixed Model,same color,same size", "global.mixColorSizeBoxingMode": "Same Model, mixed color, mixed size", "global.mixProdBoxingMode": "Mixed Model", "global.mixSizeBoxingMode": "Same Model, same color, mixed size", "global.mobile": "Mobile", "global.mobileError": "Please input 7 to 11 digit mobile phone number", "global.mode": "Mode", "global.modified": "Modify time", "global.modify": "Modify", "global.modifyRemark": "Modify Remark", "global.module": "<PERSON><PERSON><PERSON>", "global.more": "More", "global.mustBeBoolean": "Must be true or false", "global.mustBeNumber": "Must be a number", "global.mustBeText": "Must be text", "global.name": "Name", "global.loginFailed": "<PERSON><PERSON> failed", "global.loginIncorrect": "Please check whether your account and password is correct, or contact the management", "global.networkError": "Unable to connect to server, please check your network connection", "global.new": "New", "global.newBox": "New Box", "global.newOrder": "New Order", "global.newTag": "New label", "global.nextStep": "Next Step", "global.no": "No", "global.noChangeColorGrpWithSku": "This product already has barcodes, the color set cannot be modified", "global.noChangeSizeGrpWithSku": "This product already has a barcode and the size group cannot be modified.", "global.noDataFoundInExcel": "Can't find data in Excel", "global.noPermission": "No permission currently", "global.noPrinter": "No printer", "global.normal": "Normal", "global.notFoundDataInNextPage": "No available data found in the next page", "global.notFoundEPC": "Unable to find relevant information for this EPC", "global.notFoundUniqueCode": "Unable to find relevant information for this unique code", "global.notPass": "Failed", "global.notSelected": "Not selected", "global.notYetReleased": "Not releasing at this moment", "global.notification": "Global notification", "global.num": "Number", "global.number": "Number", "global.offLine": "Offline", "global.offLineDevice": "Offline devices", "global.offLineTime": "Offline time", "global.ok": "OK", "global.oldTag": "Old label", "global.onLine": "Online", "global.onLineDevice": "Online devices", "global.onLineRatio": "Online devices percentage.", "global.onLineTime": "Online at", "global.open": "Open", "global.openOrder": "Open the Order", "global.openLinkInNewTab": "Open the link in a new tab", "global.operateEmployeeName": "Operator", "global.operateMode": "Operating mode", "global.operatingPosition": "Operating position", "global.operatingPositionType": "Operating position type", "global.operation": "Act.", "global.operationSource": "Operation source location", "global.operationSourceType": "Operation source location type", "global.operator": "Operator", "global.orderAmount": "Order amount", "global.orderCode": "Order No.", "global.orderConfirm": "Order Audit", "global.orderCount": "Order Counts", "global.orderReject": "Whole Order Rejection", "global.orderRejectSuccess": "Whole order has been rejected", "global.orderType": "Document type", "global.orderTypeCode": "Document type code", "global.orders": "Orders", "global.other": "Others", "global.otherCode": "Other code", "global.outbound": "Outbound", "global.outboundBox": "Outbound Boxes", "global.outboundCount": "Outbound Counts", "global.outboundOrder": "Outbound Document", "global.over": "Exceeded", "global.overSizeError": "The imported file cannot be larger than 10MB", "global.packingPlatform": "Packing platform", "global.pageNotFound": "Page not found", "global.parameters": "Parameter", "global.partPass": "Partially passed", "global.partner": "Partner", "global.partnerCode": "Partner code", "global.partnerType": "Partner type", "global.partnerDetails": "Partner Details", "global.pass": "Passed", "global.password": "Password", "global.pend": "Scheduled", "global.pendAppointment": "For schedule", "global.pendAppointmentBoxes": "Boxes For Schedule", "global.permission": "Permission", "global.pleaseInput": "Please Enter...", "global.pleaseInputApiUrl": "Please enter your API address", "global.pleaseInputPassword": "Please enter your password", "global.pleaseInputSomething": "Please enter your {{text}}", "global.pleaseInputPartnerCode": "Please enter your unit code", "global.pleaseInputTenatnCode": "Please enter tenant code", "global.pleaseInputYourAccount": "Please enter your account", "global.pleaseSelect": "Please select", "global.pleaseSelectDate": "Please select a date", "global.pleaseSelectShopToSearch": "Please select a store to search", "global.pleaseSelectTime": "Please select the time", "global.pleaseSelectType": "Please select type", "global.pleaseSelectWarehouseToSearch": "Please select a warehouse to search", "global.pleaseMakeSureDDSStarted": "Please confirm the DDS service is started", "global.position": "Position", "global.positionCode": "Position code", "global.postalCode": "Postcode", "global.preBox": "Pre-packed", "global.preStep": "Previous Step", "global.priCategory": "Primary category", "global.priCategoryCode": "Primary category code", "global.priCategoryName": "Primary category name", "global.priCategoryRate": "Primary category proportion", "global.price": "Price", "global.print": "Print", "global.printCnt": "Print counts", "global.printFinished": "Printed", "global.printLog": "Print logs", "global.printLogDetails": "Print logs detail", "global.printNone": "Unprinted", "global.printPosition": "Print location", "global.printQty": "Print counts", "global.printTask": "Print task", "global.printTime": "Print time", "global.printing": "Printing...", "global.printBoxSticker": "Print box sticker", "global.processing": "Processing", "global.prodDetail": "Product details", "global.prodDisabled": "Product disabled", "global.prodFile": "Product profile", "global.prodImg": "Product images", "global.prodImport": "Product import", "global.prodImportFail": "Product import failed", "global.prodImportSuccess": "Product import success", "global.prodName": "Product name", "global.prodRemark": "Product remark", "global.product": "Model", "global.productBarcode": "Model barcode", "global.productCode": "Model code", "global.productImage": "Model images", "global.productName": "Model name", "global.profit": "Inventory surplus", "global.proportion": "Proportion", "global.proportionCategory": "Proportion of each category", "global.province": "Province", "global.publishNow": "Release now", "global.queryArea": "Click to search area code", "global.range": "Range", "global.ratio": "<PERSON><PERSON>", "global.refresh": "Refresh", "global.readCount": "Read counts", "global.readTime": "Read time", "global.bulkRead": "Bulk Read (Read quantity >=20)", "global.smallRead": "Small Read (Read quantity 1~20)", "global.isOwnPartner": "Ownership", "global.onlyOwnWarehouse": "Own Warehouse Only", "global.onlyOwnStore": "Own Store Only", "global.nonOwnWarehouse": "Non-Own Warehouse", "global.nonOwnStore": "Non-Own Store", "global.readTagPower": "Reading power", "global.readTagPowerRules": "The minimum reading power is 1", "global.realAmount": "Actual amount", "global.reason": "Reason", "global.recQty": "Received qty", "global.recentlyInventory": "Last Inventory Check", "global.rejectFadOrder": "Confirm to reject this scheduled delivery order?", "global.relTime": "Actual delivery time", "global.relToPartner": "Actual target warehouse", "global.release": "Release", "global.remark": "Remark", "global.rememberMe": "Remember me", "global.rememberMeExplain": "Checking the \"Remember Me\" box will extend the validity of your authentication.", "global.reportTemplate": "Report template", "global.requestData": "Request value", "global.requestUrl": "Request URL", "global.required": "Required", "global.reset": "Reset", "global.resetSyncTask": "Resend", "global.resetTsOrderConfirm": "Confirm to reset this inventory order?", "global.responseData": "Return value", "global.result": "Result", "global.retailPrice": "Actual retail price", "global.retry": "Retry", "global.returnWarehouse": "Return Warehouse", "global.returnFrequency": "Return frequency", "global.returnFrequencyRules": "The minimum return frequency is 60!", "global.reconnect": "Reconnect", "global.serviceStatus": "Service status", "global.rfidTag": "RFID Only", "global.right": "Accurate", "global.role": "Role", "global.row": "Row", "global.rule": "Rules", "global.runningAlready": "Runned", "global.save": "Save", "global.saveSuccess": "Saved", "global.saving": "Saving...", "global.scanCount": "<PERSON><PERSON>", "global.scanToCheck": "Enter or scan the <PERSON><PERSON> number to automatically check", "global.scheduleQty": "Schedule quantity", "global.scheduleUpdate": "Schedule update?", "global.scrollToLoad": "Prompt: scroll to load", "global.search": "Search", "global.searchByProd": "Input Model code to search", "global.second": "Second", "global.select": "Select", "global.selectBarCode": "Select barcodes", "global.selectBarCodeAndWriteNumber": "Select barcode and fill in the quantity", "global.selectCountTip": "Selected Boxes: {{selectedBoxCount}}, Products: {{selectedRfidsCount}}", "global.selectEmp": "Select employee", "global.selectFactory": "Select factory", "global.selectFieldType": "Please choose field type", "global.selectShop": "Select store", "global.selectShopWarehouse": "Please select a store or warehouse for preview", "global.selectToOperateTips": "Prompt: Select data to operate", "global.selectTypeToRead": "Please select a type from the left to preview", "global.selectWarehouse": "Select warehouse", "global.selected": "Selected", "global.selectedDataNum": "Selected {{num}} piece of data", "global.selector": "Selector", "global.selectPrinter": "Select a printer", "global.switch": "Switch", "global.serialNumber": "Flow number", "global.serviceUnavailableForNow": "Service unavailable temperally", "global.setDefault": "Set as default", "global.setRange": "Set up range", "global.setStockRange": "Set up inventory order range", "global.setTsOrderRange": "Set up inventory order details range", "global.setting": "Setting", "global.settingTransferGroup": "Set up transfer group", "global.setupFieldMapWarning": "Please maintain field matching", "global.from": "From", "global.shipper": "Shipper", "global.shipperCode": "Shipper code", "global.shipperType": "Shipper type", "global.to": "To", "global.receiver": "Receiver", "global.receiverCode": "Receiver code", "global.receiverSubWarehouse": "Receiver storage", "global.receiverType": "Receiver type", "global.receiverTypeInfo": "Receiver type (Warehouse, Store, Factory)", "global.shop": "Store", "global.shopCode": "Store code", "global.shopEmployee": "Store Employee", "global.shopRanking": "Store Ranking", "global.shortcutOperation": "Shortcut operation", "global.showUnicode": "Show unique code", "global.si": "Store Inbound", "global.singleBoxingMode": "Actual retail price", "global.size": "Size", "global.sizeCode": "Size code", "global.sizeGroup": "Size group", "global.sizeGrpCode": "Size group code", "global.sizeGrpName": "Size group name", "global.sizeName": "Size name", "global.sizeRate": "Size ratio", "global.sizes": "Size", "global.skuImage": "Model image", "global.skuName": "Barcode name", "global.snapCost": "Accounted stock value", "global.snapDownFail": "Inventory synchronization failure", "global.snapDownSuccess": "Inventory synchronization success", "global.snapImport": "Import Stock", "global.snapStatus": "Stock Status", "global.so": "Store Outbound", "global.sort": "Sort", "global.sortId": "Sort ID", "global.sourceCode": "Source number", "global.sourceOrderCode": "Source order code", "global.spec": "Specifications", "global.specCode": "Specifications code", "global.specGroup": "Specifications group", "global.specGrpCode": "Specifications group code", "global.specGrpName": "Specifications group name", "global.specName": "Specifications name", "global.specs": "Specifications", "global.spell": "Spell", "global.start": "Start", "global.startTime": "Sart time", "global.startTs": "Start Inventory", "global.startTsOrderConfirm": "Confirm to start inventory?", "global.status": "Status", "global.stockCost": "Real stock value", "global.stockCount": "Real inventory", "global.stockMode": "Inventory mode", "global.stockOrder": "Inventory order", "global.stockSnap": "Inventory order stock", "global.stop": "Stop", "global.string": "String", "global.sts": "Store Inventory", "global.subCategory": "Sub-category", "global.subCategoryCode": "Sub-category code", "global.subCategoryName": "Sub-category name", "global.subCategoryRate": "Sub-category percentage.", "global.subWarehouse": "Storage", "global.subWarehouseCode": "Storage code", "global.subWarehouseName": "Storage name", "global.submitSuccess": "Submitted successfully", "global.succeed": "Succeed", "global.success": "Operate successfully", "global.suchAs": "Such as", "global.sumRSSI": "Summerized RSSI", "global.supplier": "Supplier", "global.supportType": "Supported file types", "global.supportedFileType": "Supported file types", "global.syncAction": "Return action", "global.syncCount": "Number of Return", "global.syncFail": "Return failed", "global.syncInfo": "Return details", "global.syncNone": "Not returned", "global.syncOrder": "Return task", "global.syncStatus": "Sync status", "global.syncSuccess": "Sync successed", "global.syncing": "Synchronizing", "global.systemDefault": "System Presets", "global.systemDefaultCodecRule": "System Default Codec Rule", "global.systemDefaultCodecRule24": "System Default Codec Rule(24 bit)", "global.tag": "Label", "global.tagInfo": "Label info", "global.tagLeaveTime": "Label detachment time", "global.tagPositionError": "Exception: Label detached", "global.tagPrice": "Listed price", "global.tagReadTime": "Label start reading time", "global.tagSource": "Label Traceability", "global.tagTime": "Label read time range", "global.takeStock": "Stock sync", "global.targetPlace": "Target location", "global.targetWarehouse": "Target warehouse", "global.tel": "Telephone", "global.telError": "Please enter a 6 to 11 digit phone number", "global.templateCheckAddress": "Template preview source data address", "global.templateDetails": "Template details", "global.templateFile": "Template files", "global.templateManage": "Template Management", "global.templateType": "Template Type", "global.tenantAdmin": "Tenant Management Terminal", "global.tenantCode": "Tenant code", "global.text": "Text", "global.toPartnerCode": "Target warehouse code", "global.toPartnerType": "Target warehouse type (Warehouse, Store)", "global.toPartnerTypeSlim": "Target warehouse type", "global.toWarehouseCode": "Receiver position code", "global.total": "Total", "global.totalDataNum": "Total {{num}} piece of data", "global.totalDevice": "Total device", "global.totalFitting": "Total fitting counts", "global.totalInventoryCount": "Total Inventories", "global.totalNumber": "Total", "global.town": "District/Town", "global.transferGroup": "Transfer group", "global.tsBenefitAmount": "Inventory surplus", "global.tsBenefitAmountRatio": "Inventory surplus ratio", "global.tsBenefitCost": "Inventory surplus amount", "global.tsBenefitCostRatio": "Inventory surplus amount ratio", "global.tsLossAmount": "Inventory discrepancy", "global.tsLossAmountRatio": "Inventory discrepancy rate", "global.tsLossCost": "Inventory discrepancy value", "global.tsLossCostRatio": "Inventory discrepancy value ratio", "global.tsOrderDiffLine": "Inventory discrepancy details", "global.tsOrderErrorLine": "Inventory Count Anomalies <PERSON>", "global.tsOrderLine": "Inventory lists", "global.tsOrderModeAll": "Whole-store inventory", "global.tsOrderModeRandom": "Partial inventory", "global.tsOrderRecordLine": "Inventory Record Details", "global.txt": "Text", "global.type": "Type", "global.typeError": "Incorrect format", "global.typeRatio": "Types percentage.", "global.unFinish": "Incomplete", "global.unIntoBox": "Unpacked", "global.underCode": "Underscore split placeholder string", "global.uniqueCode": "Unique code", "global.uniqueCodeRule": "Unique Code Rules", "global.unknown": "Unknown", "global.unlimited": "Unlimited", "global.updateContent": "Updates", "global.updateTime": "Scheduled update time", "global.uploadError": "Upload failed", "global.uploadOrder": "Uploaded", "global.uploadSuccess": "Uploaded successfully", "global.uploadTemplateFile": "Upload template", "global.uploadTime": "Upload time", "global.uploadTip": "Click or drag and drop files here to upload", "global.versionNumber": "Version number", "global.view": "View", "global.visitorFittingCount": "Number of garments tried on per customer", "global.visitorFittingTime": "Duration of time spent on trying on clothes per customer", "global.volume": "Volume(mm³)", "global.waitConfirm": "Pending approval", "global.waitInbound": "Pending placement into storage", "global.waitInit": "Pending initialization", "global.waitOutboundMsg": "Pending retrieval from storage", "global.waitingForBoxing": "Awaiting Packing", "global.waitingForDelivery": "Awaiting Outbound", "global.waitingForInventory": "Awaiting Inventory", "global.waitingForReceive": "Awaiting Inbound", "global.warehouse": "Warehouse", "global.warehouseShortening": "WHSE", "global.warehouseCode": "Warehouse code", "global.warehouseEmployee": "Warehouse Employee", "global.warehouseImporterType": "Warehouse Types (Warehouse, Store, Factory)", "global.warehouseOutBoundOrder": "Warehouse outbound order", "global.warehouseShopPermission": "Warehouse shop permission", "global.website": "Website Address", "global.wi": "Warehouse Inbound", "global.width": "Width(mm)", "global.wo": "Warehouse Outbound", "global.writeBarcodeInfo": "Please fill out the barcode information completely", "global.writeBasicInfo": "Fill out the basic information", "global.writeTagLog": "Encoding record", "global.writeTime": "Encoding time", "global.writeTagPower": "Write label power", "global.writeTagPowerRules": "The minimum write label power is 1", "global.wrongDateFormat": "Date format error", "global.wrongDateTimeFormat": "Date+time format error", "global.wts": "Warehouse Inventory", "global.year": "Year", "global.yearCode": "Year code", "global.yearName": "Year ", "global.years": "Year", "global.yes": "Yes", "global.yesAndSave": "Yes, keep saving", "global.changePassword": "Change password", "global.logout": "Log out", "global.passwordSuccess": "Password reset complete", "global.oldPassword": "Old Password", "global.newPassword": "New Password", "global.unit": "Switch Unit", "global.successfullySwitched": "Successfully switched {{name}}", "global.partnerSelector": "Partner Selector", "global.employeeSelector": "Employee Selector", "global.skuSelector": "SKU Selector", "global.genderSelector": "Gender Selector", "global.priCategorySelector": "Primary Category Selector", "global.productSelector": "Model Selector", "global.yearSelector": "Year Selector", "global.brandSelector": "Brand Selector", "global.inputCodeName": "Enter code or name query...", "global.perPage": "{{num}} / Page", "global.currentPage": "Page {{num}}", "global.allowTakeStocking": "Allow inventory", "global.disallowTakeStock": "Prohibited Inventory Check", "global.newWarehouse": "New Warehouse", "global.newShop": "New Store", "global.newFactory": "New Factory", "global.newSupplier": "New Supplier", "global.newStorage": "New Storage", "global.newLogistics": "New Logistics", "global.newProduct": "New Product", "global.newBarcode": "New Barcode", "global.newBrand": "New Brand", "global.newYear": "New Year", "global.newGender": "New Gender", "global.newMainCategory": "New Main Category", "global.newSubCategory": "New Secondary Category", "global.newBoxSpec": "New Box Specification", "global.editBoxSpec": "Edit Box Specification", "global.newColorGrp": "New Color Group", "global.newSizeGrp": "New Size Group", "global.newSpecGrp": "New Specification Group", "global.newInboundOrder": "New Inbound Order", "global.newOutboundOrder": "New Outbound Order", "global.editSpecGrp": "Edit Specification Group", "global.editSizeGrp": "Edit Size Group", "global.editColorGrp": "Edit Color Group", "global.editSubCategory": "Edit Secondary Category", "global.editMainCategory": "Edit Main Category", "global.editGender": "Edit Gender", "global.editYear": "Edit Year", "global.editBrand": "Edit Brand", "global.editStorage": "Edit Storage", "global.editLogistics": "Edit Logistics", "global.warehouseDetails": "Warehouse Details", "global.shopDetails": "Store Details", "global.factoryDetails": "Factory Details", "global.supplierDetails": "Supplier Details", "global.searchCode": "Search the code...", "global.searchName": "Search the name...", "global.searchKeyword": "Search the keyword...", "global.newRole": "New Role", "global.newEmployee": "New Employee", "global.apply": "Apply", "global.isDefaulting": "Set as default?", "global.employeeDetail": "Employee details", "global.web": "Web page", "global.android": "Android", "global.windows": "Windows", "global.globals": "Global", "global.searchTips": "Enter keywords to search", "global.permissionDetails": "Role Details", "global.rfidTagTips": "This model number can only be packed with RFID", "global.fileNameError": "File name format is incorrect", "global.barcodeDisable": "Whether the barcode is disabled", "global.prodDisable": "Whether the model is disabled", "global.colorDisable": "Whether the color is disabled", "global.sizeDisable": "Whether the size is disabled", "global.specDisable": "Whether the specification is disabled", "global.disturbTag": "Whether it is an interference material", "global.isEnableRfidTag": "Whether to enable RFID", "global.ext": "(Custom fields)", "global.gbCode": "International code", "global.productDetails": "Model Details", "global.preview": "Preview", "global.tagRule": "Label encoding rules", "global.WORFIDPositionValidMode": "Warehouse Outbound RFID Tag Position Validation Mode", "global.WIRFIDPositionValidMode": "Warehouse Inbound RFID Tag Position Validation Mode", "global.SORFIDPositionValidMode": "Store Outbound RFID Tag Position Validation Mode", "global.SIRFIDPositionValidMode": "Store Inbound RFID Tag Position Validation Mode", "global.SMORFIDPositionValidMode": "Sample Outbound RFID Tag Position Validation Mode", "global.SMIRFIDPositionValidMode": "Sample Inbound RFID Tag Position Validation Mode", "global.allowRemind": "Allow stocking and prompt", "global.allowRemindTooltipStore": "The handheld or platform shipper needs to prompt \"Tag 001 is not in Store S1, whether to confirm shipment, yes to ship, no to cancel\"", "global.allowRemindTooltipWarehouse": "The handheld or platform shipper needs to prompt \"Tag 001 is not in Warehouse W1, whether to confirm shipment, yes to ship, no to cancel\"", "global.allowNotRemind": "Allow stocking without prompt", "global.allowNotRemindTooltip": "Handheld device proceeds with normal shipment, backend records operational information", "global.notAllow": "Do not allow stocking", "global.notAllowTooltip": "Detected tags not in the current location, shipment not allowed", "global.notConnected": "Not connected", "global.WOGenerateWI": "Generate Warehouse Inbound Order for Warehouse Outbound", "global.WOGenerateFI": "Generate Factory Inbound Order for Warehouse Outbound", "global.WOGenerateSI": "Generate Store Inbound Order for Warehouse Outbound", "global.SOGenerateWI": "Generate Warehouse Inbound Order for Store Outbound", "global.SOGenerateSI": "Generate Store Inbound Order for Store Outbound", "global.FOGenerateWI": "Generate Warehouse Inbound Order for Factory Outbound", "global.FOGenerateSI": "Generate Store Inbound Order for Factory Outbound", "global.appSICreateBox": "Handheld Device Store Inbound - Create New Box", "global.appWICreateBox": "Handheld Device Warehouse Inbound - Create New Box", "global.appUpgradeAddr": "Handheld Device Upgrade Address", "global.fileBaseAddr": "API File Service Address", "global.appWOrderDeleteDays": "Days to Delete Warehouse Orders on Handheld Device", "global.appWOrderDeleteStatus": "Status for Deleting Warehouse Orders on Handheld Device", "global.appSOrderDeleteDays": "Days to Delete Store Orders on Handheld Device", "global.appSOrderDeleteStatus": "Status for Deleting Store Orders on Handheld Device", "global.appSMOrderDeleteDays": "Days to Delete Sample Orders on Handheld Device", "global.appSMOrderDeleteStatus": "Status for Deleting Sample Orders on Handheld Device", "global.symbol": "Symbol", "global.illustrate": "Illustration", "global.syncMenu": "Synchronize Menu", "global.cross": "Cross", "global.crossTag": "Cross Tag Switch", "global.crossRule": "Cross Rule", "global.overTag": "Overage Switch", "global.overRule": "Overage Mode", "global.overValue": "Allowable Overage Percentage (%)", "global.order": "Order", "global.dataDefaultQueryDays": "Data default query days", "global.enableUnicode": "Enable Unique Code", "global.complementTag": "Enable Complement Code", "global.supplyTagToBindingRule": "Complement Code Binding Rule", "global.supplyTagToBindingRuleRemark": "Constants follow the '#' symbol, variables follow the '$' symbol, '{3}' indicates the number of digits for a variable, ',' is used for segmentation, available variables include: [year];[flow]", "global.complementSeq": "Complement Code Starting Sequence", "global.tagRuleRelyOnField": "Tag Rule Generation Dependent Field", "global.general": "General", "global.constantCharacter": "Constant Characters", "global.codeMsg": "No Changes to '==IN==' in the Generated Code", "global.variableCharacters": "Variable Characters", "global.generatedCodeMsg": "Concatenate 'Shipper Code' from actual business data into the generated code", "global.parametersContent": "Parameter Content", "global.braceMsg": "Parameters include constants, variables, and running water inside curly braces", "global.currentYearTwo": "Current Year in Two Digits, e.g., 2020 becomes 20", "global.currentYearFour": "Current Year in Four Digits, e.g., 2020", "global.currentMonth": "Current Month", "global.currentDate": "Current Date", "global.runningWaterMsg": "Running Water, 'n' is the running water length that can be customized, e.g.,", "global.runningWaterLengthMsg": "6-digit running water, adjust based on the maximum code length", "global.businessMsg": "Current Business Order Number, often used for box number generation", "global.businessCodeMsg": "Shipper Code of the current business order, used when the order has both sender and receiver", "global.businessReceivingMsg": "Receiver's Warehouse Code of the current business order, used when the order has both sender and receiver", "global.businessReceivingShopMsg": "Store or Warehouse Code of the current business order, used when there is only one store information in the order (e.g., inventory without sender or receiver)", "global.StructuralExpression": "Structural Expression Explanation", "global.ruleContentMst": "Rules use the following markers to identify generated content", "global.variableCharacterList": "List of Variable Characters", "global.FadAutoConfirm": "Automatic Approval for Scheduled Delivery", "global.showTagExpandInfo": "Display Tag Expansion Information", "global.currency": "<PERSON><PERSON><PERSON><PERSON>", "global.thoroughfareInBoundCheckWay": "Dimension for Inbound Channel by Box", "global.checkWayByRFID": "Check Box based on Preloaded Box RFID Information", "global.checkWayBySKU": "Check Box based on Preloaded Box SKU Information", "global.showRFIDExtFlag": "Record Manual Input Count", "global.confusionSize": "String Code", "global.confusionColor": "Cross Color", "global.confusionProd": "Cross Payment", "global.overRuleTip": "Allow Exceeding Quantity Not Less Than 0.1%", "global.wiAutoConfirmWhenSaveWithBox": "Warehouse Inbound Channel Automatically Approves Boxes when Saved by Box", "global.wiAutoConfirmWhenSaveWithOrder": "Warehouse Inbound Channel Automatically Approves Boxes when Saved by Order", "global.woAutoConfirmWhenSaveWithOrder": "Warehouse Outbound Channel Automatically Approves Boxes when Saved by Order", "global.woBoxCheckWay": "Warehouse Inbound Channel Box Verification Method", "global.plCheckCrossSellingRules": "Please check the cross-selling rules", "global.errorMsg": "Please enter a valid value", "global.partition": "Partition", "global.filterVal": "Filter Value", "global.barcodeLen": "Barcode Length", "global.unicodeFlowNumBase": "Unique Code Flow Base", "global.unicodeFlowLen": "Unique Code Flow Length", "global.structExp": "Structure Expression", "global.tooltip1": "{{sku}} stands for barcode escape, and {{flow}} stands for flow escape", "global.tooltip2": "For example: 1,2,3 represents the first, second, and third positions (please use a comma to separate)", "global.epcRule": "EPC Rule", "global.epcFlowNumBase": "EPC Flow Base", "global.encodeIndexList": "Conversion Hexadecimal Digit Index", "global.epcLen": "EPC Length", "global.fillZeroAtPrefixMsg": "Fill Zero", "global.preFillZeroAtPrefixMsg": "Pre-fill", "global.sufFillZeroAtPrefixMsg": "Suf-fill", "global.epcPrefix": "EPC Prefix", "global.epcSuffix": "EPC Suffix", "global.epcFlowLen": "EPC Flow Number Length", "global.bindingProductOrders": "Binding Production Orders", "global.notAllowSameReceiveShipper": "Consistent shipper and receiver are not allowed", "global.choosePrinterAndPrint": "Select A Printer And Print", "global.totalPrinter": "Total {{total}} Printers", "global.Enter": "Enter", "global.Space": "Space", "global.newOrderType": "New Order Type", "global.editOrderType": "Edit Order Type", "global.systemPresets": "System Default", "global.newBusinessModal": "New business model", "global.editBusinessModal": "Edit business model", "global.newExtField": "New Extension Fields", "global.editExtField": "Edit Extension Fields", "global.newTransferGroup": "New dispatch group", "global.editTransferGroup": "Edit dispatch group", "global.importTransferGroup": "Import dispatch group", "global.delivery": "Delivery", "global.transferGroupDetail": "Transfer Group Details", "global.configuration": "Configuration", "global.back": "Back", "global.editDict": "Edit Dictionary", "global.newDict": "New Dictionary", "global.newDictData": "New Dictionary Data", "global.editDictData": "Edit Dictionary Data", "global.newVersion": "New Version", "global.versionDetail": "Version Details", "global.printLogsDetail": "Print Logs Details", "global.pleaseEnterNumber": "Please enter a number", "global.selectTip": "{{currentProdCount}} models, {{skuCount}} barcodes selected", "global.accountsNum": "Number of accounts", "global.accountsAmount": "Account amount", "global.carryingAmountRatio": "Carrying Amount Ratio", "global.pleaseHandleOverAndError": "Please handle exceptions or exceeding data first", "global.pleaseHandleError": "Please handle the exception first", "global.selectOperateMode": "Select Operation Mode", "global.except": "Exception", "global.exceptQty": "Exception Quantity", "global.excessQty": "Excess Quantity", "global.excess": "Excess", "global.normalQty": "Normal Quantity", "global.operateQty": "Operation Quantity", "global.readedQty": "Readed Quantity", "global.readed": "Readed", "global.remainingQty": "Remaining Quantity", "global.reading": "Reading", "global.confirmDiscard": "Data in process is detected. Do you want to discard it?", "global.barcodeNotInOrder": "The barcode is not included in the document", "global.labelNotInPreBox": "The label is not included in the pre-packed box", "global.labelNotInOrder": "The label is not included in the document", "global.epcPacked": "EPC has been packed", "global.barcodeNotInPreBoxRange": "The barcode is not included in the pre-packed box", "global.barcodeEmpty": "Barcode is empty", "global.epcNotBind": "Label not yet bound", "global.epcNotInSys": "The EPC is not included in the system", "global.excessByOrder": "Excess By Order", "global.excessByBarcode": "Excess By Barcode", "global.boxBarcodeDeleteSuccess": "{{count}} items successfully deleted", "global.labelsDeleteSuccess": "Successfully deleted {{count}} labels", "global.exceptionHanding": "Exception Handling", "global.excessHanding": "Excess Handling", "global.deletion": "Deletion", "global.readNoDataTip": "If there is no normal data, click '{{name}}' button to start RFID device reading", "global.rfidInbound": "RFID Inbound", "global.barcodeInbound": "Barcode Inbound", "global.lastBarcodeProcessingTips": "The previous barcode is being processed. Please scan again later", "global.prodOnlyForRFID": "This model can only be packed with RFID", "global.scanSuccess": "Scaned", "global.readedQtyExcessOrderQty": "The number of reads items exceeds the number of documents", "global.readedQtyExcessRemainingQty": "The number of reads items exceeds the number of remaining items", "global.deleteQtyExcess": "The number of subtracted items exceeds the number of read items", "global.pleaseInputOperateQty": "Please enter operation quantity", "global.operateQtyError": "Operation quantity must be greater than 0", "global.notFoundBarcode": "Unable to find relevant information for this barcode", "global.notFoundGbCode": "Unable to find relevant information for this international code", "global.notFoundInnerCode": "Unable to find relevant information for this internal code", "global.notFoundOtherCode": "Unable to find relevant information for this other code", "global.shortcuts": "Keyboard Shortcuts", "global.handleExcessData": "Handle Excess Data", "global.handleExceptionData": "Handle Exception Data", "global.handleReadedData": "Handle Readed Data", "global.scanBarcode": "Scan Barcode", "global.pleaseConnectToDDS": "Please connect to DDS first", "global.DDSConnected": "DDS Connected", "global.connected": "Connected", "global.packing": "Packing", "global.rfidPacking": "RFID Packing", "global.barcodePacking": "Barcode Packing", "global.singleBoxOverTip": "No more than 1,000 products in a single box", "global.excessOperableQtyTip": "Do not allow more than the operable quantity", "global.zeroReadedDeleteTip": "Readed number is 0, cannot perform deletion operation", "global.barcodeNotInExceptionRange": "The barcode is not included in the exception range", "global.readerStatus": "Reader Status", "global.noData": "No Data", "global.printTemplateNotFound": "Print Template Not Found. Please contact system administrator", "global.unreviewed": "Not reviewed", "global.pleaseEnter": "Please Enter", "global.basicInformation": "Basic Information", "global.newProductionOrder": "New Production Order", "global.deliverySubWarehouse": "Shipping warehouse position", "global.boxedCount": "Packed Quantity", "global.reserveDeliveryBoxesMsg": "Review scheduled delivery orders (total {{numtotal}}) boxes of {{numcount}} items", "global.packingModeSwitched": "You have switched to ({{mode}}) mode", "global.singlePack": "Single Pack", "global.mixedModel": "Mixed Model", "global.mixedSize": "Same Model Same Color Mixed Size", "global.mixedColor": "Same Model Same size Mixed Color", "global.mixedColorMixedSize": "Same Model Mixed Color Mixed Size", "global.autoPrintBoxSticker": "Auto Print Box Sticker", "global.diffBarcode": "Not the same barcode", "global.diffModel": "Not the same model", "global.diffSize": "Not the same size", "global.diffColor": "Not the same color", "global.lockRefProd": "Lock Reference Product", "global.rescan": "Rescan", "global.lockProdTips": "Need to lock the reference product. Please put the reference product into the reader", "global.confirmRefProd": "Please confirm the following reference product", "global.invalidLabel": "Invalid Label", "global.selectPackRule": "Please select packing rule", "global.pleaseInputOldPassword": "Please enter your old password", "global.pleaseInputNewPassword": "Please enter your new password", "global.newInventory": "New Stocktaking Order", "global.SWarehouseCode": "Store's storage code", "global.shopOutbound": "Store delivery order", "global.schedule": "Schedule", "global.scheduleDelivery": "Schedule Delivery", "global.exitScheduleDeliveryMode": "Exit Schedule Delivery Mode", "global.exit": "Exit", "global.expectedDeliveryTime": "Expected delivery time", "global.scheduleSuccess": "Schedule successful", "global.packingRule": "Packing Rule", "global.ship": "Ship", "global.scanOrInputBoxCode": "Scan/Input Box Number", "global.boxCodeNotFound": "Box code not found", "global.selectedSchedulableBoxes": "For selected {{count}} schedulable boxes", "global.scheduleStatus": "Schedule Status", "global.quickSearch": "Quick Search...", "global.pendingTasks": "Pending Tasks", "global.inventoryAnalysis": "Inventory Analysis", "global.lastWeek": "Last week", "global.lastMonth": "Last month", "global.lastSixMonths": "Last six months", "global.recentInventory": "Recent Stocktaking", "global.lastYear": "Last year", "global.newReportTemplate": "New Report Template", "global.editReportTemplate": "Edit Report Template", "global.newLabelTemplate": "New Label Printing Template", "global.editLabelTemplate": "Edit Label Printing Template", "global.pleaseEnterOrReadTag": "Please enter or read tag", "global.operationLogs": "Operation logs", "global.pleaseEnterTagQuery": "Please enter tag query", "global.noEpcFound": "Please enter correct EPC information", "global.noUniqueCodeFound": "Please enter the correct unique code information", "global.orderCodeOrFmOrderCode": "Order No. or production order No.", "global.newPrintTask": "New Print Task", "global.addSkus": "Add SKUs", "global.alarmData": "Alarm data", "global.displayData": "Display data", "global.retail": "Retail", "global.retailReturns": "Retail returns", "global.antiTheftAlarm": "Anti-theft alarm", "global.antenna": "<PERSON><PERSON><PERSON>", "global.alarmDateTime": "Alarm date and time", "global.selectPiecesData": "Select up to 10 pieces of data", "global.newDeviceManagement": "New Device Profile", "global.editDeviceManagement": "Edit Device Profile", "global.labelEncoding": "Encoding", "global.encodingOneByOne": "Encoding One By One", "global.batchEncoding": "Batch Encoding", "global.startEncoding": "Start to write RFID labels", "global.putLabelOnReader": "Please place the label on the code writer...", "global.confirmWriteLabel": "Confirm to write the label?", "global.checkWriteLabelTip": "Please verify the content of label and confirm the label is written.", "global.getNewTagFailed": "Failed to get new tag information", "global.desktopDataService": "Desktop Data Service", "global.newLabel": "New", "global.oldLabel": "Old", "global.restart": "<PERSON><PERSON>", "global.writeSuccess": "Write success!", "global.writeSuccessTip": "The label was successfully written", "global.writeFail": "Write failure!", "global.encodingAnother": "Encoding the next one", "global.readingTip": "Reading, please wait...", "global.encodingExcessTip": "Please remove the extra labels and <0>Restart</0>", "global.writingTip": "Writing, please wait...", "global.writePartSuccess": "Write partial success!", "global.writeResult": "Label writing is completed, serial number: {{flowNumQuantity}}, actual success: {{successQty}}, failure: {{errorQty}}", "global.networkErrorMessage": "Network exception, unable to connect to the server", "global.oneByOneBind": "\"One-to-one binding\" can only operate one tag at a time", "global.isForceBinding": "Force binding", "global.isBatchBinding": "Batch binding", "global.readDataEmptyTip": "There is no data yet. Click the {{start}} button to start reading from the RFID device.", "global.barcodeNotInSys": "The barcode is not in the system", "global.errorQty": "Abnormal number", "global.fillInBarcode": "Please fill in the barcode", "global.tagNotInSys": "Label is not system wide", "global.deleteBarcodeTip": "{{length}} abnormal barcodes successfully deleted", "global.handleAbnormal": "Handle exceptions", "global.tagNotBinding": "Label not yet bound", "global.unbind": "Unbind", "global.confirmDeleteFilter": "Confirm to delete the filter?", "global.mixPackingNotSupport": "Mixed packing are not supported", "global.saveFilters": "Save filters", "global.pleaseSelectFiltersAndSearch": "Please select filters and search", "global.uniqueCodeBoxingNotSupport": "Unique code boxing is not supported", "global.recover": "<PERSON><PERSON>", "global.logsUploading": "Record uploading...", "global.logsUploadSuccess": "Record uploaded successfully", "global.logsUploadFailed": "Failed to upload encode log", "global.importPartner": "Import partners", "global.upload": "Upload", "global.positionError": "Label position anomaly", "global.positionErrorTips": "{{count}} location anomalies labels detected, do you want to continue save?", "global.belongPartnerCode": "Partner code", "global.roleCode": "Role code", "global.antiTheft": "Anti-theft", "global.selectRequiredFields": "Please select the Excel column corresponding to the required field [{{fields}}]", "global.defaultImportError": "An exception occurred while importing, please import again.", "global.clickHereForDetails": "Click here for details", "global.importFmOrder": "Import production orders", "global.dataFound": "The data cannot be found", "global.proportions": "Proportion", "global.ratioValue": "Proportional value", "global.makeDate": "Order making time", "global.boxQty": "Number of boxes", "global.syncVersion": "Sync version number", "global.archived": "Archived", "global.reviewStatus": "Review status", "global.boxIndex": "Box serial number", "global.initSetting": "Initialization settings", "global.initSettingSuccess": "Configuration completed", "global.wayWiWeightOffsetValue": "Warehouse channel incoming weight error value (%)", "global.wayWiCheckWeight": "Whether to check the weight when entering the warehouse channel", "global.wayWoCheckRatio": "Whether to enable ratio verification for warehouse channel outbound delivery", "global.weight": "weight", "global.weightUnit": "weight（gram）", "global.gram": "gram", "global.inputWeight": "Input weight", "global.pleaseInputWeight": "Please input weight", "global.summary": "Summary", "global.selectedItemsData": "Selected {{count}} items", "global.confirmSyncTask": "Confirm sync task?", "global.summation": "Summation", "global.posCheckout": "POS checkout", "global.selfServiceCheckout": "Self-service checkout", "global.stockQty": "Inventory quantity", "global.getEncode": "Get encode", "global.encryptedEncode": "Encrypted Encode", "global.sampleWarehouse": "Sample Garment Warehouse", "global.PurchaseSalesInventory": "Purchase, sales, and inventory", "global.allowMultipleCoding": "Allow multiple coding", "global.codingPassword": "Coding password", "global.tagLog": "Rfid tag log", "global.basicInfoTips": "Please fill in the correct basic information", "global.resend": "Resend", "global.confirmResend": "Confirm to resubmit?", "global.pleaseEnterTheCorrectFormat": "Please enter an 8-digit password consisting of numbers (0-9) or uppercase English letters (A-Z).", "global.productImportValidationPlan": "Product import validation plan", "global.uniqueStyleAndColorCode": "Unique style and color code", "global.uniqueBarcode": "Unique barcode", "global.pleaseEnterTheCodePassword": "Please enter the code password.", "global.employeeCode": "Employee Code", "global.employeeType": "Employee Type", "global.orderImport": "Order Import", "global.orderResultImport": "Order Result Import", "global.tagPacking": "RFID Packing", "global.requestParams": "Request Params", "global.responseParams": "Response Params", "global.message": "Message", "global.messageQuality": "Message Quality", "global.action": "Action", "global.businessSubtype": "Business Subtype", "global.warehouseShop": "Warehouse shop", "global.warehouseShopCode": "Warehouse shop code", "global.warehouseShopName": "Warehouse shop name", "global.className": "Class name", "global.customer": "Customer", "global.taskName": "Task name", "global.failCount": "Failure count", "global.lastSuccessTime": "Last success time", "global.editTask": "Edit Task", "global.inputTaskNameThenEnterToSearch": "Enter task name and press En<PERSON> to search", "global.overOrderNumber": "Allow order overage {{num}}", "global.overBarcodeNumber": "Allow excess {{num}} by barcode", "global.returnInspection": "Return Inspection", "global.outboundOrderNumber": "Outbound Order Number", "global.thisTagDoesNotCurrentWarehouse": "This label does not belong to the current logged-in warehouse's outbound records", "global.systemTid": "System TID", "global.actualTid": "Actual TID", "global.behavior": "Behavior", "global.documentBehavior": "Document Behavior", "global.createBinding": "Create Binding", "global.update": "Update", "global.bind": "Bind", "global.positionOrStatus": "Update Location or Status", "global.forceBinding": "Overwrite Mode Bind", "global.rebind": "<PERSON><PERSON><PERSON>", "global.tagInventory": "Tag Inventory", "global.storageLocationCode": "Storage Location Code", "global.storageLocationName": "Storage Location Name", "global.inventoryAtLocations": "Inventory at locations", "global.getRealTimeInventory": "Get real-time inventory", "global.confirmGetRealTimeInventory": "Confirm receipt of real-time inventory？", "global.ddsDownloadUrl": "dds download url", "global.ddsUsageGuide": "dds usage guide", "global.downloadUrl": "download url", "global.usageGuide": "usage guide", "global.theftPreventionConfiguration": "Theft prevention configuration", "global.antiTheftDataSharingOperation": "Anti-theft data sharing operation", "global.antiTheftCollection": "Anti-theft collection", "global.attachmentType": "Attachment type", "global.database": "Database", "global.mobileTerminal": "Mobile", "global.desktop": "Desktop", "global.attachmentSize": "Attachment size (KB)", "global.extractUniqueCode": "Extract unique code", "global.allowOutboundWithNotification": "Allow outbound with notification", "global.allowOutboundWithoutNotification": "Allow outbound without notification", "global.outboundNotAllowed": "Outbound not allowed", "global.currentWarehouse": "Current warehouse", "global.model": "Model", "global.version": "Version", "global.softName": "Software name", "global.softVersion": "Software version", "global.power": "power", "global.groupCoding": "Group coding", "global.messageLog": "Message Log", "global.send": "Send", "global.receive": "Receive", "global.topic": "Topic", "global.retained": "Retain messages", "global.configDown": "Deliver configuration", "global.confirmTimeSync": "Confirm time synchronization?", "global.confirmConfigDown": "Confirm deliver configuration?", "global.confirmRestart": "Confirm restart?", "global.confirmUpdateVersion": "Confirm firmware upgrade?", "global.communicationLog": "Telemetry log", "global.timeSync": "Time Synchronization", "global.firmwareUpdate": "Firmware upgrade", "global.description": "Description", "global.getSysConfigFail": "Failed to retrieve device configuration. Please click refresh to try again.", "global.deviceIsOnline": "The device is offline. Please check!", "global.reboot": "Reboot", "global.confirmBatchFirmwareUpdate": "Confirm firmware upgrade for the selected {{num}} items?", "global.confirmBatchSyncTime": "Confirm time synchronization for the selected {{num}} items?", "global.confirmBatchRestart": "Confirm restart for the selected {{num}} items?", "global.confirmBatchConfigDown": "Confirm to [deliver configuration] for the selected {{num}} items?", "global.otherConfig": "Other Configurations", "global.businessConfig": "Business Configurations", "global.sysConfig": "System Configurations", "global.deviceDescription": "Device Description", "global.timeZone": "Time Zone", "global.startWorkingHour": "Start Work Hour", "global.startWorkingMinute": "Start Work Minute", "global.endWorkingHour": "End Work Hour", "global.endWorkingMinute": "End Work Minute", "global.effectiveDuration": "Fitting Valid Duration (sec)", "global.maxDuration": "Maximum Fitting Time (sec)", "global.intervalDuration": "Fitting Interval Duration (sec)", "global.MqttServerHost": "MQTT Server Host IP", "global.MqttServerPort": "MQTT Server Port", "global.uploadInterval": "Fitting Data Upload Interval (sec)", "global.heartBeatUploadInterval": "Device Heartbeat Upload Interval (sec)", "global.enableSsl": "Enable SSL", "global.EPCLengthConfig": "EPC Length Filter", "global.filterData": "Filter Data", "global.epcLengthMaxValue": "Maximum EPC Length", "global.epcLengthMinValue": "Minimum EPC Length", "global.sharedAntiTheftConfig": "Shared Anti-Theft Configuration Warehouse", "global.useMachingCodeOnly": "Whether to use matching code only", "global.prodRatioValue": "Product ratio value", "global.currentTagOverTips": "This tag has exceeded", "global.currentTagMatchTips": "This tag does not match the current product ratio", "global.currentNotMatchTips": "This tag does not meet the ratio", "global.currentDeleteNormalNotMatchTip": "The currently deleted product does not meet the ratio, please reselect", "global.lastBox": "Last box", "global.matching": "Matching", "global.enableMachingCode": "Whether to enable matching code mode", "global.filterValue": "Filter Value", "global.compatibleEan8": "Compatible EAN8", "global.indicatorCode": "Indicator Code", "global.decodeRemoveCheckcode": "Remove Check Code When Decoding", "global.matchRatio": "Matching <PERSON>io", "global.currentOrderNotMatchTips": "The current order does not have matching information, please select another packing mode", "global.currentPrice": "Actual Selling Price", "global.shopRetailOrder": "Shop Retail Order", "global.shopName": "Store Name", "global.fittingTime": "Fitting Duration", "global.deviceName": "Device Name", "global.deviceType": "Device Type", "global.deviceIndex": "Device Serial Number", "global.sendOutAntenna": "Deliver antenna", "global.numberOfAntennas": "Antenna Count", "global.confirmSendOutAntenna": "Confirm deliver antenna?", "global.addField": "Add Field", "global.addTagFilterType": "Add Tag Filter Type", "global.anyFilterMatch": "Any condition matches", "global.allFilterMustMatch": "All conditions must match", "global.defaultTag": "Default <PERSON>", "global.editTagFilterType": "Edit Tag Filter Type", "global.filterMode": "Filter Mode", "global.epcLength": "EPC Length", "global.priority": "Priority", "global.startPosition": "Start Position", "global.startPositionStr": "Start Position Character", "global.suffixCharacters": "Suffix Characters", "global.addTagFilterRule": "Add Tag Filter Rule", "global.editTagFilterRule": "Edit Tag Filter Rule", "global.includes": "Include", "global.excludes": "Exclude", "global.tagNotInBatch": "This tag does not belong to the current batch", "global.devConfig": "Development Configuration", "global.dockingSettings": "Docking Settings", "global.gatheringWatersLake": "Gathering Waters Lake", "global.ownMall": "Own Mall", "global.serviceProvider": "Service Provider", "global.sourceNumber": "Source Number", "global.sourceBarcode": "Source Barcode", "global.immediateReturn": "Immediate Return", "global.intervalReturn": "Interval Return", "global.dianPlus": "<PERSON>an <PERSON>", "global.enableImportFieldMatchMemory": "Enable Import Field Match Memory", "global.readTid": "Read TID", "global.ddsOutdateTips": "The current DDS version is outdated, please download and update to the latest version", "global.codeMessage": "Message", "global.expressionRules": "Expression Rules", "global.startFlow": "Start Flow", "global.expressionRuleParam": "Rule Parameter", "global.expressionRuleParamTips": "'#' followed by constants, '$' followed by variables, '{3}' followed by digits, ',' followed by division, supported variables: year(year), month(month), flow(flow), rdhex(random hex number), rddec(random decimal number)", "global.tagPrintMode": "Tag Print Mode", "global.tagPrintModeManual": "Manual", "global.tagPrintModeAuto": "Auto", "global.tagPrintModeByProductField": "Tag Print by Product Field (Garments)", "global.barcodeUrl": "Barcode Image URL", "global.productUrl": "Product Image URL", "global.barcodeDefaultUrl": "Barcode Image URL is Default", "global.productDefaultUrl": "Product Image URL is Default", "global.illegalLabel": "Illegal Label", "global.illegalLabelTips": "This label is illegal and cannot be written. Please scan the label again!", "global.illegalLabelNotWriteTips": "Illegal label, writing code is not allowed", "global.multiSelect": "{{name}} selected in batch", "global.noMatchingDataFound": "No matching data found", "global.warehouseNoOrderTypeRequired": "Warehouse No Order Type Required", "global.shopNoOrderTypeRequired": "Shop No Order Type Required", "global.longitudeError": "Longitude must be between -180 and 180", "global.latitudeError": "Latitude must be between -90 and 90", "global.enableBarcodeScanCutoff": "Enable barcode scan cutoff", "global.barcodeCutoffPrefixDigits": "Barcode cutoff prefix digits", "global.barcodeCutoffAfterDigits": "Barcode cutoff after digits", "global.barcodeCutoffTips": "Barcode cutoff is enabled, cut the first {{prefix}} digits and the last {{after}} digits of the barcode", "global.barcodeTooShort": "Barcode is too short, please enter {{required}} digits, and the current barcode is {{current}} digits", "global.enablePaymentPasswordVerification": "Enable Payment Password Verification", "global.sliceAlias": "Sliced: ", "global.currentDataResetTips": "The current data does not match the target mode. Would you like to reset the data and switch?", "global.sampleEnableConfirmAutoUp": "Sample confirmation auto up", "global.uniqueCodePacking": "Unique Code Packing", "global.uniqueCodeAlreadyExist": "Unique Code Already Exist", "global.uniqueCodeException": "Unique Code Exception", "global.noNormalData": "No normal data, {{tip}}", "global.noMatchingBarcode": "Barcode does not match, please re-enter", "global.noMatchingMixSize": "Style/Color does not match, please re-enter", "global.noMatchingMixColor": "Style/Size does not match, please re-enter", "global.noMatchingMixColorSize": "Style does not match, please re-enter", "global.labelError": "Label Error", "global.exceptionDetails": "Exception Details", "global.sampleLocation": "Sample Storage Location", "global.newSampleLocation": "New Sample Storage Location", "global.editSampleLocation": "Edit Sample Storage Location", "global.importSampleLocation": "Import Sample Storage Location", "global.uniqueCodeNotInCurrentBox": "Unique Code Not In Current Box", "global.byLocation": "By Storage Location", "global.task": "Task", "global.taskCode": "Task Code", "global.taskDetail": "Task Details", "global.packingModeNotPermission": "No {{mode}} permission, please contact administrator", "global.fmOrderPackedQtyLessThanOrderQtyNotAllowConfirm": "Production order packed quantity less than order quantity, approval not allowed", "global.fadOrderScheduleQtyLessThanFmOrderQtyNotAllowSchedule": "Scheduled delivery quantity less than production order quantity, scheduling not allowed", "global.inboundActualQtyLessThanOrderQtyNotAllowConfirm": "Inbound actual quantity less than order quantity, approval not allowed", "global.outboundActualQtyLessThanOrderQtyNotAllowConfirm": "Outbound actual quantity less than order quantity, approval not allowed", "global.unprocessed": "Unprocessed", "global.adGenerateWib": "Appointment Review Generate Warehouse Inbound", "global.sibBoxAntiTheftDataSend": "Store Inbound Box Anti-theft Data Send", "global.sobBoxAntiTheftDataSend": "Store Outbound Box Anti-theft Data Send", "global.stsAntiTheftDataSend": "Store Stocktaking Anti-theft Data Send", "global.alarmGatherAntiTheftDataSend": "Alarm Gather Anti-theft Data Send", "global.smAlarmGatherAntiTheftDataSend": "Sample Alarm Gather Anti-theft Data Send", "global.displayGatherAntiTheftDataSend": "Display Gather Anti-theft Data Send", "global.srGatherAntiTheftDataSend": "Retail Gather Data Send", "global.srtGatherAntiTheftDataSend": "Retail Return Gather Data Send", "global.cancelAlarmGatherAntiTheftDataSend": "Cancel Alarm Anti-theft Data Send", "global.smCancelAlarmGatherAntiTheftDataSend": "Sample Cancel Alarm Anti-theft Data Send", "global.wibRepairGeneratePo": "Warehouse Inbound Repair Generate Production Order", "global.srDataSend": "Retail Data Send", "global.srtDataSend": "Retail Return Data Send", "global.wobGenerateWib": "Warehouse Outbound Auto Generate Warehouse Inbound", "global.wobGenerateSib": "Warehouse Outbound Auto Generate Store Inbound", "global.sobGenerateWib": "Store Outbound Auto Generate Warehouse Inbound", "global.sobGenerateSib": "Store Outbound Auto Generate Store Inbound", "global.wobGenerateIb": "Warehouse Outbound Auto Generate Inbound", "global.sobGenerateIb": "Store Outbound Auto Generate Inbound", "global.smWibBoxAntiTheftDataSend": "Sample Warehouse Inbound Box Anti-theft Data Send", "global.smWobBoxAntiTheftDataSend": "Sample Warehouse Outbound Box Anti-theft Data Send", "global.smWtsAntiTheftDataSend": "Sample Warehouse Stocktaking Anti-theft Data Send", "global.smLrLendAntiTheftDataSend": "Sample Lend Anti-theft Data Send", "global.smLrReturnAntiTheftDataSend": "Sample Return Anti-theft Data Send", "global.smLrDisposeAntiTheftDataSend": "Sample Dispose Anti-theft Data Send", "global.enableBarcodePadding": "Enable Barcode Padding", "global.barcodePaddingDescription": "When enabled, barcodes with less than 14 digits will be padded with leading zeros to reach 14 digits during encoding, and leading zeros will be removed during decoding; when disabled, no padding or zero removal processing is performed on barcodes", "global.enableIndependentUniqueCodeRule": "Enable Independent Unique Code Rule", "global.uniqueCodeRuleSystemParameters": "Unique Code Rule System Parameters", "global.variableRuleDescription": "Constants follow '#', variables follow '$', '{3}' indicates the number of digits in sequence, ',' is used for segmentation, available variables are: [sku];[flow]", "global.positionStatus": "Position Status", "global.sold": "SOLD", "global.enableOverrideListing": "Enable Override Listing", "global.secondsTime": " {{num}} second(s)", "global.minutesTime": " {{num}} minute(s)", "global.hoursTime": " {{num}} hour(s)", "global.daysTime": " {{num}} day(s)", "global.factoryBarcodePackingOperationThreshold": "Factory barcode packing operation quantity threshold", "global.inboundBarcodePackingOperationThreshold": "{{client}} inbound barcode packing operation quantity threshold", "global.outboundBarcodePackingOperationThreshold": "{{client}} outbound barcode packing operation quantity threshold", "global.alreadyPackedQty": "Quantity Packed", "global.toBePackedQty": "Quantity to be Packed", "global.batchConfirm": "<PERSON><PERSON> Confirm", "global.batchCancel": "<PERSON><PERSON>", "global.cancelSelect": "Cancel Selection", "global.successOrErrorCountTips": "Success {{count}} documents, failed {{num}} documents", "global.successTips": "Success {{count}} documents", "global.exceptDealTip": "Please resolve abnormal quantities through reduction operations before saving"}