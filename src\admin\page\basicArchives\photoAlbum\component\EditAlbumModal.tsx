/**
 * Create by codegen at 2022-06-28
 * 相册 - 编辑
 */
import { Checkbox, Col, Form, Input, InputNumber, Row } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';
import Modal from 'common/components/Modal';

interface EditAlbumModalProps {
  current?: Record<string, any>;
  visible?: boolean;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
  editType?: 'SHOP' | 'COMMON';
}

const EditAlbumModal: React.FC<EditAlbumModalProps> = (props) => {
  const { current, visible, confirmLoading, onOk, onCancel, editType: _editType } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);
  const editType = _editType ?? 'SHOP';

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  useEffect(() => {
    let timerId: number | null = null;
    if (visible && current) {
      form.setFieldsValue(current);
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
    // eslint-disable-next-line
  }, [visible, current]);

  return (
    <Modal
      title={editType === 'SHOP' ? i18n.t('global.editStoreAlbum') : i18n.t('global.editCommonAlbum')}
      open={visible}
      confirmLoading={confirmLoading}
      onOk={modalOnOk}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      transitionName=""
      maskTransitionName=""
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Form.Item name="name" label={i18n.t('global.albumName')} rules={canNotBeNullRules}>
          <Input ref={inputRef} />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={2} />
        </Form.Item>
        <Form.Item label={i18n.t('global.sort')} name="sort">
          <InputNumber />
        </Form.Item>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              wrapperCol={{ span: 16, offset: 0 }}
              valuePropName="checked"
              name="defaultTag"
              style={{ marginLeft: 0 }}
            >
              <Checkbox>{i18n.t('global.isDefault')}</Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              wrapperCol={{ span: 16, offset: 0 }}
              valuePropName="checked"
              name="disable"
              style={{ marginLeft: 0 }}
            >
              <Checkbox>{i18n.t('global.disabled')}</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default EditAlbumModal;
