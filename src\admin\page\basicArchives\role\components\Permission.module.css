.rolePermissionDrawer :global(.ant-tabs .ant-tabs-content .permission-content .drawer-inner-box) {
  height: 100%;
}

.rolePermissionDrawer :global(.permission-content) {
  overflow-y: auto;
}

.rolePermissionDrawer :global(.permission-content .menu-card ) {
  margin-bottom: 16px;
}

.rolePermissionDrawer :global(.permission-content .ant-spin-nested-loading, ant-spin-container) {
  height: 100%;
}

.rolePermissionDrawer :global(.permission-content .ant-spin-container) {
  display: flex;
  flex-flow: column nowrap;
}

.rolePermissionDrawer :global(.permission-content .ant-spin-container .search-input) {
  height: 32px;
}

.rolePermissionDrawer :global(.permission-content .ant-spin-container .menu-content) {
  flex: 1;
  overflow-y: auto;
}

.rolePermissionDrawer :global(.ant-tabs > .ant-tabs-nav .ant-tabs-tab) {
  padding: 6px 8px 6px 0 !important;
}