import { message } from 'antd';
import { ModalProps } from 'antd/es/modal';
import * as TransferGroupApi from 'common/api/core/TransferGroup';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onOk } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const template: TemplateItem[] = [
    {
      dataIndex: 'code',
      display: i18n.t('global.groupCode'),
      type: 'STRING',
      remark: i18n.t('global.allocatingGroupCode'),
      required: true,
    },
    {
      dataIndex: 'name',
      display: i18n.t('global.groupName'),
      type: 'STRING',
      remark: i18n.t('global.allocatingGroupName'),
      required: true,
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.groupRemark'),
      type: 'STRING',
      remark: i18n.t('global.allocatingGroupRemark'),
    },
    {
      dataIndex: 'shipType',
      display: i18n.t('global.shipperType'),
      type: 'STRING',
      remark: i18n.t('global.allocatingGroupDeliverType'),
      required: true,
    },
    {
      dataIndex: 'receiptType',
      display: i18n.t('global.receiverType'),
      type: 'STRING',
      remark: i18n.t('global.allocatingGroupTakeType'),
      required: true,
    },
    {
      dataIndex: 'shipData',
      display: i18n.t('global.shipperCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'receiptData',
      display: i18n.t('global.receiverCode'),
      type: 'STRING',
      required: true,
    },
  ];

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    data.forEach((item) => {
      switch (item.shipType) {
        case i18n.t('global.shop'):
          item.shipType = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.shipType = 'WAREHOUSE';
          break;
        default:
          break;
      }
      switch (item.receiptType) {
        case i18n.t('global.shop'):
          item.receiptType = 'SHOP';
          break;
        case i18n.t('global.warehouse'):
          item.receiptType = 'WAREHOUSE';
          break;
        default:
          break;
      }
    });
    const result: any = [];
    // eslint-disable-next-line
    data.forEach((item) => {
      if (item.code) {
        item.shipData = [item.shipData];
        item.receiptData = [item.receiptData];
        result.push(item);
        const codes = result.filter((n) => n.code === item.code);
        if (codes.length > 1) {
          message.error(i18n.t('global.doubleGroupCodeError'));
          return false;
        }
      } else {
        if (item.shipData) {
          result[result.length - 1].shipData.push(item.shipData);
        }
        if (item.receiptData) {
          result[result.length - 1].receiptData.push(item.receiptData);
        }
      }
    });
    try {
      setProgressPercent(0);
      await TransferGroupApi.Imports(
        {
          data: result,
          // @ts-ignore
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  return (
    <Modal
      width={960}
      title={i18n.t('global.importTransferGroup')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.transferGroup')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
