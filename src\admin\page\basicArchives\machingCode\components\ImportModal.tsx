import { ModalProps } from 'antd/es/modal';
import * as Maching<PERSON>odeA<PERSON> from 'common/api/core/MachingCode';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface IImportModalProps {
  modalProps?: ModalProps;
  onOk?: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onOk } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    try {
      setProgressPercent(0);
      await MachingCodeApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );

      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  const template: TemplateItem[] = [
    {
      dataIndex: 'prodCode',
      display: i18n.t('global.productCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'colorCode',
      display: i18n.t('global.colorCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'sizeCode',
      display: i18n.t('global.sizeCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'type',
      display: i18n.t('global.type'),
      type: 'STRING',
      required: true,
      remark: ['FACTORY', 'SHOP', 'WAREHOUSE'].join(','),
    },
    {
      dataIndex: 'ratio',
      display: i18n.t('global.ratioValue'),
      type: 'NUMBER',
      required: true,
    },
  ];

  return (
    <Modal
      title={i18n.t('global.import')}
      confirmLoading={confirmLoading}
      destroyOnClose
      footer={false}
      width={1000}
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.matching')}
        template={template}
        onImport={onImport}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
