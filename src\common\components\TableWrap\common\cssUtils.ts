/**
 * 检测当前是否为大字体模式
 * @returns boolean
 */
export const isLargeUIScale = (): boolean => {
  if (typeof window === 'undefined') return false;
  return document.documentElement.getAttribute('data-ui-scale') === 'large';
};

/**
 * 获取当前的缩放因子
 * @returns number
 */
export const getCurrentScaleFactor = (): number => {
  // 直接根据UI模式返回缩放因子，避免解析CSS
  return isLargeUIScale() ? 1.125 : 1;
};

/**
 * 获取CSS变量的数值（仅用于简单数值，不处理calc表达式）
 * @param variableName CSS变量名（不包含--前缀）
 * @param defaultValue 默认值
 * @returns 数值
 */
export const getCSSVariableValue = (variableName: string, defaultValue: number = 0): number => {
  if (typeof window === 'undefined') return defaultValue;

  const value = getComputedStyle(document.documentElement).getPropertyValue(`--${variableName}`).trim();

  if (!value) return defaultValue;

  // 只处理简单的数值，不处理calc表达式
  if (value.includes('calc(')) {
    // console.warn(`CSS variable --${variableName} contains calc() expression, using scale factor instead`);
    return defaultValue;
  }

  // 直接的数值
  const numValue = parseFloat(value.replace(/px|rem|em|%/, ''));
  return Number.isNaN(numValue) ? defaultValue : numValue;
};

/**
 * 计算Action列宽度
 * @param actionCount 操作按钮数量
 * @param size 尺寸
 * @returns 计算后的宽度
 */
export const calcActionWidthByScale = (actionCount: number, size: 'small' | 'middle' | 'large' = 'middle'): number => {
  const scaleFactor = getCurrentScaleFactor();

  // 基础尺寸（来自设计规范）
  const baseUnitWidth = size === 'small' ? 24 : 32;
  const baseGap = 8;
  const basePadding = size === 'small' ? 16 : 24;

  // 应用缩放因子
  const unitWidth = baseUnitWidth * scaleFactor;
  const gapsWidth = actionCount > 1 ? (actionCount - 1) * baseGap * scaleFactor : 0;
  const padding = basePadding * scaleFactor;

  const totalWidth = actionCount * unitWidth + gapsWidth + padding;

  return Math.ceil(totalWidth);
};
