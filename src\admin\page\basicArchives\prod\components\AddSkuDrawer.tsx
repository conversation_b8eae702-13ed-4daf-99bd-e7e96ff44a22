import SkuOperateForm from 'admin/page/basicArchives/prod/components/SkuOperateForm';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
// import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect } from 'react';

import { ISkuItem } from '../data';

interface AddSkuDrawerProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: ISkuItem) => void;
  onCancel: () => void;
}

const AddSkuDrawer: React.FC<AddSkuDrawerProps> = (props) => {
  const [form] = Form.useForm();

  const { onSubmit, onCancel, visible, confirmLoading } = props;

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISkuItem);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Drawer
      title={i18n.t('global.newBarcode')}
      width={640}
      // confirmLoading={confirmLoading}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      okButtonProps={{
        loading: confirmLoading,
      }}
      onOk={handleSubmit}
      onClose={() => {
        if (onCancel) onCancel();
        form.resetFields();
      }}
    >
      <SkuOperateForm form={form} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddSkuDrawer;
