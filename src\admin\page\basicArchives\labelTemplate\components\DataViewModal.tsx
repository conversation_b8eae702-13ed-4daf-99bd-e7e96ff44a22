import Modal from 'common/components/Modal';
import PowerTable from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import React from 'react';

interface DataViewModalProps {
  open: boolean;
  onCancel: () => void;
}

const DataViewModal: React.FC<DataViewModalProps> = (props) => {
  const { open, onCancel } = props;
  const tableData = [
    {
      key: 'alias',
      desc: i18n.t('global.alias'),
    },
    {
      key: 'barcode',
      desc: i18n.t('global.barcode'),
    },
    {
      key: 'brandCode',
      desc: i18n.t('global.brandCode'),
    },
    {
      key: 'brandName',
      desc: i18n.t('global.brandName'),
    },
    {
      key: 'colorCode',
      desc: i18n.t('global.colorCode'),
    },
    {
      key: 'colorName',
      desc: i18n.t('global.colorName'),
    },
    {
      key: 'yearCode',
      desc: i18n.t('global.yearCode'),
    },
    {
      key: 'yearName',
      desc: i18n.t('global.yearName'),
    },
    {
      key: 'specCode',
      desc: i18n.t('global.specCode'),
    },
    {
      key: 'specName',
      desc: i18n.t('global.specName'),
    },
    {
      key: 'subCategoryCode',
      desc: i18n.t('global.subCategoryCode'),
    },
    {
      key: 'subCategoryName',
      desc: i18n.t('global.subCategoryName'),
    },
    {
      key: 'genderCode',
      desc: i18n.t('global.genderCode'),
    },
    {
      key: 'genderName',
      desc: i18n.t('global.genderName'),
    },
    {
      key: 'name',
      desc: i18n.t('global.skuName'),
    },
    {
      key: 'priCategoryCode',
      desc: i18n.t('global.priCategoryCode'),
    },
    {
      key: 'priCategoryName',
      desc: i18n.t('global.priCategoryName'),
    },
    {
      key: 'prodCode',
      desc: i18n.t('global.productCode'),
    },
    {
      key: 'prodName',
      desc: i18n.t('global.prodName'),
    },
    {
      key: 'sizeCode',
      desc: i18n.t('global.sizeCode'),
    },
    {
      key: 'sizeName',
      desc: i18n.t('global.sizeName'),
    },
    {
      key: 'source',
      desc: i18n.t('global.defaultImg'),
    },
    {
      key: 'rfidTag',
      desc: i18n.t('global.rfidTag'),
    },
    {
      key: 'disturbTag',
      desc: i18n.t('global.disturb'),
    },
    {
      key: 'disabled',
      desc: i18n.t('global.isDisabled'),
    },
    {
      key: 'tagPrice',
      desc: i18n.t('global.tagPrice'),
    },
    {
      key: 'retailPrice',
      desc: i18n.t('global.retailPrice'),
    },
    {
      key: 'created',
      desc: i18n.t('global.created'),
    },
    {
      key: 'modified',
      desc: i18n.t('global.modified'),
    },
  ];

  const tableColumns = [
    {
      title: 'key',
      dataIndex: 'key',
      width: 200,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'desc',
      width: 200,
    },
  ];

  return (
    <Modal
      title={i18n.t('global.templateDetails')}
      width={740}
      destroyOnClose
      open={open}
      footer={false}
      onCancel={onCancel}
    >
      <PowerTable
        initialized
        rowKey="key"
        columns={tableColumns}
        pagination={false}
        tableProps={{ size: 'small', dataSource: tableData, scroll: { x: 600, y: 560 } }}
        refreshBtnVisible={false}
      />
    </Modal>
  );
};

export default DataViewModal;
