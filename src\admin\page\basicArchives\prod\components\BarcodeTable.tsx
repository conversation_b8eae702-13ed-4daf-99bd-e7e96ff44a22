import AddSkuDrawer from 'admin/page/basicArchives/prod/components/AddSkuDrawer';
import EditSkuDrawer from 'admin/page/basicArchives/prod/components/EditSkuDrawer';
import { ISkuItem } from 'admin/page/basicArchives/prod/data';
import { ProdContext, TProdContext } from 'admin/page/basicArchives/prod/reducer';
import { Button, Image, Tooltip } from 'antd';
import * as SkuA<PERSON> from 'common/api/core/Sku';
import * as FileApi from 'common/api/file/Img';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import AliasViewer from 'common/components/AliasViewer';
import DisabledTrigger from 'common/components/DisabledTrigger';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import useSetting from 'common/hooks/useSetting';
import { formatCurrency } from 'common/utils';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useContext, useEffect, useRef, useState } from 'react';

export interface BarcodeTableProps {
  fetchSkuList?: () => void;
}
const BarcodeTable: React.FC<BarcodeTableProps> = (props) => {
  const { fetchSkuList } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state, dispatch } = useContext<TProdContext>(ProdContext);
  const [searchSkuList, setSearchSkuList] = useState<ISkuItem[]>([]);
  const [currencyData, setCurrencyData] = useState<any>();
  const [editModalCurrent, setEditModalCurrent] = useState<Partial<ISkuItem> | undefined>({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addModalConfirmLoading, setAddModalConfirmLoading] = useState(false);
  const [editModalConfirmLoading, setEditModalConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  const [permission] = usePermission('A:BASE:PRODUCT');
  const createPermission = permission.codes.includes('SKU_CREATE');
  const editPermission = permission.codes.includes('SKU_UPDATE');
  const deletePermission = permission.codes.includes('SKU_DELETE');

  const refreshSkuList = async () => {
    setLoading(true);
    if (fetchSkuList) await fetchSkuList();
    setLoading(false);
  };

  useEffect(() => {
    setSearchSkuList(state.skuList);
  }, [state.skuList]);

  const { CURRENCY: currency } = useSetting([{ code: 'CURRENCY', valueType: 'STRING' }]);
  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }
  const showEditModal = (item: ISkuItem) => {
    setEditModalCurrent(item);
    setEditModalVisible(true);
  };

  const onDelete = (record) => {
    if (!record) return;
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: record.barcode,
      onOk: async () => {
        try {
          await SkuApi.Delete({ id: record.id });
          refreshSkuList();
          setEditModalVisible(false);
          NoticeUtil.success();
        } catch (e) {}
      },
    });
  };

  const handleAddModalSubmit = async (values: ISkuItem) => {
    setAddModalConfirmLoading(true);
    try {
      await SkuApi.Create({
        ...values,
        prodId: state.prodId,
      });
      setAddModalConfirmLoading(false);
      setAddModalVisible(false);
      refreshSkuList();
      NoticeUtil.success();
    } catch (e) {
      setAddModalConfirmLoading(false);
    }
  };

  const handleAddModalCancel = () => {
    setAddModalVisible(false);
  };

  const handleEditModalSubmit = async (values: ISkuItem) => {
    setEditModalConfirmLoading(true);
    try {
      await SkuApi.Update({
        ...values,
        id: editModalCurrent?.id,
      });
      setEditModalConfirmLoading(false);
      setEditModalVisible(false);
      refreshSkuList();
      NoticeUtil.success();
    } catch (e) {
      setEditModalConfirmLoading(false);
    }
  };

  const handleEditModalCancel = () => {
    setEditModalCurrent(undefined);
    refreshSkuList();
    setEditModalVisible(false);
  };

  const onSearch = (value) => {
    if (value) {
      setSearchSkuList(
        state.skuList.filter((n) => n.barcode.indexOf(value.trim()) !== -1 || n.name.indexOf(value.trim()) !== -1),
      );
    } else {
      setSearchSkuList(state.skuList);
    }
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'barcode',
      label: i18n.t('global.barcode'),
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} onSearch={onSearch} />
      ),
    },
  ];

  const columns: any = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      width: 120,
    },
    {
      title: i18n.t('global.image'),
      width: 100,
      dataIndex: 'source',
      render: (value, record) => {
        let url = '';
        let previewUrl = '';
        const { defaultImgUrl } = record;

        if (defaultImgUrl) {
          url = defaultImgUrl.includes('http') ? defaultImgUrl : FileApi.Get(defaultImgUrl);
          previewUrl = url;
        }

        if (value) {
          url = FileApi.Get(value, 100, 100);
          previewUrl = FileApi.Get(value);
        }

        return (
          <Image
            preview={
              url
                ? {
                    src: previewUrl,
                  }
                : false
            }
            src={url}
          />
        );
      },
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      valueType: 'text',
      auto: true,
      minWidth: 180,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.alias'),
      dataIndex: 'alias',
      align: 'center',
      width: 80,
      render: (value: string, record) => <AliasViewer aliasData={record.alias} />,
    },
    {
      title: i18n.t('global.sourceBarcode'),
      dataIndex: 'barcodeSourceCode',
      width: 200,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.parameters'),
      width: 200,
      render: (value: string, record) => {
        return (
          <>
            <div>
              <Tooltip
                placement="topLeft"
                title={
                  <>
                    {record?.colorCode}
                    <br />
                    {record?.colorName}
                  </>
                }
              >
                {i18n.t('global.colors')}: {record.colorName || '--'}
              </Tooltip>
            </div>
            <div>
              <Tooltip
                placement="topLeft"
                title={
                  <>
                    {record?.sizeCode}
                    <br />
                    {record?.sizeName}
                  </>
                }
              >
                {i18n.t('global.sizes')}: {record.sizeName || '--'}
              </Tooltip>
            </div>
            <div>
              <Tooltip
                placement="topLeft"
                title={
                  <>
                    {record?.specCode}
                    <br />
                    {record?.specName}
                  </>
                }
              >
                {i18n.t('global.specs')}: {record.specName || '--'}
              </Tooltip>
            </div>
            <div>
              {i18n.t('global.weight')}: {record.weight || '--'}
            </div>
          </>
        );
      },
    },
    {
      title: i18n.t('global.price'),
      width: 300,
      render: (value: string, record) => (
        <>
          <div>
            {i18n.t('global.barcodeTagPrice')}：
            {record.tagPrice ? `${formatCurrency(record.tagPrice || 0, currencyData?.symbol)}` : ''}
          </div>
          <div>
            {i18n.t('global.barcodeRetailPrice')}：
            {record.retailPrice ? `${formatCurrency(record.retailPrice || 0, currencyData?.symbol)}` : ''}
          </div>
        </>
      ),
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        onDelete(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible={false}
        rightToolbar={[
          <DisabledTrigger
            onChange={(val) => {
              dispatch({ type: 'setDisabled', payload: val });
            }}
          />,
          createPermission && (
            <Button type="primary" onClick={() => setAddModalVisible(true)}>
              {i18n.t('global.new')}
            </Button>
          ),
          <Button
            type="default"
            loading={loading}
            onClick={refreshSkuList}
            icon={
              <span className="flex h-full w-full items-center justify-center">
                <RefreshIcon className="fill-slate-500" />
              </span>
            }
          />,
        ]}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        tableProps={{
          dataSource: searchSkuList,
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  showEditModal(record);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
      />
      <AddSkuDrawer
        visible={addModalVisible}
        confirmLoading={addModalConfirmLoading}
        onSubmit={handleAddModalSubmit}
        onCancel={handleAddModalCancel}
      />
      <EditSkuDrawer
        current={editModalCurrent}
        visible={editModalVisible}
        confirmLoading={editModalConfirmLoading}
        onSubmit={handleEditModalSubmit}
        onDelete={onDelete}
        onCancel={handleEditModalCancel}
      />
    </>
  );
};

export default BarcodeTable;
