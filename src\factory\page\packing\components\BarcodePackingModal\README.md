## 工厂端条码装箱

工厂端需要根据已有的明细来做判断，条码装箱须有 `F_C:PACKING:PACKING_BARCODE` 权限

保存接口 `/fm-order/box-sku-save`

## 流程图

### 条码装箱操作流程

```mermaid
flowchart TD
    A[开始] --> B{是否是预装箱}
    B -->|Yes| C[请求预装箱明细数据]
    B -->|No| D[请求当前单据明细]
    C --> E[设置输入操作数，负数减少，正数增加，范围：-50~50，默认：1]
    D --> E
    E --> F[输入条码, 直接回车]
    F --> G{判断输入条码是否在明细中, 包括搜索条码的别名}
    G -->|Yes| H[条码别名是否对应明细多条条码数据]
    G -->|No| I[提示条码不在当前明细范围内]
    H -->|Yes| J[打开条码弹窗选择对应条码]
    H -->|No| K[返回输入条码在当前明细中的 Index]
    I --> F
    J --> K
    K --> L{当前条码是否只允许 RFID 装箱}
    L -->|Yes| M[提示该款只允许 RFID 装箱]
    L -->|No| N{判断条码的操作数是否大于剩余数}
    M --> F
    N -->|Yes| O[提示操作数超出]
    N -->|No| P[增加对应条码的操作数]
    O --> F
    P --> Q[保存]
    Q --> R[结束]
```

### 装箱初始化请求数据流程

```mermaid
graph TD
    A["开始"] --> B["开启加载状态"]
    B --> C{"根据 **fromPreboxTab** 或者 **isPrebox** 判断是否是预装箱"}
    C -- No --> D["**lineDataList** = 请求单据明细数据(fetchOrderLineData)"]
    C -- Yes --> E["**lineDataList** = 请求预装箱明细数据(fetchPreBoxLineData)"]
    D --> F{"根据 **boxInfo.id** 判断是否是编辑箱"}
    E --> F
    F -- Yes --> G["**packedLineDataList** = 请求箱明细数据(fetchBoxLineData)"]
    F -- No --> H["**packedLineDataList** = undefined"]
    G --> I["进入 reducer 格式化数据"]
    H --> I
    I --> J["汇总条码数据并回显操作数、单据数、剩余数"]
    J --> K["结束"]
```


