import Other from 'admin/page/basicArchives/other';
import { AdminStsOrder } from 'common/page/stsOrder';
import { AdminStsOrderDetail } from 'common/page/stsOrder/Detail';
import WcbOrder from 'admin/page/business/wcbOrder';
import WibOrder from 'admin/page/business/wibOrder';
import WibOrderDetail from 'admin/page/business/wibOrder/Detail';
import WtsOrder from 'admin/page/business/wtsOrder';
import WtsOrderDetail from 'admin/page/business/wtsOrder/Detail';
import PrintTaskDetail from 'admin/page/print/printTask/Detail';
import AlbumIcon from 'common/assets/icons/menu/album.svg?react';
import AtomIcon from 'common/assets/icons/menu/atom.svg?react';
import BarChartIcon from 'common/assets/icons/menu/bar-chart.svg?react';
import ClipboardListIcon from 'common/assets/icons/menu/clipboard-list.svg?react';
import FactoryIcon from 'common/assets/icons/menu/factory.svg?react';
import PrinterIcon from 'common/assets/icons/menu/printer.svg?react';
import RadioTowerIcon from 'common/assets/icons/menu/radio-tower.svg?react';
import SettingsIcon from 'common/assets/icons/menu/settings.svg?react';
import StoreIcon from 'common/assets/icons/menu/store.svg?react';
import WarehouseIcon from 'common/assets/icons/menu/warehouse.svg?react';
import ReportIcon from 'common/assets/icons/menu/report.svg?react';
import { IMenuItem, IMenuTreeData } from 'common/types/IMenu';
import { getTranslationsForAllLanguages } from 'common/utils/Permission';

import SptsOrder from 'admin/page/business/sptsOrder';
import SptsOrderDetail from 'admin/page/business/sptsOrder/Detail';
import SpibOrder from 'admin/page/business/spibOrder';
import SpibOrderDetail from 'admin/page/business/spibOrder/Detail';
import SpobOrder from 'admin/page/business/spobOrder';
import PsiReport from 'admin/page/report/psiReport';
import InboundBox from 'admin/page/report/inboundBox';
import OutboundBox from 'admin/page/report/outboundBox';
import Outbound from 'admin/page/report/outbound';
import SpobOrderDetail from 'admin/page/business/spobOrder/Detail';
import BorrowRepay from 'admin/page/business/borrowRepay';
import { AdminFqcOrder } from 'common/page/fqcOrder';
import { AdminFqcOrderDetail } from 'common/page/fqcOrder/Detail';
import Location from '../page/report/location';
import SampleLocation from '../page/report/sampleLocation';
import TagInventory from '../page/report/tagInventory';
import Employee from '../page/basicArchives/employee';
import LabelTemplate from '../page/basicArchives/labelTemplate';
import Logistics from '../page/basicArchives/logistics';
import Partner from '../page/basicArchives/partner';
import Prod from '../page/basicArchives/prod';
import ProdAttributes from '../page/basicArchives/prodAttributes';
import Brand from '../page/basicArchives/prodAttributes/brand';
import ReportTemplate from '../page/basicArchives/reportTemplate';
import Role from '../page/basicArchives/role';
import Sku from '../page/basicArchives/sku';
import UniqueCode from '../page/basicArchives/uniqueCode';
import SkuTag from '../page/basicArchives/skuTag';
import SkuTagLog from '../page/basicArchives/skuTagLog';
import FadOrder from '../page/business/fadOrder';
import FadOrderDetail from '../page/business/fadOrder/Detail';
import FibOrder from '../page/business/fibOrder';
import FibOrderDetail from '../page/business/fibOrder/Detail';
import FmOrder from '../page/business/fmOrder';
import FmOrderDetail from '../page/business/fmOrder/Detail';
import FobOrder from '../page/business/fobOrder';
import FobOrderDetail from '../page/business/fobOrder/Detail';
import RfidSource from '../page/business/rfidSource';
import SibOrder from '../page/business/sibOrder';
import SibOrderDetail from '../page/business/sibOrder/Detail';
import SobOrder from '../page/business/sobOrder';
import SobOrderDetail from '../page/business/sobOrder/Detail';
import SrOrder from '../page/business/srOrder';
import SrOrderDetail from '../page/business/srOrder/Detail';
import WcbOrderDetail from '../page/business/wcbOrder/Detail';
import WobOrder from '../page/business/wobOrder';
import WobOrderDetail from '../page/business/wobOrder/Detail';
import WriteTagLog from '../page/business/writeTagLog';
import AntiTheftAlarm from '../page/dataCollection/antiTheftAlarm';
import AntiTheftGather from '../page/dataCollection/antiTheftGather';
import AntiTheftGatherDetail from '../page/dataCollection/antiTheftGather/Detail';
import FittingAnalysis from '../page/dataCollection/fittingAnalysis';
import FittingDashboard from '../page/dataCollection/fittingDashboard';
import FittingRecord from '../page/dataCollection/fittingRecord';
import FittingRecordNew from '../page/dataCollection/fittingRecordNew';
import DeviceDashboard from '../page/device/deviceDashboard';
import DeviceFiles from '../page/device/deviceFiles';
import DeviceManagement from '../page/device/deviceManagement';
import DeviceManagementDetail from '../page/device/deviceManagement/Detail';
import Overview from '../page/overview';
import PrintLog from '../page/print/printLog';
import PrintTask from '../page/print/printTask';
import Setting from '../page/setting';
import AsyncTask from '../page/task/asyncTask';
import SyncTask from '../page/task/syncTask';
import ScheduledTask from '../page/task/scheduledTask';
import PushLog from '../page/task/pushLog';
import MqttLog from '../page/task/mqttLog';
import SampleTag from '../page/basicArchives/sampleTag';
import Sample from '../page/basicArchives/sample';
import SampleAttributes from '../page/basicArchives/sampleAttributes';
import SampleBrand from '../page/basicArchives/sampleAttributes/sampleBrand';
import SampleSku from '../page/basicArchives/sampleSku';
import SampleRfidSource from '../page/business/sampleRfidSource';
import PhotoAlbum from '../page/basicArchives/photoAlbum';
import QualityCheckIssues from '../page/basicArchives/qualityCheckIssues';
import ClientLog from '../page/task/clientLog';
import MachingCode from '../page/basicArchives/machingCode';
import RfidFilterRules from '../page/basicArchives/skuFilterRules';
import WorkflowTask from '../page/task/workflowTask';

export const menuTreeData: IMenuTreeData = {
  client: 'WEB',
  subClient: 'WEB_ADMIN_2',
  data: {
    code: 'A',
    name: 'WEB管理后台2.0',
    i18nCode: 'menu.admin.webAdmin',
    permissionCodes: [
      'IC_API_CORE:USER:QUERY',
      'IC_API_CORE:USER:UPDATE',
      'IC_API_CORE:PERMISSION:QUERY',
      'IC_API_CORE:PARTNER_PERMISSION:QUERY',
      'IC_API_CORE:TENANT RPC:QUERY',
      'IC_API_CORE:EXT_FIELD:QUERY',
      'IC_API_CORE:CONFIG:QUERY',
      'IC_API_CORE:PERMISSION:CREATE',
      'IC_API_CORE:BUSINESS_MODEL:QUERY',
      'IC_API_CORE:CONFIG:UPDATE',
      'IC_API_CORE:TENANT_RPC:QUERY',
      'IC_API_SHOP:DASHBOARD:QUERY',
      'IC_API_SHOP:TS_ORDER:QUERY',
      'IC_API_WAREHOUSE:DASHBOARD:QUERY',
      'IC_API_WAREHOUSE:W_TS_ORDER:QUERY',
      'IC_API_FACTORY:DASHBOARD:QUERY',
      'IC_API_CORE:ORDER_TYPE:QUERY',
      // 样品全局权限
      'IC_API_SAMPLE:SAMPLE_BRAND:QUERY', // 品牌
      'IC_API_SAMPLE:SAMPLE_COLOR_GRP:QUERY', // 颜色组
      'IC_API_SAMPLE:SAMPLE_COLOR:QUERY', // 颜色
      'IC_API_SAMPLE:SAMPLE_SIZE_GRP:QUERY', // 尺码组
      'IC_API_SAMPLE:SAMPLE_SIZE:QUERY', // 尺码
      'IC_API_SAMPLE:SAMPLE_YEAR:QUERY', // 年份
      'IC_API_SAMPLE:SAMPLE_SERIES:QUERY', // 系列
      'IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:QUERY', // 主要分类
      'IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:QUERY', // 次要分类
      'IC_API_SAMPLE:SAMPLE_EXT_FIELD:QUERY', // 扩展字段
    ],
    type: 'MENU',
    module: 'CORE',
    child: [
      {
        code: 'A:HOME',
        name: '概述',
        i18nCode: 'menu.admin.overview',
        type: 'MENU',
        module: 'CORE',
        icon: BarChartIcon,
        permissionCodes: [
          'IC_API_SHOP:DASHBOARD:QUERY',
          'IC_API_SHOP:TS_ORDER:QUERY',
          'IC_API_BSHOP:DASHBOARD:QUERY',
          'IC_API_BSHOP:TS_ORDER:QUERY',
          'IC_API_FACTORY:DASHBOARD:QUERY',
          'IC_API_BFACTORY:DASHBOARD:QUERY',
          'IC_API_WAREHOUSE:DASHBOARD:QUERY',
          'IC_API_WAREHOUSE:W_TS_ORDER:QUERY',
          'IC_API_BWAREHOUSE:DASHBOARD:QUERY',
          'IC_API_BWAREHOUSE:W_TS_ORDER:QUERY',
          'IC_API_SAMPLE:SAMPLE_LR_RECORD:QUERY',
          'IC_API_SAMPLE:SAMPLE_DASHBOARD:QUERY',
          'IC_API_SAMPLE:SAMPLE_W_TS_ORDER:QUERY',
          'IC_API_SAMPLE:SAMPLE_WIB_ORDER:QUERY',
          'IC_API_SAMPLE:SAMPLE_WOB_ORDER:QUERY',
        ],
        route: {
          path: '/app',
          exact: true,
          component: Overview,
        },
      },
      {
        code: 'A:BASE',
        name: '基础档案',
        i18nCode: 'menu.admin.baseArchives',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        icon: AlbumIcon,
        child: [
          {
            code: 'A:BASE:PARTNER',
            name: '合作伙伴',
            i18nCode: 'menu.admin.partner',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/partner',
              component: Partner,
            },
            child: [
              {
                code: 'A:BASE:PARTNER:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_CORE:PARTNER:QUERY',
                  'IC_API_CORE:WAREHOUSE:QUERY',
                  'IC_API_CORE:DEV_CONFIG:QUERY',
                  'IC_API_CORE:STORAGE_LOCATION:QUERY',
                  'IC_API_CORE:ANTI_THEFT_SHARE_GROUP:QUERY',
                  'IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:QUERY',
                ],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_CORE:PARTNER:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_CORE:PARTNER:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:DEL',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:PARTNER:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE_W',
                name: '创建仓位',
                i18nCode: 'menu.admin.createWarehouse',
                permissionCodes: ['IC_API_CORE:WAREHOUSE:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:EDIT_W',
                name: '编辑仓位',
                i18nCode: 'menu.admin.editWarehouse',
                permissionCodes: ['IC_API_CORE:WAREHOUSE:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:DEL_W',
                name: '删除仓位',
                i18nCode: 'menu.admin.deleteWarehouse',
                permissionCodes: ['IC_API_CORE:WAREHOUSE:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:EDIT_PAY_CONFIG',
                name: '修改支付配置',
                i18nCode: 'menu.admin.editPaymentConfig',
                permissionCodes: ['IC_API_CORE:DEV_CONFIG:CREATE', 'IC_API_CORE:DEV_CONFIG:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:IMPORT_LOCATION',
                name: '导入货位',
                i18nCode: 'menu.admin.importLocation',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE_LOCATION',
                name: '创建货位',
                i18nCode: 'menu.admin.createLocation',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE_BATCH_LOCATION',
                name: '批量创建货位',
                i18nCode: 'menu.admin.multipleCreateLocation',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:EDIT_LOCATION',
                name: '编辑货位',
                i18nCode: 'menu.admin.editLocation',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:DELETE_LOCATION',
                name: '删除货位',
                i18nCode: 'menu.admin.deleteLocation',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:IMPORT_SAMPLE_LOCATION',
                name: '导入样品货位',
                i18nCode: 'menu.admin.importSampleLocation',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE_SAMPLE_LOCATION',
                name: '创建样品货位',
                i18nCode: 'menu.admin.createSampleLocation',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:CREATE_BATCH_SAMPLE_LOCATION',
                name: '批量创建样品货位',
                i18nCode: 'menu.admin.multipleCreateSampleLocation',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:EDIT_SAMPLE_LOCATION',
                name: '编辑样品货位',
                i18nCode: 'menu.admin.editSampleLocation',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PARTNER:DELETE_SAMPLE_LOCATION',
                name: '删除样品货位',
                i18nCode: 'menu.admin.deleteSampleLocation',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },

              {
                code: 'A:BASE:PARTNER:ANTI_THEFT_PARTNER',
                name: '维护防盗配置',
                i18nCode: 'menu.admin.maintainAntiTheftConfig',
                permissionCodes: [
                  'IC_API_CORE:PARTNER:UPDATE',
                  'IC_API_CORE:ANTI_THEFT_SHARE_GROUP:CREATE',
                  'IC_API_CORE:ANTI_THEFT_SHARE_GROUP_CONFIG:QUERY',
                  'IC_API_CORE:ANTI_THEFT_SHARE_GROUP_CONFIG:CREATE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:LOGISTICS',
            name: '物流公司',
            i18nCode: 'menu.admin.logisticsCompany',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/logistics',
              component: Logistics,
            },
            child: [
              {
                code: 'A:BASE:LOGISTICS:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:PARTNER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:LOGISTICS:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_CORE:PARTNER:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:LOGISTICS:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_CORE:PARTNER:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:LOGISTICS:DEL',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:PARTNER:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:EMPLOYEE',
            name: '员工',
            i18nCode: 'menu.admin.employee',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/employee',
              component: Employee,
            },
            child: [
              {
                code: 'A:BASE:EMPLOYEE:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:EMP:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_CORE:EMP:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_CORE:EMP:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:RESET',
                name: '重置密码',
                i18nCode: 'menu.admin.resetPassword',
                permissionCodes: ['IC_API_CORE:USER:RESET'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:DEL',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:EMP:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:SET_P_P',
                name: '设置合作伙伴权限',
                i18nCode: 'menu.admin.setUpPartnerPermission',
                permissionCodes: ['IC_API_CORE:PARTNER_PERMISSION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:EMPLOYEE:PARTNER_IMPORT',
                name: '合作伙伴权限导入',
                i18nCode: 'menu.admin.importPartnerPermission',
                permissionCodes: ['IC_API_CORE:PARTNER_PERMISSION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:ROLE',
            name: '角色',
            i18nCode: 'menu.admin.role',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/role',
              component: Role,
            },
            child: [
              {
                code: 'A:BASE:ROLE:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:PERMISSION:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ROLE:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_CORE:PERMISSION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ROLE:ASSIGN_EMP',
                name: '分配员工',
                i18nCode: 'menu.admin.assignEmployee',
                permissionCodes: ['IC_API_CORE:PERMISSION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ROLE:ASSIGN_P',
                name: '分配权限',
                i18nCode: 'menu.admin.assignPermission',
                permissionCodes: ['IC_API_CORE:PERMISSION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ROLE:ENCODE',
                name: '获取编码',
                i18nCode: 'menu.admin.getEncode',
                permissionCodes: ['IC_API_CORE:PERMISSION:QUERY'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ROLE:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:PERMISSION:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:PRODUCT',
            name: '商品',
            i18nCode: 'menu.admin.product',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/prod',
              component: Prod,
            },
            child: [
              {
                code: 'A:BASE:PRODUCT:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_CORE:PROD:QUERY',
                  'IC_API_CORE:COLOR:QUERY',
                  'IC_API_CORE:SIZE:QUERY',
                  'IC_API_CORE:SPEC:QUERY',
                  'IC_API_CORE:SKU:QUERY',
                  'IC_API_CORE:BRAND:QUERY',
                  'IC_API_CORE:GENDER:QUERY',
                  'IC_API_CORE:PRI_CATEGORY:QUERY',
                  'IC_API_CORE:SUB_CATEGORY:QUERY',
                  'IC_API_CORE:YEAR:QUERY',
                ],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_CORE:PROD:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: [
                  'IC_API_CORE:SKU:CREATE',
                  'IC_API_CORE:SKU_IMG:CREATE',
                  'IC_API_FILE:IMAGE:SAVE',
                  'IC_API_FILE:IMAGE:DELETE',
                  'IC_API_CORE:ASYNC_TASK:QUERY',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: [
                  'IC_API_CORE:PROD:UPDATE',
                  'IC_API_CORE:SKU:UPDATE',
                  'IC_API_FILE:IMAGE:SAVE',
                  'IC_API_FILE:IMAGE:DELETE',
                  'IC_API_CORE:SKU_IMG:CREATE',
                  'IC_API_CORE:SKU_IMG:UPDATE',
                  'IC_API_CORE:SKU_IMG:QUERY',
                  'IC_API_CORE:SKU_IMG:DELETE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:PROD:DELETE', 'IC_API_CORE:SKU_IMG:DELETE', 'IC_API_CORE:SKU:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:SKU_CREATE',
                name: '创建条码',
                i18nCode: 'menu.admin.createBarcode',
                permissionCodes: ['IC_API_CORE:SKU:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:SKU_UPDATE',
                name: '编辑条码',
                i18nCode: 'menu.admin.editBarcode',
                permissionCodes: [
                  'IC_API_CORE:SKU:UPDATE',
                  'IC_API_CORE:SKU_IMG:CREATE',
                  'IC_API_CORE:SKU_IMG:UPDATE',
                  'IC_API_CORE:SKU_IMG:DELETE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:PRODUCT:SKU_DELETE',
                name: '删除条码',
                i18nCode: 'menu.admin.deleteBarcode',
                permissionCodes: ['IC_API_CORE:SKU:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:SKU',
            name: '条码',
            i18nCode: 'menu.admin.sku',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/sku',
              component: Sku,
            },
            child: [
              {
                code: 'A:BASE:SKU:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:EXT_FIELD:QUERY', 'IC_API_CORE:SKU:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:MACHING',
            name: '配码',
            i18nCode: 'menu.admin.matchingCode',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/maching',
              component: MachingCode,
            },
            child: [
              {
                code: 'A:BASE:MACHING:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:MACHING_CODE:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              // {
              //   code: 'A:BASE:MACHING:CREATE',
              //   name: '创建',
              //   i18nCode:'menu.admin.create',
              //   permissionCodes: [],
              //   type: 'ITEM',
              //   module: 'CORE',
              // },
              // {
              //   code: 'A:BASE:MACHING:EDIT',
              //   name: '编辑',i18nCode: 'menu.admin.edit',
              //
              //   permissionCodes: [],
              //   type: 'ITEM',
              //   module: 'CORE',
              // },
              {
                code: 'A:BASE:MACHING:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:MACHING_CODE:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:MACHING:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_CORE:MACHING_CODE:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:SAMPLE',
            name: '样品',
            i18nCode: 'menu.admin.sampleClothes',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/Sample',
              component: Sample,
            },
            child: [
              {
                code: 'A:BASE:SAMPLE:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: [
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:CREATE',
                  'IC_API_SAMPLE:SAMPLE_SKU:CREATE',
                  'IC_API_FILE:IMAGE:SAVE',
                  'IC_API_FILE:IMAGE:DELETE',
                ],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: [
                  'IC_API_SAMPLE:SAMPLE:QUERY',
                  'IC_API_SAMPLE:SAMPLE:UPDATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:QUERY',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:CREATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:UPDATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:DELETE',
                ],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE:DELETE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:SKU_CREATE',
                name: '创建条码',
                i18nCode: 'menu.admin.createBarcode',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_SKU:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:SKU_UPDATE',
                name: '编辑条码',
                i18nCode: 'menu.admin.editBarcode',
                permissionCodes: [
                  'IC_API_SAMPLE:SAMPLE_SKU:QUERY',
                  'IC_API_SAMPLE:SAMPLE_SKU:UPDATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:QUERY',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:CREATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:UPDATE',
                  'IC_API_SAMPLE:SAMPLE_SKU_IMG:DELETE',
                ],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLE:SKU_DELETE',
                name: '删除条码',
                i18nCode: 'menu.admin.deleteBarcode',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_SKU:DELETE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:BASE:SAMPLE_SKU',
            name: '样品条码',
            i18nCode: 'menu.admin.sampleClothesBarcode',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/sample-sku',
              component: SampleSku,
            },
            child: [
              {
                code: 'A:BASE:SAMPLE_SKU:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_SKU:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:BASE:SAMPLETAG',
            name: '样品标签',
            i18nCode: 'menu.admin.sampleClothesLabel',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/sample-tag',
              component: SampleTag,
            },
            child: [
              {
                code: 'A:BASE:SAMPLETAG:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_RFID:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
              {
                code: 'A:BASE:SAMPLETAG:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_RFID:CREATE'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:BASE:SAMPLE_ATTRIBUTE',
            name: '样品属性',
            i18nCode: 'menu.admin.sampleClothesAttribute',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/sample-attribute',
              component: SampleAttributes,
            },
            child: [
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:BRAND',
                name: '品牌',
                i18nCode: 'menu.admin.brand',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/brand',
                  component: SampleBrand,
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:BRAND:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_BRAND:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:BRAND:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_BRAND:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:BRAND:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_BRAND:UPDATE', 'IC_API_SAMPLE:SAMPLE_BRAND:QUERY'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:BRAND:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_BRAND:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP',
                name: '颜色组',
                i18nCode: 'menu.admin.colorGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/color-grp',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_COLOR_GRP:QUERY', 'IC_API_SAMPLE:SAMPLE_COLOR:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_COLOR_GRP:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_COLOR_GRP:UPDATE', 'IC_API_SAMPLE:SAMPLE_COLOR_GRP:QUERY'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_COLOR_GRP:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:COLOR_GRP:MODIFY_COLORS',
                    name: '维护颜色',
                    i18nCode: 'menu.admin.maintainColor',
                    permissionCodes: [
                      'IC_API_SAMPLE:SAMPLE_COLOR:QUERY',
                      'IC_API_SAMPLE:SAMPLE_COLOR:CREATE',
                      'IC_API_SAMPLE:SAMPLE_COLOR:DELETE',
                      'IC_API_SAMPLE:SAMPLE_COLOR:UPDATE',
                    ],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP',
                name: '尺码组',
                i18nCode: 'menu.admin.sizeGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/size-grp',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SIZE_GRP:QUERY', 'IC_API_SAMPLE:SAMPLE_SIZE:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SIZE_GRP:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SIZE_GRP:QUERY', 'IC_API_SAMPLE:SAMPLE_SIZE_GRP:UPDATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SIZE_GRP:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP:MODIFY_SIZES',
                    name: '维护尺码',
                    i18nCode: 'menu.admin.maintainSize',
                    permissionCodes: [
                      'IC_API_SAMPLE:SAMPLE_SIZE:QUERY',
                      'IC_API_SAMPLE:SAMPLE_SIZE:CREATE',
                      'IC_API_SAMPLE:SAMPLE_SIZE:DELETE',
                      'IC_API_SAMPLE:SAMPLE_SIZE:UPDATE',
                    ],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:YEAR',
                name: '年份',
                i18nCode: 'menu.admin.year',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/year',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:YEAR:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_YEAR:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:YEAR:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_YEAR:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:YEAR:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_YEAR:QUERY', 'IC_API_SAMPLE:SAMPLE_YEAR:UPDATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:YEAR:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_YEAR:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:SERIES',
                name: '系列',
                i18nCode: 'menu.admin.series',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/series',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SERIES:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SERIES:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SERIES:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SERIES:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SERIES:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SERIES:QUERY', 'IC_API_SAMPLE:SAMPLE_SERIES:UPDATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SERIES:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SERIES:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY',
                name: '主要分类',
                i18nCode: 'menu.admin.primaryCategory',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/pri-category',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: [
                      'IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:UPDATE',
                      'IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:QUERY',
                    ],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_PRI_CATEGORY:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY',
                name: '次要分类',
                i18nCode: 'menu.admin.secondaryCategory',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/sub-category',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: [
                      'IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:QUERY',
                      'IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:UPDATE',
                    ],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_SUB_CATEGORY:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
              {
                code: 'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD',
                name: '扩展字段',
                i18nCode: 'menu.admin.extendedField',
                permissionCodes: [],
                type: 'MENU',
                module: 'SAMPLE',
                route: {
                  path: '/app/sample-attribute/ext-field',
                },
                child: [
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_EXT_FIELD:QUERY'],
                    type: 'VIEW',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_EXT_FIELD:CREATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_EXT_FIELD:QUERY', 'IC_API_SAMPLE:SAMPLE_EXT_FIELD:UPDATE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                  {
                    code: 'A:BASE:SAMPLE_ATTRIBUTE:EXT_FIELD:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_SAMPLE:SAMPLE_EXT_FIELD:DELETE'],
                    type: 'ITEM',
                    module: 'SAMPLE',
                  },
                ],
              },
            ],
          },
          {
            code: 'A:BASE:ATTRIBUTE',
            name: '商品属性',
            i18nCode: 'menu.admin.productAttribute',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/prod-attribute',
              component: ProdAttributes,
            },
            child: [
              {
                code: 'A:BASE:ATTRIBUTE:BRAND',
                name: '品牌',
                i18nCode: 'menu.admin.brand',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/brand',
                  component: Brand,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:BRAND:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:BRAND:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:BRAND:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:BRAND:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:BRAND:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:BRAND:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:BRAND:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:BRAND:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:COLOR_GRP',
                name: '颜色组',
                i18nCode: 'menu.admin.colorGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/color-grp',
                  // component: ColorGrp,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:COLOR_GRP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:COLOR_GROUP:QUERY', 'IC_API_CORE:COLOR:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:COLOR_GRP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:COLOR_GROUP:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:COLOR_GRP:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:COLOR_GROUP:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:COLOR_GRP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:COLOR_GROUP:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:COLOR_GRP:MODIFY_COLORS',
                    name: '维护颜色',
                    i18nCode: 'menu.admin.maintainColor',
                    permissionCodes: [
                      'IC_API_CORE:COLOR:QUERY',
                      'IC_API_CORE:COLOR:CREATE',
                      'IC_API_CORE:COLOR:DELETE',
                      'IC_API_CORE:COLOR:UPDATE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:SIZE_GRP',
                name: '尺码组',
                i18nCode: 'menu.admin.sizeGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/size-grp',
                  // component: SizeGrp,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:SIZE_GRP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:SIZE_GRP:QUERY', 'IC_API_CORE:SIZE:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SIZE_GRP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:SIZE_GRP:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SIZE_GRP:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:SIZE_GRP:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SIZE_GRP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:SIZE_GRP:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SIZE_GRP:MODIFY_SIZES',
                    name: '维护尺码',
                    i18nCode: 'menu.admin.maintainSize',
                    permissionCodes: [
                      'IC_API_CORE:SIZE:QUERY',
                      'IC_API_CORE:SIZE:CREATE',
                      'IC_API_CORE:SIZE:UPDATE',
                      'IC_API_CORE:SIZE:DELETE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:SPEC_GRP',
                name: '规格组',
                i18nCode: 'menu.admin.specificationGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/spec-grp',
                  // component: SpecGrp,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:SPEC_GRP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:SPEC_GRP:QUERY', 'IC_API_CORE:SPEC:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SPEC_GRP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:SPEC_GRP:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SPEC_GRP:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:SPEC_GRP:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SPEC_GRP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:SPEC_GRP:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SPEC_GRP:MODIFY_SPECS',
                    name: '维护规格',
                    i18nCode: 'menu.admin.maintainSpecification',
                    permissionCodes: [
                      'IC_API_CORE:SPEC:QUERY',
                      'IC_API_CORE:SPEC:CREATE',
                      'IC_API_CORE:SPEC:UPDATE',
                      'IC_API_CORE:SPEC:DELETE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:YEAR',
                name: '年份',
                i18nCode: 'menu.admin.year',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/year',
                  // component: Year,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:YEAR:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:YEAR:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:YEAR:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:YEAR:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:YEAR:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:YEAR:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:YEAR:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:YEAR:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:GENDER',
                name: '性别',
                i18nCode: 'menu.admin.sex',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/gender',
                  // component: Gender,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:GENDER:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:GENDER:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:GENDER:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:GENDER:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:GENDER:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:GENDER:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:GENDER:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:GENDER:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:PRI_CATEGORY',
                name: '主要分类',
                i18nCode: 'menu.admin.primaryCategory',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/pri-category',
                  // component: PriCategory,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:PRI_CATEGORY:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:PRI_CATEGORY:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:PRI_CATEGORY:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:PRI_CATEGORY:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:PRI_CATEGORY:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:PRI_CATEGORY:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:PRI_CATEGORY:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:PRI_CATEGORY:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:ATTRIBUTE:SUB_CATEGORY',
                name: '次要分类',
                i18nCode: 'menu.admin.secondaryCategory',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/prod-attribute/sub-category',
                  // component: SubCategory,
                },
                child: [
                  {
                    code: 'A:BASE:ATTRIBUTE:SUB_CATEGORY:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:SUB_CATEGORY:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SUB_CATEGORY:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:SUB_CATEGORY:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SUB_CATEGORY:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:SUB_CATEGORY:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ATTRIBUTE:SUB_CATEGORY:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:SUB_CATEGORY:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
            ],
          },
          {
            code: 'A:BASE:ALBUM',
            name: '门店相册',
            i18nCode: 'menu.admin.storeAlbum',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/photo-album',
              component: PhotoAlbum,
            },
            child: [
              {
                code: 'A:BASE:ALBUM:STORE',
                name: '门店相册',
                i18nCode: 'menu.admin.storeAlbum',
                permissionCodes: [],
                type: 'ITEM',
                module: 'CORE',
                child: [
                  {
                    code: 'A:BASE:ALBUM:STORE:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM:QUERY', 'IC_API_CORE:PARTNER_PHOTO_ALBUM:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: [
                      'IC_API_CORE:PARTNER_PHOTO_ALBUM:CREATE',
                      'IC_API_CORE:PHOTO_ALBUM:QUERY',
                      'IC_API_CORE:PARTNER_PHOTO_ALBUM:QUERY',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:DEFAULT',
                    name: '设为默认',
                    i18nCode: 'menu.admin.setupDefault',
                    permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:CONVERT',
                    name: '转为公共相册',
                    i18nCode: 'menu.admin.convertPublicAlbum',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:STORE:PHOTO',
                    name: '图片',
                    i18nCode: 'menu.admin.photo',
                    permissionCodes: [],
                    type: 'ITEM',
                    module: 'CORE',
                    child: [
                      {
                        code: 'A:BASE:ALBUM:STORE:PHOTO:VIEW',
                        name: '查看',
                        i18nCode: 'menu.admin.view',
                        permissionCodes: [
                          'IC_API_CORE:PARTNER_PHOTO_ALBUM_LINE:QUERY',
                          'IC_API_CORE:PARTNER_PHOTO_ALBUM_LINE:UPDATE',
                        ],
                        type: 'VIEW',
                        module: 'CORE',
                      },
                      {
                        code: 'A:BASE:ALBUM:STORE:PHOTO:UPLOAD',
                        name: '上传',
                        i18nCode: 'menu.admin.upload',
                        permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM_LINE:CREATE', 'IC_API_FILE:IMAGE:SAVE'],
                        type: 'ITEM',
                        module: 'CORE',
                      },
                      {
                        code: 'A:BASE:ALBUM:STORE:PHOTO:DELETE',
                        name: '删除',
                        i18nCode: 'menu.admin.delete',
                        permissionCodes: ['IC_API_CORE:PARTNER_PHOTO_ALBUM_LINE:DELETE'],
                        type: 'ITEM',
                        module: 'CORE',
                      },
                    ],
                  },
                ],
              },
              {
                code: 'A:BASE:ALBUM:COMMON',
                name: '公共相册',
                i18nCode: 'menu.admin.publicAlbum',
                permissionCodes: [],
                type: 'ITEM',
                module: 'CORE',
                child: [
                  {
                    code: 'A:BASE:ALBUM:COMMON:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:COMMON:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:COMMON:DEFAULT',
                    name: '设为默认',
                    i18nCode: 'menu.admin.setupDefault',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:COMMON:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:COMMON:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:PHOTO_ALBUM:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:ALBUM:COMMON:PHOTO',
                    name: '图片',
                    i18nCode: 'menu.admin.photo',
                    permissionCodes: [],
                    type: 'ITEM',
                    module: 'CORE',
                    child: [
                      {
                        code: 'A:BASE:ALBUM:COMMON:PHOTO:VIEW',
                        name: '查看',
                        i18nCode: 'menu.admin.view',
                        permissionCodes: ['IC_API_CORE:PHOTO_ALBUM_LINE:QUERY', 'IC_API_CORE:PHOTO_ALBUM_LINE:UPDATE'],
                        type: 'VIEW',
                        module: 'CORE',
                      },
                      {
                        code: 'A:BASE:ALBUM:COMMON:PHOTO:UPLOAD',
                        name: '上传',
                        i18nCode: 'menu.admin.upload',
                        permissionCodes: ['IC_API_FILE:IMAGE:SAVE', 'IC_API_CORE:PHOTO_ALBUM_LINE:CREATE'],
                        type: 'ITEM',
                        module: 'CORE',
                      },
                      {
                        code: 'A:BASE:ALBUM:COMMON:PHOTO:DELETE',
                        name: '删除',
                        i18nCode: 'menu.admin.delete',
                        permissionCodes: ['IC_API_CORE:PHOTO_ALBUM_LINE:DELETE'],
                        type: 'ITEM',
                        module: 'CORE',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            code: 'A:BASE:ISSUES',
            name: '质检问题',
            i18nCode: 'menu.admin.qualityCheckIssue',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/fqc-issues',
              component: QualityCheckIssues,
            },
            child: [
              {
                code: 'A:BASE:ISSUES:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_FACTORY:FQC_PROBLEM_GROUP:QUERY',
                  'IC_API_FACTORY:FQC_PROBLEM:QUERY',
                  'IC_API_BFACTORY:FQC_PROBLEM_GROUP:QUERY',
                  'IC_API_BFACTORY:FQC_PROBLEM:QUERY',
                ],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:GROUPCREATE',
                name: '创建分组',
                i18nCode: 'menu.admin.createGroup',
                permissionCodes: [
                  'IC_API_FACTORY:FQC_PROBLEM_GROUP:CREATE',
                  'IC_API_BFACTORY:FQC_PROBLEM_GROUP:CREATE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:GROUPEDIT',
                name: '编辑分组',
                i18nCode: 'menu.admin.editGroup',
                permissionCodes: [
                  'IC_API_FACTORY:FQC_PROBLEM_GROUP:UPDATE',
                  'IC_API_BFACTORY:FQC_PROBLEM_GROUP:UPDATE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:GROUPDELETE',
                name: '删除分组',
                i18nCode: 'menu.admin.deleteGroup',
                permissionCodes: [
                  'IC_API_FACTORY:FQC_PROBLEM_GROUP:UPDATE',
                  'IC_API_BFACTORY:FQC_PROBLEM_GROUP:UPDATE',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:ISSUESCREATE',
                name: '创建问题',
                i18nCode: 'menu.admin.createIssue',
                permissionCodes: ['IC_API_FACTORY:FQC_PROBLEM:CREATE', 'IC_API_BFACTORY:FQC_PROBLEM:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:ISSUESEDIT',
                name: '编辑问题',
                i18nCode: 'menu.admin.editIssue',
                permissionCodes: ['IC_API_FACTORY:FQC_PROBLEM:UPDATE', 'IC_API_BFACTORY:FQC_PROBLEM:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:ISSUESDELETE',
                name: '删除问题',
                i18nCode: 'menu.admin.deleteIssue',
                permissionCodes: ['IC_API_FACTORY:FQC_PROBLEM:UPDATE', 'IC_API_BFACTORY:FQC_PROBLEM:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:BASE:ISSUES:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_FACTORY:FQC_PROBLEM:CREATE', 'IC_API_BFACTORY:FQC_PROBLEM:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:BASE:OTHER',
            name: '其他',
            i18nCode: 'menu.admin.other',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/other',
              component: Other,
            },
            child: [
              {
                code: 'A:BASE:OTHER:BOX_SPEC',
                name: '箱规格',
                i18nCode: 'menu.admin.boxSpecification',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/box-spec',
                  // component: BoxSpec,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:BOX_SPEC:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:BOX_SPEC:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BOX_SPEC:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:BOX_SPEC:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BOX_SPEC:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:BOX_SPEC:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BOX_SPEC:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:BOX_SPEC:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:ORDER_TYPE',
                name: '单据类型',
                i18nCode: 'menu.admin.orderType',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/order-type',
                  // component: OrderType,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:ORDER_TYPE:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:ORDER_TYPE:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:ORDER_TYPE:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:ORDER_TYPE:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:ORDER_TYPE:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:ORDER_TYPE:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:ORDER_TYPE:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:ORDER_TYPE:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:EXT_FIELD',
                name: '扩展字段',
                i18nCode: 'menu.admin.extendedField',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/ext-field',
                  // component: ExtField,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:EXT_FIELD:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:EXT_FIELD:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:EXT_FIELD:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:EXT_FIELD:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:EXT_FIELD:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:EXT_FIELD:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:EXT_FIELD:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:EXT_FIELD:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:BUSINESS_MODEL',
                name: '经营模式',
                i18nCode: 'menu.admin.businessModel',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/business-model',
                  // component: BusinessModel,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:BUSINESS_MODEL:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:BUSINESS_MODEL:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BUSINESS_MODEL:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:BUSINESS_MODEL:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BUSINESS_MODEL:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:BUSINESS_MODEL:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:BUSINESS_MODEL:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:BUSINESS_MODEL:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:TRANSFER-GROUP',
                name: '调拨组',
                i18nCode: 'menu.admin.dispatchGroup',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/transfer-group',
                  // component: TransferGroup,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:TRANSFER-GROUP:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:TRANSFER_GROUP:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:TRANSFER-GROUP:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_CORE:TRANSFER_GROUP:CREATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:TRANSFER-GROUP:UPDATE',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:TRANSFER_GROUP:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:TRANSFER-GROUP:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:TRANSFER_GROUP:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:B:DICT',
                name: '数据字典',
                i18nCode: 'menu.admin.dict',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/dict',
                  // component: Dict,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:B:DICT:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:DICT:QUERY', 'IC_API_CORE:DICT_DATA:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                    // route: {
                    //   path: '/app/warehouse/:id',
                    // //   component: WarehouseDetail,
                    // },
                  },
                  {
                    code: 'A:BASE:OTHER:B:DICT:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_CORE:DICT:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:B:DICT:DET',
                    name: '明细维护',
                    i18nCode: 'menu.admin.maintainDict',
                    permissionCodes: [
                      'IC_API_CORE:DICT_DATA:QUERY',
                      'IC_API_CORE:DICT_DATA:CREATE',
                      'IC_API_CORE:DICT_DATA:UPDATE',
                      'IC_API_CORE:DICT_DATA:DELETE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:AREA',
                name: '地区档案',
                i18nCode: 'menu.admin.area',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/area',
                  // component: Area,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:AREA:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:AREA:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                ],
              },
              {
                code: 'A:BASE:OTHER:O:VU',
                name: '应用版本升级',
                i18nCode: 'menu.admin.applicationVersionUpdate',
                permissionCodes: [],
                type: 'MENU',
                module: 'CORE',
                route: {
                  path: '/app/version-update',
                  // component: VersionUpdate,
                },
                child: [
                  {
                    code: 'A:BASE:OTHER:O:VU:VIEW',
                    name: '查看',
                    i18nCode: 'menu.admin.view',
                    permissionCodes: ['IC_API_CORE:DICT_DATA:QUERY', 'IC_API_CORE:VERSION_UPDATE:QUERY'],
                    type: 'VIEW',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:O:VU:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: [
                      'IC_API_CORE:DICT_DATA:QUERY',
                      'IC_API_CORE:VERSION_UPDATE:CREATE',
                      'IC_API_CORE:FILE:SAVE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:O:VU:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: [
                      'IC_API_CORE:VERSION_UPDATE:UPDATE',
                      'IC_API_CORE:DICT_DATA:QUERY',
                      'IC_API_CORE:FILE:SAVE',
                    ],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:O:VU:REL',
                    name: '发布',
                    i18nCode: 'menu.admin.publish',
                    permissionCodes: ['IC_API_CORE:VERSION_UPDATE:UPDATE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:O:VU:DOW',
                    name: '下载',
                    i18nCode: 'menu.admin.download',
                    permissionCodes: ['IC_API_CORE:FILE:EXPORT'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                  {
                    code: 'A:BASE:OTHER:O:VU:DEL',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_CORE:VERSION_UPDATE:DELETE'],
                    type: 'ITEM',
                    module: 'CORE',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        code: 'A:LABEL',
        name: '标签管理',
        i18nCode: 'menu.admin.tagManagement',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        icon: AlbumIcon,
        child: [
          {
            code: 'A:LABEL:ST',
            name: '标签',
            i18nCode: 'menu.admin.tag',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/rfid-tag',
              component: SkuTag,
            },
            child: [
              {
                code: 'A:LABEL:ST:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:ST:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_CORE:R_F_I_D:CREATE'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:ST:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:LABEL:STL',
            name: '标签日志',
            i18nCode: 'menu.admin.tagLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/rfid-tag-log',
              component: SkuTagLog,
            },
            child: [
              {
                code: 'A:LABEL:STL:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY', 'IC_API_CORE:RFID_LOG:QUERY', 'IC_API_CORE:EMP:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              // {
              //   code: 'A:LABEL:STL:IMPORT',
              //   name: '导入',i18nCode: 'menu.admin.import',
              //
              //   permissionCodes: ['IC_API_CORE:R_F_I_D:CREATE'],
              //   type: 'VIEW',
              //   module: 'CORE',
              // },
              {
                code: 'A:LABEL:STL:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY', 'IC_API_CORE:RFID_LOG:QUERY', 'IC_API_CORE:EMP:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:LABEL:SFR',
            name: '标签过滤规则',
            i18nCode: 'menu.admin.filterTagRule',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/rfid-filter-rules',
              component: RfidFilterRules,
            },
            child: [
              {
                code: 'A:LABEL:SFR:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_TYPE:QUERY', 'IC_API_CORE:RFID_FILTER_CONDITION:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:CREATE_TYPE',
                name: '创建类型',
                i18nCode: 'menu.admin.createType',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_TYPE:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:EDIT_TYPE',
                name: '编辑类型',
                i18nCode: 'menu.admin.editType',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_TYPE:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:DELETE_TYPE',
                name: '删除类型',
                i18nCode: 'menu.admin.deleteType',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_TYPE:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:CREATE_CONDITION',
                name: '创建规则',
                i18nCode: 'menu.admin.createRule',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_CONDITION:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:EDIT_CONDITION',
                name: '编辑规则',
                i18nCode: 'menu.admin.editRule',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_CONDITION:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:SFR:DELETE_CONDITION',
                name: '删除规则',
                i18nCode: 'menu.admin.deleteRule',
                permissionCodes: ['IC_API_CORE:RFID_FILTER_CONDITION:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:LABEL:RS',
            name: '标签溯源',
            i18nCode: 'menu.admin.tagTraceability',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/rfidSource',
              component: RfidSource,
            },
            child: [
              {
                code: 'A:LABEL:RS:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY', 'IC_API_CORE:SKU:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:LABEL:UNIQUE',
            name: '唯一码',
            i18nCode: 'menu.admin.uniqueCode',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/unique',
              component: UniqueCode,
            },
            child: [
              {
                code: 'A:LABEL:UNIQUE:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:UNIQUE_CODE:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:LABEL:UNIQUE:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_CORE:UNIQUE_CODE:CREATE'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
        ],
      },
      {
        code: 'A:TAG',
        name: '标签打印',
        i18nCode: 'menu.admin.labelPrint',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        icon: PrinterIcon,
        child: [
          {
            code: 'A:TAG:PT',
            name: '打印任务',
            i18nCode: 'menu.admin.printTask',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/print-task',
              component: PrintTask,
            },
            child: [
              {
                code: 'A:TAG:PT:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:PRINT_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
                route: {
                  path: '/app/print-task/detail/:id',
                  component: PrintTaskDetail,
                },
              },
              {
                code: 'A:TAG:PT:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_CORE:PRINT_ORDER:CREATE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TAG:PL',
            name: '打印日志',
            i18nCode: 'menu.admin.printLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/print-log',
              component: PrintLog,
            },
            child: [
              {
                code: 'A:TAG:PL:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
        ],
      },
      {
        code: 'A:F',
        name: '供应链',
        i18nCode: 'menu.admin.factory',
        permissionCodes: [],
        type: 'MENU',
        module: 'FACTORY',
        icon: FactoryIcon,
        child: [
          {
            code: 'A:F:FM',
            name: '生产订单',
            i18nCode: 'menu.admin.fm',
            permissionCodes: [],
            type: 'MENU',
            module: 'FACTORY',
            route: {
              path: '/app/fm',
              component: FmOrder,
            },
            child: [
              {
                code: 'A:F:FM:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_FACTORY:FM_ORDER:QUERY',
                  'IC_API_BFACTORY:FM_ORDER:QUERY',
                  'IC_API_BFACTORY:FAD_ORDER:QUERY',
                  'IC_API_FACTORY:FAD_ORDER:QUERY',
                  'IC_API_FACTORY:FQC_ORDER:QUERY',
                  'IC_API_BFACTORY:FQC_ORDER:QUERY',
                  'IC_API_CORE:SIZE:QUERY',
                ],
                type: 'VIEW',
                module: 'FACTORY',
                route: {
                  path: '/app/fm/detail/:id',
                  component: FmOrderDetail,
                },
              },
              {
                code: 'A:F:FM:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_FACTORY:FM_ORDER:CONFIRM', 'IC_API_BFACTORY:FM_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_FACTORY:FM_ORDER:CREATE', 'IC_API_BFACTORY:FM_ORDER:CREATE'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_FACTORY:FM_ORDER:CREATE', 'IC_API_BFACTORY:FM_ORDER:CREATE'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_FACTORY:FM_ORDER:UPDATE', 'IC_API_BFACTORY:FM_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_FACTORY:FM_ORDER:CANCEL', 'IC_API_BFACTORY:FM_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_BFACTORY:FM_ORDER:CONFIRM', 'IC_API_FACTORY:FM_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FM:BATCH_CANCEL',
                name: '批量取消',
                i18nCode: 'menu.admin.batchCancel',
                permissionCodes: ['IC_API_BFACTORY:FM_ORDER:CANCEL', 'IC_API_FACTORY:FM_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'FACTORY',
              },
            ],
          },
          {
            code: 'A:F:FQC',
            name: '质检单',
            i18nCode: 'menu.admin.factoryQualityCheckOrder',
            permissionCodes: [],
            type: 'MENU',
            module: 'FACTORY',
            route: {
              path: '/app/fqc',
              component: AdminFqcOrder,
            },
            child: [
              {
                code: 'A:F:FQC:QCVIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FACTORY:FQC_ORDER:QUERY', 'IC_API_BFACTORY:FQC_ORDER:QUERY'],
                type: 'VIEW',
                module: 'FACTORY',
                route: {
                  path: '/app/fqc/detail/:id',
                  component: AdminFqcOrderDetail,
                },
              },
              {
                code: 'A:F:FQC:QCCONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_FACTORY:FQC_ORDER:CONFIRM', 'IC_API_BFACTORY:FQC_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FQC:QCCREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_FACTORY:FQC_ORDER:CREATE', 'IC_API_BFACTORY:FQC_ORDER:CREATE'],
                type: 'ITEM',
                module: 'FACTORY',
              },
            ],
          },
          {
            code: 'A:F:FAD',
            name: '预约送货单',
            i18nCode: 'menu.admin.scheduleDeliveryOrder',
            permissionCodes: [],
            type: 'MENU',
            module: 'FACTORY',
            route: {
              path: '/app/fad',
              component: FadOrder,
            },
            child: [
              {
                code: 'A:F:FAD:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FACTORY:FAD_ORDER:QUERY', 'IC_API_BFACTORY:FAD_ORDER:QUERY'],
                type: 'VIEW',
                module: 'FACTORY',
                route: {
                  path: '/app/fad/detail/:id',
                  component: FadOrderDetail,
                },
              },
              {
                code: 'A:F:FAD:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_FACTORY:FAD_ORDER:CONFIRM', 'IC_API_BFACTORY:FAD_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'FACTORY',
              },
              {
                code: 'A:F:FAD:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_FACTORY:FAD_ORDER:CONFIRM', 'IC_API_BFACTORY:FAD_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'FACTORY',
              },
            ],
          },
          {
            code: 'A:F:IB',
            name: '入库',
            i18nCode: 'menu.admin.inbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'FACTORY',
            route: {
              path: '/app/fib',
              component: FibOrder,
            },
            child: [
              {
                code: 'A:F:IB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FACTORY:FIB_ORDER:QUERY', 'IC_API_BFACTORY:FIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'FACTORY',
                route: {
                  path: '/app/fib/detail/:id',
                  component: FibOrderDetail,
                },
              },
            ],
          },
          {
            code: 'A:F:OB',
            name: '出库',
            i18nCode: 'menu.admin.outbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'FACTORY',
            route: {
              path: '/app/fob',
              component: FobOrder,
            },
            child: [
              {
                code: 'A:F:OB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FACTORY:FOB_ORDER:QUERY', 'IC_API_BFACTORY:FOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'FACTORY',
                route: {
                  path: '/app/fob/detail/:id',
                  component: FobOrderDetail,
                },
              },
              {
                code: 'A:F:OB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_FACTORY:FOB_ORDER:UPDATE', 'IC_API_BFACTORY:FOB_ORDER:UPDATE'],
                type: 'VIEW',
                module: 'FACTORY',
              },
              {
                code: 'A:F:OB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_FACTORY:FOB_ORDER:CONFIRM', 'IC_API_BFACTORY:FOB_ORDER:CONFIRM'],
                type: 'VIEW',
                module: 'FACTORY',
              },
            ],
          },
          {
            code: 'A:F:REPORT_TMP',
            name: '报表模板',
            i18nCode: 'menu.admin.reportTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/report-template/:type',
              path: '/app/report-template/factory',
              component: ReportTemplate,
            },
            child: [
              {
                code: 'A:F:REPORT_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:F:REPORT_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
          {
            code: 'A:F:LP_TMP',
            name: '标签打印模板',
            i18nCode: 'menu.admin.labelPrintTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/label-printing-template/:type',
              path: '/app/label-printing-template/factory',
              component: LabelTemplate,
            },
            child: [
              {
                code: 'A:F:LP_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:F:LP_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
        ],
      },
      {
        code: 'A:W',
        name: '仓库',
        i18nCode: 'menu.admin.warehouse',
        permissionCodes: [],
        type: 'MENU',
        module: 'WAREHOUSE',
        icon: WarehouseIcon,
        child: [
          {
            code: 'A:W:TSO',
            name: '盘点',
            i18nCode: 'menu.admin.wts',
            permissionCodes: [],
            type: 'MENU',
            module: 'WAREHOUSE',
            route: {
              path: '/app/wts',
              component: WtsOrder,
            },
            child: [
              {
                code: 'A:W:TSO:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:QUERY', 'IC_API_BWAREHOUSE:W_TS_ORDER:QUERY'],
                type: 'VIEW',
                module: 'WAREHOUSE',
                route: {
                  path: '/app/wts/detail/:id',
                  component: WtsOrderDetail,
                },
              },
              {
                code: 'A:W:TSO:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: [
                  'IC_API_WAREHOUSE:W_TS_ORDER:CREATE',
                  'IC_API_BWAREHOUSE:W_TS_ORDER:CREATE',
                  'IC_API_CORE:BRAND:QUERY',
                  'IC_API_CORE:GENDER:QUERY',
                  'IC_API_CORE:PRI_CATEGORY:QUERY',
                  'IC_API_CORE:SUB_CATEGORY:QUERY',
                  'IC_API_CORE:YEAR:QUERY',
                  'IC_API_CORE:PROD:QUERY',
                ],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:BATCH_CREATE',
                name: '批量创建',
                i18nCode: 'menu.admin.batchCreate',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:CREATE', 'IC_API_BWAREHOUSE:W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:START',
                name: '开始盘点',
                i18nCode: 'menu.admin.startInventory',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:UPDATE', 'IC_API_BWAREHOUSE:W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:IMPORT_SNAP',
                name: '导入账存',
                i18nCode: 'menu.admin.importSnap',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:CREATE', 'IC_API_BWAREHOUSE:W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:SET_RANGE',
                name: '设置盘点范围',
                i18nCode: 'menu.admin.setRange',
                permissionCodes: [
                  'IC_API_WAREHOUSE:W_TS_ORDER:UPDATE',
                  'IC_API_WAREHOUSE:W_TS_ORDER:CREATE',
                  'IC_API_BWAREHOUSE:W_TS_ORDER:UPDATE',
                  'IC_API_BWAREHOUSE:W_TS_ORDER:CREATE',
                  'IC_API_CORE:BRAND:QUERY',
                  'IC_API_CORE:GENDER:QUERY',
                  'IC_API_CORE:PRI_CATEGORY:QUERY',
                  'IC_API_CORE:SUB_CATEGORY:QUERY',
                  'IC_API_CORE:YEAR:QUERY',
                  'IC_API_CORE:PROD:QUERY',
                ],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:DELETE', 'IC_API_BWAREHOUSE:W_TS_ORDER:DELETE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:CONFIRM', 'IC_API_BWAREHOUSE:W_TS_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:CANCEL', 'IC_API_BWAREHOUSE:W_TS_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:EXPORT_LINE',
                name: '导出明细',
                i18nCode: 'menu.admin.exportDetail',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:EXPORT', 'IC_API_BWAREHOUSE:W_TS_ORDER:EXPORT'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:DOWN_SNAP',
                name: '下载账存',
                i18nCode: 'menu.admin.downloadSnap',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:UPDATE', 'IC_API_BWAREHOUSE:W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:TSO:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_WAREHOUSE:W_TS_ORDER:UPDATE', 'IC_API_BWAREHOUSE:W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
            ],
          },
          {
            code: 'A:W:IB',
            name: '入库',
            i18nCode: 'menu.admin.inbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'WAREHOUSE',
            route: {
              path: '/app/wib',
              component: WibOrder,
            },
            child: [
              {
                code: 'A:W:IB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:QUERY', 'IC_API_BWAREHOUSE:WIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'WAREHOUSE',
                route: {
                  path: '/app/wib/:id',
                  component: WibOrderDetail,
                },
              },
              {
                code: 'A:W:IB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: [
                  'IC_API_WAREHOUSE:WIB_ORDER:CREATE',
                  'IC_API_BWAREHOUSE:WIB_ORDER:CREATE',
                  'IC_API_BFACTORY:FM_ORDER:QUERY',
                ],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:CONFIRM', 'IC_API_BWAREHOUSE:WIB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:CANCEL', 'IC_API_BWAREHOUSE:WIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:CANCEL', 'IC_API_BWAREHOUSE:WIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:UPDATE', 'IC_API_BWAREHOUSE:WIB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:FAST_RECEIPT',
                name: '快速收货',
                i18nCode: 'menu.admin.quickReceipt',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:SAVE', 'IC_API_BWAREHOUSE:WIB_ORDER:SAVE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:CONFIRM', 'IC_API_BWAREHOUSE:WIB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:IB:BATCH_CANCEL',
                name: '批量取消',
                i18nCode: 'menu.admin.batchCancel',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:CANCEL', 'IC_API_BWAREHOUSE:WIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
            ],
          },
          {
            code: 'A:W:OB',
            name: '出库',
            i18nCode: 'menu.admin.outbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'WAREHOUSE',
            route: {
              path: '/app/wob',
              component: WobOrder,
            },
            child: [
              {
                code: 'A:W:OB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:QUERY', 'IC_API_BWAREHOUSE:WOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'WAREHOUSE',
                route: {
                  path: '/app/wob/:id',
                  component: WobOrderDetail,
                },
              },
              {
                code: 'A:W:OB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: [
                  'IC_API_WAREHOUSE:WOB_ORDER:CREATE',
                  'IC_API_BWAREHOUSE:WOB_ORDER:CREATE',
                  'IC_API_CORE:TRANSFER_GROUP:QUERY',
                ],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:CREATE', 'IC_API_BWAREHOUSE:WOB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:CONFIRM', 'IC_API_BWAREHOUSE:WOB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:DELETE', 'IC_API_BWAREHOUSE:WOB_ORDER:DELETE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:CANCEL', 'IC_API_BWAREHOUSE:WOB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:UPDATE', 'IC_API_BWAREHOUSE:WIB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:CONFIRM', 'IC_API_BWAREHOUSE:WOB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:OB:BATCH_CANCEL',
                name: '批量取消',
                i18nCode: 'menu.admin.batchCancel',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:CANCEL', 'IC_API_BWAREHOUSE:WOB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
            ],
          },
          {
            code: 'A:W:CB',
            name: '集装箱单',
            i18nCode: 'menu.admin.containerList',
            permissionCodes: [],
            type: 'MENU',
            module: 'WAREHOUSE',
            route: {
              path: '/app/wcb',
              component: WcbOrder,
            },
            child: [
              {
                code: 'A:W:CB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_WAREHOUSE:CONTAINERS:QUERY', 'IC_API_BWAREHOUSE:CONTAINERS:QUERY'],
                type: 'VIEW',
                module: 'WAREHOUSE',
                route: {
                  path: '/app/wcb/:id',
                  component: WcbOrderDetail,
                },
              },
              {
                code: 'A:W:CB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_BWAREHOUSE:CONTAINERS:CONFIRM', 'IC_API_WAREHOUSE:CONTAINERS:CONFIRM'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
              {
                code: 'A:W:CB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_WAREHOUSE:CONTAINERS:UPDATE', 'IC_API_BWAREHOUSE:CONTAINERS:UPDATE'],
                type: 'ITEM',
                module: 'WAREHOUSE',
              },
            ],
          },
          {
            code: 'A:W:REPORT_TMP',
            name: '报表模板',
            i18nCode: 'menu.admin.reportTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/report-template/:type',
              path: '/app/report-template/warehouse',
              component: ReportTemplate,
            },
            child: [
              {
                code: 'A:W:REPORT_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:W:REPORT_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
          {
            code: 'A:W:LP_TMP',
            name: '标签打印模板',
            i18nCode: 'menu.admin.labelPrintTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/label-printing-template/:type',
              path: '/app/label-printing-template/warehouse',
              component: LabelTemplate,
            },
            child: [
              {
                code: 'A:W:LP_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:W:LP_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
          {
            code: 'A:W:WRITE_TAG_LOG',
            name: '写码记录',
            i18nCode: 'menu.admin.writeTagLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            mode: 'WRITE',
            route: {
              path: '/app/write-tag-log',
              component: WriteTagLog,
            },
            child: [
              {
                code: 'A:W:WRITE_TAG_LOG:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
            ],
          },
        ],
      },
      {
        code: 'A:S',
        name: '门店',
        i18nCode: 'menu.admin.shop',
        permissionCodes: [],
        type: 'MENU',
        module: 'SHOP',
        icon: StoreIcon,
        child: [
          {
            code: 'A:S:TSO',
            name: '盘点',
            i18nCode: 'menu.admin.wts',
            permissionCodes: [],
            type: 'MENU',
            module: 'SHOP',
            route: {
              path: '/app/sts',
              component: AdminStsOrder,
            },
            child: [
              {
                code: 'A:S:TSO:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:QUERY', 'IC_API_BSHOP:TS_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SHOP',
                route: {
                  path: '/app/sts/detail/:id',
                  component: AdminStsOrderDetail,
                },
              },
              {
                code: 'A:S:TSO:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:CREATE', 'IC_API_BSHOP:TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:BATCH_CREATE',
                name: '批量创建',
                i18nCode: 'menu.admin.batchCreate',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:CREATE', 'IC_API_BSHOP:TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:START',
                name: '开始盘点',
                i18nCode: 'menu.admin.startInventory',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:UPDATE', 'IC_API_BSHOP:TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:IMPORT_SNAP',
                name: '导入账存',
                i18nCode: 'menu.admin.importSnap',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:CREATE', 'IC_API_BSHOP:TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:SET_RANGE',
                name: '设置盘点范围',
                i18nCode: 'menu.admin.setRange',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:UPDATE', 'IC_API_BSHOP:TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:DELETE', 'IC_API_BSHOP:TS_ORDER:DELETE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:CONFIRM', 'IC_API_BSHOP:TS_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:CANCEL', 'IC_API_BSHOP:TS_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:EXPORT_LINE',
                name: '导出明细',
                i18nCode: 'menu.admin.exportDetail',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:EXPORT', 'IC_API_BSHOP:TS_ORDER:EXPORT'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:DOWN_SNAP',
                name: '下载账存',
                i18nCode: 'menu.admin.downloadSnap',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:UPDATE', 'IC_API_BSHOP:TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:TSO:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SHOP:TS_ORDER:UPDATE', 'IC_API_BSHOP:TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
            ],
          },
          {
            code: 'A:S:IB',
            name: '入库',
            i18nCode: 'menu.admin.inbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'SHOP',
            route: {
              path: '/app/sib',
              component: SibOrder,
            },
            child: [
              {
                code: 'A:S:IB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:QUERY', 'IC_API_BSHOP:SIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SHOP',
                route: {
                  path: '/app/sib/:id',
                  component: SibOrderDetail,
                },
              },
              {
                code: 'A:S:IB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CREATE', 'IC_API_BSHOP:SIB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CONFIRM', 'IC_API_BSHOP:SIB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CANCEL', 'IC_API_BSHOP:SIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CANCEL', 'IC_API_BSHOP:SIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:UPDATE', 'IC_API_BSHOP:SIB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:FAST_RECEIPT',
                name: '快速收货',
                i18nCode: 'menu.admin.quickReceipt',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:SAVE', 'IC_API_BSHOP:SIB_ORDER:SAVE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CONFIRM', 'IC_API_BSHOP:SIB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:IB:BATCH_CANCEL',
                name: '批量取消',
                i18nCode: 'menu.admin.batchCancel',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:CANCEL', 'IC_API_BSHOP:SIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
            ],
          },
          {
            code: 'A:S:OB',
            name: '出库',
            i18nCode: 'menu.admin.outbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'SHOP',
            route: {
              path: '/app/sob',
              component: SobOrder,
            },
            child: [
              {
                code: 'A:S:OB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:QUERY', 'IC_API_BSHOP:SOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SHOP',
                route: {
                  path: '/app/sob/:id',
                  component: SobOrderDetail,
                },
              },
              {
                code: 'A:S:OB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: [
                  'IC_API_SHOP:SOB_ORDER:CREATE',
                  'IC_API_BSHOP:SOB_ORDER:CREATE',
                  'IC_API_CORE:TRANSFER_GROUP:QUERY',
                ],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:CREATE', 'IC_API_BSHOP:SOB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:CONFIRM', 'IC_API_BSHOP:SOB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:DELETE', 'IC_API_BSHOP:SOB_ORDER:DELETE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:CANCEL', 'IC_API_BSHOP:SOB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:UPDATE', 'IC_API_BSHOP:SOB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:BATCH_CONFIRM',
                name: '批量审核',
                i18nCode: 'menu.admin.batchConfirm',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:CONFIRM', 'IC_API_BSHOP:SOB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:S:OB:BATCH_CANCEL',
                name: '批量取消',
                i18nCode: 'menu.admin.batchCancel',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:CANCEL', 'IC_API_BSHOP:SOB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SHOP',
              },
            ],
          },
          {
            code: 'A:S:SR',
            name: '零售',
            i18nCode: 'menu.admin.sr',
            permissionCodes: [],
            type: 'MENU',
            module: 'SHOP',
            route: {
              path: '/app/sr',
              component: SrOrder,
            },
            child: [
              {
                code: 'A:S:SR:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_SHOP:WEB_SR_ORDER:QUERY',
                  'IC_API_BSHOP:WEB_SR_ORDER:QUERY',
                  'IC_API_SHOP:WEB_SR_ORDER_RFID:QUERY',
                  'IC_API_BSHOP:WEB_SR_ORDER_RFID:QUERY',
                  'IC_API_SHOP:WEB_SR_ORDER_LOG:QUERY',
                  'IC_API_BSHOP:WEB_SR_ORDER_LOG:QUERY',
                ],
                type: 'VIEW',
                module: 'SHOP',
                route: {
                  path: '/app/sr/:id',
                  component: SrOrderDetail,
                },
              },
              {
                code: 'A:S:SR:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_BSHOP:WEB_SR_ORDER:CREATE', 'IC_API_SHOP:WEB_SR_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
            ],
          },
          {
            code: 'A:S:REPORT_TMP',
            name: '报表模板',
            i18nCode: 'menu.admin.reportTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/report-template/:type',
              path: '/app/report-template/shop',
              component: ReportTemplate,
            },
            child: [
              {
                code: 'A:S:REPORT_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:S:REPORT_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
          {
            code: 'A:S:LP_TMP',
            name: '标签打印模板',
            i18nCode: 'menu.admin.labelPrintTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/label-printing-template/:type',
              path: '/app/label-printing-template/shop',
              component: LabelTemplate,
            },
            child: [
              {
                code: 'A:S:LP_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:S:LP_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
        ],
      },
      {
        code: 'A:P',
        name: '样品管理',
        i18nCode: 'menu.admin.sampleClothesManage',
        permissionCodes: [],
        type: 'MENU',
        module: 'SAMPLE',
        icon: AlbumIcon,
        child: [
          {
            code: 'A:P:TSO',
            name: '盘点',
            i18nCode: 'menu.admin.wts',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/spts',
              component: SptsOrder,
            },
            child: [
              {
                code: 'A:P:TSO:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
                route: {
                  path: '/app/spts/:id',
                  component: SptsOrderDetail,
                },
              },
              {
                code: 'A:P:TSO:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:BATCH_CREATE',
                name: '批量创建',
                i18nCode: 'menu.admin.batchCreate',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:REALTIME',
                name: '获取即时库存',
                i18nCode: 'menu.admin.getRealTimeInventory',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:IMPORT_SNAP',
                name: '导入账存',
                i18nCode: 'menu.admin.importSnap',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:SET_RANGE',
                name: '设置盘点范围',
                i18nCode: 'menu.admin.setRange',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SHOP',
              },
              {
                code: 'A:P:TSO:START',
                name: '开始盘点',
                i18nCode: 'menu.admin.startInventory',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:DELETE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:EXPORT_LINE',
                name: '导出明细',
                i18nCode: 'menu.admin.exportDetail',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:EXPORT'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:TSO:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_W_TS_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:P:IB',
            name: '入库',
            i18nCode: 'menu.admin.inbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/spib',
              component: SpibOrder,
            },
            child: [
              {
                code: 'A:P:IB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
                route: {
                  path: '/app/spib/:id',
                  component: SpibOrderDetail,
                },
              },
              {
                code: 'A:P:IB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:IB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:IB:RESET',
                name: '重置',
                i18nCode: 'menu.admin.reset',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:DELETE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:IB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:IB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WIB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:P:OB',
            name: '出库',
            i18nCode: 'menu.admin.outbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/spob',
              component: SpobOrder,
            },
            child: [
              {
                code: 'A:P:OB:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
                route: {
                  path: '/app/spob/:id',
                  component: SpobOrderDetail,
                },
              },
              {
                code: 'A:P:OB:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:OB:IMPORT',
                name: '导入',
                i18nCode: 'menu.admin.import',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:CREATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:OB:CONFIRM',
                name: '审核',
                i18nCode: 'menu.admin.confirm',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:CONFIRM'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:OB:CANCEL',
                name: '取消',
                i18nCode: 'menu.admin.cancel',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:CANCEL'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:OB:MODIFY_REMARK',
                name: '修改备注',
                i18nCode: 'menu.admin.modifyRemark',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_WOB_ORDER:UPDATE'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:P:BR',
            name: '借还记录',
            i18nCode: 'menu.admin.borrowRepay',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/borrow-repay',
              component: BorrowRepay,
            },
            child: [
              {
                code: 'A:P:BR:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_LR_RECORD:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:BR:DISPOSE',
                name: '处置',
                i18nCode: 'menu.admin.dispose',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_LR_RECORD:UPDATE', 'IC_API_CORE:DICT_DATA:QUERY'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
              {
                code: 'A:P:BR:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_LR_RECORD:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:P:SAMPLE_RS',
            name: '样品标签溯源',
            i18nCode: 'menu.admin.sampleClothesTraceability',
            permissionCodes: [],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/sample-rfidSource',
              component: SampleRfidSource,
            },
            child: [
              {
                code: 'A:P:SAMPLE_RS:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_RFID:QUERY', 'IC_API_SAMPLE:SAMPLE_SKU:QUERY'],
                type: 'VIEW',
                module: 'SAMPLE',
              },
            ],
          },
          {
            code: 'A:P:REPORT_TMP',
            name: '报表模板',
            i18nCode: 'menu.admin.reportTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/report-template/:type',
              path: '/app/report-template/sample',
              component: ReportTemplate,
            },
            child: [
              {
                code: 'A:P:REPORT_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:P:REPORT_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
          {
            code: 'A:P:LP_TMP',
            name: '标签打印模板',
            i18nCode: 'menu.admin.labelPrintTemplate',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              specialPath: '/app/label-printing-template/:type',
              path: '/app/label-printing-template/sample',
              component: LabelTemplate,
            },
            child: [
              {
                code: 'A:P:LP_TMP:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_REPORT:TEMPLATE:QUERY'],
                type: 'VIEW',
                module: 'REPORT',
              },
              {
                code: 'A:P:LP_TMP:MANAGEMENT',
                name: '模板管理',
                i18nCode: 'menu.admin.managementTemplate',
                permissionCodes: [
                  'IC_API_REPORT:TEMPLATE:CREATE',
                  'IC_API_REPORT:TEMPLATE:UPDATE',
                  'IC_API_REPORT:TEMPLATE:DELETE',
                  'IC_API_FILE:FILE:SAVE',
                ],
                type: 'ITEM',
                module: 'REPORT',
              },
            ],
          },
        ],
      },
      {
        code: 'A:DC',
        name: '数据采集',
        i18nCode: 'menu.admin.dataCollection',
        permissionCodes: [],
        type: 'MENU',
        module: 'FITTING',
        icon: AtomIcon,
        child: [
          {
            code: 'A:DC:FITTING_DASHBOARD',
            name: '试衣间看板',
            i18nCode: 'menu.admin.fittingDashboard',
            permissionCodes: [],
            type: 'MENU',
            module: 'FITTING',
            route: {
              path: '/app/fitting-dashboard',
              component: FittingDashboard,
            },
            child: [
              {
                code: 'A:DC:FITTING_DASHBOARD:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FITTING:FITTING_ROOM:QUERY'],
                type: 'VIEW',
                module: 'FITTING',
              },
            ],
          },
          {
            code: 'A:DC:FITTING_ANALYSIS',
            name: '试衣数据分析',
            i18nCode: 'menu.admin.fittingAnalysis',
            permissionCodes: [],
            type: 'MENU',
            module: 'FITTING',
            route: {
              path: '/app/fitting-analysis',
              component: FittingAnalysis,
            },
            child: [
              {
                code: 'A:DC:FITTING_ANALYSIS:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FITTING:FITTING_ROOM:QUERY'],
                type: 'VIEW',
                module: 'FITTING',
              },
            ],
          },
          {
            code: 'A:DC:FITTING_RECORD',
            name: '试衣原始数据',
            i18nCode: 'menu.admin.fittingRecord',
            permissionCodes: [],
            type: 'MENU',
            module: 'FITTING',
            route: {
              path: '/app/fitting-record',
              component: FittingRecord,
            },
            child: [
              {
                code: 'A:DC:FITTING_RECORD:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FITTING:FITTING_ROOM:QUERY'],
                type: 'VIEW',
                module: 'FITTING',
              },
              {
                code: 'A:DC:FITTING_RECORD:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_FITTING:FITTING_ROOM:QUERY'],
                type: 'ITEM',
                module: 'FITTING',
              },
            ],
          },
          {
            code: 'A:DC:FITTING_RECORD_NEW',
            name: '试衣原始数据_新',
            i18nCode: 'menu.admin.fittingRecordNew',
            permissionCodes: [],
            type: 'MENU',
            module: 'EXT',
            route: {
              path: '/app/fitting-record_new',
              component: FittingRecordNew,
            },
            child: [
              {
                code: 'A:DC:FITTING_RECORD_NEW:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_EXT:FITTING_ROOM_DATA:QUERY'],
                type: 'VIEW',
                module: 'EXT',
              },
              {
                code: 'A:DC:FITTING_RECORD_NEW:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_EXT:FITTING_ROOM_DATA:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
            ],
          },
          {
            code: 'A:DC:FITTING_ANT_GATHER',
            name: '防盗采集',
            i18nCode: 'menu.admin.antiTheftGather',
            permissionCodes: [],
            type: 'MENU',
            module: 'FITTING',
            route: {
              path: '/app/anti-theft-gather',
              component: AntiTheftGather,
            },
            child: [
              {
                code: 'A:DC:FITTING_ANT_GATHER:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: [
                  'IC_API_FITTING:WEB_ANTI_THEFT_GATHER_DATA:QUERY',
                  'IC_API_FITTING:WEB_ANTI_THEFT_GATHER_DATA_RFID:QUERY',
                ],
                type: 'VIEW',
                module: 'FITTING',
                route: {
                  path: '/app/anti-theft-gather/detail/:id',
                  component: AntiTheftGatherDetail,
                },
              },
              {
                code: 'A:DC:FITTING_ANT_GATHER:RESEND',
                name: '重新推送',
                i18nCode: 'menu.admin.resend',
                permissionCodes: ['IC_API_FITTING:APP_ANTI_THEFT_GATHER_DATA:UPDATE'],
                type: 'VIEW',
                module: 'FITTING',
              },
            ],
          },
          {
            code: 'A:DC:FITTING_ANT_ALARM',
            name: '防盗报警',
            i18nCode: 'menu.admin.antiTheftAlarm',
            permissionCodes: [],
            type: 'MENU',
            module: 'FITTING',
            route: {
              path: '/app/anti-theft-alarm',
              component: AntiTheftAlarm,
            },
            child: [
              {
                code: 'A:DC:FITTING_ANT_ALARM:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_FITTING:WEB_ANTI_THEFT_DATA:QUERY'],
                type: 'VIEW',
                module: 'FITTING',
              },
            ],
          },
        ],
      },
      {
        code: 'A:REPORT',
        name: '报表',
        i18nCode: 'menu.admin.report',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        icon: ReportIcon,
        isBottom: true,
        child: [
          {
            code: 'A:R:PSI',
            name: '进销存',
            i18nCode: 'menu.admin.psi',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/psi',
              component: PsiReport,
            },
            child: [
              {
                code: 'A:R:PSI:WAREHOUSE',
                name: '仓库',
                i18nCode: 'menu.admin.warehouse',
                permissionCodes: ['IC_API_REPORT:WAREHOUSE_DATA:QUERY', 'IC_API_REPORT:B_WAREHOUSE_DATA:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:PSI:SHOP',
                name: '门店',
                i18nCode: 'menu.admin.shop',
                permissionCodes: ['IC_API_REPORT:B_SHOP_DATA:QUERY', 'IC_API_REPORT:SHOP_DATA:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:PSI:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: [
                  'IC_API_REPORT:WAREHOUSE_DATA:QUERY',
                  'IC_API_REPORT:B_WAREHOUSE_DATA:QUERY',
                  'IC_API_REPORT:B_SHOP_DATA:QUERY',
                  'IC_API_REPORT:SHOP_DATA:QUERY',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:INBOUND_BOX',
            name: '入库箱',
            i18nCode: 'menu.admin.inboundBox',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/report-inbound-box',
              component: InboundBox,
            },
            child: [
              {
                code: 'A:R:INBOUND_BOX:WAREHOUSE',
                name: '仓库',
                i18nCode: 'menu.admin.warehouse',
                permissionCodes: ['IC_API_WAREHOUSE:WIB_ORDER:QUERY', 'IC_API_BWAREHOUSE:WIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:INBOUND_BOX:SHOP',
                name: '门店',
                i18nCode: 'menu.admin.shop',
                permissionCodes: ['IC_API_SHOP:SIB_ORDER:QUERY', 'IC_API_BSHOP:SIB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:INBOUND_BOX:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: [
                  'IC_API_WAREHOUSE:WIB_ORDER:QUERY',
                  'IC_API_BWAREHOUSE:WIB_ORDER:QUERY',
                  'IC_API_SHOP:SIB_ORDER:QUERY',
                  'IC_API_BSHOP:SIB_ORDER:QUERY',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:OUTBOUND_BOX',
            name: '出库箱',
            i18nCode: 'menu.admin.outboundBox',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/report-outbound-box',
              component: OutboundBox,
            },
            child: [
              {
                code: 'A:R:OUTBOUND_BOX:WAREHOUSE',
                name: '仓库',
                i18nCode: 'menu.admin.warehouse',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:QUERY', 'IC_API_BWAREHOUSE:WOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:OUTBOUND_BOX:SHOP',
                name: '门店',
                i18nCode: 'menu.admin.shop',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:QUERY', 'IC_API_BSHOP:SOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:OUTBOUND_BOX:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: [
                  'IC_API_WAREHOUSE:WOB_ORDER:QUERY',
                  'IC_API_BWAREHOUSE:WOB_ORDER:QUERY',
                  'IC_API_SHOP:SOB_ORDER:QUERY',
                  'IC_API_BSHOP:SOB_ORDER:QUERY',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:OUTBOUND',
            name: '出库',
            i18nCode: 'menu.admin.outbound',
            permissionCodes: [],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/report-outbound',
              component: Outbound,
            },
            child: [
              {
                code: 'A:R:OUTBOUND:WAREHOUSE',
                name: '仓库',
                i18nCode: 'menu.admin.warehouse',
                permissionCodes: ['IC_API_WAREHOUSE:WOB_ORDER:QUERY', 'IC_API_BWAREHOUSE:WOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:OUTBOUND:SHOP',
                name: '门店',
                i18nCode: 'menu.admin.shop',
                permissionCodes: ['IC_API_SHOP:SOB_ORDER:QUERY', 'IC_API_BSHOP:SOB_ORDER:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:R:OUTBOUND:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: [
                  'IC_API_WAREHOUSE:WOB_ORDER:QUERY',
                  'IC_API_BWAREHOUSE:WOB_ORDER:QUERY',
                  'IC_API_SHOP:SOB_ORDER:QUERY',
                  'IC_API_BSHOP:SOB_ORDER:QUERY',
                ],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:LOCATION',
            name: '货位库存',
            i18nCode: 'menu.admin.inventoryLocation',
            permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:QUERY', 'IC_API_CORE:PARTNER:QUERY'],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/location-inventory',
              component: Location,
            },
            child: [
              {
                code: 'A:R:LOCATION:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_CORE:STORAGE_LOCATION:QUERY'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:TAG_INVENTORY',
            name: '标签库存',
            i18nCode: 'menu.admin.tagInventory',
            permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
            type: 'MENU',
            module: 'REPORT',
            route: {
              path: '/app/tag-inventory',
              component: TagInventory,
            },
            child: [
              {
                code: 'A:R:TAG_INVENTORY:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_CORE:R_F_I_D:QUERY'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:R:SAMPLE_LOCATION',
            name: '样品货位库存',
            i18nCode: 'menu.admin.sampleLocation',
            permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:QUERY', 'IC_API_CORE:PARTNER:QUERY'],
            type: 'MENU',
            module: 'SAMPLE',
            route: {
              path: '/app/sample-storage-location',
              component: SampleLocation,
            },
            child: [
              {
                code: 'A:R:SAMPLE_LOCATION:EXPORT',
                name: '导出',
                i18nCode: 'menu.admin.export',
                permissionCodes: ['IC_API_SAMPLE:SAMPLE_STORAGE_LOCATION:QUERY'],
                type: 'ITEM',
                module: 'SAMPLE',
              },
            ],
          },
        ],
      },
      {
        code: 'A:DEVICE',
        name: '设备',
        i18nCode: 'menu.admin.device',
        permissionCodes: [],
        type: 'MENU',
        module: 'DEVICE',
        icon: RadioTowerIcon,
        child: [
          {
            code: 'A:DEVICE:DEVICE_DASHBOARD',
            name: '设备看板',
            i18nCode: 'menu.admin.deviceDashboard',
            permissionCodes: [],
            type: 'MENU',
            module: 'DEVICE',
            route: {
              path: '/app/device-dashboard',
              component: DeviceDashboard,
            },
            child: [
              {
                code: 'A:DEVICE:DEVICE_DASHBOARD:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_DEVICE:DEVICE:QUERY'],
                type: 'VIEW',
                module: 'DEVICE',
              },
            ],
          },
          {
            code: 'A:DEVICE:DEVICE_FILES',
            name: '设备档案',
            i18nCode: 'menu.admin.deviceFiles',
            permissionCodes: [],
            type: 'MENU',
            module: 'DEVICE',
            route: {
              path: '/app/device-files',
              component: DeviceFiles,
            },
            child: [
              {
                code: 'A:DEVICE:DEVICE_FILES:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_DEVICE:DEVICE:QUERY'],
                type: 'VIEW',
                module: 'DEVICE',
              },
              {
                code: 'A:DEVICE:DEVICE_FILES:CREATE',
                name: '创建',
                i18nCode: 'menu.admin.create',
                permissionCodes: ['IC_API_DEVICE:DEVICE:CREATE'],
                type: 'ITEM',
                module: 'DEVICE',
              },
              {
                code: 'A:DEVICE:DEVICE_FILES:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_DEVICE:DEVICE:UPDATE'],
                type: 'ITEM',
                module: 'DEVICE',
              },
              {
                code: 'A:DEVICE:DEVICE_FILES:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_DEVICE:DEVICE:DELETE'],
                type: 'ITEM',
                module: 'DEVICE',
              },
            ],
          },
          {
            code: 'A:DEVICE:DEVICE_MANAGEMENT',
            name: '设备管理',
            i18nCode: 'menu.admin.deviceManagement',
            permissionCodes: [],
            type: 'MENU',
            module: 'EXT',
            route: {
              path: '/app/device-management',
              component: DeviceManagement,
            },
            child: [
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_EXT:DEVICE:QUERY', 'IC_API_EXT:MQTT:QUERY'],
                type: 'VIEW',
                module: 'EXT',
                route: {
                  path: '/app/device-management/:id',
                  component: DeviceManagementDetail,
                },
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:DELETE',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_EXT:DEVICE:DELETE'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:TIME_SYNC',
                name: '时间同步',
                i18nCode: 'menu.admin.timeSync',
                permissionCodes: ['IC_API_EXT:MQTT:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:RESTART',
                name: '重启',
                i18nCode: 'menu.admin.restart',
                permissionCodes: ['IC_API_EXT:MQTT:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:UPGRADE',
                name: '升级',
                i18nCode: 'menu.admin.upgrade',
                permissionCodes: ['IC_API_EXT:MQTT:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:CONFIG_PUBLISH',
                name: '配置下发',
                i18nCode: 'menu.admin.configPublish',
                permissionCodes: ['IC_API_EXT:MQTT:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:OCU',
                name: '其他配置修改',
                i18nCode: 'menu.admin.otherConfigEdit',
                permissionCodes: ['IC_API_EXT:DEVICE:UPDATE'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:SCU',
                name: '系统配置修改',
                i18nCode: 'menu.admin.sysConfigEdit',
                permissionCodes: ['IC_API_EXT:DEVICE:UPDATE'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:DCU',
                name: '业务配置修改',
                i18nCode: 'menu.admin.bizConfigEdit',
                permissionCodes: ['IC_API_EXT:DEVICE:UPDATE'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:LOG',
                name: '查看日志',
                i18nCode: 'menu.admin.viewLog',
                permissionCodes: ['IC_API_EXT:MQTT_LOG:QUERY'],
                type: 'ITEM',
                module: 'EXT',
              },
              {
                code: 'A:DEVICE:DEVICE_MANAGEMENT:AN',
                name: '天线',
                i18nCode: 'menu.admin.antenna',
                permissionCodes: ['IC_API_EXT:DEVICE_ANTENNA_CONFIG:QUERY'],
                type: 'ITEM',
                module: 'EXT',
                child: [
                  {
                    code: 'A:DEVICE:DEVICE_MANAGEMENT:AN:CREATE',
                    name: '创建',
                    i18nCode: 'menu.admin.create',
                    permissionCodes: ['IC_API_EXT:DEVICE_ANTENNA_CONFIG:CREATE'],
                    type: 'ITEM',
                    module: 'EXT',
                  },
                  {
                    code: 'A:DEVICE:DEVICE_MANAGEMENT:AN:EDIT',
                    name: '编辑',
                    i18nCode: 'menu.admin.edit',
                    permissionCodes: ['IC_API_EXT:DEVICE_ANTENNA_CONFIG:UPDATE'],
                    type: 'ITEM',
                    module: 'EXT',
                  },
                  {
                    code: 'A:DEVICE:DEVICE_MANAGEMENT:AN:DELETE',
                    name: '删除',
                    i18nCode: 'menu.admin.delete',
                    permissionCodes: ['IC_API_EXT:DEVICE_ANTENNA_CONFIG:DELETE'],
                    type: 'ITEM',
                    module: 'EXT',
                  },
                  {
                    code: 'A:DEVICE:DEVICE_MANAGEMENT:AN:ATTR_SYNC',
                    name: '天线下发',
                    i18nCode: 'menu.admin.publishAntenna',
                    permissionCodes: ['IC_API_EXT:DEVICE_ANTENNA_CONFIG:CREATE'],
                    type: 'ITEM',
                    module: 'EXT',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        code: 'A:TASK',
        name: '运维',
        i18nCode: 'menu.admin.exploiation',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        icon: ClipboardListIcon,
        isBottom: true,
        child: [
          {
            code: 'A:TASK:AT',
            name: '异步任务',
            i18nCode: 'menu.admin.asyncTask',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/async-task',
              component: AsyncTask,
            },
            child: [
              {
                code: 'A:TASK:AT:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:ASYNC_TASK:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TASK:SYNC',
            name: '回传任务',
            i18nCode: 'menu.admin.syncTask',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/sync-task',
              component: SyncTask,
            },
            child: [
              {
                code: 'A:TASK:SYNC:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:ORDER_SYNC_TASK:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:TASK:SYNC:UPDATE',
                name: '重新回传',
                i18nCode: 'menu.admin.restartTask',
                permissionCodes: ['IC_API_CORE:ORDER_SYNC_TASK:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:TASK:SYNC:BATCH_UPDATE',
                name: '批量重新回传',
                i18nCode: 'menu.admin.batchRestartTask',
                permissionCodes: ['IC_API_CORE:ORDER_SYNC_TASK:UPDATE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TASK:SCHEDULED',
            name: '定时任务',
            i18nCode: 'menu.admin.scheduledTask',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/scheduled-task',
              component: ScheduledTask,
            },
            child: [
              {
                code: 'A:TASK:SCHEDULED:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:SCHEDULED_TASK:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:TASK:SCHEDULED:LOG',
                name: '查看日志',
                i18nCode: 'menu.admin.viewLog',
                permissionCodes: ['IC_API_CORE:SCHEDULED_TASK:QUERY'],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:TASK:SCHEDULED:EDIT',
                name: '编辑',
                i18nCode: 'menu.admin.edit',
                permissionCodes: ['IC_API_CORE:SCHEDULED_TASK:UPDATE'],
                type: 'ITEM',
                module: 'DEVICE',
              },
            ],
          },
          {
            code: 'A:TASK:WORKFLOW',
            name: '工作流任务',
            i18nCode: 'menu.admin.workflowTask',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/workflow-task',
              component: WorkflowTask,
            },
            child: [
              {
                code: 'A:TASK:WORKFLOW:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:WORK_FLOW_TASK:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:TASK:WORKFLOW:LOG',
                name: '查看日志',
                i18nCode: 'menu.admin.viewLog',
                permissionCodes: ['IC_API_CORE:WORK_FLOW_TASK_LOG:QUERY'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TASK:PT',
            name: '推送日志',
            i18nCode: 'menu.admin.pushLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/push-log',
              component: PushLog,
            },
            child: [
              {
                code: 'A:TASK:PT:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:OPEN_API_LOG:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TASK:CL',
            name: '客户端日志',
            i18nCode: 'menu.admin.clientLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'CORE',
            route: {
              path: '/app/client-log',
              component: ClientLog,
            },
            child: [
              {
                code: 'A:TASK:CL:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_CORE:FILE_RECORD:QUERY'],
                type: 'VIEW',
                module: 'CORE',
              },
              {
                code: 'A:TASK:CL:DOW',
                name: '下载',
                i18nCode: 'menu.admin.download',
                permissionCodes: [''],
                type: 'ITEM',
                module: 'CORE',
              },
              {
                code: 'A:TASK:CL:DEL',
                name: '删除',
                i18nCode: 'menu.admin.delete',
                permissionCodes: ['IC_API_CORE:FILE_RECORD:DELETE'],
                type: 'ITEM',
                module: 'CORE',
              },
            ],
          },
          {
            code: 'A:TASK:MQTT_LOG',
            name: 'MQTT日志',
            i18nCode: 'menu.admin.mqttLog',
            permissionCodes: [],
            type: 'MENU',
            module: 'EXT',
            route: {
              path: '/app/mqtt-log',
              component: MqttLog,
            },
            child: [
              {
                code: 'A:TASK:MQTT_LOG:VIEW',
                name: '查看',
                i18nCode: 'menu.admin.view',
                permissionCodes: ['IC_API_EXT:MQTT:QUERY'],
                type: 'VIEW',
                module: 'EXT',
              },
            ],
          },
        ],
      },
      {
        code: 'A:SETTING',
        name: '系统设置',
        i18nCode: 'menu.admin.sysSetting',
        permissionCodes: [],
        type: 'MENU',
        module: 'CORE',
        route: {
          path: '/app/setting',
          component: Setting,
        },
        icon: SettingsIcon,
        isBottom: true,
        child: [
          {
            code: 'A:SETTING:VIEW_G_S',
            name: '查看全局设置',
            i18nCode: 'menu.admin.viewGlobalSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:QUERY'],
            type: 'VIEW',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:EDIT_G_S',
            name: '修改全局设置',
            i18nCode: 'menu.admin.modifyGlobalSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:VIEW_W_S',
            name: '查看仓库设置',
            i18nCode: 'menu.admin.viewWarehouseSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:QUERY'],
            type: 'VIEW',
            module: 'WAREHOUSE',
          },
          {
            code: 'A:SETTING:EDIT_W_S',
            name: '修改仓库设置',
            i18nCode: 'menu.admin.modifyWarehouseSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'WAREHOUSE',
          },
          {
            code: 'A:SETTING:VIEW_S_S',
            name: '查看门店设置',
            i18nCode: 'menu.admin.viewShopSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:QUERY'],
            type: 'VIEW',
            module: 'SHOP',
          },
          {
            code: 'A:SETTING:EDIT_S_S',
            name: '修改门店设置',
            i18nCode: 'menu.admin.modifyShopSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'SHOP',
          },
          {
            code: 'A:SETTING:VIEW_F_S',
            name: '查看工厂设置',
            i18nCode: 'menu.admin.viewFactorySetting',
            permissionCodes: ['IC_API_CORE:CONFIG:QUERY'],
            type: 'VIEW',
            module: 'FACTORY',
          },
          {
            code: 'A:SETTING:EDIT_F_S',
            name: '修改工厂设置',
            i18nCode: 'menu.admin.modifyFactorySetting',
            permissionCodes: ['IC_API_CORE:CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'FACTORY',
          },
          {
            code: 'A:SETTING:VIEW_SM_S',
            name: '查看样品设置',
            i18nCode: 'menu.admin.viewSampleSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:QUERY'],
            type: 'VIEW',
            module: 'SAMPLE',
          },
          {
            code: 'A:SETTING:EDIT_SM_S',
            name: '修改样品设置',
            i18nCode: 'menu.admin.modifySampleSetting',
            permissionCodes: ['IC_API_CORE:CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'SAMPLE',
          },
          {
            code: 'A:SETTING:VIEW_TAG_RULE',
            name: '查看标签编码规则',
            i18nCode: 'menu.admin.viewTagRule',
            permissionCodes: ['IC_API_CORE:EPC_CODEC_CONFIG:QUERY'],
            type: 'VIEW',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:EDIT_TAG_RULE',
            name: '修改标签编码规则',
            i18nCode: 'menu.admin.modifyTagRule',
            permissionCodes: ['IC_API_CORE:EPC_CODEC_CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:VIEW_SAMPLE_TAG_RULE',
            name: '查看样品标签编码规则',
            i18nCode: 'menu.admin.viewSampleTagRule',
            permissionCodes: ['IC_API_CORE:EPC_CODEC_CONFIG:QUERY'],
            type: 'VIEW',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:EDIT_SAMPLE_TAG_RULE',
            name: '修改样品标签编码规则',
            i18nCode: 'menu.admin.modifySampleTagRule',
            permissionCodes: ['IC_API_CORE:EPC_CODEC_CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:VIEW_DEV_CONFIG',
            name: '查看开发配置',
            i18nCode: 'menu.admin.viewDevConfig',
            permissionCodes: ['IC_API_CORE:DEV_CONFIG:QUERY'],
            type: 'VIEW',
            module: 'CORE',
          },
          {
            code: 'A:SETTING:EDIT_DEV_CONFIG',
            name: '修改开发配置',
            i18nCode: 'menu.admin.modifyDevConfig',
            permissionCodes: ['IC_API_CORE:DEV_CONFIG:CREATE', 'IC_API_CORE:DEV_CONFIG:UPDATE'],
            type: 'ACTION',
            module: 'CORE',
          },
        ],
      },
    ],
  },
};

const getListItem = (treeItem: IMenuItem, parentCode) => {
  const { code, name, i18nName, i18nCode, icon, permissionCodes, type, module, route, sort, isBottom, mode } = treeItem;
  const i18nNameByCode = getTranslationsForAllLanguages(i18nCode);
  return {
    code,
    name,
    i18nName: i18nNameByCode ?? i18nName,
    i18nCode,
    icon,
    permissionCodes,
    type,
    module,
    route,
    sort,
    isBottom,
    parentCode,
    mode,
  };
};

menuTreeData.data.sort = 0;

export const getMenuListData = () => {
  const menuListData = [getListItem(menuTreeData.data, null)];

  const getChildData = (treeItem, index, parentCode) => {
    treeItem.sort = index;
    menuListData.push(getListItem(treeItem, parentCode));
    if (treeItem.child && treeItem.child.length > 0) {
      treeItem.child.forEach((item, index) => getChildData(item, index, treeItem.code));
    }
  };

  if (menuTreeData.data.child) {
    menuTreeData.data.child.forEach((treeItem, index) => {
      getChildData(treeItem, index, null);
    });
  }
  return menuListData;
};
