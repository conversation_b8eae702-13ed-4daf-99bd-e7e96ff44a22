import { Form, Input, Switch, Tabs } from 'antd';
import * as RoleA<PERSON> from 'common/api/core/Permission';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useRef, useState } from 'react';

import { IRoleItem } from '../data';
import Employee from './Employee';
import Permission from './Permission';

interface IOperationModalProps {
  visible: boolean;
  current: Partial<IRoleItem> | undefined;
  onClose: () => void;
}

const OperationModal: React.FC<IOperationModalProps> = (props) => {
  const { onClose, current, visible } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectPermissionKeys, setSelectPermissionKeys] = useState<Record<string, any> | undefined>();
  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);
  const [currentSource, setCurrentSource] = useState<Record<string, any>>();
  const permissionRef = useRef<any>(null);
  const employeeRef = useRef<any>(null);
  const [activeKey, setActiveKey] = useState('INFO');

  const [permission] = usePermission('A:BASE:ROLE');
  const editPermission = permission.codes.includes('EDIT');
  const assignEmpPermission = permission.codes.includes('ASSIGN_EMP');
  const assignPPermission = permission.codes.includes('ASSIGN_P');
  const deletePermission = permission.codes.includes('DELETE');
  const nameInputRef = useRef<any>(null);

  const permissionOnChange = (values) => {
    setSelectPermissionKeys(values);
  };

  const employeeOnChange = (values) => {
    setDataSource(values);
  };

  const onSave = async () => {
    setLoading(true);
    try {
      if (editPermission) {
        const values = await form.validateFields();
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await RoleApi.UpdateRole({
          ...current,
          ...values,
        });
      }

      // 有权限，且selectPermissionKeys有值，才能进行保存
      if (assignPPermission && selectPermissionKeys) {
        const payload: any = {
          data: [],
        };
        Object.keys(selectPermissionKeys).forEach((client) => {
          const clientValue = selectPermissionKeys[client];
          Object.keys(clientValue).forEach((subClient) => {
            const subClientValue = clientValue[subClient];
            payload.data.push({
              client,
              subClient,
              roleId: current?.id,
              codes: subClientValue,
            });
          });
        });
        await RoleApi.SaveRole(payload);
      }

      // 有权限，且dataSource有值了才能进行保存
      if (assignEmpPermission && dataSource.length > 0) {
        const empIds = dataSource.map((item) => item.empId);
        await RoleApi.BindRole({
          data: [
            {
              empIds,
              roleId: current?.id,
            },
          ],
        });
      }

      NoticeUtil.success();
      setLoading(false);
      if (onClose) onClose();
    } catch (e) {
      setLoading(false);
    }
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await RoleApi.DeleteRole({ id: current?.id });
          NoticeUtil.success();
          if (onClose) onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    setActiveKey('INFO');
    if (form && currentSource) {
      form.setFieldsValue(currentSource);
    }
    if (permissionRef.current) {
      permissionRef.current.recover();
    }
    if (employeeRef.current) {
      employeeRef.current.recover();
    }
  };

  const items: any[] = [];

  if (editPermission) {
    items.push({
      label: i18n.t('global.information'),
      key: 'INFO',
      children: (
        <Form layout="vertical" form={form}>
          <Form.Item
            name="code"
            label={i18n.t('global.code')}
            rules={[
              {
                required: true,
                message: i18n.t('global.fieldCanNotBeNull'),
              },
            ]}
          >
            <Input autoFocus readOnly />
          </Form.Item>
          <Form.Item
            label={i18n.t('global.name')}
            name="name"
            rules={[
              {
                required: true,
                message: i18n.t('global.fieldCanNotBeNull'),
              },
            ]}
          >
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
            <Switch />
          </Form.Item>
        </Form>
      ),
    });
  }
  if (assignPPermission) {
    items.push({
      label: i18n.t('global.permission'),
      key: 'PERMISSION',
      children: <Permission innerRef={permissionRef} current={current} onChange={permissionOnChange} />,
    });
  }

  if (assignEmpPermission) {
    items.push({
      label: i18n.t('global.employee'),
      key: 'EMPLOYEE',
      children: <Employee innerRef={employeeRef} current={current} onChange={employeeOnChange} />,
    });
  }

  useEffect(() => {
    if (!visible) return;
    if (!editPermission && assignPPermission) {
      setActiveKey('PERMISSION');
    }
    if (!editPermission && !assignPPermission && assignEmpPermission) {
      setActiveKey('EMPLOYEE');
    }
  }, [visible, editPermission, assignEmpPermission, assignPPermission]);

  useEffect(() => {
    if (current && visible) {
      const values = {
        status: !current?.disabled,
        ...current,
      };
      form.setFieldsValue(values);
      setCurrentSource(values);
      nameInputRef.current?.focus();
    } else {
      form.resetFields();
      setActiveKey('INFO');
      setSelectPermissionKeys(undefined);
      setDataSource([]);
      setCurrentSource(undefined);
    }
  }, [visible, current, form]);

  const onCancel = () => {
    if (onClose) onClose();
  };

  return (
    <Drawer
      title={`${i18n.t('global.permissionDetails')}${current ? ` [${current.code}]` : ''}`}
      width={700}
      destroyOnClose
      open={visible}
      onClose={onCancel}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          onApply={onSave}
          deletePermission={deletePermission}
          onDelete={deleteBtnOnClick}
          onRecover={onRecover}
        />
      }
    >
      <Tabs size="small" animated={false} items={items} activeKey={activeKey} onChange={(e) => setActiveKey(e)} />
    </Drawer>
  );
};

export default OperationModal;
