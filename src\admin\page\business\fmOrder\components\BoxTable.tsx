import { Input, Tooltip } from 'antd';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import * as DataSourceApi from 'common/api/report/DataSource';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, { PowerTableColumnsType, SearchFieldsConfig } from 'common/components/PowerTable';
import PrinterSelectorModal from 'common/components/PrinterSelectorModal';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { GlobalDDSConnector } from 'common/utils/DDSConnector';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

interface IBoxTableProps {
  orderId: string;
  // eslint-disable-next-line react/no-unused-prop-types
  orderStatus?: string;
  powerTableRef: any;
}

const printConfig = {
  module: 'FACTORY',
  businessCode: 'FM',
  businessTypeCode: 'BOX_STICKERS',
};

const BoxTable: React.FC<IBoxTableProps> = (props) => {
  const { orderId, powerTableRef } = props;

  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewModalLoading, setBoxDetailsPreviewModalLoading] = useState<boolean>(false);
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [currentBox, setCurrentBox] = useState<Record<string, any>>({});
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const [selectPrinterModalOpen, setSelectPrinterModalOpen] = useState<boolean>(false);
  const { currentUser } = globalState;
  const { ENABLE_UNICODE: enableUnicode, FACTORY_FQC_ENABLE: factoryFqcEnable } = useSetting([
    { code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' },
    { code: 'FACTORY_FQC_ENABLE', valueType: 'BOOLEAN' },
  ]);

  const boxCodeOnClick = async (record) => {
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewModalLoading(true);
    setCurrentBoxCode(record.code);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BfmOrderApi.Rfids({
          fmOrderBoxId: record.id,
          fmOrderId: orderId,
          enablePage: false,
        });
      } else {
        result = await FmOrderApi.Rfids({
          fmOrderBoxId: record.id,
          fmOrderId: orderId,
          enablePage: false,
        });
      }
      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewModalLoading(false);
  };

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      let result = { data: [] };
      const payload = {
        ...params,
        fmOrderId: orderId,
      };
      try {
        if (currentUser.mode === 'BINDING') {
          result = await BfmOrderApi.Boxs(payload);
        } else {
          result = await FmOrderApi.Boxs(payload);
        }
      } catch (e) {}
      return result;
    },
    // eslint-disable-next-line
    [orderId],
  );

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId, powerTableRef]);

  const print = async (record) => {
    const printer = LocalStorageUtil.getItem(`PRINTER_${printConfig.businessCode}_${printConfig.businessTypeCode}`);
    if (!printer) {
      setSelectPrinterModalOpen(true);
      setCurrentBox(record);
      return;
    }
    const hide = NoticeUtil.loading(i18n.t('global.printing'));
    try {
      const res = await DataSourceApi.Get({
        ...printConfig,
        code: record.code,
      });
      await GlobalDDSConnector.printHttp({
        module: printConfig.module,
        businessType: printConfig.businessCode,
        templateType: printConfig.businessTypeCode,
        json: JSON.stringify(res),
        printer,
        printCount: 1,
        preview: false,
      });
    } catch (e) {}
    hide();
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} autoFocus />,
    },
    {
      name: 'barcode',
      inputComponent: <Input placeholder={i18n.t('global.barcode')} />,
    },
    {
      name: 'prodCode',
      inputComponent: <Input placeholder={i18n.t('global.productCode')} />,
    },
  ];

  const columns: PowerTableColumnsType = useMemo(() => {
    const arr: any = [
      {
        title: i18n.t('global.box'),
        dataIndex: 'code',
        valueType: 'text',
        width: 220,
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'prodCodes',
        width: 180,
        ellipsis: true,
        render(text) {
          const prodCodes = text?.split(',');
          if (prodCodes?.length > 0) {
            return (
              <Tooltip
                placement="topLeft"
                title={
                  <div>
                    {prodCodes.map((item) => (
                      <div key={item}>{item}</div>
                    ))}
                  </div>
                }
              >
                <span>{text}</span>
              </Tooltip>
            );
          }
          return text;
        },
      },
      {
        title: i18n.t('global.status'),
        dataIndex: 'status',
        width: 150,
        render(text, record) {
          return (
            <Tag
              color={
                {
                  NEW: 'red',
                  UPLOADED: 'blue',
                  FINISHED: 'green',
                }[record.status]
              }
            >
              {record.statusDesc}
            </Tag>
          );
        },
      },
      {
        title: i18n.t('global.operateMode'),
        dataIndex: 'operateModeDesc',
        width: 200,
      },
      {
        title: i18n.t('global.packingRule'),
        dataIndex: 'modeDesc',
        auto: true,
        minWidth: 200,
      },
      {
        title: i18n.t('global.scheduleStatus'),
        dataIndex: 'fadStatusDesc',
        width: 180,
      },
      // {
      //   title: i18n.t('global.netWeight'),
      //   dataIndex: 'netWeight',
      //   valueType: 'number',
      //   width: 180,
      // },
      {
        title: i18n.t('global.weight'),
        dataIndex: 'weight',
        valueType: 'number',
        width: 180,
      },
      {
        title: i18n.t('global.count'),
        dataIndex: 'qty',
        sorter: true,
        valueType: 'number',
        width: 200,
      },
    ];

    if (factoryFqcEnable) {
      arr.splice(4, 0, {
        title: i18n.t('global.fqcStatus'),
        dataIndex: 'fqcStatusDesc',
        minWidth: 180,
      });
    }

    return arr;
  }, [factoryFqcEnable]);

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={10}
        request={fetchData}
        pagination
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        quickSearchPanelSubmitButtonVisible
        refreshBtnVisible
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              boxCodeOnClick(record);
            },
          }),
        }}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewModalLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
      <PrinterSelectorModal
        type={`${printConfig.businessCode}_${printConfig.businessTypeCode}`}
        open={selectPrinterModalOpen}
        onCancel={() => {
          setSelectPrinterModalOpen(false);
        }}
        onSelect={() => {
          print(currentBox);
        }}
        title={i18n.t('global.choosePrinterAndPrint')}
      />
    </>
  );
};

export default BoxTable;
