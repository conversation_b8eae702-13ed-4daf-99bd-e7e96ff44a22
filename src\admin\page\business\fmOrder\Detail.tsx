import { Descriptions, Radio, Space, Statistic, Tabs } from 'antd';
import * as Print<PERSON>rder<PERSON><PERSON> from 'common/api/core/PrintOrder';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrder<PERSON>pi from 'common/api/factory/FmOrder';
import * as Bfqc<PERSON><PERSON>r<PERSON>pi from 'common/api/factory/BfqcOrder';
import * as FqcOrderApi from 'common/api/factory/FqcOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import FailCircleIcon from 'common/assets/icons/icon-fail-circle.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import Button from 'common/components/Button';
import RefreshButton from 'common/components/Button/Refresh';
import ExternalLinkLine from 'common/assets/icons/icon-external-link-line.svg?react';
import FadOrderRecordTable, { DataViewTableInnerRef } from 'common/components/FadOrderRecordModal/DataViewTable';
import LogDrawer from 'common/components/LogDrawer';
import ProdCodeParagraph from 'common/components/Paragraph/ProdCodeParagraph';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import RemarkEditModal from 'common/components/RemarkEditModal';
import Tag from 'common/components/Tag';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import SearchInput from 'common/components/SearchInput';
import MatchRuleTable, { MatchRuleTableInnerRef } from './components/MatchRuleTable';
import { useCommonFn } from './common';
import BoxTable from './components/BoxTable';
import PrintModal from './components/PrintModal';
import PreBoxTable, { PreBoxTableInnerRef } from './components/PreBoxTable';
import { DetailContext, initialState, reducer } from './DetailReducer';
import SummaryTable, { SummaryTableInnerRef } from './components/SummaryTable';

const FmOrderDetail: React.FC = () => {
  const params: any = useParams();
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [prodList, setProdList] = useState<any[]>([]);
  const [printTaskPermission] = usePermission('A:TAG:PT');
  const printPermission = printTaskPermission.codes.includes('CREATE');
  const FadOrderRecordTableRef = useRef<DataViewTableInnerRef>();
  const SummaryTableRef = useRef<SummaryTableInnerRef>();
  const matchRuleTableRef = useRef<MatchRuleTableInnerRef>();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { loading, orderRecord, remarkModalOpen, printTaskAddModalOpen, logDrawerOpen, logs } = state;
  const navigate = useNavigate();
  const { confirm, cancel } = useCommonFn();
  const setting = useSetting([
    { code: 'RFID_RULES_DEPEND_ON_FIELD', valueType: 'STRING' },
    { code: 'ENABLE_MACHING_CODE', valueType: 'BOOLEAN', defaultValue: false },
  ]);

  const [permission] = usePermission('A:F:FM');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');

  const { RFID_RULES_DEPEND_ON_FIELD: dependField, ENABLE_MACHING_CODE: enableMachingCode } = setting;

  const boxTableRef = useRef<IPowerTableInnerRef>();
  const recordTableRef = useRef<IPowerTableInnerRef>();
  const preBoxTableRef = useRef<PreBoxTableInnerRef>();

  let orderStatus = '';

  const linesTableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      width: 180,
      fixed: 'left',
      valueType: 'text',
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'skuName',
      width: 200,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 200,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} disturbTag={record.disturbTag} rfidTag={record.rfidTag} />
      ),
    },
    {
      title: i18n.t('global.colors'),
      dataIndex: 'colorName',
      width: 120,
    },
    {
      title: i18n.t('global.sizes'),
      dataIndex: 'sizeName',
      valueType: 'number',
      width: 120,
    },
    {
      title: i18n.t('global.specs'),
      dataIndex: 'specName',
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    // {
    //   title: i18n.t('global.recQty'),
    //   dataIndex: 'recQty',
    //   valueType: 'number',
    //   sorter: true,
    //   width: 200,
    // },
    // {
    //   title: i18n.t('global.availableQty'),
    //   dataIndex: 'availableQty',
    //   valueType: 'number',
    //   width: 200,
    // },
  ];

  const fqcQuickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.fqcOrderCodeOrFqcSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const fqcLinesTableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.sourceOrderCode'),
      width: 250,
      dataIndex: 'sourceCode',
      valueType: 'text',
      tooltip: true,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PASS: 'green',
                NOT_PASS: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      valueType: 'text',
      sorter: true,
      width: 160,
    },
    {
      title: i18n.t('global.count'),
      valueType: 'number',
      dataIndex: 'totalQty',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.qualifiedQty'),
      valueType: 'number',
      dataIndex: 'qualifiedQty',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.shipper'),
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      valueType: 'dateTime',
      dataIndex: 'created',
      sorter: true,
      width: 200,
    },
  ];

  const onPrintModalSave = async (value) => {
    try {
      const res: any = await PrintOrderApi.Generate(value);
      NoticeUtil.success();
      dispatch({ type: 'setPrintTaskAddModalOpen', payload: false });
      navigate(`/app/print-task/detail/${res.id}`);
    } catch (e) {}
  };

  const fetchOrderData = useCallback(async () => {
    if (!params || !params.id) return;
    dispatch({ type: 'setLoading', payload: true });
    try {
      let rec;
      let prodList;
      if (currentUser.mode === 'BINDING') {
        rec = await BfmOrderApi.Get({ id: params.id });
        dispatch({ type: 'setOrderRecord', payload: rec });

        prodList = await BfmOrderApi.FmProdList({
          fmOrderIds: [params.id],
        });
      } else {
        rec = await FmOrderApi.Get({ id: params.id });
        dispatch({ type: 'setOrderRecord', payload: rec });

        prodList = await FmOrderApi.FmProdList({
          fmOrderIds: [params.id],
        });
      }

      setProdList(prodList);
      dispatch({ type: 'setLoading', payload: false });
    } catch (err) {
      dispatch({ type: 'setLoading', payload: false });
    }
    // eslint-disable-next-line
  }, [params]);

  const fetchLinesData = async (data) => {
    const payload: any = { ...data };
    payload.fmOrderId = params.id;
    if (orderStatus === 'processing') {
      payload.qtyContrastMode = 'LT';
    }

    if (orderStatus === 'finish') {
      payload.qtyContrastMode = 'GE';
    }
    let record;
    if (currentUser.mode === 'BINDING') {
      record = await BfmOrderApi.Lines(payload);
    } else {
      record = await FmOrderApi.Lines(payload);
    }
    record.data.forEach((n) => {
      n.availableQty = n.qty - n.actQty + n.rejectQty;
    });
    return record;
  };

  const fetchIssuseLinesData = async (data) => {
    const payload: any = { ...data };
    payload.fmOrderId = orderRecord.id;

    let record;
    if (currentUser.mode === 'BINDING') {
      record = await BfqcOrderApi.List(payload);
    } else {
      record = await FqcOrderApi.List(payload);
    }
    return record;
  };

  const statusRadioOnChange = (data) => {
    orderStatus = data.target.value;
    powerTableRef.current?.load();
  };

  const fetchLogs = async () => {
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfmOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      } else {
        res = await FmOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      }
      dispatch({ type: 'setLogs', payload: res.data });
    } catch (e) {}
  };

  useEffect(() => {
    LocalStorageUtil.setItem('RFID_RULES_DEPEND_ON_FIELD', dependField);
  }, [dependField]);

  useEffect(() => {
    if (logDrawerOpen) {
      fetchLogs();
    }
    // eslint-disable-next-line
  }, [logDrawerOpen]);

  useEffect(() => {
    if (!params || !params.id) return;
    fetchOrderData();
    // boxTableRef.current?.load();
    // eslint-disable-next-line
  }, [params]);

  const refresh = () => {
    fetchOrderData();
    boxTableRef.current?.load();
    powerTableRef.current?.load();
    recordTableRef.current?.load();
    FadOrderRecordTableRef.current?.load();
    SummaryTableRef.current?.load();
    matchRuleTableRef.current?.load();
    preBoxTableRef.current?.load();
  };

  const modifyRemark = () => {
    dispatch({ type: 'setRemarkEditModalOpen', payload: true });
  };

  const remarkEditModalOnSubmit = async (values) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BfmOrderApi.Update({
          id: params.id,
          ...values,
        });
      } else {
        await FmOrderApi.Update({
          id: params.id,
          ...values,
        });
      }

      NoticeUtil.success();
      refresh();
    } catch {}
    // setRemarkEditModalVisible(false);
    dispatch({ type: 'setRemarkEditModalOpen', payload: false });
  };

  const confirmBtnOnClick = async () => {
    try {
      await confirm(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const cancelBtnOnClick = async () => {
    try {
      await cancel(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const tabItems = useMemo(() => {
    const result = [
      {
        key: 'BOX',
        label: `${i18n.t('global.boxes')} (${orderRecord.boxCount || 0})`,
        children: (
          <BoxTable
            orderStatus={orderRecord.status}
            powerTableRef={boxTableRef}
            orderId={params ? params.id : undefined}
          />
        ),
      },
      {
        key: 'RFIDS',
        label: i18n.t('global.line'),
        children: (
          <PowerTable
            initialized
            rowKey="id"
            columns={linesTableColumns}
            innerRef={powerTableRef}
            leftToolbar={
              <Radio.Group defaultValue="all" onChange={statusRadioOnChange}>
                <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
                <Radio.Button value="processing">{i18n.t('global.unFinish')}</Radio.Button>
                <Radio.Button value="finish">{i18n.t('global.finished')}</Radio.Button>
              </Radio.Group>
            }
            refreshBtnVisible
            defaultPageSize={10}
            pagination
            autoLoad
            tableProps={{ sticky: true }}
            request={fetchLinesData}
          />
        ),
      },
      {
        key: 'FQCRECORD',
        label: i18n.t('global.qualityCheckRecord'),
        children: (
          <PowerTable
            initialized
            rowKey="id"
            columns={fqcLinesTableColumns}
            innerRef={recordTableRef}
            quickSearchFieldsConfig={fqcQuickSearchFieldsConfig}
            refreshBtnVisible
            defaultPageSize={10}
            pagination
            autoLoad
            tableProps={{ sticky: true }}
            request={fetchIssuseLinesData}
          />
        ),
      },
      {
        key: 'RECORD',
        label: i18n.t('global.fadOrderRecord'),
        children: <FadOrderRecordTable fmOrderCode={orderRecord.code} innerRef={FadOrderRecordTableRef} />,
      },
      {
        key: 'SUMMARY',
        label: i18n.t('global.summary'),
        children: orderRecord && (
          <SummaryTable fmOrderId={orderRecord.id} notFetch={orderRecord.status === 'NEW'} innerRef={SummaryTableRef} />
        ),
      },
    ];

    if (enableMachingCode) {
      result.push({
        key: 'MATCH',
        label: i18n.t('global.proportions'),
        children: <MatchRuleTable fmOrderId={orderRecord.id} innerRef={matchRuleTableRef} />,
      });
    }

    if (orderRecord.preBoxTag) {
      result.unshift({
        key: 'PRE_BOX',
        label: `${i18n.t('global.preBox')} (${orderRecord.preboxCount || 0})`,
        children: <PreBoxTable innerRef={preBoxTableRef} />,
      });
    }

    return result;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    fetchIssuseLinesData,
    fetchLinesData,
    fqcQuickSearchFieldsConfig,
    linesTableColumns,
    orderRecord,
    params,
    statusRadioOnChange,
    enableMachingCode,
  ]);

  const subTitle = useMemo(() => {
    return (
      <Tag
        color={
          {
            NEW: 'red',
            PROCESSING: 'blue',
            FINISHED: 'green',
            CANCELED: 'slate',
          }[orderRecord.status]
        }
      >
        {orderRecord.statusDesc}
      </Tag>
    );
  }, [orderRecord.status, orderRecord.statusDesc]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <DetailContext.Provider value={detailContextValue}>
      <div>
        <AppHeader
          title={loading ? i18n.t('global.loading') : orderRecord.code}
          subTitle={subTitle}
          toolbar={
            <Space>
              <RefreshButton size="middle" onClick={refresh} />
              <Button
                type="default"
                icon={<ListIcon className="fill-lead-dark" />}
                onClick={() => dispatch({ type: 'setLogDrawerOpen', payload: true })}
              >
                {i18n.t('global.log')}
              </Button>
              {(orderRecord.status === 'NEW' || orderRecord.status === 'PROCESSING') && cancelPermission && (
                <Button type="danger" icon={<FailCircleIcon className="fill-white" />} onClick={cancelBtnOnClick}>
                  {i18n.t('global.cancel')}
                </Button>
              )}
              {printPermission && !orderRecord.printOrderCode && (
                <Button
                  type="primary"
                  icon={<AddFillIcon className="fill-white" />}
                  onClick={() => dispatch({ type: 'setPrintTaskAddModalOpen', payload: true })}
                >
                  {i18n.t('global.createPrintTask')}
                </Button>
              )}
              {confirmPermission && orderRecord.status === 'PROCESSING' && (
                <Button
                  type="primary"
                  icon={<DoubleCheckIcon className="fill-white" />}
                  className="global-blue"
                  onClick={confirmBtnOnClick}
                >
                  {i18n.t('global.confirm')}
                </Button>
              )}
            </Space>
          }
        />
        <div className="rounded-md border border-lead-light-slate">
          <div className="flex gap-x-6 bg-lead-light-bg p-5">
            <SendReceiveLayout
              className="flex-auto"
              left={
                <PartnerViewer
                  partnerType="FACTORY"
                  partnerCode={orderRecord.partnerCode}
                  partnerName={orderRecord.partnerName}
                  warehouseCode={orderRecord.warehouseCode}
                  warehouseName={orderRecord.warehouseName}
                  label={i18n.t('global.shipper')}
                />
              }
              right={
                <PartnerViewer
                  partnerType={orderRecord.toPartnerType}
                  partnerCode={orderRecord.toPartnerCode}
                  partnerName={orderRecord.toPartnerName}
                  warehouseCode={orderRecord.toWarehouseCode}
                  warehouseName={orderRecord.toWarehouseName}
                  label={i18n.t('global.receiver')}
                />
              }
            />
            <div className="flex flex-initial items-center gap-x-6">
              {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
              <Statistic title={i18n.t('global.boxedCount')} value={orderRecord.actQty} />
              <Statistic title={i18n.t('global.deliveryQty')} value={orderRecord.deliveryQty} />
              <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
              {/* <OrderStatusStatistic status={orderRecord.status} /> */}
            </div>
          </div>
          <div className="px-5 pt-4">
            <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
              {prodList.length > 0 && (
                <Descriptions.Item label={i18n.t('global.productCode')}>
                  <ProdCodeParagraph prodList={prodList} badgeClassName="mr-2" />
                </Descriptions.Item>
              )}
              {orderRecord.sourceCode && (
                <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>{orderRecord.sourceCode}</Descriptions.Item>
              )}
              {orderRecord.printOrderCode && (
                <Descriptions.Item label={i18n.t('global.printTask')}>
                  <a
                    role="button"
                    tabIndex={0}
                    onClick={() => navigate(`/app/print-task/detail/${orderRecord.printOrderId}`)}
                    onKeyDown={() => {}}
                    className="flex"
                  >
                    <span>{orderRecord.printOrderCode}</span>
                    <ExternalLinkLine className="fill-lead-orange" />
                  </a>
                </Descriptions.Item>
              )}
              {orderRecord.billingDate && (
                <Descriptions.Item label={i18n.t('global.issueDate')}>
                  {moment(orderRecord.billingDate).format('YYYY-MM-DD')}
                </Descriptions.Item>
              )}
              {orderRecord.deliveryDate && (
                <Descriptions.Item label={i18n.t('global.deliveryDate')}>
                  {moment(orderRecord.deliveryDate).format('YYYY-MM-DD')}
                </Descriptions.Item>
              )}
              {orderRecord.modified && (
                <Descriptions.Item label={i18n.t('global.modified')}>
                  {moment(orderRecord.modified).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              )}
              {orderRecord.created && (
                <Descriptions.Item label={i18n.t('global.created')}>
                  {moment(orderRecord.created).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              )}
              {((orderRecord.remark && orderRecord.status === 'FINISHED') || orderRecord.status !== 'FINISHED') && (
                <Descriptions.Item label={i18n.t('global.remark')} contentStyle={{ alignItems: 'center' }}>
                  {orderRecord.remark}
                  {modifyRemarkPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
                    <>
                      <Button
                        type="link"
                        size="small"
                        icon={<EditBoxLineIcon className="fill-lead-orange" />}
                        onClick={() => modifyRemark()}
                        className="-my-px ml-2"
                      />
                      <RemarkEditModal
                        current={orderRecord}
                        onSubmit={remarkEditModalOnSubmit}
                        onCancel={() => dispatch({ type: 'setRemarkEditModalOpen', payload: false })}
                        open={remarkModalOpen}
                        title={i18n.t('global.modifyRemark')}
                      />
                    </>
                  )}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        </div>
        <div className="mt-5">
          <Tabs size="small" defaultActiveKey={orderRecord.preBoxTag ? 'PRE_BOX' : 'BOX'} items={tabItems} />
        </div>
        <PrintModal
          onSave={onPrintModalSave}
          onCancel={() => {
            dispatch({ type: 'setPrintTaskAddModalOpen', payload: false });
          }}
          visible={printTaskAddModalOpen}
        />
        <LogDrawer
          open={logDrawerOpen}
          orderId={orderRecord.id}
          onClose={() => dispatch({ type: 'setLogDrawerOpen', payload: false })}
          data={logs}
        />
      </div>
    </DetailContext.Provider>
  );
};

export default FmOrderDetail;
