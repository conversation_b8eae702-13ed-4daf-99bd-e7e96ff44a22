import { But<PERSON>, DatePicker, Input, Space, Spin } from 'antd';
import * as SyncTaskApi from 'common/api/core/SyncTask';
import UploadCloudIcon from 'common/assets/icons/icon-upload-cloud.svg?react';
import AliasViewer from 'common/components/AliasViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import * as NoticeUtil from 'common/utils/Notice';

import ConfirmTypeSelect from './components/ConfirmTypeSelect';
import SyncTaskInfoModal from './components/SyncTaskInfoModal';
import SyncTaskStatusSelect from './components/SyncTaskStatusSelect';
import SyncTaskTypeSelect from './components/SyncTaskTypeSelect';

const SyncTask: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [syncTaskLogDrawerVisible, setSyncTaskLogDrawerVisible] = useState<boolean>(false);
  const [taskId, setTaskId] = useState<string>('');
  const [initialized, setInitialized] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

  const [permission] = usePermission('A:TASK:SYNC');
  const syncPermission = permission.codes.includes('UPDATE');
  const batchSyncPermission = permission.codes.includes('BATCH_UPDATE');

  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);

  const fetchData = (params) => {
    if (params.modified) {
      params.modifiedStart = params.modified[0].startOf('day');
      params.modifiedEnd = params.modified[1].endOf('day');
    }
    delete params.modified;
    return SyncTaskApi.List(params);
  };

  const updateSyncTask = async (record) => {
    try {
      await SyncTaskApi.Update({
        id: record.id,
        status: 'NONE',
      });
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'orderCode',
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.inputOrderCodeThenEnterToSearch')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      label: i18n.t('global.boxCode'),
      inputComponent: <Input />,
    },
    {
      name: 'type',
      label: i18n.t('global.type'),
      inputComponent: <SyncTaskTypeSelect allowClear />,
    },
    {
      name: 'action',
      label: i18n.t('global.syncAction'),
      inputComponent: <ConfirmTypeSelect allowClear />,
    },
    {
      name: 'status',
      label: i18n.t('global.status'),
      inputComponent: <SyncTaskStatusSelect allowClear />,
    },
    {
      name: 'modified',
      label: i18n.t('global.modified'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'orderCode',
      fixed: 'left',
      // width: 200,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.alias'),
      dataIndex: 'alias',
      align: 'center',
      width: 100,
      render: (value, record) => <AliasViewer aliasData={record.alias} />,
    },
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      width: 240,
    },
    {
      title: i18n.t('global.syncAction'),
      dataIndex: 'actionDesc',
      width: 150,
    },
    {
      title: i18n.t('global.syncCount'),
      dataIndex: 'count',
      valueType: 'number',
      width: 100,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      descDataIndex: 'statusDesc',
      valueType: 'status',
      statusConfig: {
        NONE: { status: 'Blue' },
        FAIL: { status: 'Slate' },
        SUCCESS: { status: 'Green' },
      },
      width: 150,
    },
    {
      title: i18n.t('global.type'),
      valueType: 'codeName',
      dataIndex: 'type',
      codeDataIndex: 'type',
      nameDataIndex: 'typeDesc',
      width: 150,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (syncPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.resetSyncTask'),
      icon: <UploadCloudIcon className="fill-lead-dark" />,
      onClick: (record) => {
        updateSyncTask(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const addBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmSyncTask'),
      content: i18n.t('global.selectedItemsData', {
        count: selectedRowKeys?.length,
      }),
      okType: 'primary',
      onOk: async () => {
        try {
          await SyncTaskApi.BatchUpdate({
            ids: selectedRowKeys,
            status: 'NONE',
          });
          NoticeUtil.success();
          setSelectedRowKeys([]);
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {batchSyncPermission && (
              <Button
                icon={<UploadCloudIcon className="fill-white" />}
                type="primary"
                onClick={addBtnOnClick}
                disabled={selectedRowKeys.length === 0}
              >
                {i18n.t('global.resetSyncTask')} ({selectedRowKeys.length})
              </Button>
            )}
          </Space>
        }
      />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={{
            disabled: 'false',
            created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
          }}
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          enableCache
          cacheKey="SYNC_TASK"
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: (record) => ({
              onClick: () => {
                setTaskId(record.id);
                setSyncTaskLogDrawerVisible(true);
              },
            }),
            rowSelection: batchSyncPermission
              ? {
                  type: 'checkbox',
                  selectedRowKeys,
                  onChange: (selectedRowKeys) => {
                    setSelectedRowKeys(selectedRowKeys);
                  },
                }
              : undefined,
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
      <SyncTaskInfoModal
        visible={syncTaskLogDrawerVisible}
        onClose={() => {
          setSyncTaskLogDrawerVisible(false);
        }}
        taskId={taskId}
      />
    </div>
  );
};

export default SyncTask;
