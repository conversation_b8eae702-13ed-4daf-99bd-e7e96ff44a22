import { Form, Input, Switch } from 'antd';
import * as SamplePriCategoryApi from 'common/api/sample/SamplePriCategory';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { ISamplePriCategoryItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  priCategoryId: string | undefined;
  onSubmit: (values: ISamplePriCategoryItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, priCategoryId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:PRI_CATEGORY');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchPriCategory = useCallback(async () => {
    setLoading(true);
    try {
      const priCategory: any = await SamplePriCategoryApi.Get({
        id: priCategoryId,
      });
      if (typeof priCategory.disabled === 'boolean') {
        priCategory.status = !priCategory.disabled;
      }
      form.setFieldsValue(priCategory);
      setCurrent(priCategory);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [priCategoryId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (priCategoryId) fetchPriCategory();
      setTimeout(() => {
        if (priCategoryId) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [priCategoryId, visible, form, fetchPriCategory]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISamplePriCategoryItem);
    }
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editMainCategory')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newMainCategory')
  );

  const onClose = () => {
    onCancel();
    form.resetFields();
  };
  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SamplePriCategoryApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchPriCategory();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!priCategoryId}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      onClose={onClose}
      open={visible}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input readOnly={!!current} ref={codeInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
