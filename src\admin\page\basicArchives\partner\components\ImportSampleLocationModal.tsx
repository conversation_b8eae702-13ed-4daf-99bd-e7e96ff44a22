import { ModalProps } from 'antd/es/modal';
import * as SampleStorageLocationApi from 'common/api/sample/sampleStorageLocation';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface ImportStorageLocationModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
}

const ImportStorageLocationModal: React.FC<ImportStorageLocationModalProps> = (props) => {
  const { modalProps, onGoBack, onOk } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const template: TemplateItem[] = [
    {
      dataIndex: 'code',
      display: i18n.t('global.code'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'name',
      display: i18n.t('global.name'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'disable',
      display: i18n.t('global.isDisabled'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.partnerCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'partnerType',
      display: i18n.t('global.partnerType'),
      type: 'STRING',
      required: true,
      remark: i18n.t('global.warehouse'),
    },
    {
      dataIndex: 'warehouseCode',
      display: i18n.t('global.subWarehouseCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'rowIndex',
      display: i18n.t('global.rowIndex'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'columnIndex',
      display: i18n.t('global.columnIndex'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'upSort',
      display: i18n.t('global.upSort'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'pickSort',
      display: i18n.t('global.pickSort'),
      type: 'NUMBER',
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.remark'),
      type: 'STRING',
    },
  ];

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');

    data.forEach((item) => {
      if (item.partnerType === i18n.t('global.warehouse')) {
        item.partnerType = 'WAREHOUSE';
      }
    });
    try {
      setProgressPercent(0);
      await SampleStorageLocationApi.Import(
        {
          data,
        },
        {
          throwError: false,
          timeout: 300000,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e: any) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  return (
    <Modal
      width={960}
      title={i18n.t('global.importSampleLocation')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.sampleLocation')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportStorageLocationModal;
