import { Radio } from 'antd';
import * as BfibOrderApi from 'common/api/factory/BfibOrder';
import * as FibOrderApi from 'common/api/factory/FibOrder';
import PowerTable, { PowerTableColumnsType } from 'common/components/PowerTable';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useState } from 'react';

export interface LineRfidsTableProps {
  orderId: string;
  powerTableRef: any;
}

const LineRfidsTable: React.FC<LineRfidsTableProps> = (props) => {
  const { orderId, powerTableRef } = props;
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [orderStatus, setOrderStatus] = useState('all');
  // let orderStatus = '';

  const statusRadioOnChange = (data) => {
    setOrderStatus(data.target.value);
  };

  const fetchLinesData = useCallback(
    async (data) => {
      if (!orderId) return Promise.reject();
      const payload: any = { ...data };
      payload.fibOrderId = orderId;
      if (orderStatus === 'processing') {
        payload.qtyContrastMode = 'LT';
      }

      if (orderStatus === 'finish') {
        payload.qtyContrastMode = 'GE';
      }
      let record;
      if (currentUser.mode === 'BINDING') {
        record = await BfibOrderApi.Lines(payload);
      } else {
        record = await FibOrderApi.Lines(payload);
      }
      record.data.forEach((item, index) => {
        record.data[index].diffQty = item.qty - item.actQty;
      });
      return record;
    },
    [orderId, orderStatus, currentUser.mode],
  );

  useEffect(() => {
    if (orderStatus) {
      powerTableRef.current?.load();
    }
    // eslint-disable-next-line
  }, [orderStatus]);

  const rfidsTableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      width: 180,
      fixed: 'left',
      valueType: 'text',
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'skuName',
      width: 200,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 200,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} disturbTag={record.disturbTag} rfidTag={record.rfidTag} />
      ),
    },
    {
      title: i18n.t('global.colors'),
      dataIndex: 'colorName',
      width: 120,
    },
    {
      title: i18n.t('global.sizes'),
      dataIndex: 'sizeName',
      valueType: 'number',
      width: 120,
    },
    {
      title: i18n.t('global.specs'),
      dataIndex: 'specName',
      minWidth: 150,
      auto: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.diffQty'),
      valueType: 'number',
      dataIndex: 'diffQty',
      width: 220,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      columns={rfidsTableColumns}
      innerRef={powerTableRef}
      leftToolbar={
        <>
          <Radio.Group defaultValue="all" onChange={statusRadioOnChange}>
            <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
            <Radio.Button value="processing">{i18n.t('global.unFinish')}</Radio.Button>
            <Radio.Button value="finish">{i18n.t('global.finished')}</Radio.Button>
          </Radio.Group>
        </>
      }
      refreshBtnVisible
      autoLoad
      tableProps={{ sticky: true }}
      request={fetchLinesData}
    />
  );
};

export default LineRfidsTable;
