import { Input, Radio } from 'antd';
import * as Bwib<PERSON>rder<PERSON><PERSON> from 'common/api/warehouse/BwibOrder';
import * as WibOrder<PERSON><PERSON> from 'common/api/warehouse/WibOrder';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import BoxAbnormalDetailsPreviewModal from 'common/components/BoxAbnormalDetailsPreviewModal';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import BoxSpecToolTip from 'common/components/BoxSpecToolTip';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import BoxStatusSelect from 'common/components/Select/BoxStatusSelect';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  onRefresh?: () => void;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId, localTag, normalBoxCount, abnormalBoxCount } = orderRecord;
  const { onRefresh, innerRef } = props;
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [dataType, setDataType] = useState<'normal' | 'error'>('normal');
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);
  const [boxAbnormalDetailsPreviewModalVisible, setBoxAbnormalDetailsPreviewModalVisible] = useState<boolean>(false);
  const [currentBox, setCurrentBox] = useState<Record<string, any>>({});

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:W:IB');
  const boxConfirmPermission = permission.codes.includes('BOX_CONFIRM');
  const boxResetPermission = permission.codes.includes('BOX_RESET');

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);
  const { SHOW_RFID_EXT_FLAG: showRfidExt } = useSetting([{ code: 'SHOW_RFID_EXT_FLAG', valueType: 'BOOLEAN' }]);
  const { WI_SAVE_BOX_ENABLE_BOX_SPEC: enableBoxSpec } = useSetting([
    { code: 'WI_SAVE_BOX_ENABLE_BOX_SPEC', valueType: 'BOOLEAN' },
  ]);

  const fetchData = useCallback(
    async (params) => {
      let res;
      const payload = {
        ...params,
        wibOrderId: orderRecord.id,
        correctTag: dataType === 'normal',
      };
      if (currentUser.mode === 'BINDING') {
        res = await BwibOrderApi.Boxs(payload);
      } else if (currentUser.mode === 'WRITE') {
        res = await WibOrderApi.Boxs(payload);
      }
      return res;
    },
    [currentUser.mode, dataType, orderRecord.id],
  );

  const _onRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmConfirmBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BwibOrderApi.ConfirmBox({
              id: record.id,
            });
          } else {
            await WibOrderApi.ConfirmBox({
              id: record.id,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const resetBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmResetBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BwibOrderApi.BoxReset({
              boxCode: record.boxCode,
              wibOrderId: orderId,
            });
          } else {
            await WibOrderApi.BoxReset({
              boxCode: record.boxCode,
              wibOrderId: orderId,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const openDetail = async (record) => {
    setCurrentBox(record);
    if (record.correctTag) {
      setBoxDetailsPreviewDrawerOpen(true);
      setBoxDetailsPreviewDrawerLoading(true);
      try {
        let result;
        const payload = {
          wibBoxId: record.id,
          wibOrderId: orderId,
          enablePage: false,
          showRfidExtFlag: showRfidExt,
        };
        if (currentUser.mode === 'BINDING') {
          result = await BwibOrderApi.Rfids(payload);
        } else {
          result = await WibOrderApi.Rfids(payload);
        }

        // setCurrentBoxDetailRfidData(result.data.filter((n) => n.epc !== null));
        setCurrentBoxDetailRfidData(result.data);
      } catch (e) {}
      setBoxDetailsPreviewDrawerLoading(false);
    } else {
      setBoxAbnormalDetailsPreviewModalVisible(true);
    }
  };

  const abnormalSkuListRequest = useCallback(
    (params: Record<string, any>) => {
      params.wibOrderId = orderId;
      params.wibBoxId = currentBox.boxId;
      let result;
      if (currentUser.mode === 'BINDING') {
        result = BwibOrderApi.AbnormalLines(params);
      } else {
        result = WibOrderApi.AbnormalLines(params);
      }
      return result;
    },
    // eslint-disable-next-line
    [orderId, currentBox],
  );

  const abnormalEpcListRequest = useCallback(
    (params: Record<string, any>) => {
      params.wibOrderId = orderId;
      params.wibBoxId = currentBox.boxId;
      let result;
      if (currentUser.mode === 'BINDING') {
        result = BwibOrderApi.AbnormalTags(params);
      } else {
        result = WibOrderApi.AbnormalTags(params);
      }
      return result;
    },
    // eslint-disable-next-line
    [orderId, currentBox],
  );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId, dataType]);

  let columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      ellipsis: true,
      sorter: true,
      // valueType: 'link',
      minWidth: 180,
      auto: true,
      // onLinkClick: openDetail,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      valueType: 'codeName',
      codeDataIndex: 'operateMode',
      nameDataIndex: 'operateModeDesc',
      dataIndex: 'operateMode',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.boxSpec'),
      width: 200,
      render: (text, record) => <BoxSpecToolTip current={record} />,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.reason'),
      dataIndex: 'msg',
      width: 150,
    },
  ];

  if (enableBoxSpec) {
    if (!columns.map((n) => n.title).includes(i18n.t('global.boxSpec'))) {
      columns.splice(3, 0, {
        title: i18n.t('global.boxSpec'),
        width: 200,
        render: (text, record) => <BoxSpecToolTip current={record} />,
      });
    }
  }

  if (dataType === 'normal') {
    columns = columns.filter((n) => n.dataIndex !== 'msg');
  }

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: <BoxStatusSelect placeholder={i18n.t('global.status')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    valueType: 'action',
    fixed: 'right',
    align: 'center',
    actionConfig: [],
  };

  if (boxConfirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-green" />,
      isDisabled: (record) =>
        record.status === 'FINISHED' || orderRecord.status !== 'PROCESSING' || dataType === 'error',
      onClick: (record) => confirmBtnOnClick(record),
    });
  }

  if (boxResetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        (orderRecord.status !== 'NEW' && orderRecord.status !== 'PROCESSING') ||
        record.status !== 'UPLOADED' ||
        dataType === 'error',
      onClick: resetBtnOnClick,
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  if (localTag) {
    columns = columns.filter((n) => n.dataIndex !== 'qty');
  }

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={20}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        pagination
        // request={(data) => request(data, dataType)}
        request={fetchData}
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible
        leftToolbar={[
          <Radio.Group
            value={dataType}
            optionType="button"
            onChange={({ target: { value } }) => {
              setDataType(value);
            }}
            options={[
              {
                label: `${i18n.t('global.normal')} (${normalBoxCount})`,
                value: 'normal',
              },
              {
                label: `${i18n.t('global.abnormal')} (${abnormalBoxCount})`,
                value: 'error',
              },
            ]}
          />,
        ]}
      />
      <BoxDetailsPreviewDrawer
        title={
          <div className="flex gap-x-2">
            {i18n.t('global.boxLine')} [{currentBox.boxCode}]
            <div className="flex">
              {currentBox.boxSpecCode && (
                <>
                  [<BoxSpecToolTip current={currentBox} />]
                </>
              )}
            </div>
          </div>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
      <BoxAbnormalDetailsPreviewModal
        title={
          <div className="flex gap-x-2">
            {i18n.t('global.boxLine')} - {currentBox.boxCode}
            <div className="flex">
              {currentBox.boxSpecCode && (
                <>
                  [<BoxSpecToolTip current={currentBox} />]
                </>
              )}
            </div>
          </div>
        }
        message={currentBox.msg}
        skuListRequest={abnormalSkuListRequest}
        epcListRequest={abnormalEpcListRequest}
        open={boxAbnormalDetailsPreviewModalVisible}
        onCancel={() => setBoxAbnormalDetailsPreviewModalVisible(false)}
      />
    </>
  );
};

export default BoxTable;
