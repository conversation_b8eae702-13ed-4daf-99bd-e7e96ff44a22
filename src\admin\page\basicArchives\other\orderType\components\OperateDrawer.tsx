import { Checkbox, Form, Input, Radio, Switch } from 'antd';
import * as OrderTypeApi from 'common/api/core/OrderType';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IOrderTypeItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  orderTypeId: string | undefined;
  onSubmit: (values: IOrderTypeItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, orderTypeId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:OTHER:ORDER_TYPE');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchOrderType = useCallback(async () => {
    setLoading(true);
    try {
      const orderType: any = await OrderTypeApi.Get({
        id: orderTypeId,
      });
      orderType.categoryCodeList = orderType.categories.map((n) => n.code);
      if (typeof orderType.disabled === 'boolean') {
        orderType.status = !orderType.disabled;
      }
      form.setFieldsValue(orderType);
      setCurrent(orderType);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [orderTypeId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (orderTypeId) fetchOrderType();
      setTimeout(() => {
        if (orderTypeId) {
          nameInputRef.current?.focus();
        } else {
          codeInputRef.current?.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [visible, orderTypeId, form, fetchOrderType]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IOrderTypeItem);
    }
  };

  const plainOptions = [
    { label: i18n.t('global.warehouse'), value: 'WAREHOUSE' },
    { label: i18n.t('global.factory'), value: 'FACTORY' },
    { label: i18n.t('global.shop'), value: 'SHOP' },
  ];

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editOrderType')}
      {` [${current?.code}]`}
    </>
  ) : (
    i18n.t('global.newOrderType')
  );
  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await OrderTypeApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchOrderType();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      width={600}
      destroyOnClose
      open={visible}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!orderTypeId}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={codeInputRef} readOnly={!!current} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item name="type" label={i18n.t('global.type')} initialValue="IB">
            <Radio.Group>
              <Radio value="IB">{i18n.t('global.inboundOrder')}</Radio>
              <Radio value="OB">{i18n.t('global.outboundOrder')}</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="categoryCodeList" label={i18n.t('global.categoryCodeList')} rules={canNotBeNullRules}>
            <Checkbox.Group options={plainOptions} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
