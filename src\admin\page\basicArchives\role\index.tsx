import { Button, Input, Space } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/core/Permission';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import ExternalLinkLineIcon from 'common/assets/icons/icon-external-link-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import DisableSelect from 'common/components/Select/DisableSelect';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import AddDrawer from './components/AddDrawer';
import DetailDrawer from './components/DetailDrawer';
import { IRoleItem } from './data';
import GetEncodeModal from './components/GetEncodeModal';

const Role: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IRoleItem> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [getUrlModalVisible, setGetEncodeModalVisible] = useState(false);
  const [permission] = usePermission('A:BASE:ROLE');
  const editPermission = permission.codes.includes('EDIT');
  const assignEmpPermission = permission.codes.includes('ASSIGN_EMP');
  const assignPPermission = permission.codes.includes('ASSIGN_P');
  const deletePermission = permission.codes.includes('DELETE');
  const getRoleEncodePermission = permission.codes.includes('ENCODE');

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const deleteBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await RoleApi.DeleteRole({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const getEncodeBtnOnClick = async (record) => {
    setCurrent(record);
    setGetEncodeModalVisible(true);
  };

  const fetchData = useCallback((params) => {
    return RoleApi.ListRole(params);
  }, []);

  const handleSubmit = async (values: IRoleItem) => {
    setConfirmLoading(true);
    try {
      await RoleApi.CreateRole(values);
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
  };

  const detailsDrawerOnClose = () => {
    setDetailsVisible(false);
    powerTableRef.current?.load();
  };

  const getEncodeModalOnClose = () => {
    setGetEncodeModalVisible(false);
    setCurrent(undefined);
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'disabled',
      label: i18n.t('global.disabled'),
      inputComponent: <DisableSelect />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      width: 200,
      ellipsis: true,
      sorter: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (getRoleEncodePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.encryptedEncode'),
      icon: <ExternalLinkLineIcon className="fill-lead-blue" />,
      onClick: (record) => {
        getEncodeBtnOnClick(record);
      },
    });
  }

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {editPermission && (
              <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="ROLE"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
          onRow:
            editPermission || assignPPermission || assignEmpPermission
              ? (record) => ({
                  onClick: () => {
                    setCurrent(record);
                    setDetailsVisible(true);
                  },
                })
              : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <AddDrawer visible={visible} confirmLoading={confirmLoading} onSubmit={handleSubmit} onClose={handleCancel} />
      <DetailDrawer visible={detailsVisible} current={current} onClose={detailsDrawerOnClose} />
      <GetEncodeModal visible={getUrlModalVisible} current={current} onClose={getEncodeModalOnClose} />
    </div>
  );
};

export default Role;
