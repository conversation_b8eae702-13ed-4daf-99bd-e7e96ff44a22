import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { IEmployeeItem } from '../data';
import OperateForm, { OperateFormInnerRef } from './OperateForm';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: IEmployeeItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<OperationModalProps> = (props) => {
  const { onSubmit, onCancel, visible } = props;
  const [form] = Form.useForm();
  const operateFormRef = useRef<OperateFormInnerRef>();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      if (values.mobileArea && values.mobile) {
        values.mobile = `+(${values.mobileArea})${values.mobile}`;
      } else if (values.mobileArea) {
        values.mobile = `+(${values.mobileArea})`;
      }
      delete values.mobileArea;
      delete values.status;
      onSubmit(values as IEmployeeItem);
    }
  };

  const onClose = () => {
    if (onCancel) {
      onCancel();
      form.resetFields();
    }
  };

  useEffect(() => {
    if (visible) {
      operateFormRef.current?.focusCodeInput();
    }
  }, [visible]);

  return (
    <Drawer
      title={i18n.t('global.newEmployee')}
      destroyOnClose
      okText={i18n.t('global.apply')}
      open={visible}
      onOk={handleSubmit}
      onClose={onClose}
    >
      <OperateForm innerRef={operateFormRef} form={form} name="addForm" statusVisible={false} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddDrawer;
