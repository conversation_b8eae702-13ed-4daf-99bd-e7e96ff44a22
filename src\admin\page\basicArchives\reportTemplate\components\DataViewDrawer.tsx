import { Button, Modal, Space, Tag } from 'antd';
import { DrawerProps } from 'antd/es/drawer';
import * as FileA<PERSON> from 'common/api/file/File';
import * as TemplateApi from 'common/api/report/Template';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import CheckFillIcon from 'common/assets/icons/icon-check-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import DownloadCloudLineIcon from 'common/assets/icons/icon-download-cloud-line.svg?react';
import InformationLineIcon from 'common/assets/icons/icon-information-line.svg?react';
import Drawer from 'common/components/Drawer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import ReportHelpModal from 'common/components/ReportHelpModal';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { saveAs } from 'file-saver';
import React, { useEffect, useRef, useState } from 'react';

import { ITemplateItem } from '../data';
// import DataViewModal from './DataViewModal';
import OperationDrawer from './OperationDrawer';

export interface ITableParam {
  id: number;
  barTenderTag: boolean;
  businessCode?: string;
  module?: string;
  businessTypeCode?: string;
}

interface IDataViewDrawer {
  drawerProps: DrawerProps;
  tableParam: ITableParam;
}

const DataViewDrawer: React.FC<IDataViewDrawer> = (props) => {
  const { drawerProps, tableParam } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [operationDrawerOpen, setOperationDrawerOpen] = useState(false);
  const [dataViewModalOpen, setDataViewModalOpen] = useState(false);
  const [current, setCurrent] = useState<Record<string, any> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [modal, contextHolder] = Modal.useModal();

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    modal.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await TemplateApi.Delete({ id: record.id });
          NoticeUtil.success();
          setOperationDrawerOpen(false);
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const downBtnOnClick = async (record: Record<string, any>) => {
    const hide = NoticeUtil.processing();
    try {
      const res: any = await FileApi.Download('template', record.source);
      saveAs(res, record.source);
      hide();
    } catch (err) {
      hide();
    }
  };

  const fetchData = (params) =>
    TemplateApi.List({
      ...params,
      ...tableParam,
    });

  const operationDrawerOnSubmit = async (values: ITemplateItem) => {
    const id = current ? current.id : '';
    values.barTenderTag = false;
    if (tableParam.businessCode) {
      values.businessType = tableParam.businessCode;
    }
    if (tableParam.module) {
      values.module = tableParam.module;
    }
    if (tableParam.businessTypeCode) {
      values.templateType = tableParam.businessTypeCode;
    }
    setConfirmLoading(true);
    try {
      if (id) {
        await TemplateApi.Update({
          ...current,
          ...values,
          templateId: id,
        });
      } else {
        values.name = values.name.trim();
        await TemplateApi.Create(values);
      }
      setConfirmLoading(false);
      setOperationDrawerOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  /**
   * 更新为业务模板中默认模板
   * @param businessId
   * @param templateId
   * @param language
   */
  const updateDefault = async (businessId: any, templateId: any, language: string) => {
    const hide = NoticeUtil.processing();
    try {
      const payLoad = {
        language,
        templateId,
      };
      await TemplateApi.Default(payLoad);
      powerTableRef.current?.load();
      hide();
      NoticeUtil.success();
    } catch (e) {
      hide();
    }
  };

  const operationDrawerOnClose = () => {
    setOperationDrawerOpen(false);
    setCurrent(undefined);
  };

  useEffect(() => {
    if (drawerProps.open) {
      powerTableRef.current?.load();
    }
  }, [drawerProps?.open]);

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.name'),
      // dataIndex: 'name',
      width: 200,
      render: (text, record) => (
        <span>
          {record.name} {record.defaultTag && <Tag color="processing">{i18n.t('global.default')}</Tag>}
        </span>
      ),
    },
    {
      title: i18n.t('global.language'),
      dataIndex: 'language',
      width: 120,
      render: (text) => {
        if (text && text.includes('zh-CN')) {
          return <span title={text}>{i18n.t('global.chinese')}</span>;
        }
        return <span title={text}>English</span>;
      },
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 150,
      auto: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.setDefault'),
    icon: <CheckFillIcon className="fill-lead-green" />,
    isDisabled: (record) => record.defaultTag,
    onClick: (record) => {
      updateDefault(current?.id, record.id, record.language);
    },
  });

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.download'),
    icon: <DownloadCloudLineIcon className="fill-lead-dark" />,
    onClick: (record) => {
      downBtnOnClick(record);
    },
  });

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.delete'),
    icon: <DeleteBinLineIcon className="fill-lead-red" />,
    onClick: (record) => {
      deleteBtnOnClick(record);
    },
  });

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <>
      <Drawer title={i18n.t('global.templateManage')} footer={false} {...drawerProps}>
        <PowerTable
          initialized
          rowKey="id"
          leftToolbar={
            <Space>
              <Button
                type="primary"
                icon={<AddFillIcon className="fill-white" />}
                onClick={() => {
                  setOperationDrawerOpen(true);
                  setCurrent(undefined);
                }}
              >
                {i18n.t('global.new')}
              </Button>
            </Space>
          }
          rightToolbar={[
            <Button
              icon={<InformationLineIcon className="fill-lead-dark" />}
              onClick={() => {
                setDataViewModalOpen(true);
              }}
            />,
          ]}
          columns={tableColumns}
          innerRef={powerTableRef}
          defaultPageSize={20}
          tableProps={{
            sticky: true,
            scroll: { x: 600, y: 760 },
            onRow: (record) => ({
              onClick: () => {
                setOperationDrawerOpen(true);
                setCurrent(record);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      </Drawer>
      <OperationDrawer
        open={operationDrawerOpen}
        confirmLoading={confirmLoading}
        current={current}
        onSubmit={operationDrawerOnSubmit}
        onDelete={deleteBtnOnClick}
        onClose={operationDrawerOnClose}
      />
      <ReportHelpModal
        open={dataViewModalOpen}
        tableParam={{
          businessType: tableParam.businessCode,
          module: tableParam.module,
          templateType: tableParam.businessTypeCode,
        }}
        onCancel={() => {
          setDataViewModalOpen(false);
        }}
      />
      {contextHolder}
    </>
  );
};

export default DataViewDrawer;
