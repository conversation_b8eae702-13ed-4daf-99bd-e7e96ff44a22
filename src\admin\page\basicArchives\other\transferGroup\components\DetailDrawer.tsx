import Configuration from 'admin/page/basicArchives/other/transferGroup/components/Configuration';
import OperateForm from 'admin/page/basicArchives/other/transferGroup/components/OperateForm';
import { Form, Tabs } from 'antd';
import * as TransferGroupApi from 'common/api/core/TransferGroup';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

interface OperationModalProps {
  visible: boolean;
  groupId: string | undefined;
  onCancel: () => void;
}

const DetailDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onCancel, groupId, visible } = props;
  const operateFormRef = useRef<any>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [shipData, setShipData] = useState<Record<string, any>[]>([]);
  const [receiptData, setReceiptData] = useState<Record<string, any>[]>([]);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [activeKey, setActiveKey] = useState('INFO');
  const configurationRef = useRef<any>(null);

  const [permission] = usePermission('A:BASE:OTHER:TRANSFER-GROUP');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchTransferGroup = useCallback(async () => {
    setLoading(true);
    try {
      const transferGroup: any = await TransferGroupApi.Get({
        id: groupId,
      });
      form.setFieldsValue({
        status: !transferGroup.disabled,
        ...transferGroup,
      });
      setCurrent(transferGroup);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [groupId, form]);

  useEffect(() => {
    if (visible) {
      if (groupId) fetchTransferGroup();
      setTimeout(() => {
        operateFormRef.current?.focusNameInput();
      }, 300);
    } else {
      setActiveKey('INFO');
    }
  }, [visible, groupId, form, fetchTransferGroup]);

  const onClose = () => {
    if (onCancel) onCancel();
  };

  const handleSubmit = async () => {
    setConfirmLoading(true);
    try {
      const values = await form.validateFields();
      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
        delete values.status;
      }
      await TransferGroupApi.Update({
        // @ts-ignore
        id: current?.id,
        ...values,
      });

      const shipIds = shipData.map((item) => item.id);
      const receiptIds = receiptData.map((item) => item.id);
      await TransferGroupApi.Save({
        id: current?.id,
        shipIds,
        receiptIds,
      });

      setConfirmLoading(false);
      NoticeUtil.success();
      onClose();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const configurationOnChange = (values) => {
    setShipData(values.shipData);
    setReceiptData(values.receiptData);
  };

  const items: any[] = [
    {
      label: i18n.t('global.information'),
      key: 'INFO',
      children: <OperateForm innerRef={operateFormRef} codeReadOnly form={form} statusVisible />,
    },
    {
      label: i18n.t('global.configuration'),
      key: 'CUSTOM',
      children: <Configuration innerRef={configurationRef} current={current} onChange={configurationOnChange} />,
    },
  ];

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await TransferGroupApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchTransferGroup();
    if (configurationRef.current) {
      configurationRef.current.recover();
    }
    setActiveKey('INFO');
  };

  return (
    <Drawer
      destroyOnClose
      title={
        <>
          {i18n.t('global.transferGroupDetail')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      width={720}
      open={visible}
      onClose={onClose}
      bodyStyle={{
        paddingTop: 0,
      }}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission}
          onDelete={deleteBtnOnClick}
          onCancel={onClose}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Tabs size="small" animated={false} items={items} activeKey={activeKey} onChange={(e) => setActiveKey(e)} />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
