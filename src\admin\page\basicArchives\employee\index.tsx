import { Input, Space } from 'antd';
import * as EmployeeApi from 'common/api/core/Employee';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import Button from 'common/components/Button';
import ImportButton from 'common/components/Button/Import';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';

import RoleParagraph from 'common/components/Paragraph/RoleParagraph';
import AddDrawer from './components/AddDrawer';
import DetailDrawer from './components/DetailDrawer';
import ImportModal from './components/ImportModal';
import { IEmployeeItem } from './data';

export type EmployeeType = 'WAREHOUSE' | 'SHOP' | 'FACTORY';

const Employee: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IEmployeeItem> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [partnerPermissionDrawerVisible, setPartnerPermissionDrawerVisible] = useState(false);
  const [currentType, setCurrentType] = useState<EmployeeType>('WAREHOUSE');
  const [permission] = usePermission('A:BASE:EMPLOYEE');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DEL');
  const setPermission = permission.codes.includes('SET_P_P');
  const importPermission = permission.codes.includes('CREATE');
  const resetPermission = permission.codes.includes('RESET');

  const [importModalVisible, setImportModalVisible] = useState(false);

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };
  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  const fetchData = useCallback((params: Record<string, any>, actualTabKey: string) => {
    const payload = { ...params };
    payload.type = [actualTabKey];
    if (payload.createDateRange) {
      payload.createdStart = payload.createDateRange[0].startOf('day');
      payload.createdEnd = payload.createDateRange[1].endOf('day');
      delete payload.createDateRange;
    }
    return EmployeeApi.List(payload);
  }, []);

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await EmployeeApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const tabsOnChange = (activeKey) => {
    setCurrentType(activeKey);
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.codeOrAccount'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
  ];

  const resetBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmResetPasswordTips'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await EmployeeApi.Reset({ empId: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (error) {}
      },
    });
  };

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      ellipsis: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.allowLogin'),
      dataIndex: 'allowLogin',
      sorter: true,
      valueType: 'boolean',
      width: 160,
    },
    {
      title: i18n.t('global.allPermission'),
      dataIndex: 'allPermission',
      sorter: true,
      valueType: 'boolean',
      width: 200,
    },
    {
      title: i18n.t('global.email'),
      dataIndex: 'email',
      valueType: 'text',
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.roleGroups'),
      dataIndex: 'roleList',
      width: 160,
      render: (value) => value && <RoleParagraph roleList={value} />,
    },
    {
      title: i18n.t('global.mobile'),
      dataIndex: 'mobile',
      ellipsis: true,
      width: 120,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (resetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.resetPassword'),
      icon: <ResetIcon className="fill-lead-yellow" />,
      onClick: (record) => {
        resetBtnOnClick(record);
      },
    });
  }

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const handleSubmit = async (values: IEmployeeItem) => {
    setConfirmLoading(true);
    values.type = currentType;
    values.disabled = false;
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      await EmployeeApi.Create(values);
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const partnerPermissionDrawerOnClose = () => {
    setPartnerPermissionDrawerVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {importPermission && <ImportButton onClick={importBtnOnClick} />}
            {createPermission && (
              <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        tabDefaultActiveKey="WAREHOUSE"
        tabStatus={[
          {
            code: 'WAREHOUSE',
            name: i18n.t('global.warehouseEmployee'),
          },
          {
            code: 'SHOP',
            name: i18n.t('global.shopEmployee'),
          },
          {
            code: 'FACTORY',
            name: i18n.t('global.factoryEmployee'),
          },
        ]}
        tabsOnChange={tabsOnChange}
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="EMPLOYEE"
        tableProps={{
          sticky: {
            offsetHeader: 95,
          },
          onRow:
            editPermission || setPermission
              ? (record) => ({
                  onClick: () => {
                    setCurrent(record);
                    setPartnerPermissionDrawerVisible(true);
                  },
                })
              : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: importModalOnCancel,
          maskClosable: false,
        }}
        currentType={currentType}
        onOk={() => {
          powerTableRef.current?.load();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
      <AddDrawer visible={visible} confirmLoading={confirmLoading} onCancel={handleCancel} onSubmit={handleSubmit} />
      <DetailDrawer
        open={partnerPermissionDrawerVisible}
        onClose={partnerPermissionDrawerOnClose}
        empId={current ? current.id : ''}
      />
    </div>
  );
};

export default Employee;
