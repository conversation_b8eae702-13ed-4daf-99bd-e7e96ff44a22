import i18n from 'common/utils/I18n';

export const workflowTaskStatusFilterOptions = [
  {
    value: 'SUCCESS',
    label: i18n.t('global.succeed'),
  },
  {
    value: 'FAIL',
    label: i18n.t('global.failed'),
  },
  {
    value: 'NONE',
    label: i18n.t('global.unprocessed'),
  },
];

export const workflowTaskTypeFilterOptions = [
  {
    value: 'AD_GENERATE_WIB',
    label: i18n.t('global.adGenerateWib'),
  },
  {
    value: 'SIB_BOX_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.sibBoxAntiTheftDataSend'),
  },
  {
    value: 'SOB_BOX_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.sobBoxAntiTheftDataSend'),
  },
  {
    value: 'STS_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.stsAntiTheftDataSend'),
  },
  {
    value: 'ALARM_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.alarmGatherAntiTheftDataSend'),
  },
  {
    value: 'SM_ALARM_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smAlarmGatherAntiTheftDataSend'),
  },
  {
    value: 'DISPLAY_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.displayGatherAntiTheftDataSend'),
  },
  {
    value: 'SR_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.srGatherAntiTheftDataSend'),
  },
  {
    value: 'SRT_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.srtGatherAntiTheftDataSend'),
  },
  {
    value: 'CANCEL_ALARM_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.cancelAlarmGatherAntiTheftDataSend'),
  },
  {
    value: 'SM_CANCEL_ALARM_GATHER_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smCancelAlarmGatherAntiTheftDataSend'),
  },
  {
    value: 'WIB_REPAIR_GENERATE_PO',
    label: i18n.t('global.wibRepairGeneratePo'),
  },
  {
    value: 'SR_DATA_SEND',
    label: i18n.t('global.srDataSend'),
  },
  {
    value: 'SRT_DATA_SEND',
    label: i18n.t('global.srtDataSend'),
  },
  {
    value: 'WOB_GENERATE_WIB',
    label: i18n.t('global.wobGenerateWib'),
  },
  {
    value: 'WOB_GENERATE_SIB',
    label: i18n.t('global.wobGenerateSib'),
  },
  {
    value: 'SOB_GENERATE_WIB',
    label: i18n.t('global.sobGenerateWib'),
  },
  {
    value: 'SOB_GENERATE_SIB',
    label: i18n.t('global.sobGenerateSib'),
  },
  {
    value: 'WOB_GENERATE_IB',
    label: i18n.t('global.wobGenerateIb'),
  },
  {
    value: 'SOB_GENERATE_IB',
    label: i18n.t('global.sobGenerateIb'),
  },
  {
    value: 'SM_WIB_BOX_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smWibBoxAntiTheftDataSend'),
  },
  {
    value: 'SM_WOB_BOX_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smWobBoxAntiTheftDataSend'),
  },
  {
    value: 'SM_WTS_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smWtsAntiTheftDataSend'),
  },
  {
    value: 'SM_LR_LEND_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smLrLendAntiTheftDataSend'),
  },
  {
    value: 'SM_LR_RETURN_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smLrReturnAntiTheftDataSend'),
  },
  {
    value: 'SM_LR_DISPOSE_ANTI_THEFT_DATA_SEND',
    label: i18n.t('global.smLrDisposeAntiTheftDataSend'),
  },
];
