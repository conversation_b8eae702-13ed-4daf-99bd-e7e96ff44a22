import AppHeader from 'common/layout/AppHeader';
import { <PERSON><PERSON>, Select } from 'antd';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import * as RfidFilterApi from 'common/api/core/RfidFilter';

import AddTypeDrawer from './components/AddTypeDrawer';
import AddConditionDrawer from './components/AddConditionDrawer';
import GroupMenu from './components/GroupMenu';
import EditTypeDrawer from './components/EditTypeDrawer';
import EditConditionDrawer from './components/EditConditionDrawer';
import { filterModeMap } from './common';

const QualityCheckIssues: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:LABEL:SFR');
  const typeCreatePermission = permission.codes.includes('CREATE_TYPE');
  const conditionCreatePermission = permission.codes.includes('CREATE_CONDITION');
  const conditionEditPermission = permission.codes.includes('EDIT_CONDITION');
  const conditionDeletePermission = permission.codes.includes('DELETE_CONDITION');

  const [typeList, setTypeList] = useState([]);
  const [groupCode, setGroupCode] = useState('');

  const [addTypeDrawerOpen, setAddTypeDrawerOpen] = useState(false);
  const [addTypeDrawerLoading, setAddTypeDrawerLoading] = useState(false);

  const [addConditionOpen, setAddConditionOpen] = useState(false);
  const [addConditionLoading, setAddConditionLoading] = useState(false);

  const selectGroupItem = useRef<any>();
  const [editGroupDrawerVisible, setEditGroupDrawerVisible] = useState(false);

  const selectConditionItem = useRef<any>();
  const [editConditionOpen, setEditConditionOpen] = useState(false);

  const filterModeOptions = Object.keys(filterModeMap).map((key) => ({
    label: filterModeMap[key],
    value: key,
  }));

  const getTypeData = async () => {
    try {
      const res: any = await RfidFilterApi.TypeList({ enablePage: false });
      if (res?.data?.length === 0) return;
      setTypeList(res.data);

      if (groupCode && groupCode === res.data[0]?.code) {
        powerTableRef.current?.load();
      } else setGroupCode(res?.data?.[0]?.code);
    } catch (err) {}
  };

  const deleteConditionOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.rfidFilterTypeName} - ${filterModeMap[record.filterMode]}`,
      okType: 'danger',
      onOk: async () => {
        const payload = {
          rfidFilterConditionId: record.id,
        };
        try {
          await RfidFilterApi.ConditionDelete(payload);

          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const fetchConditionData = useCallback(
    (params) => {
      if (!groupCode) {
        return new Promise<any>((resolve) => {
          resolve({ data: [] });
        });
      }

      const item: any = typeList.find((item: any) => item.code === groupCode);

      return RfidFilterApi.ConditionList({ ...params, rfidFilterTypeId: item.id });
    },
    [groupCode, typeList],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'filterMode',
      label: i18n.t('global.filterMode'),
      inputComponent: <Select options={filterModeOptions} autoFocus />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.filterMode'),
      dataIndex: 'filterMode',
      width: 160,
      ellipsis: true,
      sorter: true,
      render: (value) => {
        return filterModeMap[value] || '';
      },
    },
    {
      title: i18n.t('global.epcLength'),
      dataIndex: 'length',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.priority'),
      dataIndex: 'priority',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.startPosition'),
      dataIndex: 'startPos',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.startPositionStr'),
      dataIndex: 'startStr',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.suffixCharacters'),
      dataIndex: 'suffix',
      ellipsis: true,
      width: 200,
      sorter: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      ellipsis: true,
      width: 200,
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      ellipsis: true,
      width: 200,
      sorter: true,
      valueType: 'dateTime',
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (conditionDeletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteConditionOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);
  const addBtnOnClick = () => {
    setAddTypeDrawerOpen(true);
  };

  const addConditionOnSubmit = async (values) => {
    if (!groupCode) return;

    const item: any = typeList.find((item: any) => item.code === groupCode);

    try {
      setAddTypeDrawerLoading(true);
      const payload = {
        ...values,
        rfidFilterTypeId: item.id,
      };

      await RfidFilterApi.ConditionCreate(payload);

      setAddConditionOpen(false);
      setAddConditionLoading(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAddTypeDrawerLoading(false);
  };
  const addConditionOnCancel = () => {
    setAddConditionOpen(false);
  };

  const onGroupMenuDelClick = (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await RfidFilterApi.TypeDelete({ rfidFilterTypeId: record.id });

          NoticeUtil.success();
          getTypeData();
        } catch (err) {}
      },
    });
  };

  const onGroupMenuChangeClick = (value) => {
    selectGroupItem.current = value;
    setEditGroupDrawerVisible(true);
  };

  const addTypeDrawerOnSubmit = async (values) => {
    try {
      setAddTypeDrawerLoading(true);
      const payload = values;
      if (typeof payload.disabled !== 'boolean') {
        payload.disabled = false;
      }

      await RfidFilterApi.TypeCreate(payload);

      setAddTypeDrawerLoading(false);
      setAddTypeDrawerOpen(false);
      NoticeUtil.success();

      getTypeData();
    } catch (e) {}
    setAddTypeDrawerLoading(false);
  };
  const addTypeDrawerOnCancel = () => {
    setAddTypeDrawerOpen(false);
  };

  const addIssuesBtnOnClick = () => {
    setAddConditionOpen(true);
  };

  const editGroupDrawerOnOk = () => {
    setEditGroupDrawerVisible(false);
    setGroupCode('');
    getTypeData();
  };

  const editConditionOnOk = () => {
    setEditConditionOpen(false);
    selectConditionItem.current = {};
    powerTableRef.current?.load();
  };

  useEffect(() => {
    powerTableRef.current?.load();
  }, [groupCode]);

  useEffect(() => {
    getTypeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <AppHeader />

      <div>
        <div className="flex h-[calc(100vh_-_160px)] gap-x-6">
          <div className="sticky top-24 box-border h-full w-[220px] shrink-0 overflow-y-auto">
            {typeCreatePermission && (
              <Button className="mb-4" block icon={<AddFillIcon className="fill-black" />} onClick={addBtnOnClick}>
                {i18n.t('global.addTagFilterType')}
              </Button>
            )}
            <GroupMenu
              code={groupCode}
              menuList={typeList}
              onClick={setGroupCode}
              onChangeClick={onGroupMenuChangeClick}
              onDelClick={onGroupMenuDelClick}
            />
          </div>
          <div className="h-full flex-1 overflow-y-auto">
            <PowerTable
              initialized
              rowKey="id"
              columns={tableColumns}
              innerRef={powerTableRef}
              quickSearchFieldsConfig={quickSearchFieldsConfig}
              searchFieldsConfig={searchFieldsConfig}
              defaultPageSize={10}
              settingToolVisible
              pagination
              autoLoad
              enableCache
              cacheKey="RFID_FILTER_RULE"
              rightToolbar={[
                conditionCreatePermission && (
                  <Button
                    type="primary"
                    icon={<AddFillIcon className="fill-white" />}
                    onClick={addIssuesBtnOnClick}
                    disabled={!groupCode}
                  >
                    {i18n.t('global.new')}
                  </Button>
                ),
              ]}
              tableProps={{
                onRow: conditionEditPermission
                  ? (record) => ({
                      onClick: () => {
                        selectConditionItem.current = record;
                        setEditConditionOpen(true);
                      },
                    })
                  : undefined,
              }}
              defaultSorter={{ field: 'created', order: 'DESCEND' }}
              request={fetchConditionData}
            />
          </div>
        </div>
        <AddTypeDrawer
          onSubmit={addTypeDrawerOnSubmit}
          open={addTypeDrawerOpen}
          onCancel={addTypeDrawerOnCancel}
          loading={addTypeDrawerLoading}
        />
        <EditTypeDrawer
          visible={editGroupDrawerVisible}
          current={selectGroupItem.current}
          onOk={editGroupDrawerOnOk}
          onClose={() => setEditGroupDrawerVisible(false)}
        />
        <AddConditionDrawer
          open={addConditionOpen}
          onSubmit={addConditionOnSubmit}
          onCancel={addConditionOnCancel}
          loading={addConditionLoading}
        />
        <EditConditionDrawer
          open={editConditionOpen}
          current={selectConditionItem.current}
          onOk={editConditionOnOk}
          onClose={() => setEditConditionOpen(false)}
        />
      </div>
    </div>
  );
};

export default QualityCheckIssues;
