import BoxSpec from 'admin/page/basicArchives/other/boxSpec';
import BusinessModel from 'admin/page/basicArchives/other/businessModel';
import Dict from 'admin/page/basicArchives/other/dict';
import ExtField from 'admin/page/basicArchives/other/extField';
import OrderType from 'admin/page/basicArchives/other/orderType';
import TransferGroup from 'admin/page/basicArchives/other/transferGroup';
import VersionUpdate from 'admin/page/basicArchives/other/versionUpdate';
import TabsMenu from 'common/components/TabsMenu';
import AppHeader from 'common/layout/AppHeader';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useState } from 'react';

// export interface ProdAttributesProps {
//   children?: React.ReactNode;
// }

const currentMenuCode = 'A:BASE:OTHER';

const Other: React.FC = () => {
  // const { children } = props;
  // const { state } = useContext<TGlobalContext>(GlobalContext);
  // const { localPermissionList } = state;
  // 获取当前菜单编码的所有权限
  const [permission] = usePermission(currentMenuCode);
  // 筛选当前编码的有权限的子集菜单
  const permissionMenu = permission.allChild.filter(
    (item: Record<string, any>) => item.depth === 4 && item.code !== 'A:BASE:OTHER:AREA',
  );
  // const navigate = useNavigate();
  const [activeMenuCode, setActiveMenuCode] = useState('');
  const tabsMenuOnChange = (record) => {
    setActiveMenuCode(record.code);
    // navigate(path);
  };

  useEffect(() => {
    if (permissionMenu.length > 0 && !activeMenuCode) {
      setActiveMenuCode(permissionMenu[0].code);
    }
    // eslint-disable-next-line
  }, [permissionMenu, activeMenuCode]);

  const elementMap: Record<string, any> = {
    'A:BASE:OTHER:BOX_SPEC': <BoxSpec />,
    'A:BASE:OTHER:ORDER_TYPE': <OrderType />,
    'A:BASE:OTHER:BUSINESS_MODEL': <BusinessModel />,
    'A:BASE:OTHER:EXT_FIELD': <ExtField />,
    'A:BASE:OTHER:TRANSFER-GROUP': <TransferGroup />,
    'A:BASE:OTHER:B:DICT': <Dict />,
    'A:BASE:OTHER:O:VU': <VersionUpdate />,
  };

  return (
    <div>
      <AppHeader />
      <div className="flex h-[calc(100vh_-_160px)] gap-x-6">
        <div className="sticky top-24 box-border h-full w-[200px] shrink-0 cursor-pointer overflow-y-auto">
          <TabsMenu code={currentMenuCode} menuList={permissionMenu} onClick={tabsMenuOnChange} />
        </div>
        <div className="h-full flex-1 overflow-y-auto">{elementMap[activeMenuCode]}</div>
      </div>
    </div>
  );
};

export default Other;
