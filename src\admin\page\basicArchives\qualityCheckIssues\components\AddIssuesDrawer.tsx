import { Form, Input } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

interface AddDrawerProps {
  open: boolean;
  saving?: boolean;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: (e: React.MouseEvent<HTMLElement>) => void;
}

const AddIssuesDrawer: React.FC<AddDrawerProps> = (props) => {
  const { onSubmit, saving, open, onCancel } = props;
  const [form] = Form.useForm();
  const codeInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
        }
        onSubmit(values);
      })
      .catch(() => {});
  };

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      setTimeout(() => {
        codeInputRef.current?.focus({ cursor: 'end' });
      }, 300);
    }
  }, [open, form]);

  return (
    <Drawer
      title={i18n.t('global.newQualityCheckIssues')}
      okButtonProps={{
        loading: saving,
      }}
      okText={i18n.t('global.apply')}
      onOk={handleOk}
      onClose={onCancel}
      open={open}
    >
      <Form form={form} layout="vertical" name="basic">
        <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
          <Input ref={codeInputRef} maxLength={20} />
        </Form.Item>
        <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
          <Input maxLength={50} />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default AddIssuesDrawer;
