import { Form } from 'antd';
import Modal from 'common/components/Modal';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import i18n from 'common/utils/I18n';
import React from 'react';
import { useParams } from 'react-router-dom';

import ConfirmForm from './ConfirmForm';

interface IConfirmModalProps {
  title: string;
  visible: boolean;
  confirmLoading: boolean;
  onCancel: () => void;
  orderRecord: { [key: string]: any };
  form: any;
  onSave: (values: Record<string, any>) => void;
}

const ConfirmModal: React.FC<IConfirmModalProps> = (props) => {
  const { visible, onCancel, form, orderRecord, onSave, confirmLoading, title } = props;
  const params: any = useParams();

  const onOk = () => {
    form.submit();
  };

  const onFinish = async (values) => {
    // values.fmOrderBoxIds = selectedFmOrderBoxIds;
    values.fadOrderId = params.id;
    onSave(values);
  };

  return (
    <>
      <Modal
        title={title}
        open={visible}
        confirmLoading={confirmLoading}
        width={700}
        destroyOnClose
        onOk={onOk}
        onCancel={onCancel}
      >
        <div className="flex-col rounded-md">
          <div className="mb-6 flex gap-x-6 bg-lead-light-bg p-5">
            <SendReceiveLayout
              className="flex-auto"
              left={
                <PartnerViewer
                  partnerType="FACTORY"
                  label={i18n.t('global.delivery')}
                  partnerCode={orderRecord.partnerCode}
                  partnerName={orderRecord.partnerName}
                  warehouseCode={orderRecord.warehouseCode}
                  warehouseName={orderRecord.warehouseName}
                />
              }
              right={
                <PartnerViewer
                  partnerType={orderRecord.toPartnerType}
                  label={i18n.t('global.receiver')}
                  partnerCode={orderRecord.toPartnerCode}
                  partnerName={orderRecord.toPartnerName}
                  warehouseCode={orderRecord.toWarehouseCode}
                  warehouseName={orderRecord.toWarehouseName}
                />
              }
            />
          </div>
          <Form>
            <Form.Item label={i18n.t('global.expTime')}>{orderRecord.expTime}</Form.Item>
            {orderRecord.remark && <Form.Item label={i18n.t('global.remark')}>{orderRecord.remark}</Form.Item>}
          </Form>
        </div>
        <div className="rounded-md border">
          <div className="border-b border-lead-light-slate bg-lead-light-bg px-3 py-2 text-sm font-semibold leading-tight text-slate-700">
            {i18n.t('global.informationCheck')}
          </div>
          <div className="p-3">
            <ConfirmForm form={form} onFinish={onFinish} orderRecord={orderRecord} />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ConfirmModal;
