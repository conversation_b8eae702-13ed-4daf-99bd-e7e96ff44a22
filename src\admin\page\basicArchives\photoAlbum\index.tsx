/**
 * Create by codegen at 2022-06-28
 * 相册
 */
import { Radio } from 'antd';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';
import { usePermission } from 'common/utils/Permission';

import CommonAlbum from './component/CommonAlbum';
import ShopAlbum from './component/ShopAlbum';

type RadioKeyType = 'SHOP' | 'PUBLIC';

const PhotoAlbum: React.FC = () => {
  const [radioKey, setRadioKey] = useState<RadioKeyType>('SHOP');

  const [permission] = usePermission('A:BASE:ALBUM');
  const shopPermission = permission.codes.includes('STORE');
  const commonPermission = permission.codes.includes('COMMON');

  const radioOnChange = (e) => {
    setRadioKey(e.target.value);
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Radio.Group defaultValue="SHOP" buttonStyle="solid" onChange={radioOnChange}>
            {shopPermission && <Radio.Button value="SHOP">{i18n.t('global.storeAlbum')}</Radio.Button>}
            {commonPermission && <Radio.Button value="PUBLIC">{i18n.t('global.commonAlbum')}</Radio.Button>}
          </Radio.Group>
        }
      />

      {radioKey === 'SHOP' ? <ShopAlbum /> : <CommonAlbum />}
    </div>
  );
};

export default PhotoAlbum;
