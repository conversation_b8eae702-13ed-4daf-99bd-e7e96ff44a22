.content {
    width: 200px;
    height: 200px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
}

.content :global .ant-image {
    width: 100%;
}

.image {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    height: 160px;
    object-fit: cover;
    vertical-align: bottom;
}

.footer {
    border-top: 1px solid #d9d9d9;
    height: 40px;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
    padding: 0 10px 0 10px;
    display: flex;
    justify-content: center;
    flex-direction: column-reverse;
}

.album {
    display: flex;
}

.masking {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    /*background-color: black;*/
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    z-index: 9;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 20px;
    opacity: 0; /* 初始状态为完全透明 */
    transition: opacity 0.5s; /* 添加过渡效果 */
}

.masking :global .ant-typography {
    color: #FFFFFF!important;
    position: absolute;
    padding: 0 20px;
    bottom: 0;
}

.operation :global .ant-btn {
    color: #FFFFFF;
}

.defaultBtn {
    background-color: #52c41a!important;
    border-color: #52c41a!important;
}

.radioIcon {
    color: #108ee9;
    font-weight: bold;
    font-size: 16px;
}

.icon {
    background-color: #FFFFFF;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #FFFFFF;
    position: absolute;
    top: 10px;
    left: 10px;
}

.disable {
    position: absolute;
    top: 8px;
    left: 10px;
    background-color: #d9d9d9 !important;
}