#!/bin/bash
# 便捷发布程序脚本
# 功能：创建一个打上版本号的程序压缩文件
# 提示：本脚本依赖类unix执行环境，如需在windows下执行，建议方案是：安装gnu-sed gnu-zip 并在git bash下执行

# TODO: save ftp config to .env file (user, password, ip, path)
# FILE=.env
# if test -f "$FILE"; then
#     echo "$FILE exists."
# fi

PROJECT_PREFIX=IC-WEB
RELEASE_PATH=../releases
VERSION_FILE_PATH=./script/.version
LOG_FILE_PATH=./script/.log
ARG1=$1

FTP_USER=
FTP_PASS=
FTP_IP=

SCRIPT_FILE_PATH=$(dirname "$0")
echo "Script path: ${SCRIPT_FILE_PATH}"

ENV_FILE_DIR="${SCRIPT_FILE_PATH}/.env"
echo ".env file path: ${ENV_FILE_DIR}"

# load env variables
if [ -f "${ENV_FILE_DIR}" ];
then
  source "${ENV_FILE_DIR}"
  echo "Yeah, .env file just right there!"
else
  echo "Oh no, .env file does not exist!"
exit
fi

CUR_VER=$( cat "${VERSION_FILE_PATH}")
IFS='.' read -r -a CUR_VER_ARR <<< "${CUR_VER}"
MAIN_VER="${CUR_VER_ARR[0]}"
SUB_VER="${CUR_VER_ARR[1]}"

GIT_VER=$(git rev-parse --verify HEAD | cut -c1-8)
# echo "Git version is: "${GIT_VER}

function showHelp
{
echo "
NAME:
    release.sh - To release a new version ${PROJECT_PREFIX}

USAGE:
    release.sh [options...]

Options:
   -b,--build              build project
   -v,--version            specify version number (ignore incrementing version number options)
   -m,--incre_main_version incrementing main version number
   -s,--incre_sub_version  incrementing sub version number
   -u,--upload_to_server   upload to development test server

╭─────────── Version Number Explanation ─────────────╮
│                                                    │
│           2.1.xxxxxxxx                             │
│          /   \\    \\                                │
│         /     \\  Git hash version (First 8 chars)  │
│        /       \\                                   │
│       /    Sub version number                      │
│      /                                             │
│  Main version number                               │
│                                                    │
╰────────────────────────────────────────────────────╯
"
}

if [ "$ARG1" == "-h" ];
then
  showHelp
exit
fi






HARD_CODE_VERSION=false
INCRE_MAIN_VERSION=false
INCRE_SUB_VERSION=false
UPLOAD_TO_SERVER=false
BUILD=false

POSITIONAL_ARGS=()
while [[ $# -gt 0 ]]; do
  case $1 in
    -v|--version)
      HARD_CODE_VERSION=$2
      shift # past argument
      ;;
    -m|--incre_main_version)
      INCRE_MAIN_VERSION=true
      shift # past argument
      ;;
    -s|--incre_sub_version)
      INCRE_SUB_VERSION=true
      shift # past argument
      ;;
    -u|--upload_to_server)
      UPLOAD_TO_SERVER=true
      shift # past argument
      ;;
    -b|--build)
      BUILD=true
      shift # past argument
      ;;
    -*|--*)
      echo "Unknown option $1"
      exit 1
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

set -- "${POSITIONAL_ARGS[@]}" # restore positional parameters

if [ "$INCRE_MAIN_VERSION" == "true" ];
then
  # increment main version number
  MAIN_VER=$((MAIN_VER+1))
  SUB_VER=1
elif [ "$INCRE_SUB_VERSION" == "true" ];
then
  # increment sub version number
  SUB_VER=$((SUB_VER+1))
# else
#   echo "At least specify one version number to increment"
#   exit
fi

# echo "new main version: ${MAIN_VER}"
# echo "new sub version: ${SUB_VER}"

# exit

echo "👏👏👏"
echo "Just wait for second ..."
echo ""

if [ "$BUILD" == "true" ];
then
  echo "📦 Packing..."
  # run build task
  npm run build
fi

# update version to cache file
echo "${MAIN_VER}.${SUB_VER}" > "$VERSION_FILE_PATH"

cd dist

GIT_TAG="v${MAIN_VER}.${SUB_VER}"

if [ "$INCRE_MAIN_VERSION" == "true" ] || [ "$INCRE_SUB_VERSION" == "true" ];
then
  echo "🌟 new git tag: ${GIT_TAG}"
  git tag "${GIT_TAG}"
  git push --tags
fi

DATE=$(date '+%Y%m%d')
DATE_TIME=$(date '+%Y.%m.%d-%H:%M:%S')

VER=v${MAIN_VER}.${SUB_VER}.${GIT_VER}

if [ "$HARD_CODE_VERSION" != "false" ];
then
  VER=v${HARD_CODE_VERSION}.${GIT_VER}
fi

FILE_NAME=${PROJECT_PREFIX}_${DATE}_${VER}.zip

echo "new version: ${VER}"

if command -v gsed &> /dev/null; then
  gsed -i s/'$(VERSION)'/${VER}/g ./admin.html
  gsed -i s/'$(VERSION)'/${VER}/g ./warehouse.html
  gsed -i s/'$(VERSION)'/${VER}/g ./store.html
  gsed -i s/'$(VERSION)'/${VER}/g ./factory.html
  gsed -i s/'$(VERSION)'/${VER}/g ./sample.html
elif command -v sed &> /dev/null; then
  sed -i s/'$(VERSION)'/${VER}/g ./admin.html
  sed -i s/'$(VERSION)'/${VER}/g ./warehouse.html
  sed -i s/'$(VERSION)'/${VER}/g ./store.html
  sed -i s/'$(VERSION)'/${VER}/g ./factory.html
  sed -i s/'$(VERSION)'/${VER}/g ./sample.html
else
  echo "sed or gsed not found, please install gnu-sed"
fi

zip -r -q ${FILE_NAME} ./*

mkdir -p ${RELEASE_PATH}
mv ${PROJECT_PREFIX}*.zip ${RELEASE_PATH}


# upload to development test server
if [ "$UPLOAD_TO_SERVER" == "true" ];
then
  echo "Uploading to development server...";
lftp -u ${FTP_USER},${FTP_PASS} sftp://${FTP_IP} << EOF
cd plugins/nginx/web/ic/
rm -r -f ./img/
rm -r -f ./chunks/
rm -r -f ./assets/
rm -r -f ./_admin/
rm -r -f ./_warehouse/
rm -r -f ./_store/
rm -r -f ./_factory/
rm -r -f ./_sample/
mrm ./*
mirror -R ./ ./
bye
EOF
fi


echo "🎉🎉🎉"
echo "Go to check the following file:"
echo "Release path: ${RELEASE_PATH}/${FILE_NAME}"
echo ""
echo "Great! All Done!"

cd ..

# write release log
echo "[$DATE_TIME] -> $VER" >> ${LOG_FILE_PATH}
echo "log written successful!"

# send notification
which "notify-send" > /dev/null 2>&1
if [ $? -eq 0 ]; then
  echo "exist!!!"
  notify-send -t 0 '🎉🎉🎉 Compiled successfully 🎉🎉🎉' "[${PROJECT_PREFIX}]\n$(date '+%Y.%m.%d %H:%M:%S')\n${RELEASE_PATH}/${FILE_NAME}"
else
  echo "not exist!!!"
fi
