import * as SpobOrderApi from 'common/api/sample/SpobOrder';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback } from 'react';

export const useCommonFn = () => {
  const confirm = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          onOk: async () => {
            try {
              await SpobOrderApi.Confirm({
                id: orderId,
              });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  const cancel = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              await SpobOrderApi.Cancel({ id: orderId });
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  return { confirm, cancel };
};
