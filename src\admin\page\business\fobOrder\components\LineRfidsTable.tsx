import { Radio } from 'antd';
import * as BfobOrderApi from 'common/api/factory/BfobOrder';
import * as FobOrderApi from 'common/api/factory/FobOrder';
import PowerTable, { PowerTableColumnsType } from 'common/components/PowerTable';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useState } from 'react';

export interface LineRfidsTablePops {
  orderId: string;
  powerTableRef: any;
}
const LineRfidsTable: React.FC<LineRfidsTablePops> = (props) => {
  const { orderId, powerTableRef } = props;
  const [orderStatus, setOrderStatus] = useState('all');
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const statusRadioOnChange = (data) => {
    setOrderStatus(data.target.value);
  };

  const fetchLinesData = useCallback(
    async (data) => {
      if (!orderId) return Promise.reject();
      const payload: any = { ...data };
      payload.fobOrderId = orderId;
      if (orderStatus === 'processing') {
        payload.qtyContrastMode = 'LT';
      }

      if (orderStatus === 'finish') {
        payload.qtyContrastMode = 'GE';
      }
      let record;
      if (currentUser.mode === 'BINDING') {
        record = await BfobOrderApi.Lines(payload);
      } else {
        record = await FobOrderApi.Lines(payload);
      }
      if (record.data.length !== 0) {
        record.data.forEach((item) => {
          item.diffQty = item.qty - item.actQty;
          if (item.diffQty < 0) {
            item.diffQty = 0;
          }
        });
      }

      return record;
    },
    [orderId, orderStatus, currentUser.mode],
  );

  useEffect(() => {
    if (orderStatus) {
      powerTableRef.current?.load();
    }
    // eslint-disable-next-line
  }, [orderStatus]);

  const rfidsTableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      width: 180,
      fixed: 'left',
      valueType: 'text',
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'skuName',
      width: 200,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 200,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} disturbTag={record.disturbTag} rfidTag={record.rfidTag} />
      ),
    },
    {
      title: i18n.t('global.colors'),
      dataIndex: 'colorName',
      width: 120,
    },
    {
      title: i18n.t('global.sizes'),
      dataIndex: 'sizeName',
      valueType: 'number',
      width: 120,
    },
    {
      title: i18n.t('global.specs'),
      dataIndex: 'specName',
      minWidth: 120,
      auto: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.boxedCount'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.unIntoBox'),
      valueType: 'number',
      dataIndex: 'diffQty',
      width: 120,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      leftToolbar={
        <Radio.Group defaultValue="all" onChange={statusRadioOnChange}>
          <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
          <Radio.Button value="processing">{i18n.t('global.unFinish')}</Radio.Button>
          <Radio.Button value="finish">{i18n.t('global.finished')}</Radio.Button>
        </Radio.Group>
      }
      columns={rfidsTableColumns}
      innerRef={powerTableRef}
      refreshBtnVisible={false}
      autoLoad
      tableProps={{ sticky: true }}
      request={fetchLinesData}
    />
  );
};

export default LineRfidsTable;
