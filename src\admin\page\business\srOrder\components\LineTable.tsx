import { Radio } from 'antd';
import * as BsrOrder<PERSON><PERSON> from 'common/api/shop/BsrOrder';
import * as SrOrder<PERSON>pi from 'common/api/shop/SrOrder';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface LineTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

export interface LineTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<LineTableInnerRef | undefined>;
}

const LineTable: React.FC<LineTableProps> = (props) => {
  const { innerRef } = props;
  const status = useRef<'all' | 'processing' | 'finish'>('all');
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { orderCode } = orderRecord;
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      const payload: any = { ...params };
      payload.orderCode = orderCode;

      let record;
      if (currentUser.mode === 'BINDING') {
        record = await BsrOrderApi.RfidsList(payload);
      } else {
        record = await SrOrderApi.RfidsList(payload);
      }

      return record;
    },
    // eslint-disable-next-line
    [orderCode],
  );

  const statusRadioOnChange = (data) => {
    status.current = data.target.value;
    powerTableRef.current?.load();
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderCode) {
      powerTableRef.current?.load();
    }
  }, [orderCode]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      valueType: 'text',
      dataIndex: 'barcode',
      fixed: 'left',
      width: 180,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: 'epc',
      valueType: 'text',
      dataIndex: 'epc',
      fixed: 'left',
      width: 330,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'skuName',
      valueType: 'text',
      ellipsis: true,
      auto: true,
      minWidth: 180,
      tooltip: true,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 160,
      render: (prodCode, record) => (
        <ProdCodeRowText prodCode={prodCode} rfidTag={record.rfidTag} disturbTag={record.disturbTag} />
      ),
    },
    {
      title: i18n.t('global.color'),
      valueType: 'codeName',
      dataIndex: 'colorCode',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      valueType: 'codeName',
      dataIndex: 'sizeCode',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 180,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      innerRef={powerTableRef}
      columns={columns}
      refreshBtnVisible
      defaultSorter={{ field: 'barcode', order: 'DESCEND' }}
      request={fetchData}
      defaultPageSize={20}
      pagination
      leftToolbar={
        <Radio.Group defaultValue="all" onChange={statusRadioOnChange}>
          <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
          <Radio.Button value="processing">{i18n.t('global.unFinish')}</Radio.Button>
          <Radio.Button value="finish">{i18n.t('global.finished')}</Radio.Button>
        </Radio.Group>
      }
    />
  );
};

export default LineTable;
