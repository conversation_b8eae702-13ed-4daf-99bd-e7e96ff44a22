/** @type {import('tailwindcss').Config} */
import defaultConfig from './tailwind.config.js';

export default {
  ...defaultConfig,
  content: ['./index.html', './src/warehouse/**/*.{js,ts,jsx,tsx}', './src/common/**/*.{js,ts,jsx,tsx}'],
  // theme: {
  //   ...defaultConfig.theme,
  //   extend: {
  //     ...defaultConfig.theme.extend,
  //     fontSize: {
  //       xs: ['0.84375rem', { lineHeight: '1rem' }], // 原来 0.75rem * 1.125
  //       sm: ['0.984375rem', { lineHeight: '1.25rem' }], // 原来 0.875rem * 1.125
  //       base: ['1.125rem', { lineHeight: '1.5rem' }], // 原来 1rem * 1.125
  //       lg: ['1.265625rem', { lineHeight: '1.75rem' }], // 原来 1.125rem * 1.125
  //       xl: ['1.40625rem', { lineHeight: '1.75rem' }], // 原来 1.25rem * 1.125
  //       '2xl': ['1.6875rem', { lineHeight: '2rem' }], // 原来 1.5rem * 1.125
  //       '3xl': ['2.109375rem', { lineHeight: '2.25rem' }], // 原来 1.875rem * 1.125
  //     },
  //   },
  // },
};
