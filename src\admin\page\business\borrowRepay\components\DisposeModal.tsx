import { Alert, Col, Form, Input, List, Row } from 'antd';
import Button from 'common/components/Button';
import Modal from 'common/components/Modal';
import PowerTable, { PowerTableColumnsType } from 'common/components/PowerTable';
import DisposeTypeSelect from 'common/components/Select/DisposeTypeSelect';
import EmployeeSelect from 'common/components/Select/EmployeeSelect';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef, useState } from 'react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import * as NoticeUtil from 'common/utils/Notice';

interface FormValues {
  lenderId: string;
  planReturnDate: string;
  remark: string;
}

interface DisposeModalProps {
  open?: boolean;
  selectedRowList: Record<string, any>[];
  confirmLoading?: boolean;
  onDelete?: (id: string, smEpc: string) => void;
  onSubmit: (values: FormValues) => void;
  onCancel: () => void;
}

const DisposeModal: React.FC<DisposeModalProps> = (props) => {
  const { open, confirmLoading, onSubmit, onCancel, onDelete, selectedRowList } = props;
  const [form] = Form.useForm();
  const employeeSelectRef = useRef<any>();
  const [tableData, setTableData] = useState<Record<string, any>[]>([]);
  const [epcCount, setEpcCount] = useState<number>(0);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const onFinish = (values) => {
    const smEpcList: string[] = [];
    tableData.forEach((n) => {
      smEpcList.push(...n.epcList.map((n) => n.smEpc));
    });
    if (smEpcList.length === 0) {
      NoticeUtil.error(i18n.t('global.pleaseSelectDisposeEpc'));
      return;
    }
    if (onSubmit) {
      onSubmit({ ...values, smEpcList: [...new Set(smEpcList)] });
    }
  };

  const modalOnOk = () => {
    form.submit();
  };

  useEffect(() => {
    if (open && selectedRowList.length > 0) {
      const codeMap: Record<string, any> = {};
      let count = 0;
      const newExpandedRowKeys = JSON.parse(JSON.stringify(expandedRowKeys));
      selectedRowList.forEach((item) => {
        if (item.status === 'DISPOSED') return;
        if (!codeMap[item.code]) {
          codeMap[item.code] = {
            code: item.code,
            lendDate: item.lendDate,
            lenderName: item.lenderName,
            count: 0,
            epcList: [],
          };
        }
        if (!codeMap[item.code].epcList.some((n) => n.smEpc === item.smEpc)) {
          codeMap[item.code].epcList.push(item);
          codeMap[item.code].count += 1;
        }
        if (item.msg && !newExpandedRowKeys.includes(item.code)) {
          newExpandedRowKeys.push(item.code);
        }
        count += 1;
      });

      setEpcCount(count);
      setExpandedRowKeys(newExpandedRowKeys);
      setTableData(Object.values(codeMap));
    } else {
      setTableData([]);
    }
  }, [open, expandedRowKeys, selectedRowList]);

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        employeeSelectRef.current?.focus();
      }, 300);
    } else {
      form.resetFields();
      setExpandedRowKeys([]);
      setTableData([]);
      setEpcCount(0);
    }
  }, [form, open]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.batchCode'),
      dataIndex: 'code',
      valueType: 'text',
      width: 120,
    },
    {
      title: i18n.t('global.lendDate'),
      dataIndex: 'lendDate',
      valueType: 'dateTime',
      width: 180,
    },
    {
      title: i18n.t('global.borrower'),
      dataIndex: 'lenderName',
      valueType: 'text',
      width: 100,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'count',
      valueType: 'text',
      width: 100,
    },
  ];

  return (
    <Modal
      title={`${i18n.t('global.dispose')}${epcCount > 0 ? `(${epcCount})` : ''}`}
      open={open}
      width={800}
      onOk={modalOnOk}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
    >
      <div className="mb-4 flex flex-col gap-4">
        <Alert showIcon type="warning" message={i18n.t('global.disposeLoanedEpc')} />
        <PowerTable
          initialized
          rowKey="code"
          columns={columns}
          refreshBtnVisible={false}
          tableProps={{
            size: 'small',
            dataSource: tableData,
            expandable: {
              expandedRowKeys,
              onExpand: (expanded, record) => {
                if (expanded) {
                  expandedRowKeys.push(record.code);
                  setExpandedRowKeys(expandedRowKeys);
                } else {
                  setExpandedRowKeys(expandedRowKeys.filter((n) => n !== record.code));
                }
              },
              expandedRowRender: (record) => (
                <List
                  size="small"
                  bordered={false}
                  dataSource={record.epcList}
                  renderItem={(item: Record<string, any>) => (
                    <List.Item>
                      <div className="flex w-full items-center justify-between">
                        <div>
                          {item.smEpc}
                          {item.msg ? <span className="text-lead-red">({item.msg})</span> : ''}
                        </div>
                        {item.msg && (
                          <Button
                            icon={<DeleteBinLineIcon className="fill-lead-red" />}
                            onClick={() => {
                              if (onDelete) onDelete(item.id, item.smEpc);
                            }}
                          />
                        )}
                      </div>
                    </List.Item>
                  )}
                />
              ),
            },
          }}
        />
      </div>
      <Form
        layout="vertical"
        form={form}
        name="saveModal"
        initialValues={{
          allowTakeStock: true,
          returnTag: false,
        }}
        onFinish={onFinish}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="disposerId" label={i18n.t('global.disposer')} rules={canNotBeNullRules}>
              <EmployeeSelect ref={employeeSelectRef} types={['WAREHOUSE']} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="typeId" label={i18n.t('global.disposeType')} rules={canNotBeNullRules}>
              <DisposeTypeSelect />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default DisposeModal;
