import { PlusOutlined } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, Space } from 'antd';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import Modal from 'common/components/Modal';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import i18n from 'common/utils/I18n';
import React from 'react';
import moment from 'moment';
import type { RangePickerProps } from 'antd/es/date-picker';

interface BatchAddModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values, range?) => void;
  onCancel: () => void;
  form: any;
}

const BatchAddModal: React.FC<BatchAddModalProps> = (props) => {
  const { visible, confirmLoading, onSubmit, onCancel, form } = props;
  const { TextArea } = Input;

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  const handleOk = () => {
    form.submit();
  };

  const onFinish = (value) => {
    value.tsOrder.forEach((order) => {
      order.startTime = order.startTime.format().split('+')[0].replace('T', ' ');
    });
    onSubmit(value.tsOrder);
  };

  // eslint-disable-next-line arrow-body-style
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    return current && current < moment().endOf('day');
  };

  const range = (start: number) => {
    const result: any = [];
    for (let i = start; i >= 0; i--) {
      result.push(i);
    }
    return result;
  };

  const disabledDateTime = () => ({
    disabledHours: () => range(moment().hour() - 1),
    disabledMinutes: () => range(moment().minutes() - 1),
    disabledSeconds: () => [],
  });

  return (
    <Modal
      title={i18n.t('global.batchCreateAllModeTsOrder')}
      open={visible}
      okButtonProps={{
        loading: confirmLoading,
      }}
      width={750}
      bodyStyle={{
        padding: '24px',
        minHeight: '420px',
      }}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose
      maskClosable={false}
    >
      <Form name="tsOrderBatchCreate" form={form} onFinish={onFinish} autoComplete="off">
        <Form.List name="tsOrder">
          {(fields, { add, remove }) => (
            <div>
              {fields.map((field) => {
                return (
                  <Space
                    key={`${field.key}-${new Date()}`}
                    style={{
                      display: 'flex',
                    }}
                    align="start"
                  >
                    <Form.Item
                      style={{ width: 200, marginBottom: 8 }}
                      {...field}
                      name={[field.name, 'partnerId']}
                      rules={[
                        {
                          required: true,
                          message: i18n.t('global.fieldCanNotBeNull'),
                        },
                      ]}
                    >
                      <PartnerSelect types={['WAREHOUSE']} sourceType="PERMISSION" />
                    </Form.Item>
                    <Form.Item
                      style={{ width: 200, marginBottom: 8 }}
                      {...field}
                      name={[field.name, 'startTime']}
                      rules={[
                        {
                          required: true,
                          message: i18n.t('global.fieldCanNotBeNull'),
                        },
                      ]}
                    >
                      <DatePicker
                        showTime
                        disabledDate={disabledDate}
                        disabledTime={disabledDateTime}
                        placeholder={i18n.t('global.pleaseSelectTime')}
                      />
                    </Form.Item>
                    <Form.Item
                      style={{ width: 200, marginBottom: 8 }}
                      {...field}
                      name={[field.name, 'remark']}
                      rules={[{ required: false }]}
                    >
                      <TextArea placeholder={i18n.t('global.remark')} autoSize={{ minRows: 1 }} />
                    </Form.Item>
                    <Button
                      onClick={() => {
                        remove(field.name);
                      }}
                    >
                      <DeleteBinLineIcon className="fill-lead-red" />
                    </Button>
                  </Space>
                );
              })}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => {
                    add();
                  }}
                  block
                >
                  <PlusOutlined /> {i18n.t('global.add')}
                </Button>
              </Form.Item>
            </div>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default BatchAddModal;
