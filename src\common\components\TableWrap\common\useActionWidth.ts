import { useEffect, useState } from 'react';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import { calcActionWidthByScale, getCurrentScaleFactor } from './cssUtils';

export const useActionWidth = () => {
  const [scaleFactor, setScaleFactor] = useState(1);

  useEffect(() => {
    const updateScaleFactor = () => {
      const factor = getCurrentScaleFactor();
      setScaleFactor(factor);
    };

    // 初始化
    updateScaleFactor();

    // 监听属性变化（当用户切换大字体模式时）
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-ui-scale') {
          setTimeout(updateScaleFactor, 0); // 延迟一帧确保CSS已应用
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-ui-scale'],
    });

    return () => observer.disconnect();
  }, []);

  const calcActionWidth = (actionCount: number, size: SizeType): number => {
    return calcActionWidthByScale(actionCount, size);
  };

  return { calcActionWidth, scaleFactor };
};
