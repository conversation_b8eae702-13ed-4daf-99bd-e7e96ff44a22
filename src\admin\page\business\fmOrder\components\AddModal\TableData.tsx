import React, { useReducer } from 'react';

export const TableDataContext = React.createContext<any>([]);

// reducer
export const UPDATE_PROD = 'UPDATE_PROD';
export const UPDATE_SKU = 'UPDATE_SKU';
const reducer = (state, action) => {
  const newState = { ...state };
  switch (action.type) {
    case UPDATE_PROD:
      newState.prod = action.prod;
      return newState;
    case UPDATE_SKU:
      newState.sku = action.sku;
      return newState;
    default:
      return newState;
  }
};

interface TableDataProps {
  children: React.ReactNode;
}
export const TableData: React.FC<TableDataProps> = (props) => {
  const [contextData, saveContextData] = useReducer(reducer, {
    prod: [],
    sku: [],
  });
  return (
    <TableDataContext.Provider value={{ contextData, saveContextData }}>{props.children}</TableDataContext.Provider>
  );
};
