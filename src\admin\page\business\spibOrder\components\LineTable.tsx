import { Radio } from 'antd';
import * as SpibOrderApi from 'common/api/sample/SpibOrder';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface LineTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

export interface LineTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<LineTableInnerRef | undefined>;
}

const LineTable: React.FC<LineTableProps> = (props) => {
  const { innerRef } = props;
  const status = useRef<'all' | 'processing' | 'finish'>('all');
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId } = orderRecord;
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      const payload: any = { ...params };
      payload.wibOrderId = orderId;
      if (status.current === 'processing') {
        payload.qtyContrastMode = 'LT';
      }

      if (status.current === 'finish') {
        payload.qtyContrastMode = 'GE';
      }

      const record: any = await SpibOrderApi.Lines(payload);

      record.data.forEach((item, index) => {
        record.data[index].diffQty = item.qty - item.actQty;
        if (record.data[index].diffQty < 0) {
          record.data[index].diffQty = 0;
        }
      });
      return record;
    },
    [orderId],
  );

  const statusRadioOnChange = (data) => {
    status.current = data.target.value;
    powerTableRef.current?.load();
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      valueType: 'text',
      dataIndex: 'smBarcode',
      fixed: 'left',
      width: 180,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'smBrandName',
      valueType: 'text',
      ellipsis: true,
      auto: true,
      minWidth: 180,
      tooltip: true,
    },
    {
      title: i18n.t('global.product'),
      dataIndex: 'smCode',
      width: 160,
      render: (smCode, record) => <ProdCodeRowText prodCode={smCode} disturbTag={record.smDisturbTag} />,
    },
    {
      title: i18n.t('global.color'),
      valueType: 'codeName',
      dataIndex: 'smColorCode',
      codeDataIndex: 'smColorCode',
      nameDataIndex: 'smColorName',
      width: 120,
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      valueType: 'codeName',
      dataIndex: 'smSizeCode',
      codeDataIndex: 'smSizeCode',
      nameDataIndex: 'smSizeName',
      width: 120,
      ellipsis: true,
    },
    // {
    //   title: i18n.t('global.series'),
    //   valueType: 'codeName',
    //   dataIndex: 'smSeriesCode',
    //   codeDataIndex: 'smSeriesCode',
    //   nameDataIndex: 'smSeriesName',
    //   width: 150,
    //   ellipsis: true,
    // },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 180,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.diffQty'),
      valueType: 'number',
      dataIndex: 'diffQty',
      width: 200,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      innerRef={powerTableRef}
      columns={columns}
      refreshBtnVisible
      defaultSorter={{ field: 'smCode', order: 'DESCEND' }}
      request={fetchData}
      defaultPageSize={20}
      pagination
      leftToolbar={
        // 本地单或超出开关已经打开或者串货开关已经打开不显示
        // !localTag && !overTag && !crossTag ? (   // 此判断暂时取消
        <Radio.Group defaultValue="all" onChange={statusRadioOnChange}>
          <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
          <Radio.Button value="processing">{i18n.t('global.unFinish')}</Radio.Button>
          <Radio.Button value="finish">{i18n.t('global.finished')}</Radio.Button>
        </Radio.Group>
      }
    />
  );
};

export default LineTable;
