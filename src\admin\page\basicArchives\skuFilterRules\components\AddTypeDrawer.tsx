import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';
import TypeForm from './TypeForm';

interface AddDrawerProps {
  open: boolean;
  loading?: boolean;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

const AddTypeDrawer: React.FC<AddDrawerProps> = (props) => {
  const { onSubmit, loading, open, onCancel } = props;
  const [form] = Form.useForm();
  const codeInputRef = useRef<any>(null);

  const handleOk = () => {
    form
      .validateFields()
      .then((values) => {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
        }
        onSubmit(values);
      })
      .catch(() => {});
  };

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      setTimeout(() => {
        codeInputRef.current?.focus({ cursor: 'end' });
      }, 300);
    }
  }, [open, form]);

  const onClose = () => {
    if (onCancel) {
      onCancel();
      form.resetFields();
    }
  };

  return (
    <Drawer
      title={i18n.t('global.addTagFilterType')}
      okButtonProps={{
        loading,
      }}
      okText={i18n.t('global.apply')}
      onOk={handleOk}
      onClose={onClose}
      open={open}
      destroyOnClose
    >
      <TypeForm
        codeInputRef={codeInputRef}
        form={form}
        layout="vertical"
        name="basic"
        initialValues={{ disable: false, defaultTag: false }}
      />
    </Drawer>
  );
};

export default AddTypeDrawer;
