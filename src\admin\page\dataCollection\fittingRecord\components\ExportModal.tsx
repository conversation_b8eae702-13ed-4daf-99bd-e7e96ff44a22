import Modal from 'common/components/Modal';
import * as FittingRoomApi from 'common/api/fitting/FittingRoom';
import Export, { templateItem } from 'common/components/Export';
import React, { useEffect, useState } from 'react';
import * as DateUtil from 'common/utils/Date';
import i18n from 'common/utils/I18n';

interface ExportModalProps {
  title?: string;
  visible?: boolean;
  onCancel?: () => void;
  // 查询列表参数
  paramsRecord?: Record<string, any>;
  total?: number;
}

const ExportModal: React.FC<ExportModalProps> = (props) => {
  const { visible, onCancel, title, paramsRecord, total } = props;
  const [data, setData] = useState<Record<string, any>>();
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  const shopCodeMsg = i18n.t('global.shopCode');
  const shopNameMsg = i18n.t('global.shopName');
  const deviceCodeMsg = i18n.t('global.deviceCode');
  const productCodeMsg = i18n.t('global.productCode');
  const productNameMsg = i18n.t('global.productName');
  const readCountMsg = i18n.t('global.readCount');
  const groupCodeMsg = i18n.t('global.groupCoding');
  const minRSSIMsg = i18n.t('global.minRSSI');
  const maxRSSIMsg = i18n.t('global.maxRSSI');
  const avgRSSIMsg = i18n.t('global.avgRSSI');
  const sumRSSIMsg = i18n.t('global.sumRSSI');
  const readTimeMsg = i18n.t('global.tagReadTime');
  const leaveTimeMsg = i18n.t('global.tagLeaveTime');
  const barcodeMsg = i18n.t('global.barcode');
  const antennaCodeMsg = i18n.t('global.antennaCode');
  const createdMsg = i18n.t('global.created');
  const fittingTimeMsg = i18n.t('global.fittingTime');

  const template: templateItem[] = [
    {
      dataIndex: 'partnerCode',
      display: shopCodeMsg,
    },
    {
      dataIndex: 'partnerName',
      display: shopNameMsg,
    },
    {
      dataIndex: 'prodCode',
      display: productCodeMsg,
    },
    {
      dataIndex: 'prodName',
      display: productNameMsg,
    },
    {
      dataIndex: 'barcode',
      display: barcodeMsg,
    },
    {
      dataIndex: 'epc',
      display: 'EPC',
    },
    {
      dataIndex: 'deviceCode',
      display: deviceCodeMsg,
    },
    {
      dataIndex: 'antennaCode',
      display: antennaCodeMsg,
    },
    {
      dataIndex: 'groupCode',
      display: groupCodeMsg,
    },
    {
      dataIndex: 'duration',
      display: fittingTimeMsg,
    },
    {
      dataIndex: 'tagReadTime',
      display: readTimeMsg,
    },
    {
      dataIndex: 'tagLeaveTime',
      display: leaveTimeMsg,
    },
    {
      dataIndex: 'readCount',
      display: readCountMsg,
    },
    {
      dataIndex: 'minRSSI',
      display: minRSSIMsg,
    },
    {
      dataIndex: 'maxRSSI',
      display: maxRSSIMsg,
    },
    {
      dataIndex: 'avgRSSI',
      display: avgRSSIMsg,
    },
    {
      dataIndex: 'sumRSSI',
      display: sumRSSIMsg,
    },
    {
      dataIndex: 'created',
      display: createdMsg,
    },
  ];

  const fetchData = (pageOrCursor?: number | string) =>
    new Promise((resolve, reject) => {
      if (!paramsRecord) return;
      if (abortController?.signal.aborted) {
        reject(new Error('Request cancelled'));
        return;
      }
      const params: any = { ...paramsRecord, currentPage: 1, pageSize: 1000 };
      if (typeof pageOrCursor === 'number') {
        params.currentPage = pageOrCursor;
      } else if (typeof pageOrCursor === 'string') {
        params.cursor = pageOrCursor;
      }
      // 兼容 total/hasNext
      let totalPages = 1;
      if (typeof total === 'number' && total > 0) {
        totalPages = Math.ceil(total / params.pageSize);
      }
      FittingRoomApi.List(params)
        .then((res: any) => {
          res.data.forEach((item) => {
            item.duration = DateUtil.SecondsFormat(item.duration);
          });
          if (abortController?.signal.aborted) {
            reject(new Error('Request cancelled'));
            return;
          }
          resolve({
            ...res,
            totalRecords: total,
            totalPages,
            hasNext: res.hasNext,
            nextCursor: res.nextCursor,
          });
          setData({
            ...res,
            currentPage: Number(res.currentPage),
            totalRecords: total,
            totalPages,
            hasNext: res.hasNext,
            nextCursor: res.nextCursor,
          });
        })
        .catch((err) => reject(err));
    });

  const modalOnCancel = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    if (onCancel) onCancel();
  };

  useEffect(() => {
    if (visible) {
      const controller = new AbortController();
      setAbortController(controller);
      fetchData();
    } else {
      if (abortController) {
        abortController.abort();
        setAbortController(null);
      }
      setData(undefined);
    }
    // eslint-disable-next-line
  }, [visible]);

  return (
    <Modal
      title={
        <>
          {i18n.t('global.export')} {' - '}
          {title}
        </>
      }
      open={visible}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      footer={null}
      transitionName=""
      maskTransitionName=""
    >
      <div className="flex h-80 items-center justify-center">
        <Export fileName={title} template={template} data={data} request={fetchData} />
      </div>
    </Modal>
  );
};

export default ExportModal;
