import type { ITableBaseItem } from '../../data';

export interface ITableDataLineItem extends ITableBaseItem {
  skuId: string;
  barcode: string;
  alias: any[];
  source: string;
  brandName: string;
  brandCode: string;
  qty: number;
  availableQty: number;
  operateQty: number;
  actQty: number;
  /** 异常：错误数 */
  errorQty: number;
  /** 异常：超量数 */
  overQty: number;
}

export interface INormalDataItem {
  barcode: string;
  qty: number;
  skuId: string;
}

export interface IErrorDataItem {
  barcode: string;
  qty: number;
  operateQty: number;
  msg: string;
}
