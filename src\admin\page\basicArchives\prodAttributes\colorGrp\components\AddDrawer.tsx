import ColorGrpOperateForm from 'admin/page/basicArchives/prodAttributes/colorGrp/components/ColorGrpOperateForm';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'i18next';
import React, { useEffect } from 'react';

import { IColorGrpItem } from '../data';

interface AddModalProps {
  visible: boolean;
  confirmLoading: boolean;
  onSubmit: (values: IColorGrpItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<AddModalProps> = (props) => {
  const [form] = Form.useForm();
  const { visible, confirmLoading, onSubmit, onCancel } = props;

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IColorGrpItem);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Drawer
      title={i18n.t('global.newColorGrp')}
      okButtonProps={{
        loading: confirmLoading,
      }}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      okText={i18n.t('global.apply')}
      onOk={handleSubmit}
      onClose={() => {
        onCancel();
        form.resetFields();
      }}
      destroyOnClose
    >
      <ColorGrpOperateForm form={form} initialValues={{ disabled: false }} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddDrawer;
