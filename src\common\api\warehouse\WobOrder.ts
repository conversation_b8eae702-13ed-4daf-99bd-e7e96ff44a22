import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/create',
    method: 'post',
    data,
    ...config,
  });

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/list',
    method: 'post',
    data,
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/get',
    method: 'post',
    data,
  });

export const Boxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/boxes',
    method: 'post',
    data,
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/rfids',
    method: 'post',
    data,
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/confirm',
    method: 'post',
    data,
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/lines',
    method: 'post',
    data,
  });

export const BatchImports = (data: RequestPayloadType, config: IRequestConfig): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/batch-imports',
    method: 'post',
    data,
    ...config,
  });

export const BoxSave = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/box-save',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/box-reset',
    method: 'post',
    data,
  });

export const BoxDelete = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/box-delete',
    method: 'post',
    data,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/logs',
    method: 'post',
    data,
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/cancel',
    method: 'post',
    data,
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/update',
    method: 'post',
    data,
  });

export const AbnormalLines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/abnormal-lines',
    method: 'post',
    data,
  });

export const AbnormalTags = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/abnormal-tags',
    method: 'post',
    data,
  });

export const MatchRuleList = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/match-rule/list',
    method: 'post',
    data,
  });

export const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/reset',
    method: 'post',
    data,
  });

export const OutboundBoxData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/outbound-box-data',
    method: 'post',
    data,
  });

export const OutboundData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/outbound-data',
    method: 'post',
    data,
  });

export const PreBoxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/pre-box-list',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/batch-confirm',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/warehouse/wob-order/batch-cancel',
    method: 'post',
    data,
  });
