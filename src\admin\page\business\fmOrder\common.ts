import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback, useContext } from 'react';

export const useCommonFn = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const confirm = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BfmOrderApi.Confirm({ id: orderId });
              } else {
                await FmOrderApi.Confirm({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  const cancel = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BfmOrderApi.Cancel({ id: orderId });
              } else {
                await FmOrderApi.Cancel({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [currentUser.mode],
  );

  return { confirm, cancel };
};
