import { But<PERSON>, DatePicker, Dropdown, Form, Space, Spin } from 'antd';
import * as SptsOrderApi from 'common/api/sample/SpTsOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import StartIcon from 'common/assets/icons/icon-start.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import AddModal from './components/AddModal';
import BatchAddModal from './components/BatchAddModal';
import { useCommonFn } from './common';

const SptsOrder: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [batchAddModalVisible, setBatchAddModalVisible] = useState(false);
  const [batchAddModalLoading, setBatchAddModalLoading] = useState(false);
  const [initialized, setInitialized] = useState<boolean>(false);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [form] = Form.useForm();
  const { start, confirm, cancel, reset } = useCommonFn();

  const [permission] = usePermission('A:P:TSO');
  const createPermission = permission.codes.includes('CREATE');
  const batchCreatePermission = permission.codes.includes('BATCH_CREATE');
  const cancelPermission = permission.codes.includes('CANCEL');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const startPermission = permission.codes.includes('START');
  const resetPermission = permission.codes.includes('RESET');

  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const navigate = useNavigate();

  let defaultPartnerValue: string[] = [];
  if (currentUser.type === 'WAREHOUSE') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const addBtnOnClick = () => {
    setAddModalVisible(true);
  };

  const cancelBtnOnClick = async (record: Record<string, any>) => {
    try {
      await cancel(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    try {
      await confirm(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const resetBtnOnClick = async (record: Record<string, any>) => {
    try {
      await reset(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const startBtnOnClick = async (record: Record<string, any>) => {
    try {
      await start(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const addModalOnCancel = () => {
    setAddModalVisible(false);
  };

  // const firstFetch = useRef(true);

  const fetchData = useCallback(
    async (params: Record<string, any>, actualTabKey: string) => {
      if (params.created) {
        params.createdStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      delete params.created;

      if (params.mode) {
        params.mode = [params.mode];
      }
      if (params.code) {
        params.code = `%${params.code}%`;
      }
      if (actualTabKey === 'ALL') {
        params.status = ['NEW', 'PROCESSING', 'FINISHED'];
      } else if (actualTabKey === 'WAITING_INVENTORY') {
        params.status = ['NEW', 'PROCESSING'];
      } else {
        params.status = [actualTabKey];
      }
      try {
        const res: any = await SptsOrderApi.List({
          // orderByField: 'modified',
          // orderByMethod: 'DESCEND',
          ...params,
        });
        res.data.forEach((item) => {
          item.diffQty = Math.abs(item.qty - item.snapQty);
        });
        return res;
      } catch (e) {
        return { data: [] };
      }
    },
    // eslint-disable-next-line
    [],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'partnerIds',
      label: i18n.t('global.warehouse'),
      inputComponent: <PartnerSelect types={['WAREHOUSE']} multiple sourceType="PERMISSION" />,
    },
    // {
    //   name: 'mode',
    //   label: i18n.t('global.stockMode'),
    //   inputComponent: <TsOrderModeSelect />,
    // },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      sorter: true,
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.sourceOrderCode'),
      width: 220,
      valueType: 'text',
      dataIndex: 'sourceCode',
      sorter: true,
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      valueType: 'status',
      statusConfig: {
        NEW: { status: 'Red', desc: i18n.t('global.newOrder') },
        PROCESSING: { status: 'Blue', desc: i18n.t('global.processing') },
        FINISHED: { status: 'Green', desc: i18n.t('global.finished') },
        CANCELED: { status: 'Slate', desc: i18n.t('global.cancelled') },
      },
      width: 150,
    },
    {
      title: i18n.t('global.mode'),
      dataIndex: 'mode',
      codeDataIndex: 'mode',
      nameDataIndex: 'modeDesc',
      valueType: 'codeName',
      width: 180,
    },
    {
      title: i18n.t('global.warehouse'),
      dataIndex: 'partnerName',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      minWidth: 230,
      auto: true,
    },
    {
      title: i18n.t('global.bankStorageAmount'),
      dataIndex: 'snapQty',
      valueType: 'number',
      sorter: true,
      width: 230,
    },
    {
      title: i18n.t('global.stockCount'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.diffQty'),
      dataIndex: 'diffQty',
      valueType: 'number',
      width: 230,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (startPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.startTs'),
      icon: <StartIcon className="fill-lead-green" />,
      isDisabled: (record) => !(record.status === 'NEW'),
      onClick: (record) => {
        startBtnOnClick(record);
      },
    });
  }

  if (confirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-orange" />,
      isDisabled: (record) => !(record.status === 'PROCESSING'),
      onClick: (record) => {
        confirmBtnOnClick(record);
      },
    });
  }

  if (resetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-yellow" />,
      isDisabled: (record) => record.status === 'FINISHED' || record.status === 'CANCELED' || record.actQty === 0,
      onClick: (record) => {
        resetBtnOnClick(record);
      },
    });
  }

  if (cancelPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.cancel'),
      isDisabled: (record) => !(record.status === 'NEW' || record.status === 'PROCESSING'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        cancelBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const batchCreateModalOnSubmit = async (dataList) => {
    setBatchAddModalLoading(true);

    try {
      await SptsOrderApi.BatchCreate({
        data: dataList,
      });

      setBatchAddModalLoading(false);
      setBatchAddModalVisible(false);
      powerTableRef.current?.load();
      form.resetFields();
      NoticeUtil.success();
    } catch (e) {
      setBatchAddModalLoading(false);
    }
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues: any = {
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {createPermission && batchCreatePermission && (
              <Dropdown.Button
                type="primary"
                onClick={addBtnOnClick}
                menu={{
                  items: [
                    {
                      label: i18n.t('global.batchCreateAllModeTsOrder'),
                      key: 'BATH',
                    },
                  ],
                  onClick: () => setBatchAddModalVisible(true),
                }}
              >
                <AddFillIcon className="fill-white" />
                {i18n.t('global.new')}
              </Dropdown.Button>
            )}
            {createPermission && !batchCreatePermission && (
              <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
            {!createPermission && batchCreatePermission && (
              <Button
                type="primary"
                icon={<AddFillIcon className="fill-white" />}
                onClick={() => {
                  setBatchAddModalVisible(true);
                }}
              >
                {i18n.t('global.batchCreateAllModeTsOrder')}
              </Button>
            )}
          </Space>
        }
      />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={searchPanelInitialValues}
          enableCache
          cacheKey="SPTS_ORDER"
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            {
              code: 'NEW',
              name: i18n.t('global.newOrder'),
            },
            {
              code: 'PROCESSING',
              name: i18n.t('global.processing'),
            },
            {
              code: 'FINISHED',
              name: i18n.t('global.finished'),
            },
            {
              code: 'CANCELED',
              name: i18n.t('global.cancelled'),
            },
            {
              code: 'WAITING_INVENTORY',
              name: i18n.t('global.waitingForInventory'),
            },
          ]}
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: (record) => ({
              onClick: () => {
                navigate(`/app/spts/${record.id}`);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
      <AddModal
        visible={addModalVisible}
        onCancel={addModalOnCancel}
        onOk={() => {
          powerTableRef.current?.load();
        }}
      />
      <BatchAddModal
        form={form}
        visible={batchAddModalVisible}
        confirmLoading={batchAddModalLoading}
        onSubmit={batchCreateModalOnSubmit}
        onCancel={() => {
          setBatchAddModalVisible(false);
        }}
      />
    </div>
  );
};

export default SptsOrder;
