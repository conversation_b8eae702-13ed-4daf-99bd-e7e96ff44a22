import { Col, DatePicker, Form, Input, Row, Steps } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import CloseButton from 'common/components/Button/Close';
import NextStepButton from 'common/components/Button/NextStep';
import OpenButton from 'common/components/Button/Open';
import RetryButton from 'common/components/Button/Retry';
import Modal from 'common/components/Modal';
import Result from 'common/components/Result';
import PartnerProSelect from 'common/components/Select/PartnerSelect';
import WarehouseSelect from 'common/components/Select/WarehouseSelect';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import TPartnerType from 'common/types/TPartnerType';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import moment from 'moment';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import DataViewTable from './DataViewTable';
import { TableData } from './TableData';

interface IAddModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk?: () => void;
}

const AddModal: React.FC<IAddModalProps> = (props) => {
  const { visible, onCancel, onOk } = props;
  const [tableData, setTableData] = useState<any>([]);
  const [result, setResult] = useState<'progress' | 'success' | 'error'>('progress');
  const [errorMsg, setErrorMsg] = useState('');
  const [orderId, setOrderId] = useState('');
  const [orderCode, setOrderCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [receiverType, setReceiverType] = useState<TPartnerType>('WAREHOUSE');
  const [receiverId, setReceiverId] = useState<string>('');
  const [deliveryId, setDeliveryId] = useState<string>('');
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const navigate = useNavigate();

  const formValues = useRef<Record<string, any>>({});
  const [form] = Form.useForm();

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const reset = () => {
    setOrderId('');
    setCurrentStep(0);
    setResult('progress');
    setErrorMsg('');
    setLoading(false);
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      reset();
    }
  }, [visible, form]);

  const onSubmit = async () => {
    const payload: any = {};
    payload.header = { ...formValues.current };
    if (payload.header.billingDate) {
      payload.header.billingDate = payload.header.billingDate.format('YYYY-MM-DD hh:mm:ss');
    }
    if (payload.header.deliveryDate) {
      payload.header.deliveryDate = payload.header.deliveryDate.format('YYYY-MM-DD hh:mm:ss');
    }
    payload.prods = [];
    payload.details = JSON.parse(JSON.stringify(tableData.sku));
    if (tableData.sku.length > 0 && tableData.sku.some((item) => !item.qty || !item.price)) {
      NoticeUtil.warn(i18n.t('global.writeBarcodeInfo'));
      return;
    }
    payload.header.toPartnerType = receiverType;
    payload.details.forEach((n) => {
      delete n.prodCode;
    });
    payload.matchRules = [];
    tableData.sku.forEach((n) => {
      if (!payload.prods.some((i) => i.prodCode === n.prodCode)) {
        payload.prods.push({ prodCode: n.prodCode });
      }
      if (n.ratio > 0) {
        payload.matchRules.push({
          prodCode: n.prodCode,
          colorCode: n.colorCode,
          sizeCode: n.sizeCode,
          ratio: n.ratio,
        });
      }
    });

    setResult('progress');
    setLoading(true);
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfmOrderApi.Imports(payload, {
          throwError: false,
        });
      } else {
        res = await FmOrderApi.Imports(payload, {
          throwError: false,
        });
      }
      setResult('success');
      setOrderId(res.id);
      setOrderCode(res.code);
      if (onOk) {
        onOk();
      }
    } catch (e) {
      // @ts-ignore
      setErrorMsg(e?.response?.data?.message || '');
      setResult('error');
    }
    setLoading(false);
    setCurrentStep(currentStep + 1);
  };

  const linkToNewOrder = () => {
    navigate(`/app/fm/detail/${orderId}`);
    onCancel();
  };

  const formOnFinish = async (values) => {
    try {
      await form.validateFields();
      formValues.current = values;
      setCurrentStep(currentStep + 1);
    } catch (e) {}
  };

  return (
    <Modal
      title={i18n.t('global.newProductionOrder')}
      onCancel={onCancel}
      open={visible}
      destroyOnClose
      width={currentStep !== 1 ? 800 : 1200}
      footer={false}
      maskClosable={false}
      keyboard={false}
    >
      <div className="flex flex-col">
        <div className="mb-6">
          <Steps
            current={currentStep}
            direction="horizontal"
            items={[
              {
                // key: 1,
                title: i18n.t('global.basicInformation'),
                subTitle: (
                  <>
                    <br />
                    {i18n.t('global.fillOrderBasicInfo')}
                  </>
                ),
              },
              {
                // key: 2,
                title: i18n.t('global.selectBarCode'),
                subTitle: (
                  <>
                    <br />
                    {i18n.t('global.selectBarCodeAndWriteNumber')}
                  </>
                ),
              },
              {
                // key: 3,
                title: i18n.t('global.result'),
              },
            ]}
          />
        </div>
        <Form
          form={form}
          name="basic"
          onFinish={formOnFinish}
          layout="vertical"
          style={{
            flex: 1,
            display: currentStep === 0 ? 'block' : 'none',
          }}
          initialValues={{
            toPartnerType: 'WAREHOUSE',
            billingDate: moment().startOf('day'),
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label={i18n.t('global.agreementCode')} name="contractNumber" rules={canNotBeNullRules}>
                <Input maxLength={20} autoFocus />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label={i18n.t('global.sourceOrderCode')} name="orderCode" rules={canNotBeNullRules}>
                <Input maxLength={20} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label={i18n.t('global.shipper')} rules={canNotBeNullRules} name="partnerCode">
                <PartnerProSelect
                  types={['FACTORY']}
                  sourceType="PERMISSION"
                  valueField="code"
                  onChange={(value, opts: any) => {
                    setDeliveryId(opts?.data.id);
                    form.setFieldValue('warehouseCode', '');
                  }}
                />
              </Form.Item>
            </Col>
            {deliveryId && (
              <Col span={12}>
                <Form.Item label={i18n.t('global.shipperSubWarehouse')} name="warehouseCode">
                  <WarehouseSelect partnerId={deliveryId} type="FACTORY" valueField="code" />
                </Form.Item>
              </Col>
            )}
            <Col span={12}>
              <Form.Item label={i18n.t('global.receiver')} name="toPartnerCode" rules={canNotBeNullRules}>
                <PartnerProSelect
                  types={['WAREHOUSE', 'SHOP']}
                  sourceType="PERMISSION"
                  valueField="code"
                  onChange={(value, opts: any) => {
                    setReceiverType(opts?.data.type);
                    setReceiverId(opts?.data.id);
                    form.setFieldValue('toWarehouseCode', '');
                  }}
                />
              </Form.Item>
            </Col>
            {receiverId && (
              <Col span={12}>
                <Form.Item label={i18n.t('global.receiverSubWarehouse')} name="toWarehouseCode">
                  {/* @ts-ignore */}
                  <WarehouseSelect partnerId={receiverId} type={receiverType} valueField="code" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label={i18n.t('global.issueDate')} name="billingDate">
                <DatePicker />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label={i18n.t('global.deliveryDate')} name="deliveryDate">
                <DatePicker />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={i18n.t('global.remark')} name="remark">
                <TextArea rows={3} maxLength={100} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item style={{ textAlign: 'left' }} noStyle>
                <NextStepButton htmlType="submit" loading={loading} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <TableData>
          <DataViewTable
            visible={currentStep === 1 ? 'block' : 'none'}
            getTableData={(data) => setTableData(data)}
            nextButtonOnClick={() => {
              onSubmit();
            }}
            preButtonOnClick={() => setCurrentStep(currentStep - 1)}
            loading={loading}
          />
        </TableData>
        <div
          style={{
            flex: 1,
            display: currentStep === 2 ? 'block' : 'none',
          }}
        >
          {result === 'success' && (
            <Result
              status="success"
              title={i18n.t('global.createdDone')}
              subTitle={`${i18n.t('global.orderCode')} : ${orderCode}`}
              extra={[
                <CloseButton
                  onClick={() => {
                    onCancel();
                  }}
                />,
                <OpenButton onClick={linkToNewOrder} />,
              ]}
            />
          )}
          {result === 'error' && (
            <Result
              status="error"
              title={i18n.t('global.createdFail')}
              subTitle={errorMsg}
              extra={[
                <CloseButton
                  onClick={() => {
                    onCancel();
                  }}
                />,
                <RetryButton
                  type="primary"
                  icon={undefined}
                  onClick={() => {
                    reset();
                  }}
                />,
              ]}
            />
          )}
        </div>
      </div>
    </Modal>
  );
};

export default AddModal;
