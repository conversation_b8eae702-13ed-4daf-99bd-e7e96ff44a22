import { DatePicker, Image, Input } from 'antd';
import * as SampleExtFieldApi from 'common/api/sample/SampleExtField';
import * as SampleSkuApi from 'common/api/sample/SampleSku';
import * as FileApi from 'common/api/file/Img';
import PowerTable, { IPowerTableInnerRef, SearchFieldsConfig } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const SampleSku: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smBarcode',
      label: i18n.t('global.barcode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smSkuName',
      label: i18n.t('global.skuName'),
      inputComponent: <Input />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: any = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.image'),
      width: 100,
      dataIndex: 'source',
      render: (text) => (
        <Image
          preview={
            text
              ? {
                  src: FileApi.Get(text),
                }
              : false
          }
          src={text ? FileApi.Get(text, 100, 100) : 'img/noImage.svg'}
        />
      ),
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'smBarcode',
      sorter: 'true',
      width: 180,
      ellipsis: true,
    },
    {
      title: i18n.t('global.skuName'),
      dataIndex: 'smSkuName',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'smCode',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'smName',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.brand'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'smBrandCode',
      nameDataIndex: 'smBrandName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.color'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'smColorCode',
      nameDataIndex: 'smColorName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.size'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'smSizeCode',
      nameDataIndex: 'smSizeName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.series'),
      width: 150,
      valueType: 'codeName',
      codeDataIndex: 'smSeriesCode',
      nameDataIndex: 'smSeriesName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.gender'),
      width: 120,
      dataIndex: 'smGender',
      valueType: 'text',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.priCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'smPriCategoryCode',
      nameDataIndex: 'smPriCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.subCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'smSubCategoryCode',
      nameDataIndex: 'smSubCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.designer'),
      width: 150,
      dataIndex: 'smDesigner',
      valueType: 'text',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.costPrice'),
      dataIndex: 'costPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.smRetailPrice'),
      dataIndex: 'retailPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.barcodeTagPrice'),
      dataIndex: 'tagPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const [columns, setColumns] = useState<any>(tableColumns);

  const fetchData = useCallback(async (params: Record<string, any>) => {
    if (params.barcode) {
      params.barcode = params.barcode.trim();
    }
    if (params.prodCode) {
      params.prodCode = params.prodCode.trim();
    }
    if (params.createDateRange) {
      params.createdStart = params.createDateRange[0].startOf('day');
      params.createdEnd = params.createDateRange[1].endOf('day');
    }
    delete params.createDateRange;
    let data: any = [];
    try {
      data = await SampleSkuApi.List(params);
      data.data.forEach((item) => {
        Object.keys(item.smExt).forEach((n) => {
          item[`smExt-${n}`] = item.prodExt[n];
        });
      });
    } catch (e) {}
    return data;
  }, []);

  const [addColumnsWidth, setAddColumnsWidth] = useState<number>(0);
  const getExtField = async () => {
    const extFieldData = await SampleExtFieldApi.List({
      disabled: false,
      enablePage: false,
      type: 'SAMPLE',
    });
    const addColumns: any = [];
    extFieldData.data.forEach((n) => {
      addColumns.push({
        title: n.name,
        dataIndex: `smExt-${n.code}`,
        width: 140,
        ellipsis: true,
      });
    });
    setAddColumnsWidth(extFieldData.data.length * 140);
    setColumns(tableColumns.slice(0, -1).concat(addColumns).concat(tableColumns.slice(-1)));
  };

  useEffect(() => {
    getExtField();
    // eslint-disable-next-line
  }, []);

  return (
    <div>
      <AppHeader />
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="SAMPLE_SKU"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
          scroll: { x: 2480 + addColumnsWidth },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
    </div>
  );
};

export default SampleSku;
