import { Form, message, Tabs } from 'antd';
import * as ProdApi from 'common/api/core/Prod';
import * as Sku<PERSON><PERSON> from 'common/api/core/Sku';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import useSetting from 'common/hooks/useSetting';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';

import { IProdItem } from '../data';
import { ProdContext, TProdContext } from '../reducer';
import BarcodeTable from './BarcodeTable';
import OperateForm, { extFieldFormItemNamePreFix, OperateFormInnerRef } from './OperateForm';

interface IDetailDrawerProps {
  onClose?: () => void;
  open?: boolean;
  title?: ReactNode;
}

const DetailDrawer: React.FC<IDetailDrawerProps> = (props) => {
  const { open, title, onClose } = props;
  const [loading, setLoading] = useState(false);
  const [prodRecord, setProdRecord] = useState<Partial<IProdItem>>({});
  const [activeType, setActiveType] = useState<string>('INFO');
  const operateFormRef = useRef<OperateFormInnerRef>();
  const [form] = Form.useForm();
  const { state, dispatch } = useContext<TProdContext>(ProdContext);
  const [fetchLoading, setFetchLoading] = useState(false);

  const [permission] = usePermission('A:BASE:PRODUCT');
  const deletePermission = permission.codes.includes('DELETE');

  const [currencyData, setCurrencyData] = useState<any>();
  const { CURRENCY: currency } = useSetting([{ code: 'CURRENCY', valueType: 'STRING' }]);
  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }

  const fetchProd = useCallback(async () => {
    setFetchLoading(true);
    try {
      const rec: any = await ProdApi.Get({
        id: state.prodId,
      });
      // 处理扩展字段
      if (rec.ext) {
        Object.keys(rec.ext).forEach((key) => {
          rec[extFieldFormItemNamePreFix + key] = rec.ext[key];
        });
      }
      rec.status = !rec.disabled;
      dispatch({ type: 'setProdRecord', payload: rec });
      setProdRecord(rec);
      setFetchLoading(false);
    } catch (err) {
      setFetchLoading(false);
    }
    // eslint-disable-next-line
  }, [state.prodId]);

  const fetchSkuList = useCallback(
    async (payload: { [key: string]: any } = {}) => {
      if (!state.prodId) return;
      setFetchLoading(true);
      try {
        const params: Record<string, any> = {
          prodId: state.prodId,
          enablePage: false,
          orderByMethod: 'ASCEND',
          orderByField: 'barcode',
          ...payload,
        };
        if (typeof state.disabled === 'boolean') {
          params.disabled = state.disabled;
        }
        const skuList: any = await SkuApi.List(params);
        dispatch({ type: 'setSkuList', payload: skuList.data });
        setFetchLoading(false);
      } catch (err) {
        setFetchLoading(false);
      }
    },
    // eslint-disable-next-line
    [state.prodId, state.disabled],
  );

  useEffect(() => {
    fetchSkuList();
    // eslint-disable-next-line
  }, [state.disabled])


  useEffect(() => {
    if (open) {
      // console.log('执行');
      fetchProd();
      fetchSkuList();
      setTimeout(() => {
        operateFormRef.current?.focusNameInput();
      }, 300);
    } else {
      setProdRecord({});
      form.resetFields();
      setActiveType('INFO');
    }
    // eslint-disable-next-line
  }, [open, form]);

  const onSave = () => {
    if (form) form.submit();
  };
  const onFinish = async (values) => {
    setLoading(true);
    try {
      const ext = {};
      Object.keys(values).forEach((key) => {
        if (key.startsWith(extFieldFormItemNamePreFix)) {
          ext[key.split(extFieldFormItemNamePreFix)[1]] = values[key];
        }
      });
      if (state.skuList.length > 0) {
        if (values.colorGrpId !== prodRecord.colorGrpId && !values.disableColor) {
          message.warning(i18n.t('global.noChangeColorGrpWithSku'));
          setLoading(false);
          return;
        }
        if (values.sizeGrpId !== prodRecord.sizeGrpId && !values.disableSize) {
          message.warning(i18n.t('global.noChangeSizeGrpWithSku'));
          setLoading(false);
          return;
        }
      }

      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
      }
      if (activeType === 'INFO') {
        await ProdApi.Update({
          id: prodRecord.id,
          ...values,
          ext,
        });
      }

      setLoading(false);
      NoticeUtil.success();
      if (onClose) onClose();
    } catch (e) {
      setLoading(false);
    }
  };

  const deleteBtnOnClick = () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${prodRecord.code} - ${prodRecord.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await ProdApi.Delete({ id: prodRecord.id });
          NoticeUtil.success();
          if (onClose) onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    setActiveType('INFO');
    fetchProd();
    fetchSkuList();
  };

  const items: any[] = [];

  items.push({
    label: i18n.t('global.information'),
    key: 'INFO',
    children: (
      <OperateForm
        form={form}
        innerRef={operateFormRef}
        // initialValues={prodRecord}
        refresh={() => fetchProd()}
        codeReadOnly
        statusVisible
        imageVisible
        onFinish={onFinish}
      />
    ),
  });

  items.push({
    label: `${i18n.t('global.barcodes')} (${state.skuList.length})`,
    key: 'CUSTOM',
    children: <BarcodeTable fetchSkuList={fetchSkuList} />,
  });

  return (
    <Drawer
      title={title}
      open={open}
      width={1000}
      maskClosable={false}
      destroyOnClose
      okText={i18n.t('global.apply')}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          onApply={onSave}
          deletePermission={deletePermission}
          onDelete={deleteBtnOnClick}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={fetchLoading}>
        <Tabs
          size="small"
          onChange={(key) => {
            setActiveType(key);
          }}
          animated={false}
          activeKey={activeType}
          items={items}
        />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
