import { Form, Tabs } from 'antd';
import * as SampleSizeGrpApi from 'common/api/sample/SampleSizeGrp';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useState } from 'react';
import SizeOperateForm from './SizeOperateForm';
import SizeGrpOperateForm from './SizeGrpOperateForm';

import { ISampleSizeGrpItem } from '../data';

interface IEditModalProps {
  visible: boolean;
  confirmLoading: boolean;
  sizeId: string | undefined;
  onSubmit: (values: ISampleSizeGrpItem) => void;
  onCancel: () => void;
}

const DetailDrawer: React.FC<IEditModalProps> = (props) => {
  const [form] = Form.useForm();
  const [grpid, setGrpid] = useState('');
  const { visible, confirmLoading, sizeId, onSubmit, onCancel } = props;
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState('INFO');
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:SIZE_GRP');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchSize = useCallback(async () => {
    setLoading(true);
    try {
      const sizeGrp: any = await SampleSizeGrpApi.Get({
        id: sizeId,
      });
      setGrpid(sizeGrp.id);
      setCurrent(sizeGrp);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [sizeId]);

  useEffect(() => {
    if (visible && sizeId) {
      fetchSize();
    }
    return () => {
      setActiveKey('INFO');
    };
  }, [visible, sizeId, fetchSize]);

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISampleSizeGrpItem);
    }
  };

  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnClick = () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleSizeGrpApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchSize();
    setActiveKey('INFO');
  };

  return (
    <Drawer
      title={
        <>
          {i18n.t('global.editSizeGrp')}
          {current ? ` [${current.code}]` : ''}
        </>
      }
      width={800}
      open={visible}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          deletePermission={deletePermission && !!sizeId}
          onDelete={deleteBtnClick}
          onApply={handleSubmit}
          onRecover={onRecover}
        />
      }
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="1"
          animated={false}
          activeKey={activeKey}
          onChange={(e) => setActiveKey(e)}
          items={[
            {
              label: i18n.t('global.information'),
              key: 'INFO',
              children: <SizeGrpOperateForm current={current} form={form} onFinish={handleFinish} />,
            },
            {
              label: i18n.t('global.sizes'),
              key: 'SIZE',
              children: <SizeOperateForm grpId={grpid} />,
            },
          ]}
        />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
