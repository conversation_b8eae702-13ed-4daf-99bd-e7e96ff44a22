import type { ITableDataLineItem, INormalDataItem } from './data';

export type TState = {
  loading: boolean;
  saving: boolean;
  tableLinesData: ITableDataLineItem[];
  normalData: INormalDataItem[];
  normalTotalQty: number;
  abnormalTotal: number;
};

export const initialState: TState = {
  loading: false,
  saving: false,
  tableLinesData: [],
  normalData: [],
  normalTotalQty: 0,
  abnormalTotal: 0,
};

export type TActionType =
  | { type: 'setLoading'; payload: boolean }
  | { type: 'setSaving'; payload: boolean }
  | {
      type: 'initData';
      payload: {
        linesData: ITableDataLineItem[];
        boxRfidData?: INormalDataItem[];
        localTag?: boolean;
        callback?: (data?: any) => void;
      };
    }
  | {
      type: 'resetState';
      payload: {
        localTag?: boolean;
      };
    }
  | {
      type: 'tableLinesDataItemDelete';
      payload: {
        barcode: string;
      };
    }
  | {
      type: 'tableLinesDataItemReset';
      payload: {
        barcode: string;
      };
    }
  | {
      type: 'tableLinesDataItemAdd';
      payload: {
        operateSize: number;
        tableDataLineItem: any;
        callback?(): void;
      };
    }
  | {
      type: 'tableLinesDataItemUpdate';
      payload: {
        operateSize: number;
        barcode: string;
        localTag: boolean;
        callback?(): void;
      };
    }
  | {
      type: 'setErrorDataInTableLine';
      payload: {
        errorData: any[];
        callback?(): void;
      };
    }
  | {
      type: 'setErrorDataDeleteByModal';
      payload: {
        data: any[];
      };
    };

const formatBaseInfo = (data?: any) => ({
  barcode: data?.barcode || '',
  prodCode: data?.prodCode || '',
  colorCode: data?.colorCode || '',
  colorName: data?.colorName || '',
  sizeCode: data?.sizeCode || '',
  sizeName: data?.sizeName || '',
  skuName: data?.skuName || data?.name || '',
  skuId: data?.skuId || data?.id || '',
  specCode: data?.specCode || '',
  specName: data?.specName || '',
  source: data?.source || '',
  brandCode: data?.brandCode || '',
  brandName: data?.brandName || '',
  alias: data?.alias || [],
  rfidTag: data?.rfidTag || false,
});

export function reducer(state: TState, action: TActionType): TState {
  let newTableLinesData: ITableDataLineItem[] = [];
  let newNormalData: INormalDataItem[] = [];
  let newNormalTotalQty = 0;
  let newAbnormalTotal = 0;

  switch (action.type) {
    case 'setLoading':
      return { ...state, loading: action.payload };
    case 'setSaving':
      return { ...state, saving: action.payload };
    case 'initData':
      // eslint-disable-next-line no-case-declarations
      const { linesData, boxRfidData } = action.payload;
      if (linesData && linesData.length > 0) {
        // 根据 epc 汇总条码
        const collectBarcodeMap = new Map<string, any>();
        linesData.forEach((item) => {
          if (collectBarcodeMap.has(item.barcode)) {
            const collectBarcodeItem = collectBarcodeMap.get(item.barcode);
            collectBarcodeMap.set(item.barcode, {
              ...collectBarcodeItem,
              actQty: collectBarcodeItem.actQty + (item?.actQty || 0),
              qty: collectBarcodeItem.qty + item.qty,
            });
          } else {
            collectBarcodeMap.set(item.barcode, {
              ...item,
              actQty: item?.actQty || 0,
              qty: item.qty,
            });
          }
        });

        collectBarcodeMap.forEach((item) => {
          newTableLinesData.push({
            ...formatBaseInfo(item),
            qty: item.qty,
            availableQty: item.qty ? item.qty - item.actQty : 0,
            // 本地单操作数回显
            operateQty: action.payload.localTag ? item.qty : 0,
            actQty: item.actQty,
            errorQty: 0,
            overQty: 0,
          });

          if (action.payload.localTag) {
            // 本地单数据回显
            newNormalData.push({
              barcode: item.barcode,
              qty: item.qty,
              skuId: item.skuId,
            });

            newNormalTotalQty += item.qty;
          }
        });
      }

      if (boxRfidData && boxRfidData.length > 0) {
        boxRfidData.forEach((item) => {
          const tableDataLineItem = newTableLinesData.find((n) => n.barcode === item.barcode);
          if (tableDataLineItem) {
            tableDataLineItem.operateQty = item.qty;
          }

          newNormalData.push({
            barcode: item.barcode,
            qty: item.qty,
            skuId: item.skuId,
          });

          newNormalTotalQty += item.qty;
        });
      }

      if (action.payload.callback && newNormalTotalQty > 0) {
        action.payload.callback();
      }

      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: newNormalData,
        normalTotalQty: newNormalTotalQty,
      };
    case 'tableLinesDataItemDelete':
      // 从 tableLinesData 中移除该条码
      newTableLinesData = state.tableLinesData.filter((n) => n.barcode !== action.payload.barcode);
      // 从 normalData 中移除该条码
      newNormalData = state.normalData.filter((n) => n.barcode !== action.payload.barcode);
      newNormalTotalQty = newNormalData.reduce((prev, next) => prev + next.qty, 0);
      // 重新计算异常总数
      newAbnormalTotal = newTableLinesData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
      // 删除某一条后需要重新计算正常总数
      if (newAbnormalTotal > 0) {
        newNormalTotalQty -= newAbnormalTotal;
      }
      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: newNormalData,
        normalTotalQty: newNormalTotalQty,
        abnormalTotal: newAbnormalTotal,
      };
    case 'tableLinesDataItemReset': {
      newTableLinesData = [...state.tableLinesData];
      const resetTableItem = newTableLinesData.find((n) => n.barcode === action.payload.barcode);
      if (resetTableItem) {
        // 恢复可用数量
        resetTableItem.availableQty += resetTableItem.operateQty;
        // 重置操作数量为0
        resetTableItem.operateQty = 0;
        resetTableItem.errorQty = 0;
        resetTableItem.overQty = 0;
      }

      // 从 normalData 中移除该条码
      newNormalData = state.normalData.filter((n) => n.barcode !== action.payload.barcode);
      newNormalTotalQty = newNormalData.reduce((prev, next) => prev + next.qty, 0);
      // 重新计算异常总数
      newAbnormalTotal = newTableLinesData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
      // 重置某一条后需要重新计算正常总数
      if (newAbnormalTotal > 0) {
        newNormalTotalQty -= newAbnormalTotal;
      }
      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: newNormalData,
        normalTotalQty: newNormalTotalQty,
        abnormalTotal: newAbnormalTotal,
      };
    }
    case 'tableLinesDataItemAdd':
      // 新增条码到表格上只有本地单才会触发
      newTableLinesData = [...state.tableLinesData];
      newNormalData = [...state.normalData];
      newNormalTotalQty = state.normalTotalQty;
      if (action.payload.tableDataLineItem) {
        const originTableDataLineItem = action.payload.tableDataLineItem;
        const newTableDataLineItem = {
          ...formatBaseInfo(originTableDataLineItem),
          qty: 0,
          availableQty: 0,
          operateQty: action.payload.operateSize,
          actQty: 0,
          errorQty: 0,
          overQty: 0,
        };

        newTableLinesData.unshift(newTableDataLineItem);

        newNormalData.push({
          barcode: newTableDataLineItem.barcode,
          qty: action.payload.operateSize,
          skuId: newTableDataLineItem.skuId,
        });

        newNormalTotalQty += action.payload.operateSize;
        // 添加新条码后需要重新计算异常总数 和 正常总数
        newAbnormalTotal = newTableLinesData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
        if (newAbnormalTotal > 0) {
          newNormalTotalQty -= newAbnormalTotal;
        }

        if (action.payload.callback) {
          action.payload.callback();
        }
      }
      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: newNormalData,
        normalTotalQty: newNormalTotalQty,
      };
    case 'tableLinesDataItemUpdate':
      newTableLinesData = [...state.tableLinesData];
      newNormalData = [...state.normalData];
      newNormalTotalQty = state.normalTotalQty;

      if (action.payload.barcode) {
        newTableLinesData.forEach((item) => {
          if (item.barcode === action.payload.barcode) {
            // 如果是减少操作且当前item有异常数，则减少异常数
            if (action.payload.operateSize < 0) {
              const reduceAmount = Math.abs(action.payload.operateSize);
              if (item.errorQty > 0) {
                const errorReduce = Math.min(item.errorQty, reduceAmount);
                item.errorQty -= errorReduce;
                const remainingReduce = reduceAmount - errorReduce;
                if (remainingReduce > 0 && item.overQty > 0) {
                  item.overQty = Math.max(0, item.overQty - remainingReduce);
                }
              } else if (item.overQty > 0) {
                item.overQty = Math.max(0, item.overQty - reduceAmount);
              }
            }

            // 设置对应条码的操作数
            item.operateQty += action.payload.operateSize;
            // 非本地单情况减少对应剩余数
            if (!action.payload.localTag) {
              item.availableQty -= action.payload.operateSize;
            }

            // 判断是否存在 normalData 中
            const foundNormalDataItem = newNormalData.find((n) => n.barcode === action.payload.barcode);
            if (foundNormalDataItem) {
              // 存在直接修改操作数
              foundNormalDataItem.qty += action.payload.operateSize;
            } else {
              // 新增到 normalData
              newNormalData.push({
                barcode: action.payload.barcode,
                qty: action.payload.operateSize,
                skuId: item.skuId,
              });
            }
          }
        });

        if (action.payload.localTag) {
          // 本地单操作数为 0 时，表格数据去除该条码
          newTableLinesData = newTableLinesData.filter((n) => n.operateQty > 0);
        }

        newNormalTotalQty = newNormalData.reduce((prev, next) => prev + next.qty, 0);
        // 重新计算异常总数
        newAbnormalTotal = newTableLinesData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
        if (newAbnormalTotal > 0) {
          newNormalTotalQty -= newAbnormalTotal;
        }
      }

      if (action.payload.callback) {
        action.payload.callback();
      }

      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: newNormalData,
        normalTotalQty: newNormalTotalQty,
        abnormalTotal: newAbnormalTotal,
      };
    case 'setErrorDataInTableLine':
      newTableLinesData = [...state.tableLinesData];
      if (action.payload.errorData.length > 0) {
        newTableLinesData = newTableLinesData.map((item) => {
          const errorData = action.payload.errorData.find((n) => n.barcode === item.barcode);
          return {
            ...item,
            errorQty: errorData?.errQty || 0,
            overQty: errorData?.overQty || 0,
          };
        });
        // 重新计算异常总数
        newAbnormalTotal = newTableLinesData.reduce((total, item) => total + item.errorQty + item.overQty, 0);
        newNormalTotalQty = state.normalTotalQty - newAbnormalTotal;
        if (action.payload.callback) {
          action.payload.callback();
        }
      }
      return {
        ...state,
        tableLinesData: newTableLinesData,
        abnormalTotal: newAbnormalTotal,
        normalTotalQty: newNormalTotalQty,
      };
    case 'resetState':
      if (action.payload.localTag) {
        return { ...initialState };
      }
      newTableLinesData = state.tableLinesData.map((item) => ({
        ...item,
        operateQty: 0,
        // 重置时恢复原始剩余数量：已操作数为 0 时，不变，不为零时候把已操作数+剩余数
        availableQty: item.operateQty === 0 ? item.availableQty : item.operateQty + item.availableQty,
        errorQty: 0,
        overQty: 0,
      }));

      return {
        ...state,
        tableLinesData: newTableLinesData,
        normalData: [],
        normalTotalQty: 0,
        abnormalTotal: 0,
      };
    default:
      return state;
  }
}
