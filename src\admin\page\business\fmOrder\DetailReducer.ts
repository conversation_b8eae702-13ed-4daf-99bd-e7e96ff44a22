import { createContext } from 'react';

export type ShipperType = 'WAREHOUSE' | 'SHOP' | 'FACTORY';

export type Result = {
  id: string;
  code: string;
};

export type TState = {
  /** 加载中 */
  loading: boolean;
  /** 单据 */
  orderRecord: Record<string, any>;
  /** 备注编辑框是否显示 */
  remarkModalOpen: boolean;
  /** 入库箱数量 */
  outboundBoxCount: number;
  /** 打印机选择框是否显示 */
  printTaskAddModalOpen: boolean;
  /** 日志弹窗是否显示 */
  logDrawerOpen: boolean;
  /** 日志数据 */
  logs: Record<string, any>[];
};

export const initialState: TState = {
  loading: false,
  orderRecord: {},
  remarkModalOpen: false,
  outboundBoxCount: 0,
  printTaskAddModalOpen: false,
  logDrawerOpen: false,
  logs: [],
  // currentBox: {},
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'reset' }
  | { type: 'setTabsActiveKey'; payload: string }
  | { type: 'setLoading'; payload: boolean }
  | { type: 'setOrderRecord'; payload: Record<string, any> }
  | { type: 'setRemarkEditModalOpen'; payload: boolean }
  | { type: 'setOutboundBoxCount'; payload: number }
  | { type: 'setPrintTaskAddModalOpen'; payload: boolean }
  | { type: 'setBarcodePackingModalModalOpen'; payload: boolean }
  | { type: 'setLogDrawerOpen'; payload: boolean }
  | { type: 'setLogs'; payload: Record<string, any>[] }
  | { type: 'setSelectedRowKeys'; payload: string[] };

export function reducer(state: TStateType, action: TActionType): TStateType {
  switch (action.type) {
    case 'reset':
      return { ...initialState };
    case 'setLoading':
      return { ...state, loading: action.payload };
    case 'setOrderRecord':
      return { ...state, orderRecord: action.payload };
    case 'setRemarkEditModalOpen':
      return { ...state, remarkModalOpen: action.payload };
    case 'setOutboundBoxCount':
      return { ...state, outboundBoxCount: action.payload };
    case 'setPrintTaskAddModalOpen':
      return { ...state, printTaskAddModalOpen: action.payload };
    case 'setLogDrawerOpen':
      return { ...state, logDrawerOpen: action.payload };
    case 'setLogs':
      return { ...state, logs: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TDetailContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const DetailContext = createContext<TDetailContext>({
  state: initialState,
  dispatch: () => undefined,
});
