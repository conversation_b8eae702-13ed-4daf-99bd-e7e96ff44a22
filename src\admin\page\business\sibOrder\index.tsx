import { Button, DatePicker, Input, Space } from 'antd';
import * as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/api/shop/BsibOrder';
import * as SibOrder<PERSON><PERSON> from 'common/api/shop/SibOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import Spin from 'common/components/Spin';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import CheckIcon from 'common/assets/icons/icon-check.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';

import useTableSelection from 'common/hooks/useTableSelection';
import BusinessOrderBatchOperations from 'common/components/BusinessOrderBatchOperations';
import useConfirmOrCancel from 'common/hooks/useConfirmOrCancel';
import { useCommonFn } from './common';

import AddDrawer from './components/AddDrawer';

const WibOrder: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [addDrawerOpen, setAddDrawerOpen] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [permission] = usePermission('A:S:IB');
  const createPermission = permission.codes.includes('CREATE');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');
  const resetPermission = permission.codes.includes('RESET');
  const fastReceiptPermission = permission.codes.includes('FAST_RECEIPT');
  const batchConfirmPermission = permission.codes.includes('BATCH_CONFIRM');
  const batchCancelPermission = permission.codes.includes('BATCH_CANCEL');
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([
    { code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER', defaultValue: 7 },
  ]);

  const navigate = useNavigate();

  const { confirm, reset, fastReceipt, cancel } = useCommonFn();
  const { selectedIds, rowSelection, clearAllSelection } = useTableSelection();
  const { executeConfirmOrCancel } = useConfirmOrCancel();

  let defaultPartnerValue: string[] = [];

  if (currentUser.type === 'SHOP') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    async (params: any, tabActiveKey: string) => {
      if (params.createDateRange) {
        params.createdStart = params.createDateRange[0].startOf('day');
        params.createdEnd = params.createDateRange[1].endOf('day');
      }
      delete params.createDateRange;

      if (params.modified) {
        params.modifiedStart = params.modified[0].startOf('day');
        params.modifiedEnd = params.modified[1].endOf('day');
      }
      delete params.modified;

      if (params.code) {
        params.code = `%${params.code}%`;
      }
      const payload = {
        ...params,
      };

      switch (tabActiveKey) {
        case 'ALL':
          payload.status = ['NEW', 'PROCESSING', 'FINISHED'];
          break;
        case 'NEW':
          payload.status = ['NEW'];
          break;
        case 'PROCESSING':
          payload.status = ['PROCESSING'];
          break;
        case 'FINISHED':
          payload.status = ['FINISHED'];
          break;
        case 'CANCELLED':
          payload.status = ['CANCELED'];
          break;
        default:
          break;
      }
      let result: any;
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.List(payload);
      } else {
        result = await SibOrderApi.List(payload);
      }
      return result;
    },
    [currentUser],
  );

  const addBtnOnClick = () => {
    setAddDrawerOpen(true);
  };

  const addDrawerOnClose = () => {
    setAddDrawerOpen(false);
  };

  const cancelBtnOnClick = async (record: Record<string, any>) => {
    try {
      await cancel(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const fastReceiptBtnOnClick = async (record: Record<string, any>) => {
    try {
      await fastReceipt(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const resetBtnOnclick = async (record: Record<string, any>) => {
    try {
      await reset(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    try {
      await confirm(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const batchConfirmOrCancelOnClick = (type: 'confirm' | 'cancel') => {
    const confirmApi = currentUser.mode === 'BINDING' ? BsibOrderApi.BatchConfirm : SibOrderApi.BatchConfirm;
    const cancelApi = currentUser.mode === 'BINDING' ? BsibOrderApi.BatchCancel : SibOrderApi.BatchCancel;

    executeConfirmOrCancel(
      type,
      confirmApi,
      selectedIds,
      () => {
        clearAllSelection();
        powerTableRef.current?.load();
      },
      cancelApi,
    );
  };

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      width: 200,
      fixed: 'left',
    },
    {
      title: i18n.t('global.additionCode'),
      width: 260,
      ellipsis: true,
      dataIndex: 'sourceCode',
      tooltip: true,
      render: (text, record) => {
        const codes = record?.alias
          ?.filter((item) => item.source !== 'IC')
          ?.map((item) => ({
            label: item.sourceDesc,
            value: item.code,
          }));
        if (codes?.length > 0) {
          return <AdditionCodeViewer codes={codes} />;
        }
        return text;
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      valueType: 'text',
      width: 180,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      valueType: 'number',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PROCESSING: 'blue',
                FINISHED: 'green',
                CANCELED: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.from'),
      width: 180,
      valueType: 'text',
      dataIndex: 'fromPartnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.to'),
      width: 180,
      valueType: 'text',
      dataIndex: 'partnerName',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.type'),
      valueType: 'text',
      dataIndex: 'orderTypeName',
      ellipsis: {
        showTitle: false,
      },
      tooltip: true,
      width: 160,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.lastModified'),
      dataIndex: 'modified',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'fromPartnerIdList',
      label: i18n.t('global.from'),
      inputComponent: <PartnerSelect types={['WAREHOUSE', 'SHOP', 'FACTORY']} sourceType="DEFAULT" multiple />,
    },
    {
      name: 'partnerIds',
      label: i18n.t('global.to'),
      inputComponent: <PartnerSelect types={['SHOP']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'fmOrderCode',
      label: i18n.t('global.fmOrderCode'),
      inputComponent: <Input />,
    },
    {
      name: 'orderTypeId',
      label: i18n.t('global.orderType'),
      inputComponent: <OrderTypeSelect type="IB" categoryCode="SHOP" valueField="id" />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
    {
      name: 'modified',
      label: i18n.t('global.lastModified'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (confirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-orange" />,
      isDisabled: (record) => !(record.status === 'PROCESSING'),
      onClick: (record) => {
        confirmBtnOnClick(record);
      },
    });
  }

  if (fastReceiptPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.fastReceipt'),
      icon: <CheckIcon className="fill-lead-green" />,
      isDisabled: (record) =>
        !(
          (record.status === 'NEW' || record.status === 'PROCESSING') &&
          !record.localTag &&
          record.qty > 0 &&
          record.actQty === 0
        ),
      onClick: (record) => {
        fastReceiptBtnOnClick(record);
      },
    });
  }

  if (resetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      isDisabled: (record) => !(record.status === 'PROCESSING'),
      icon: <ResetIcon className="fill-lead-yellow" />,
      onClick: (record) => {
        resetBtnOnclick(record);
      },
    });
  }

  if (cancelPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.cancel'),
      isDisabled: (record) => !(record.status === 'NEW' || record.status === 'PROCESSING'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        cancelBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues = {
    createDateRange: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      {initialized ? (
        <>
          <AppHeader
            toolbar={
              <Space>
                <BusinessOrderBatchOperations
                  selectedIds={selectedIds}
                  clearAllSelection={clearAllSelection}
                  confirmPermission={batchConfirmPermission}
                  cancelPermission={batchCancelPermission}
                  btnOnClick={batchConfirmOrCancelOnClick}
                />
                {createPermission && (
                  <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                    {i18n.t('global.new')}
                  </Button>
                )}
              </Space>
            }
          />
          <PowerTable
            innerRef={powerTableRef}
            rowKey="id"
            columns={columns}
            quickSearchFieldsConfig={quickSearchFieldsConfig}
            searchFieldsConfig={searchFieldsConfig}
            refreshBtnVisible
            searchPanelVisible={false}
            searchPanelCollapsible
            searchPanelInitialValues={searchPanelInitialValues}
            defaultPageSize={10}
            pagination
            autoLoad
            enableCache
            cacheKey="SIB_ORDER"
            tableProps={{
              sticky: {
                offsetHeader: 96,
              },
              rowSelection: {
                type: 'checkbox',
                ...rowSelection,
                getCheckboxProps: (record: Record<string, any>) => {
                  return {
                    disabled: ['FINISHED', 'CANCELED'].includes(record.status),
                    name: record.code,
                  };
                },
              },
              onRow: (record) => ({
                onClick: () => {
                  navigate(`/app/sib/${record.id}`);
                },
              }),
            }}
            defaultSorter={{ field: 'created', order: 'DESCEND' }}
            tabStatus={[
              {
                code: 'ALL',
                name: i18n.t('global.all'),
              },
              { code: 'NEW', name: i18n.t('global.newOrder') },
              {
                code: 'PROCESSING',
                name: i18n.t('global.processing'),
              },
              {
                code: 'FINISHED',
                name: i18n.t('global.finished'),
              },
              {
                code: 'CANCELLED',
                name: i18n.t('global.cancelled'),
              },
            ]}
            request={fetchData}
          />
          <AddDrawer open={addDrawerOpen} onRefresh={() => powerTableRef.current?.load()} onClose={addDrawerOnClose} />
        </>
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default WibOrder;
