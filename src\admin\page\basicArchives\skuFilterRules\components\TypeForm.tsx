import { Form, Input, Select, Switch } from 'antd';
import i18n from 'common/utils/I18n';
import React from 'react';
import type { FormProps } from 'antd';
import { matchesMap } from '../common';

type FormComponentProps = { codeInputRef: any } & FormProps;

const TypeForm: React.FC<FormComponentProps> = ({ codeInputRef, ...args }) => {
  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const matchesOptions = Object.keys(matchesMap).map((key) => ({
    label: matchesMap[key],
    value: key,
  }));

  return (
    <Form {...args}>
      <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
        <Input ref={codeInputRef} />
      </Form.Item>
      <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.defaultTag')} name="matches" rules={canNotBeNullRules}>
        <Select options={matchesOptions} />
      </Form.Item>
      <Form.Item label={i18n.t('global.disabled')} valuePropName="checked" name="disable">
        <Switch />
      </Form.Item>
      <Form.Item label={i18n.t('global.defaultTag')} valuePropName="checked" name="defaultTag">
        <Switch />
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={4} />
      </Form.Item>
    </Form>
  );
};

export default TypeForm;
