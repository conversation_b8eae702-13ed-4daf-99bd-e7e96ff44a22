import { Form, Input, Switch } from 'antd';
import * as GenderApi from 'common/api/core/Gender';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IGenderItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  genderId: string | undefined;
  onSubmit: (values: IGenderItem) => void;
  onCancel: () => void;
}
const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, genderId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:ATTRIBUTE:GENDER');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchGender = useCallback(async () => {
    setLoading(true);
    try {
      const gender: any = await GenderApi.Get({
        id: genderId,
      });
      if (typeof gender.disabled === 'boolean') {
        gender.status = !gender.disabled;
      }
      form.setFieldsValue(gender);
      setCurrent(gender);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [genderId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (genderId) fetchGender();
      setTimeout(() => {
        if (genderId) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [genderId, visible, form, fetchGender]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IGenderItem);
    }
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editGender')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newGender')
  );

  const onClose = () => {
    onCancel();
    form.resetFields();
  };
  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await GenderApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchGender();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          deletePermission={deletePermission && !!genderId}
          onDelete={deleteBtnOnClick}
          onApply={handleSubmit}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      onClose={onClose}
      open={visible}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input readOnly={!!current} ref={codeInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
