/**
 * 门店相册
 * */
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Space, Tabs } from 'antd';
import * as PhotoAlbumApi from 'common/api/core/PhotoAlbum';
import * as PartnerPhotoAlbumApi from 'common/api/core/PartnerPhotoAlbum';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import SimplePagination from 'common/components/SimplePagination';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';

import AddShopAlbumModal from './AddShopAlbumModal';
import AlbumItem from './AlbumItem';
import EditAlbumModal from './EditAlbumModal';
import UploadPhoto from './UploadPhoto';

import styles from './shopAlbum.module.css';

const { TabPane } = Tabs;
const ShopAlbum = () => {
  const [partnerId, setPartnerId] = useState<string>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [tabsList, setTabsList] = useState<Record<string, any>[]>([]);
  const [tabsKey, setTabsKey] = useState<string>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [hasNext, setHasNext] = useState(false);
  const [photoAlbumData, setPhotoAlbumData] = useState<Record<string, any>[]>([]);
  const [current, setCurrent] = useState<Record<string, any>>();
  const [addAlbumModalVisible, setAddAlbumModalVisible] = useState(false);
  const [adding, setAdding] = useState(false);
  const [editAlbumModalVisible, setEditAlbumModalVisible] = useState(false);
  const [editIng, setEditIng] = useState(false);
  const [uploadPhotoVisible, setUploadPhotoVisible] = useState(false);

  const [permission] = usePermission('A:BASE:ALBUM:STORE');
  const addPermission = permission.codes.includes('CREATE');
  const defaultPermission = permission.codes.includes('DEFAULT');
  const editPermission = permission.codes.includes('EDIT');
  const convertPermission = permission.codes.includes('CONVERT');
  const deletePermission = permission.codes.includes('DELETE');
  const photoPermission = permission.codes.includes('PHOTO');
  const uploadPermission = permission.codes.includes('PHOTO:UPLOAD');
  const deletePhoPermission = permission.codes.includes('PHOTO:DELETE');

  const fetchTabs = useCallback(async () => {
    try {
      const res: any = await PartnerPhotoAlbumApi.ScenesList({});
      if (res.data.length > 0) {
        setTabsList(res.data);
        setTabsKey(res.data[0].code);
      }
    } catch (e) {}
  }, []);

  const fetchData = useCallback(async () => {
    const params: Record<string, any> = {
      firstFileUrlQuery: true,
      numQuery: true,
      enablePage: true,
      currentPage,
      pageSize,
      orderByField: 'default_tag',
      orderByMethod: 'DESCEND',
    };
    if (partnerId) params.partnerId = partnerId;
    try {
      const res: any = await PartnerPhotoAlbumApi.List(params);
      setCurrentPage(Number(res.currentPage));
      setPageSize(res.pageSize);
      setHasNext(res.hasNext);
      setPhotoAlbumData(res.data);
    } catch (e) {}
  }, [partnerId, currentPage, pageSize]);
  const tabsOnChange = (key) => {
    setTabsKey(key);
  };

  const paginationOnChange = (page, pageSize) => {
    setCurrentPage(page === 0 ? 1 : page);
    setPageSize(pageSize);
  };

  const addAlbumModalOnOk = async (record) => {
    const { addType, ...params } = record;
    setAdding(true);
    try {
      if (addType === 'SHOP') {
        params.disable = false;
        await PartnerPhotoAlbumApi.Create(params);
      } else {
        await PartnerPhotoAlbumApi.Copy(params);
      }
      setAddAlbumModalVisible(false);
      NoticeUtil.success();
      fetchData();
    } catch (e) {}
    setAdding(false);
  };

  const defaultOnClick = async (record) => {
    const { disable, id, name, remark, sort } = record;
    try {
      await PartnerPhotoAlbumApi.Update({
        id,
        disable,
        name,
        remark,
        sort,
        defaultTag: true,
      });
      NoticeUtil.success();
      fetchData();
    } catch (e) {}
  };

  const editOnClick = (record) => {
    setCurrent(record);
    setEditAlbumModalVisible(true);
  };

  const editAlbumModalOnOk = async (record) => {
    setEditIng(true);
    try {
      await PartnerPhotoAlbumApi.Update({
        ...record,
        id: current?.id,
      });
      NoticeUtil.success();
      setEditAlbumModalVisible(false);
      fetchData();
    } catch (e) {}
    setEditIng(false);
  };

  const photoOnClick = (record) => {
    setCurrent(record);
    setUploadPhotoVisible(true);
  };

  const convertOnClick = (record) => {
    const { defaultTag, id, name } = record;
    NoticeUtil.confirm({
      title: i18n.t('global.confirmConvertAlbum'),
      content: `${record.name}`,
      okType: 'danger',
      maskTransitionName: '',
      transitionName: '',
      onOk: async () => {
        try {
          await PhotoAlbumApi.Copy({ defaultTag, id, name });
          NoticeUtil.success();
          fetchData();
        } catch (e) {}
      },
    });
  };

  const deleteOnClick = (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDeleteAlbum'),
      content: `${record.name}`,
      okType: 'danger',
      maskTransitionName: '',
      transitionName: '',
      onOk: async () => {
        try {
          await PartnerPhotoAlbumApi.Delete({
            id: record.id,
          });
          NoticeUtil.success();
          fetchData();
        } catch (e) {}
      },
    });
  };

  // 获取当前登录仓库信息
  useEffect(() => {
    if (currentUser && currentUser.type === 'SHOP') {
      setPartnerId(currentUser.partnerId);
    }
  }, [currentUser, setPartnerId]);

  // 获取当前登录仓库相册
  useEffect(() => {
    if (tabsKey) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [tabsKey, partnerId, currentPage, pageSize]);

  // 获取tabs标签页数据
  useEffect(() => {
    fetchTabs();
    // eslint-disable-next-line
  }, []);

  // const showTotal: PaginationProps['showTotal'] = (total) => i18n.t('global.totalDataNum', { num: total });

  return (
    <>
      <PartnerSelect
        style={{ width: 280 }}
        types={['SHOP']}
        sourceType="PERMISSION"
        placeholder={i18n.t('global.pleaseSelectPartner')}
        value={partnerId}
        onChange={(value) => setPartnerId(value)}
      />
      <div className={styles.content}>
        <Tabs defaultActiveKey="1" onChange={tabsOnChange}>
          {tabsList.map((item) => (
            <TabPane tab={item.name} key={item.code} />
          ))}
        </Tabs>
        <Space size={[20, 20]} wrap>
          {addPermission && (
            <Button className={styles.button} onClick={() => setAddAlbumModalVisible(true)}>
              <FontAwesomeIcon icon={faPlus} />
              {i18n.t('global.addAlbum')}
            </Button>
          )}
          {photoAlbumData.map((item) => (
            <AlbumItem
              key={item.id}
              current={item}
              isPartner={currentUser && currentUser.type !== 'SHOP'}
              permission={{
                defaultPermission,
                editPermission,
                convertPermission,
                deletePermission,
                photoPermission,
              }}
              defaultOnClick={defaultOnClick}
              editOnClick={editOnClick}
              photoOnClick={photoOnClick}
              convertOnClick={convertOnClick}
              deleteOnClick={deleteOnClick}
            />
          ))}
        </Space>
      </div>
      <div className={styles.sticky}>
        <SimplePagination
          currentPage={currentPage}
          defaultPageSize={10}
          pageSize={pageSize}
          hasNext={hasNext}
          onChange={paginationOnChange}
        />
      </div>
      <AddShopAlbumModal
        current={{
          ...currentUser,
          tabsKey,
        }}
        partnerId={partnerId}
        visible={addAlbumModalVisible}
        confirmLoading={adding}
        onOk={addAlbumModalOnOk}
        onCancel={() => setAddAlbumModalVisible(false)}
      />
      <EditAlbumModal
        current={current}
        visible={editAlbumModalVisible}
        confirmLoading={editIng}
        onOk={editAlbumModalOnOk}
        onCancel={() => setEditAlbumModalVisible(false)}
      />
      <UploadPhoto
        current={current}
        visible={uploadPhotoVisible}
        permission={{
          uploadPermission,
          deletePhoPermission,
        }}
        onCancel={() => {
          setUploadPhotoVisible(false);
          fetchData();
        }}
      />
    </>
  );
};

export default ShopAlbum;
