import { Form, message, Tabs } from 'antd';
import * as <PERSON><PERSON><PERSON><PERSON> from 'common/api/sample/Sample';
import * as SampleSkuApi from 'common/api/sample/SampleSku';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import useSetting from 'common/hooks/useSetting';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { ISampleItem } from '../data';
import { SampleContext, TSampleContext } from '../reducer';
import BarcodeTable from './BarcodeTable';
import OperateForm, { extFieldFormItemNamePreFix, OperateFormInnerRef } from './OperateForm';

interface IDetailDrawerProps {
  onClose?: () => void;
  open?: boolean;
  title?: ReactNode;
}

const DetailDrawer: React.FC<IDetailDrawerProps> = (props) => {
  const { open, title, onClose } = props;
  const [loading, setLoading] = useState(false);
  const [sampleRecord, setSampleRecord] = useState<Partial<ISampleItem>>({});
  const [activeType, setActiveType] = useState<string>('INFO');
  const operateFormRef = useRef<OperateFormInnerRef>();
  const [form] = Form.useForm();
  const { state, dispatch } = useContext<TSampleContext>(SampleContext);
  const [fetchLoading, setFetchLoading] = useState(false);

  const [permission] = usePermission('A:BASE:SAMPLE');
  const deletePermission = permission.codes.includes('DELETE');

  const [currencyData, setCurrencyData] = useState<any>();
  const { CURRENCY: currency } = useSetting([{ code: 'CURRENCY', valueType: 'STRING' }]);
  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }

  const fetchSample = useCallback(async () => {
    setFetchLoading(true);
    try {
      const rec: any = await SampleApi.Get({
        id: state.sampleId,
      });
      // 处理扩展字段
      if (rec.ext) {
        Object.keys(rec.ext).forEach((key) => {
          rec[extFieldFormItemNamePreFix + key] = rec.ext[key];
        });
      }
      rec.status = !rec.disabled;
      dispatch({ type: 'setSampleRecord', payload: rec });
      setSampleRecord(rec);
      setFetchLoading(false);
    } catch (err) {
      setFetchLoading(false);
    }
    // eslint-disable-next-line
  }, [state.sampleId]);

  const fetchSkuList = useCallback(
    async (payload: { [key: string]: any } = {}) => {
      if (!state.sampleId) return;
      setFetchLoading(true);
      try {
        const params: Record<string, any> = {
          smId: state.sampleId,
          enablePage: false,
          orderByMethod: 'ASCEND',
          orderByField: 'smBarcode',
          ...payload,
        };
        if (typeof state.disabled === 'boolean') {
          params.disabled = state.disabled;
        }
        const skuList: any = await SampleSkuApi.List(params);
        dispatch({ type: 'setSkuList', payload: skuList.data });
        setFetchLoading(false);
      } catch (err) {
        setFetchLoading(false);
      }
    },
    // eslint-disable-next-line
    [state.sampleId, state.disabled],
  );

  useEffect(() => {
    fetchSkuList();
    // eslint-disable-next-line
  }, [state.disabled])


  useEffect(() => {
    if (open) {
      fetchSample();
      fetchSkuList();
      setTimeout(() => {
        operateFormRef.current?.focusNameInput();
      }, 300);
    } else {
      setSampleRecord({});
      form.resetFields();
      setActiveType('INFO');
    }
    // eslint-disable-next-line
  }, [open, form]);

  const onSave = () => {
    if (form) form.submit();
  };
  const onFinish = async (values) => {
    setLoading(true);
    try {
      const ext = {};
      Object.keys(values).forEach((key) => {
        if (key.startsWith(extFieldFormItemNamePreFix)) {
          ext[key.split(extFieldFormItemNamePreFix)[1]] = values[key];
          delete values[key];
        }
      });
      if (state.skuList.length > 0) {
        if (values.smColorGrpId !== sampleRecord.smColorGrpId) {
          message.warning(i18n.t('global.noChangeColorGrpWithSku'));
          setLoading(false);
          return;
        }
        if (values.smSizeGrpId !== sampleRecord.smSizeGrpId) {
          message.warning(i18n.t('global.noChangeSizeGrpWithSku'));
          setLoading(false);
          return;
        }
      }

      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
      }
      if (activeType === 'INFO') {
        await SampleApi.Update({
          id: sampleRecord.id,
          ...values,
          ext,
        });
      }

      setLoading(false);
      NoticeUtil.success();
      if (onClose) onClose();
    } catch (e) {
      setLoading(false);
    }
  };

  const deleteBtnOnClick = () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${sampleRecord.code} - ${sampleRecord.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleApi.Delete({ id: sampleRecord.id });
          NoticeUtil.success();
          if (onClose) onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    setActiveType('INFO');
    fetchSample();
    fetchSkuList();
  };

  const items: any[] = [];

  items.push({
    label: i18n.t('global.information'),
    key: 'INFO',
    children: (
      <OperateForm
        form={form}
        innerRef={operateFormRef}
        refresh={() => fetchSample()}
        codeReadOnly
        statusVisible
        imageVisible
        onFinish={onFinish}
      />
    ),
  });

  items.push({
    label: `${i18n.t('global.barcodes')} (${state.skuList.length})`,
    key: 'CUSTOM',
    children: <BarcodeTable fetchSkuList={fetchSkuList} />,
  });

  return (
    <Drawer
      title={title}
      open={open}
      width={1000}
      maskClosable={false}
      destroyOnClose
      okText={i18n.t('global.apply')}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          onApply={onSave}
          deletePermission={deletePermission}
          onDelete={deleteBtnOnClick}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={fetchLoading}>
        <Tabs
          size="small"
          onChange={(key) => {
            setActiveType(key);
          }}
          animated={false}
          activeKey={activeType}
          items={items}
        />
      </Spin>
    </Drawer>
  );
};

export default DetailDrawer;
