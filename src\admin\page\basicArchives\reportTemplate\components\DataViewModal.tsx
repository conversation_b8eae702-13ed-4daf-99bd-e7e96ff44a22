import { Typography } from 'antd';
import { baseUrl } from 'common/api/Base';
import * as DataSourceApi from 'common/api/report/DataSource';
import Modal from 'common/components/Modal';
import PowerTable from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';

const { Paragraph } = Typography;

interface ITableParam {
  businessType?: string;
  module?: string;
  templateType?: string;
}

interface DataViewModalProps {
  open: boolean;
  tableParam: ITableParam;
  onCancel: () => void;
}

const DataViewModal: React.FC<DataViewModalProps> = (props) => {
  const { open, tableParam, onCancel } = props;
  const [tableData, setTableData] = useState<any[]>([]);
  const [urlData, setUrlData] = useState<string>('');

  const fetchData = async () => {
    try {
      const arr: any[] = [];
      const res: any = await DataSourceApi.Info({
        businessType: tableParam.businessType,
        module: tableParam.module,
        templateType: tableParam.templateType,
      });
      // for (const n in res.data[0]) {
      //   arr.push({ key: n, desc: res.data[0][n] });
      // }
      Object.keys(res.data[0]).forEach((key) => {
        arr.push({
          key,
          desc: res.data[0][key],
        });
      });
      setTableData(arr);
      setUrlData(baseUrl + res.url);
    } catch (e) {}
  };

  useEffect(() => {
    if (open) {
      fetchData();
    } else {
      setTableData([]);
      setUrlData('');
    }
    // eslint-disable-next-line
  }, [open]);

  const tableColumns = [
    {
      title: 'key',
      dataIndex: 'key',
      width: 200,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'desc',
      width: 200,
    },
  ];

  return (
    <Modal
      title={i18n.t('global.templateDetails')}
      width={740}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={open}
      footer={false}
      onCancel={onCancel}
    >
      <span>
        {i18n.t('global.templateCheckAddress')}
        <Paragraph copyable>{urlData}</Paragraph>
      </span>
      <PowerTable
        initialized
        rowKey="key"
        columns={tableColumns}
        pagination={false}
        tableProps={{ size: 'small', dataSource: tableData, scroll: { x: 600, y: 560 } }}
        refreshBtnVisible={false}
      />
    </Modal>
  );
};

export default DataViewModal;
