import * as SptsOrderApi from 'common/api/sample/SpTsOrder';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback } from 'react';

export const useCommonFn = () => {
  const reset = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.resetTsOrderConfirm'),
          content: orderCode,
          onOk: async () => {
            try {
              await SptsOrderApi.Reset({ id: orderId });
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [],
  );

  const start = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.startTsOrderConfirm'),
          content: orderCode,
          onOk: async () => {
            try {
              await SptsOrderApi.Start({ id: orderId });
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [],
  );

  const confirm = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.applyTsOrderConfirm'),
          content: orderCode,
          onOk: async () => {
            try {
              await SptsOrderApi.Confirm({ id: orderId });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [],
  );

  const cancel = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode,
          okType: 'danger',
          onOk: async () => {
            try {
              await SptsOrderApi.Cancel({ id: orderId });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [],
  );

  return { reset, start, confirm, cancel };
};
