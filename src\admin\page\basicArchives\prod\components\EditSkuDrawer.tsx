import SkuOperateForm from 'admin/page/basicArchives/prod/components/SkuOperateForm';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { ISkuItem } from '../data';

interface IEditSkuDrawerProps {
  visible: boolean;
  confirmLoading: boolean;
  current: Partial<ISkuItem> | undefined;
  onSubmit: (values: ISkuItem) => void;
  onDelete: (values: Partial<ISkuItem> | undefined) => void;
  onCancel: () => void;
}

const EditSkuDrawer: React.FC<IEditSkuDrawerProps> = (props) => {
  const [form] = Form.useForm();
  const { current, onSubmit, onDelete, onCancel, visible, confirmLoading } = props;
  const skuOperateFormRef = useRef<any>(null);

  const handleSubmit = () => {
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
      }
      onSubmit(values as ISkuItem);
    }
  };

  const onDeleteBtnonClick = () => {
    if (onDelete && current) onDelete(current);
  };

  const onRecover = () => {
    if (skuOperateFormRef.current) {
      skuOperateFormRef.current.recover();
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible, form]);

  return (
    <Drawer
      title={`${i18n.t('global.editBarcode')} [${current?.barcode}]`}
      width={790}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      onClose={() => {
        if (onCancel) onCancel();
        form.resetFields();
      }}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission
          onDelete={onDeleteBtnonClick}
          onRecover={onRecover}
        />
      }
    >
      <SkuOperateForm
        innerRef={skuOperateFormRef}
        current={current}
        statusVisible
        form={form}
        onFinish={handleFinish}
      />
    </Drawer>
  );
};

export default EditSkuDrawer;
