import StorageOperateDrawer from 'admin/page/basicArchives/partner/components/StorageOperateDrawer';
import { IWarehouseItem } from 'admin/page/basicArchives/partner/data';
import { Button } from 'antd';
import * as WarehouseApi from 'common/api/core/Warehouse';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import CheckIcon from 'common/assets/icons/icon-check.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

export interface StorageProps {
  type?: string;
  partnerRecord?: Record<string, any>;
}

const Storage: React.FC<StorageProps> = (props) => {
  const { type, partnerRecord } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const recordItem = useRef<Partial<IWarehouseItem> | undefined>();
  const [open, setOpen] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [permission] = usePermission('A:BASE:PARTNER');
  const createWPermission = permission.codes.includes('CREATE_W');
  const editPermission = permission.codes.includes('EDIT_W');
  const deletePermission = permission.codes.includes('DEL_W');

  const fetchData = useCallback(
    async (payload: { [key: string]: any } = {}) => {
      const params: any = {
        partnerIds: [partnerRecord?.id],
        enablePage: false,
        ...payload,
      };
      return WarehouseApi.List(params);
    },
    [partnerRecord],
  );

  const handleCancel = () => {
    setOpen(false);
    recordItem.current = undefined;
  };

  const handleSubmit = async (values) => {
    const id = recordItem.current ? recordItem.current.id : '';

    setConfirmLoading(true);
    values.type = type;
    values.disabled = id ? !values.status : false;

    delete values.status;

    try {
      if (id) {
        await WarehouseApi.Update({
          id,
          ...values,
        });
      } else {
        await WarehouseApi.Create({
          partnerId: partnerRecord?.id,
          ...values,
        });
      }
      recordItem.current = undefined;
      setOpen(false);
      setConfirmLoading(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const defaultBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmSetDefault'),
      content: `${record.code} - ${record.name}`,
      onOk: async () => {
        try {
          await WarehouseApi.SetDefault({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const deleteBtnOnClick = async (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await WarehouseApi.Delete({ id: record.id });
          NoticeUtil.success();
          setOpen(false);
          recordItem.current = undefined;
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.codeOrAccount'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      sorter: true,
      auto: true,
      minWidth: 150,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      valueType: 'text',
      sorter: true,
      width: 150,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.isDefault'),
      dataIndex: 'defaulted',
      valueType: 'boolean',
      sorter: true,
      width: 120,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.allowStocktaking'),
      dataIndex: 'allowTakeStock',
      valueType: 'boolean',
      sorter: true,
      width: 250,
      tooltip: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
      tooltip: true,
      ellipsis: true,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.setDefault'),
    icon: <CheckIcon className="fill-lead-green" />,
    isDisabled: (record) => record.defaulted,
    onClick: (record) => {
      defaultBtnOnClick(record);
    },
  });

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        tabDefaultActiveKey="WAREHOUSE"
        enableDisabledTrigger
        rightToolbar={[
          createWPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={() => setOpen(true)}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={20}
        settingToolVisible
        pagination
        autoLoad
        tableProps={{
          // bordered: true,
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  recordItem.current = record;
                  setOpen(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <StorageOperateDrawer
        current={recordItem.current}
        open={open}
        confirmLoading={confirmLoading}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
        onDelete={deleteBtnOnClick}
      />
    </>
  );
};

export default Storage;
