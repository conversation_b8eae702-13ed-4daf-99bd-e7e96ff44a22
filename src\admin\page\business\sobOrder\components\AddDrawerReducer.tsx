import { createContext } from 'react';

export type ReceiverType = 'WAREHOUSE' | 'SHOP' | 'FACTORY';

export type Result = {
  id: string;
  code: string;
};

export type TState = {
  /** 当前步骤 */
  currentStep: number;
  /** 发货方类型 */
  receiverType: ReceiverType;
  /** 发货方ID */
  receiverId?: string;
  /** 保存中 */
  saving: boolean;
  /** 成功 */
  success: boolean;
  /** 结果 */
  result?: Result;
  /** 错误信息 */
  errorMsg?: string;
};

export const initialState: TState = {
  currentStep: 0,
  receiverType: 'SHOP',
  receiverId: undefined,
  saving: false,
  success: false,
  result: undefined,
  errorMsg: undefined,
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'reset' }
  | { type: 'setCurrentStep'; payload: number }
  | { type: 'addCurrentStep'; payload: number }
  | { type: 'setReceiverType'; payload: ReceiverType }
  | { type: 'setReceiverId'; payload: string }
  | { type: 'setSaving'; payload: boolean }
  | { type: 'setSuccess'; payload: boolean }
  | { type: 'setResult'; payload: Result }
  | { type: 'setErrorMsg'; payload: string };

export function reducer(state: TStateType, action: TActionType): TStateType {
  switch (action.type) {
    case 'reset':
      return { ...initialState };
    case 'setCurrentStep':
      return { ...state, currentStep: action.payload };
    case 'addCurrentStep':
      return { ...state, currentStep: state.currentStep + action.payload };
    case 'setReceiverType':
      return { ...state, receiverType: action.payload };
    case 'setReceiverId':
      return { ...state, receiverId: action.payload };
    case 'setSaving':
      return { ...state, saving: action.payload };
    case 'setSuccess':
      return { ...state, success: action.payload };
    case 'setResult':
      return { ...state, result: action.payload };
    case 'setErrorMsg':
      return { ...state, errorMsg: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TAddDrawerContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const AddDrawerContext = createContext<TAddDrawerContext>({
  state: initialState,
  dispatch: () => undefined,
});
