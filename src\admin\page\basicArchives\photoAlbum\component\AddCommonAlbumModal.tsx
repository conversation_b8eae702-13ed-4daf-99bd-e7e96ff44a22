/**
 * Create by codegen at 2022-06-28
 * 公共相册 - 新增
 */
import { Checkbox, Col, Form, Input, InputNumber, Row } from 'antd';
import CommonAlbumScenesSelect from 'common/components/Select/CommonAlbumScenesSelect';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';
import Modal from 'common/components/Modal';

interface AddCommonAlbumModalProps {
  current?: Record<string, any>;
  visible?: boolean;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

const AddCommonAlbumModal: React.FC<AddCommonAlbumModalProps> = (props) => {
  const { current, visible, confirmLoading, onOk, onCancel } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  useEffect(() => {
    let timerId: number | null = null;
    if (visible && current) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
      form.setFieldsValue({
        scenes: current.tabsKey,
      });
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
    // eslint-disable-next-line
  }, [visible, current]);

  return (
    <Modal
      title={i18n.t('global.addCommonAlbum')}
      open={visible}
      confirmLoading={confirmLoading}
      onOk={modalOnOk}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      transitionName=""
      maskTransitionName=""
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish} style={{ marginTop: 16 }}>
        <Form.Item name="scenes" label={i18n.t('global.type')} rules={canNotBeNullRules}>
          <CommonAlbumScenesSelect />
        </Form.Item>
        <Form.Item name="name" label={i18n.t('global.albumName')} rules={canNotBeNullRules}>
          <Input ref={inputRef} />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={2} />
        </Form.Item>
        <Form.Item label={i18n.t('global.sort')} name="sort">
          <InputNumber />
        </Form.Item>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              wrapperCol={{ span: 16, offset: 0 }}
              valuePropName="checked"
              name="defaultTag"
              style={{ marginLeft: 0 }}
            >
              <Checkbox>{i18n.t('global.isDefault')}</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddCommonAlbumModal;
