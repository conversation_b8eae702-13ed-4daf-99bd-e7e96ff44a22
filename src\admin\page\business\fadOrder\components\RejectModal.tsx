import { Form } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect } from 'react';

interface IRejectModalProps {
  visible: boolean;
  onCancel: () => void;
  form?: any;
  onSave: (value) => void;
  fadOrderId: string;
}

const RejectModal: React.FC<IRejectModalProps> = (props) => {
  const { visible, onCancel, onSave, fadOrderId } = props;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];
  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  const onOk = async () => {
    const value = await form.validateFields();
    value.fadOrderId = fadOrderId;
    if (onSave) {
      onSave(value);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      okButtonProps={{ danger: true }}
      onOk={onOk}
      title={i18n.t('global.rejectFadOrder')}
    >
      <Form form={form} layout="vertical">
        <Form.Item name="remark" rules={canNotBeNullRules} label={i18n.t('global.reason')}>
          <TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RejectModal;
