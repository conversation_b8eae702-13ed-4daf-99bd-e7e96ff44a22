import TabsMenuItem from 'common/components/TabsMenuItem';
import React, { useEffect, useState } from 'react';
import { faPenToSquare, faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Space, Tag } from 'antd';
import { usePermission } from 'common/utils/Permission';
import classNames from 'classnames';
import i18n from 'common/utils/I18n';
import { matchesMap } from '../common';

export interface TabsMenuProps {
  code?: string;
  menuList: Record<string, any>[];
  onClick: (path: string) => void;
  onChangeClick: (values: any) => void;
  onDelClick: (values: any) => void;
}
const TabsMenu: React.FC<TabsMenuProps> = (props) => {
  const { menuList, code, onClick, onChangeClick, onDelClick } = props;

  const [permission] = usePermission('A:LABEL:SFR');
  const typeEditPermission = permission.codes.includes('EDIT_TYPE');
  const typeDeletePermission = permission.codes.includes('DELETE_TYPE');

  const [activeCode, setActiveCode] = useState<string>('');

  useEffect(() => {
    if (code) setActiveCode(code);
  }, [code]);

  const menuOnClick = (value) => {
    onClick(value);
  };

  const menuItemOnChangeClick = (value) => {
    onChangeClick(value);
  };
  const menuItemOnDelClick = (value) => {
    onDelClick(value);
  };

  return (
    <>
      {menuList.map((item: Record<string, any>) => {
        return (
          <TabsMenuItem
            key={item.code}
            code={item.code}
            className="w-full"
            name={
              <div className="flex w-full">
                <div className="flex flex-1 flex-col gap-1">
                  <div className="flex-1 break-all">{item.name}</div>
                  <span className="flex-1 break-all">{matchesMap[item.matches] ?? item.matchesDesc}</span>
                  <span className="flex-1 break-all">{item.remark}</span>
                  <span className="flex flex-1 gap-2">
                    {item.defaultTag && <Tag color="processing">{i18n.t('global.default')}</Tag>}
                    {item.disable && <Tag color="default">{i18n.t('global.disabled')}</Tag>}
                  </span>
                </div>
                <div className="w-[42px]">
                  <Space
                    className={classNames('pl-2 group-hover:flex', {
                      flex: activeCode === item.code,
                      hidden: activeCode !== item.code,
                    })}
                  >
                    {typeEditPermission && (
                      <FontAwesomeIcon icon={faPenToSquare} onClick={() => menuItemOnChangeClick(item)} />
                    )}
                    {typeDeletePermission && (
                      <FontAwesomeIcon icon={faTrashCan} onClick={() => menuItemOnDelClick(item)} />
                    )}
                  </Space>
                </div>
              </div>
            }
            activeCode={activeCode}
            onClick={menuOnClick}
          />
        );
      })}
    </>
  );
};

export default TabsMenu;
