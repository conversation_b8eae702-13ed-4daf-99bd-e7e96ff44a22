/**
 * Create by codegen at 2022-06-28
 * 门店相册 - 新增
 */
import { Checkbox, Col, Form, Input, InputNumber, Radio, Row, Space, Tabs } from 'antd';
import * as PhotoAlbumApi from 'common/api/core/PhotoAlbum';
import AlbumScenesSelect from 'common/components/Select/AlbumScenesSelect';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import i18n from 'common/utils/I18n';
import Modal from 'common/components/Modal';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as NoticeUtil from 'common/utils/Notice';

import AlbumItem from './AlbumItem';

const { TabPane } = Tabs;

interface AddAlbumModalProps {
  current?: Record<string, any>;
  visible?: boolean;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
  partnerId?: string;
}

type RadioKeyType = 'SHOP' | 'PUBLIC';

const AddShopAlbumModal: React.FC<AddAlbumModalProps> = (props) => {
  const { current, visible, confirmLoading, onOk, onCancel, partnerId } = props;
  const [form] = Form.useForm();
  const [radioKey, setRadioKey] = useState<RadioKeyType>('SHOP');
  const [tabsList, setTabsList] = useState<Record<string, any>[]>([]);
  const [tabsKey, setTabsKey] = useState<string>();
  const [photoAlbumData, setPhotoAlbumData] = useState<Record<string, any>[]>([]);
  const [selectedData, setSelectedData] = useState<Record<string, any>>();
  const [selectKeys, setSelectKeys] = useState<string[]>([]);
  const inputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const fetchTabs = useCallback(async () => {
    try {
      const res: any = await PhotoAlbumApi.ScenesList({});
      if (res.data.length > 0) {
        setTabsList(res.data);
        setTabsKey(res.data[0].code);
      }
    } catch (e) {}
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const res: any = await PhotoAlbumApi.List({
        firstFileUrlQuery: true,
        numQuery: true,
        isPageable: false,
        scenes: tabsKey,
        orderByField: 'defaultTag',
        orderByMethod: 'DESCEND',
      });
      setPhotoAlbumData(res.data);
    } catch (e) {}
  }, [tabsKey]);

  const radioOnChange = (e) => {
    if (e.target.value === 'SHOP') {
      setSelectedData(undefined);
      setSelectKeys([]);
    }
    form.setFieldsValue({
      name: '',
      defaultTag: false,
    });
    setRadioKey(e.target.value);
  };

  const tabsOnChange = (key) => {
    setTabsKey(key);
  };

  const selectOnClick = (record) => {
    if (selectedData?.id !== record.id) {
      setSelectedData(record);
      setSelectKeys([record.id]);
    }
  };

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    const params = {
      ...values,
      addType: radioKey,
    };
    if (radioKey === 'PUBLIC') {
      if (!selectedData) {
        NoticeUtil.error(i18n.t('global.pleaseSelectAlbum'));
        return;
      }
      params.id = selectedData?.id;
    }
    if (onOk) onOk(params);
  };

  useEffect(() => {
    form.setFieldsValue({
      name: selectedData?.name ?? '',
    });
    // eslint-disable-next-line
  }, [selectedData]);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  // 获取当前登录仓库相册
  useEffect(() => {
    if (tabsKey) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [tabsKey]);

  useEffect(() => {
    let timerId: number | null = null;
    if (visible && current) {
      form.setFieldsValue({
        scenes: current.tabsKey,
        partnerId,
      });
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
      fetchTabs();
    } else {
      setRadioKey('SHOP');
      setPhotoAlbumData([]);
      setTabsKey(undefined);
      setSelectKeys([]);
      setSelectedData(undefined);
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
    // eslint-disable-next-line
  }, [visible, current, partnerId]);

  return (
    <Modal
      title={i18n.t('global.addStoreAlbum')}
      open={visible}
      confirmLoading={confirmLoading}
      width={radioKey === 'PUBLIC' ? 1000 : undefined}
      onOk={modalOnOk}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      transitionName=""
      maskTransitionName=""
    >
      <Radio.Group defaultValue="SHOP" buttonStyle="solid" onChange={radioOnChange}>
        <Radio.Button value="SHOP">{i18n.t('global.newAlbum')}</Radio.Button>
        <Radio.Button value="PUBLIC">{i18n.t('global.copyCommonAlbum')}</Radio.Button>
      </Radio.Group>
      {radioKey === 'SHOP' && (
        <Form layout="vertical" form={form} onFinish={formOnFinish} style={{ marginTop: 16 }}>
          <Form.Item name="partnerId" label={i18n.t('global.shop')} rules={canNotBeNullRules}>
            <PartnerSelect types={['SHOP']} sourceType="PERMISSION" placeholder={i18n.t('global.selectShop')} />
          </Form.Item>
          <Form.Item name="scenes" label={i18n.t('global.type')} rules={canNotBeNullRules}>
            <AlbumScenesSelect />
          </Form.Item>
          <Form.Item name="name" label={i18n.t('global.albumName')} rules={canNotBeNullRules}>
            <Input />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item label={i18n.t('global.sort')} name="sort">
            <InputNumber />
          </Form.Item>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                wrapperCol={{ span: 16, offset: 0 }}
                valuePropName="checked"
                name="defaultTag"
                style={{ marginLeft: 0 }}
              >
                <Checkbox>{i18n.t('global.isDefault')}</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
      {radioKey === 'PUBLIC' && (
        <>
          <Form layout="vertical" form={form} onFinish={formOnFinish} style={{ marginTop: 16 }}>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="partnerId" label={i18n.t('global.shop')} rules={canNotBeNullRules}>
                  <PartnerSelect types={['SHOP']} sourceType="PERMISSION" placeholder={i18n.t('global.selectShop')} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="name" label={i18n.t('global.albumName')} rules={canNotBeNullRules}>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  wrapperCol={{ span: 16, offset: 0 }}
                  valuePropName="checked"
                  name="defaultTag"
                  style={{ marginLeft: 0, marginBottom: 0 }}
                >
                  <Checkbox>{i18n.t('global.isDefault')}</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <Tabs defaultActiveKey="1" onChange={tabsOnChange}>
            {tabsList.map((item) => (
              <TabPane tab={item.name} key={item.code} />
            ))}
          </Tabs>
          <Space size={[20, 20]} wrap style={{ height: 400, overflow: 'auto' }}>
            {photoAlbumData.map((item) => (
              <AlbumItem
                key={item.id}
                current={item}
                isLoading={false}
                isSelectIcon={selectKeys.includes(item.id)}
                isBadge={false}
                selectOnClick={selectOnClick}
              />
            ))}
          </Space>
        </>
      )}
    </Modal>
  );
};

export default AddShopAlbumModal;
