import { Button } from 'antd';
import * as SampleSkuImgApi from 'common/api/sample/SampleSkuImg';
import ImgUpload from 'common/components/ImgUpload';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { SampleContext, TSampleContext } from '../reducer';

interface IEditImgProps {
  visible: boolean;
  onCancel: () => void;
}

const EditImgModal: React.FC<IEditImgProps> = (props) => {
  const { onCancel, visible } = props;
  const { state } = useContext<TSampleContext>(SampleContext);
  const [defaultImgId, setDefaultImgId] = useState<string>();
  const [imgList, setImgList] = useState<any[]>([]);

  const handleDefaultImg = async (id) => {
    try {
      await SampleSkuImgApi.SetDefault({ id });
      setDefaultImgId(id);
    } catch (e) {}
  };

  const fetchImgList = useCallback(async () => {
    const res = await SampleSkuImgApi.List({
      smIds: [state.sampleRecord?.id],
      enablePage: false,
    });
    let imgList: any[] = [];
    if (res.data.length > 0) {
      imgList = res.data.filter((n) => !n.smSkuId);
      const defaultImgId = imgList.find((n) => n.defaultTag === true)?.id;
      if (defaultImgId) {
        handleDefaultImg(defaultImgId);
      } else {
        handleDefaultImg(res.data[0].id);
      }
    }
    setImgList(imgList);
  }, [state.sampleRecord]);

  const handleSaveImg = async (source) => {
    try {
      const data: any = [
        {
          source,
          // smCode: state.sampleRecord?.code,
          smId: state.sampleRecord?.id,
        },
      ];
      await SampleSkuImgApi.Save({ data });
      fetchImgList();
    } catch (e) {}
  };

  const handleDeleteImg = async (id) => {
    try {
      await SampleSkuImgApi.Delete({ ids: [id] });
      fetchImgList();
    } catch (e) {}
  };

  useEffect(() => {
    if (!visible) {
      setImgList([]);
    } else {
      fetchImgList();
    }
  }, [visible, fetchImgList]);

  return (
    <Modal
      title={i18n.t('global.editSampleImg')}
      width={1000}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button
          key="back"
          onClick={() => {
            if (onCancel) {
              onCancel();
            }
          }}
        >
          {i18n.t('global.close')}
        </Button>,
      ]}
    >
      <ImgUpload
        defaultImg
        handleDefaultImg={handleDefaultImg}
        handleSaveImg={handleSaveImg}
        handleDeleteImg={handleDeleteImg}
        defaultImgId={defaultImgId}
        value={imgList}
      />
    </Modal>
  );
};

export default EditImgModal;
