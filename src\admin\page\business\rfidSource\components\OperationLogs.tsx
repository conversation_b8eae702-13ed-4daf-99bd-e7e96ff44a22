import { Timeline, Tooltip } from 'antd';
import CloseCircleFillIcon from 'common/assets/icons/icon-close-circle-fill.svg?react';
import FactoryFillIcon from 'common/assets/icons/icon-factory-fill.svg?react';
import StoreFillIcon from 'common/assets/icons/icon-store-fill.svg?react';
import WarehouseFillIcon from 'common/assets/icons/icon-warehouse-fill.svg?react';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { ReactNode, useEffect, useState } from 'react';

import { LogsItem } from './Type';

import './OperationLogs.css';

interface OperationOrderLogsType {
  data: LogsItem[];
}
type IconListItem = {
  icon: ReactNode;
  color: string;
};

type IconListKey = 'WAREHOUSE' | 'SHOP' | 'FACTORY';

const iconList: Record<IconListKey, IconListItem> = {
  WAREHOUSE: {
    icon: <WarehouseFillIcon className="fill-white" />,
    color: 'bg-lead-blue',
  },
  SHOP: {
    icon: <StoreFillIcon className="fill-white" />,
    color: 'bg-lead-purple',
  },
  FACTORY: {
    icon: <FactoryFillIcon className="fill-white" />,
    color: 'bg-lead-green',
  },
};

const OperationLogs: React.FC<OperationOrderLogsType> = (props) => {
  const { data } = props;
  const [renderData, setRenderData] = useState<LogsItem[][]>([]);
  const getItemIcon = (type: string, isDefault?: boolean) => {
    const { icon, color } = iconList[type] || { icon: null, color: null };
    if (!icon && !color) return '';
    const _isDefault = isDefault || false;
    return _isDefault ? (
      <div className={`h-3 w-3 rounded-full ${color}`} />
    ) : (
      <div className={`flex h-6 w-6 items-center justify-center rounded-full ${color}`}>{icon}</div>
    );
  };

  const createdLogsItem = (data: any, pre: 'p' | 'fromP' | 'toP') => ({
    ...data,
    renderPartnerCode: data[`${pre}artnerCode`],
    renderPartnerName: data[`${pre}artnerName`],
    renderPartnerType: data[`${pre}artnerType`],
    renderPartnerTypeDesc: data[`${pre}artnerTypeDesc`],
    icon: getItemIcon(data[`${pre}artnerType`] || ''),
  });

  const getRenderData = () => {
    let oldItem: LogsItem | undefined;
    const groupData: LogsItem[][] = [];
    data.forEach((item) => {
      // 初始化图标
      item.icon = getItemIcon(item.partnerType, true);
      const obj = Object.keys(item);
      let newItem: LogsItem = { ...item };
      if (!obj.includes('fromPartnerCode')) {
        newItem = {
          ...newItem,
          fromPartnerCode: null,
          fromPartnerId: null,
          fromPartnerName: null,
          fromPartnerType: null,
          fromPartnerTypeDesc: null,
        };
      }
      if (!obj.includes('toPartnerCode')) {
        newItem = {
          ...newItem,
          toPartnerCode: null,
          toPartnerId: null,
          toPartnerName: null,
          toPartnerType: null,
          toPartnerTypeDesc: null,
        };
      }
      // 判断单号存在且是否相同，相同则放在同一个数组中
      if (oldItem?.orderCode && item.orderCode && oldItem.orderCode === item.orderCode) {
        groupData[groupData.length - 1].push(newItem);
      } else {
        groupData.push([newItem]);
      }
      oldItem = item;
    });

    const mergerData: LogsItem[][] = [];
    groupData.forEach((item) => {
      if (mergerData.length === 0) {
        mergerData.push(item);
        return;
      }
      const last = mergerData[mergerData.length - 1].slice(-1)[0];
      const start = item[0];
      if (last.fromPartnerId === start.partnerId && last.partnerId === start.toPartnerId) {
        // 判断两条数据是否是不同仓库的出入库，例如：工厂A 出库 仓库B 入库
        mergerData[mergerData.length - 1].push(...item);
      } else if (
        !last.fromPartnerId &&
        last.toPartnerId &&
        start.fromPartnerId &&
        !start.toPartnerId &&
        last.partnerId === start.partnerId
      ) {
        // 判断两条数据是否是同一个仓库的出入库， 例如：仓库A 出库 无from 有to     仓库A 入库 有from 无to
        // 中间插入一个仓库节点
        mergerData[mergerData.length - 1].push(createdLogsItem(last, 'p'), ...item);
      } else if (
        ((!last.fromPartnerId && !last.toPartnerId && (start.fromPartnerId || start.toPartnerId)) ||
          ((last.fromPartnerId || last.toPartnerId) && !start.fromPartnerId && !start.toPartnerId) ||
          (!last.fromPartnerId && last.toPartnerId && !start.fromPartnerId && start.toPartnerId) ||
          (!last.fromPartnerId && !last.toPartnerId && !start.fromPartnerId && !start.toPartnerId)) &&
        last.partnerId === start.partnerId
      ) {
        // 上一条数据没有from和to,下一条数据有from或者to 且 两条数据的partnerId相同
        // 上一条数据有from或者to，下一条数据没有from和to 且 两条数据的partnerId相同
        // 上一条数据没有from和to,下一条数据也没有from和to 且 两条数据的partnerId相同
        // 例如：上一条为盘点单，下一条为入库单， 或者 上一条为出库单，下一条为盘点单 或者 上一条为盘点单，下一条为导入
        mergerData[mergerData.length - 1].push(...item);
      } else {
        mergerData.push(item);
      }
    });

    mergerData.forEach((item) => {
      if (item.length > 1) {
        const startObj = item[0];
        const lastObj = item[item.length - 1];
        if (startObj.fromPartnerId && !startObj.toPartnerId) {
          // 有from，无to，则是入库单
          item.unshift(createdLogsItem(startObj, 'p'));
        } else if (!startObj.fromPartnerId && startObj.toPartnerId) {
          // 无from，有to，则是出库单
          item.unshift(createdLogsItem(startObj, 'toP'));
        } else if (startObj.partnerId) {
          item.unshift(createdLogsItem(startObj, 'p'));
        }

        if (lastObj.fromPartnerId && !lastObj.toPartnerId) {
          if (lastObj.fromPartnerId && lastObj.partnerId) {
            item.push(createdLogsItem(lastObj, 'fromP'));
          } else {
            // 有from，无to，则是入库单
            item.push(createdLogsItem(lastObj, 'p'));
          }
        } else if (!lastObj.fromPartnerId && lastObj.toPartnerId) {
          if (lastObj.toPartnerId && lastObj.partnerId) {
            item.push(createdLogsItem(lastObj, 'p'));
          } else {
            // 无from，有to，则是出库单
            item.push(createdLogsItem(lastObj, 'toP'));
          }
        } else if (lastObj.partnerId) {
          item.push(createdLogsItem(lastObj, 'p'));
        }
      } else if (item.length === 1) {
        const obj = item[0];
        if (obj.fromPartnerId && !obj.toPartnerId) {
          // 有from，无to，则是入库单
          item.unshift(createdLogsItem(obj, 'p'));
          item.push(createdLogsItem(obj, 'fromP'));
        } else if (!obj.fromPartnerId && obj.toPartnerId) {
          // 无from，有to，则是出库单
          item.unshift(createdLogsItem(obj, 'toP'));
          item.push(createdLogsItem(obj, 'p'));
        } else if (obj.partnerId) {
          // 无from，无to，且有partnerId，则是盘点等其他单据
          item.push(createdLogsItem(item[0], 'p'));
        }
      }
    });

    setRenderData(mergerData);
  };

  useEffect(() => {
    if (!data || data.length === 0) return;
    getRenderData();
    // eslint-disable-next-line
  }, [data]);

  return (
    <div className="timeline-com">
      {renderData.map((n, i: any) => (
        // eslint-disable-next-line
        <Timeline mode="left" key={i}>
          {n.map((item: LogsItem, index: any) =>
            item.renderPartnerCode ? (
              // eslint-disable-next-line
              <Timeline.Item key={index} dot={item.icon}>
                <div>
                  <Tooltip
                    placement="topLeft"
                    title={
                      <>
                        <div>
                          {i18n.t('global.type')}：{item.renderPartnerTypeDesc}
                        </div>
                        <div>
                          {i18n.t('global.code')}：{item.renderPartnerCode}
                        </div>
                        <div>
                          {i18n.t('global.name')}：{item.renderPartnerName}
                        </div>
                      </>
                    }
                    arrowPointAtCenter
                  >
                    <span className="font-semibold leading-5 text-lead-dark">{item.renderPartnerName}</span>
                  </Tooltip>
                  {index === n.length - 1 && i !== renderData.length - 1 && (
                    <div className="mb-6 mt-3 flex items-center gap-x-2 text-lead-red">
                      <CloseCircleFillIcon className="fill-lead-red" />
                      <span className="font-normal leading-5">{i18n.t('global.tagPositionError')}</span>
                    </div>
                  )}
                </div>
              </Timeline.Item>
            ) : (
              // eslint-disable-next-line
              <Timeline.Item key={index} dot={item.icon}>
                <div>
                  <div className="flex flex-wrap items-center">
                    <span className="font-semibold">{item.sourceDesc}</span>
                    <div className="ml-2 text-lead-slate">
                      {item.operated ? moment(item.operated).format('YYYY-MM-DD HH:mm:ss') : ''}
                    </div>
                  </div>
                  <div className="mb-[10px] mt-[10px] flex flex-wrap gap-x-2">
                    {item.operateUser && (
                      <span className="flex-1">
                        {i18n.t('global.operator')}：{item.operateUser}
                      </span>
                    )}
                    {item.orderCode && (
                      <span className="flex-1">
                        {i18n.t('global.orderCode')}：{item.orderCode}
                      </span>
                    )}
                  </div>
                  {item.boxCode && (
                    <div className="mt-[10px] flex flex-wrap gap-x-2">
                      <span className="flex-1">
                        {i18n.t('global.boxCode')}： {item.boxCode}
                      </span>
                    </div>
                  )}
                </div>
              </Timeline.Item>
            ),
          )}
        </Timeline>
      ))}
    </div>
  );
};

export default OperationLogs;
