import { Checkbox, Tabs } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/core/Partner';
import * as PartnerPermissionApi from 'common/api/core/PartnerPermission';
import * as WarehouseApi from 'common/api/core/Warehouse';
import PowerTable from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import Spin from 'common/components/Spin';
import useWindowSize from 'common/hooks/useWindowSize';
import i18n from 'common/utils/I18n';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';

import styles from './PartnerPermissionTable.module.css';

interface PartnerPermissionTablePropsRef {
  recover: () => void;
}

interface PartnerPermissionTableProps {
  empId: any;
  type: any;
  onChange: (value: any) => void;
  selectAllOnChange: (value: boolean) => void;
  onPartnerPermissionList: (value: any) => void;
  allPermissionFlag: boolean;
  innerRef?: React.MutableRefObject<PartnerPermissionTablePropsRef | undefined>;
}

const PartnerPermissionTable: React.FC<PartnerPermissionTableProps> = (props) => {
  const { empId, type, onChange, selectAllOnChange, onPartnerPermissionList, allPermissionFlag, innerRef } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [partnerType, setPartnerType] = useState<string>(type);
  const [warehouseKeys, setWarehouseKeys] = useState<any[]>([]);
  const [shopKeys, setShopKeys] = useState<any[]>([]);
  const [factoryKeys, setFactoryKeys] = useState<any[]>([]);
  const [warehouseRows, setWarehouseRows] = useState<any[]>([]);
  const [shopRows, setShopRows] = useState<any[]>([]);
  const [factoryRows, setFactoryRows] = useState<any[]>([]);
  const [partnerList, setPartnerList] = useState<any>([]);
  const [originDataSource, setOriginDataSource] = useState<any>([]);
  const [dataSource, setDataSource] = useState<any>([]);
  const { height } = useWindowSize();
  const [isSelectAll, setIsSelectAll] = useState(allPermissionFlag || false);
  const [loading, setLoading] = useState(false);

  const allPermissionFlagRef = useRef(allPermissionFlag);

  const fetchData = async () => {
    setLoading(true);
    try {
      const partnerListResp: any = await PartnerApi.List({
        enablePage: false,
        type: ['WAREHOUSE', 'SHOP', 'FACTORY'],
      });
      let partnerList = partnerListResp.data.map((n) => ({
        ...n,
        children: [],
      }));
      const partnerIds = partnerList.map((n) => n.id);
      const warehouseListResp: any = await WarehouseApi.List({
        enablePage: false,
        partnerIds,
      });
      const warehouseList = warehouseListResp.data;
      // ------------------
      // 非选取'全部'时回显已选取权限
      let partnerPermissionList: any[] = [];
      if (!allPermissionFlagRef.current) {
        const partnerPermissionResp = await PartnerPermissionApi.List({
          empId,
          enablePage: false,
        });
        partnerPermissionList = partnerPermissionResp.data;
        onPartnerPermissionList(partnerPermissionList);
      }

      // ------------------
      const warehouseRows: any[] = [];
      const shopRows: any[] = [];
      const factoryRows: any[] = [];
      warehouseList.forEach((warehouse) => {
        const foundFromPartnerList = partnerList.find((item) => item.id === warehouse.partnerId);
        const foundFromPermissionList = partnerPermissionList.find((item) => item.warehouseId === warehouse.id);
        if (foundFromPermissionList) {
          warehouse.permissionDataId = foundFromPermissionList.id;
          if (warehouse.type === 'WAREHOUSE') {
            warehouseRows.push(warehouse);
          } else if (warehouse.type === 'SHOP') {
            shopRows.push(warehouse);
          } else if (warehouse.type === 'FACTORY') {
            factoryRows.push(warehouse);
          }
        }
        foundFromPartnerList.children.push(warehouse);
      });

      setWarehouseRows(warehouseRows);
      setShopRows(shopRows);
      setFactoryRows(factoryRows);

      let warehouseRowKeys: any[] = [];
      let shopRowKeys: any[] = [];
      let factoryRowKeys: any[] = [];
      partnerPermissionList.forEach((i) => {
        if (i.partnerType === 'WAREHOUSE') {
          warehouseRowKeys.push(i.warehouseId);
        } else if (i.partnerType === 'SHOP') {
          shopRowKeys.push(i.warehouseId);
        } else if (i.partnerType === 'FACTORY') {
          factoryRowKeys.push(i.warehouseId);
        }
      });
      warehouseRowKeys = Array.from(new Set(warehouseRowKeys));
      shopRowKeys = Array.from(new Set(shopRowKeys));
      factoryRowKeys = Array.from(new Set(factoryRowKeys));
      setWarehouseKeys(warehouseRowKeys);
      setShopKeys(shopRowKeys);
      setFactoryKeys(factoryRowKeys);

      partnerList = partnerList.filter((n) => n.children.length !== 0);
      setPartnerList(partnerList);

      const tableSource = partnerList.filter((n) => n.type === type);
      setOriginDataSource(tableSource);
      setDataSource(tableSource);
      setLoading(false);
    } catch {
      setOriginDataSource([]);
      setDataSource([]);
      setLoading(false);
    }
  };

  const treeTableColumns = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      key: 'code',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
  ];

  const rowSelectionOnSelect = (record, selected) => {
    allPermissionFlagRef.current = false;
    if (partnerType === 'WAREHOUSE') {
      let selectedRowKeys: any = JSON.parse(JSON.stringify(warehouseKeys));
      let selectedRows: any = JSON.parse(JSON.stringify(warehouseRows));
      if (selected) {
        selectedRowKeys.push(record.id);
        selectedRows.push(record);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys.push(item.id);
            selectedRows.push(item);
          });
        }
      } else {
        selectedRowKeys = selectedRowKeys.filter((n) => n !== record.id);
        selectedRows = selectedRows.filter((n) => n.id !== record.id);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys = selectedRowKeys.filter((n) => n !== item.id);
            selectedRows = selectedRows.filter((n) => n.id !== item.id);
          });
        }
      }
      setWarehouseKeys(selectedRowKeys);
      setWarehouseRows(selectedRows);
    } else if (partnerType === 'SHOP') {
      let selectedRowKeys: any = JSON.parse(JSON.stringify(shopKeys));
      let selectedRows: any = JSON.parse(JSON.stringify(shopRows));
      if (selected) {
        selectedRowKeys.push(record.id);
        selectedRows.push(record);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys.push(item.id);
            selectedRows.push(item);
          });
        }
      } else {
        selectedRowKeys = selectedRowKeys.filter((n) => n !== record.id);
        selectedRows = selectedRows.filter((n) => n.id !== record.id);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys = selectedRowKeys.filter((n) => n !== item.id);
            selectedRows = selectedRows.filter((n) => n.id !== item.id);
          });
        }
      }
      setShopKeys(selectedRowKeys);
      setShopRows(selectedRows);
    } else if (partnerType === 'FACTORY') {
      let selectedRowKeys: any = JSON.parse(JSON.stringify(factoryKeys));
      let selectedRows: any = JSON.parse(JSON.stringify(factoryRows));
      if (selected) {
        selectedRowKeys.push(record.id);
        selectedRows.push(record);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys.push(item.id);
            selectedRows.push(item);
          });
        }
      } else {
        selectedRowKeys = selectedRowKeys.filter((n) => n !== record.id);
        selectedRows = selectedRows.filter((n) => n.id !== record.id);
        if (record.children && record.children.length > 0) {
          record.children.forEach((item) => {
            selectedRowKeys = selectedRowKeys.filter((n) => n !== item.id);
            selectedRows = selectedRows.filter((n) => n.id !== item.id);
          });
        }
      }
      setFactoryKeys(selectedRowKeys);
      setFactoryRows(selectedRows);
    }
  };

  const allRowSelectionOnSelect = (record, selectedRows) => {
    if (partnerType === 'WAREHOUSE') {
      if (record) {
        const newWarehouseRows = selectedRows.filter((n) => n.type === 'WAREHOUSE');
        setWarehouseRows(newWarehouseRows);
        setWarehouseKeys(newWarehouseRows.map((n) => n.id));
      } else {
        setWarehouseRows([]);
        setWarehouseKeys([]);
      }
    } else if (partnerType === 'SHOP') {
      if (record) {
        const newShopRows = selectedRows.filter((n) => n.type === 'SHOP');
        setShopRows(newShopRows);
        setShopKeys(newShopRows.map((n) => n.id));
      } else {
        setShopRows([]);
        setShopKeys([]);
      }
    } else if (partnerType === 'FACTORY') {
      if (record) {
        const newFactoryRows = selectedRows.filter((n) => n.type === 'FACTORY');
        setFactoryRows(newFactoryRows);
        setFactoryKeys(newFactoryRows.map((n) => n.id));
      } else {
        setFactoryRows([]);
        setFactoryKeys([]);
      }
    }
  };

  useEffect(() => {
    setSelectedRowKeys([...warehouseKeys, ...shopKeys, ...factoryKeys]);
  }, [warehouseKeys, shopKeys, factoryKeys]);

  useEffect(() => {
    setSelectedRows([...warehouseRows, ...shopRows, ...factoryRows]);
  }, [warehouseRows, shopRows, factoryRows]);

  useEffect(() => {
    onChange(selectedRows);
    // eslint-disable-next-line
  }, [selectedRows]);

  const onSearchCode = (value) => {
    let filterDataSource: any = [];
    if (value && value.trim() !== '') {
      filterDataSource = originDataSource.filter(
        (item) => item.code.toUpperCase() === value.trim().toUpperCase() || item.name === value.trim(),
      );
      originDataSource.forEach((n) => {
        if (n.children) {
          const exist = n.children.some((item) => item.code.includes(value.trim()) || item.name.includes(value.trim()));
          if (exist) {
            if (!filterDataSource.some((v) => v.id === n.id)) {
              filterDataSource.push(n);
            }
          }
        }
      });
      setDataSource(filterDataSource);
    } else {
      setDataSource(originDataSource);
    }
  };

  const tabsOnChange = (activeKey) => {
    setPartnerType(activeKey);
    const newPartnerList = partnerList.filter((n) => n.type === activeKey);
    setDataSource(newPartnerList);
    setOriginDataSource(newPartnerList);
  };

  const isSelectAllOnChange = (e) => {
    setIsSelectAll(e.target.checked);
    selectAllOnChange(e.target.checked);
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchData();
      setIsSelectAll(allPermissionFlag || false);
    },
  }));

  useEffect(() => {
    if (empId) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [empId]);

  return (
    <>
      <div className="mb-4 flex justify-end">
        <Checkbox onChange={isSelectAllOnChange} checked={isSelectAll}>
          {i18n.t('global.allPermission')}
        </Checkbox>
      </div>
      <div className="relative flex">
        {isSelectAll && (
          <div className="absolute bottom-0 left-0 right-0 top-0 z-40 h-full w-full cursor-not-allowed bg-white opacity-40" />
        )}
        <div className={`shrink-0 ${styles.tab}`}>
          <Tabs
            tabPosition="left"
            defaultActiveKey={type}
            onChange={tabsOnChange}
            items={[
              {
                label: i18n.t('global.warehouse'),
                key: 'WAREHOUSE',
              },
              {
                label: i18n.t('global.shop'),
                key: 'SHOP',
              },
              {
                label: i18n.t('global.factory'),
                key: 'FACTORY',
              },
            ]}
          />
        </div>
        <div className="flex-1 overflow-auto">
          <Spin spinning={loading}>
            <PowerTable
              initialized
              rowKey="id"
              columns={treeTableColumns}
              pagination={false}
              refreshBtnVisible={false}
              leftToolbar={
                <SearchInput
                  placeholder={i18n.t('global.inputKey')}
                  onSearch={onSearchCode}
                  style={{ width: 280, marginBottom: 8 }}
                  allowClear
                />
              }
              tableProps={{
                bordered: true,
                sticky: true,
                size: 'small',
                dataSource,
                rowSelection: {
                  type: 'checkbox',
                  checkStrictly: false,
                  // hideSelectAll: true,
                  onSelect: rowSelectionOnSelect,
                  onSelectAll: allRowSelectionOnSelect,
                  selectedRowKeys,
                },
                scroll: { y: height - 350 },
              }}
            />
          </Spin>
        </div>
      </div>
    </>
  );
};

export default PartnerPermissionTable;
