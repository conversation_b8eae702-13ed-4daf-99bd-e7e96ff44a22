import { Form, Input, Switch } from 'antd';
import * as BrandA<PERSON> from 'common/api/core/Brand';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IBrandItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  brandId: string | undefined;
  onSubmit: (values: IBrandItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, brandId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:ATTRIBUTE:BRAND');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchBrand = useCallback(async () => {
    setLoading(true);
    try {
      const brand: any = await BrandApi.Get({
        id: brandId,
      });
      if (typeof brand.disabled === 'boolean') {
        brand.status = !brand.disabled;
      }
      form.setFieldsValue(brand);
      setCurrent(brand);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [brandId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (brandId) fetchBrand();
      setTimeout(() => {
        if (brandId) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
      form.resetFields();
    }
  }, [brandId, form, visible, fetchBrand]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IBrandItem);
    }
  };

  const onClose = () => {
    if (onCancel) onCancel();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await BrandApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchBrand();
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editBrand')}
      {current ? ` [${current.code}]` : ''}
    </>
  ) : (
    i18n.t('global.newBrand')
  );

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      open={visible}
      destroyOnClose
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!brandId}
          onDelete={deleteBtnOnClick}
          recoverPermission={!!current}
          onRecover={onRecover}
          cancelPermission={!current}
          onCancel={onClose}
        />
      }
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={codeInputRef} readOnly={!!current} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
