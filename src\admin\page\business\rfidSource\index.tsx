import { Col, Input, Radio, Row } from 'antd';
import * as RfidApi from 'common/api/core/Rfid';
import * as SkuApi from 'common/api/core/Sku';
import Empty from 'common/components/Empty';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useState } from 'react';

import OperationLogs from './components/OperationLogs';

import styles from './index.module.css';

const modeStorageKey = 'RFID-SOURCE-MODE';

const RfidSource: React.FC = () => {
  const [source, setSource] = useState<Record<string, any> | undefined>(undefined);
  const [sku, setSku] = useState<Record<string, any> | undefined>(undefined);
  const [position, setPosition] = useState<Record<string, any>>();
  const [loading, setLoading] = useState<boolean>(false);
  const [mode, setMode] = useState<'epc' | 'uniqueCode'>(LocalStorageUtil.getItem(modeStorageKey) || 'epc');
  const [isFetch, setIsFetch] = useState(false);

  const fetchData = async (value) => {
    setLoading(true);
    setSku(undefined);
    setSource(undefined);
    setPosition(undefined);
    setIsFetch(false);
    let epc = value;

    // 唯一码模式：先通过唯一码获取epc信息，再通过epc进行溯源
    if (mode === 'uniqueCode') {
      try {
        const res: any = await RfidApi.UniqueCode({
          uniqueCode: value,
        });
        if (!res) {
          setLoading(false);
          setIsFetch(false);
          NoticeUtil.warn(i18n.t('global.noUniqueCodeFound'));
          return;
        }
        epc = res.epc;
      } catch (e) {
        setLoading(false);
        return;
      }
    }

    try {
      const newSource: any = await RfidApi.Source({
        epc,
      });
      if (!newSource) {
        setLoading(false);
        setIsFetch(false);
        NoticeUtil.warn(i18n.t('global.noEpcFound'));
        return;
      }

      setSource(newSource);

      const newSku = await SkuApi.Get({
        id: newSource.skuId,
      });
      setSku(newSku || undefined);

      const newPosition: any = await RfidApi.Position({
        epc,
      });
      setPosition(newPosition || {});
    } catch (e) {}
    setIsFetch(true);
    setLoading(false);
  };

  const onSearch = (value) => {
    setSource(undefined);
    if (value) {
      fetchData(value);
    } else {
      setIsFetch(false);
    }
  };

  const radioOnChange = (e) => {
    setMode(e.target.value);
    setSku(undefined);
    setSource(undefined);
    LocalStorageUtil.setItem(modeStorageKey, e.target.value);
  };

  const data = [
    {
      title: i18n.t('global.productCode'),
      content: sku?.prodCode,
    },
    {
      title: i18n.t('global.productName'),
      content: sku?.prodName,
    },
    {
      title: i18n.t('global.barcode'),
      content: sku?.barcode,
    },
    {
      title: i18n.t('global.skuName'),
      content: sku?.name,
    },
    {
      title: i18n.t('global.brand'),
      content: sku?.brandName,
    },
    {
      title: i18n.t('global.priCategory'),
      content: sku?.priCategoryName,
    },
    {
      title: i18n.t('global.subCategory'),
      content: sku?.subCategoryName,
    },
    {
      title: i18n.t('global.year'),
      content: sku?.yearName,
    },
    {
      title: i18n.t('global.gender'),
      content: sku?.genderName,
    },
    {
      title: i18n.t('global.color'),
      content: sku?.colorName,
    },
    {
      title: i18n.t('global.size'),
      content: sku?.sizeName,
    },
    {
      title: i18n.t('global.spec'),
      content: sku?.specName,
    },
  ];

  const options = [
    { label: 'EPC', value: 'epc' },
    { label: i18n.t('global.uniqueCode'), value: 'uniqueCode' },
  ];
  return (
    <>
      <AppHeader />
      <div className="flex flex-col items-center justify-center">
        <div className="sticky top-24 z-40 flex w-full items-center justify-center bg-white">
          <div
            className={`sm:w-[100%] md:w-[90%] lg:w-[80%] xl:w-[70%] 2xl:w-[60%] ${
              sku && source ? 'mt-6 pb-6' : 'mt-[25vh]'
            }`}
          >
            <Radio.Group className="mb-2" optionType="button" options={options} onChange={radioOnChange} value={mode} />
            <Input.Search
              loading={loading}
              className={styles['input-search']}
              placeholder={i18n.t('global.pleaseEnterOrReadTag')}
              autoFocus
              allowClear
              enterButton={i18n.t('global.search')}
              onSearch={onSearch}
            />
          </div>
        </div>
        {source && position && (
          <div className="mb-4 mt-6 flex w-full items-center justify-between px-[10px]">
            {source?.epc && <span className="text-xl font-semibold">{source.epc}</span>}
            {position && (
              <div>
                <span className="font-normal leading-5 text-lead-slate">{i18n.t('global.currentPosition')}</span>：
                <span className="font-semibold leading-5 text-lead-dark">
                  {position?.partnerName || i18n.t('global.unknown')}
                  {position?.statusDesc ? ` (${position.statusDesc})` : ''}
                </span>
              </div>
            )}
          </div>
        )}
        {isFetch && (
          <Row gutter={[20, 20]} className="w-full">
            <Col xxl={12} xl={12} lg={24} md={24} sm={24} xs={24}>
              <div className="border-soild rounded-lg border">
                <div className="flex h-9 items-center border-b bg-lead-light-bg pl-3 font-semibold leading-5 text-lead-dark">
                  {i18n.t('global.tagInfo')}
                </div>
                <div className="flex flex-col gap-y-3 px-3 pb-6 pt-3">
                  {sku ? (
                    data.map((item) => {
                      return (
                        <div key={item.title}>
                          <span className="font-normal text-lead-slate">{item.title}：</span>
                          <span>{item.content}</span>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex min-h-[408px] items-center justify-center">
                      <Empty />
                    </div>
                  )}
                </div>
              </div>
            </Col>
            <Col xxl={12} xl={12} lg={24} md={24} sm={24} xs={24}>
              <div className="border-soild rounded-lg border">
                <div className="flex h-9 items-center border-b bg-lead-light-bg pl-3 font-semibold leading-5 text-lead-dark">
                  {i18n.t('global.operationLogs')}
                </div>
                {source && source.data && source.data.length > 0 ? (
                  <div className="flex min-h-[408px] flex-col gap-y-3 px-3 pt-3">
                    <OperationLogs data={source.data} />
                  </div>
                ) : (
                  <div className="flex min-h-[408px] items-center justify-center">
                    <Empty />
                  </div>
                )}
              </div>
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};

export default RfidSource;
