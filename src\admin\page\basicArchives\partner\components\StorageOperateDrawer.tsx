import { Form, Input, Switch, Tabs } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import * as PartnerApi from 'common/api/core/Partner';
import { IWarehouseItem } from '../data';
import StorageLocationTable from './StorageLocationTable';
import SampleStorageLocationTable from './SampleStorageLocationTable';

interface StorageOperateDrawerProps {
  open: boolean;
  confirmLoading: boolean;
  current: Partial<IWarehouseItem> | undefined;
  onSubmit: (values: IWarehouseItem) => void;
  onCancel: () => void;
  onDelete: (values: Record<string, any> | undefined) => void;
}

const StorageOperateDrawer: React.FC<StorageOperateDrawerProps> = (props) => {
  const [form] = Form.useForm();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { enableSampleServe } = globalState;

  const { onSubmit, onCancel, onDelete, current, open, confirmLoading } = props;
  const [currentSource, setCurrentSource] = useState<Record<string, any>>();
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [tabKey, setTabKey] = useState('INFO');
  const [partnerInfo, setPartnerInfo] = useState<Record<string, any>>();

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (current) {
      const values = {
        ...current,
        status: !current.disabled,
      };
      form.setFieldsValue(values);
      setCurrentSource(values);
    }
    const times = setTimeout(() => {
      if (!open) return;
      if (current) {
        nameInputRef.current.focus();
      } else {
        codeInputRef.current.focus();
      }
    }, 200);
    return () => {
      clearTimeout(times);
      form.resetFields();
    };
  }, [open, current, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values) => {
    if (onSubmit) {
      onSubmit({
        disabled: false,
        ...values,
      });
    }
  };
  const onClose = () => {
    setTabKey('INFO');
    if (onCancel) onCancel();
  };

  const onRecover = () => {
    if (currentSource) {
      form.setFieldsValue(currentSource);
    }
    setTabKey('INFO');
  };

  const storageOperateForm = () => (
    <Form layout="vertical" form={form} initialValues={{ allowTakeStock: false }} onFinish={handleFinish}>
      <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
        <Input ref={codeInputRef} readOnly={!!current} maxLength={20} />
      </Form.Item>
      <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
        <Input ref={nameInputRef} maxLength={50} />
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item label={i18n.t('global.allowStocktaking')} valuePropName="checked" name="allowTakeStock">
        <Switch />
      </Form.Item>
      {current && (
        <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
          <Switch />
        </Form.Item>
      )}
    </Form>
  );

  const items = useMemo(() => {
    const arr = [
      {
        key: 'INFO',
        label: i18n.t('global.information'),
        children: storageOperateForm(),
      },
      {
        label: i18n.t('global.storageLocation'),
        key: 'STORAGE_LOCATION',
        children: <StorageLocationTable warehouseId={current?.id} partnerId={current?.partnerId} />,
      },
    ];

    if (current?.type === 'WAREHOUSE' && enableSampleServe && partnerInfo?.sampleFlag) {
      arr.push({
        label: i18n.t('global.sampleLocation'),
        key: 'SAMPLE_LOCATION',
        children: <SampleStorageLocationTable warehouseId={current?.id} partnerId={current?.partnerId} />,
      });
    }

    return arr;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current, enableSampleServe, partnerInfo]);

  const getPartnerInfo = async () => {
    const data = await PartnerApi.Get({
      id: current?.partnerId,
    });
    setPartnerInfo(data);
  };

  useEffect(() => {
    if (current?.partnerId) {
      getPartnerInfo();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current]);

  return (
    <Drawer
      width={tabKey === 'INFO' ? 400 : 1000}
      title={current ? `${i18n.t('global.editStorage')} [${current.code}]` : i18n.t('global.newStorage')}
      open={open}
      destroyOnClose
      maskClosable={false}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          onDelete={() => {
            onDelete(current);
          }}
          onRecover={onRecover}
          deletePermission={!!current}
        />
      }
    >
      {!current ? (
        storageOperateForm()
      ) : (
        <Tabs
          defaultActiveKey="INFO"
          size="small"
          items={items}
          activeKey={tabKey}
          onChange={(key) => setTabKey(key)}
        />
      )}
    </Drawer>
  );
};

export default StorageOperateDrawer;
