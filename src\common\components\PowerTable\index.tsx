// TODO: [x] 实现远程排序功能 (defaultSorter)
// TODO: [x] 查询参数改变时重置页数
// TODO: [x] 支持传入查询表单默认值
// TODO: [x] 支持自动计算action列宽
// TODO: [x] 点重置时需要将两个查询表单的值全部重置
// TODO: [x] 点搜索时需要将使用两个查询表单的值
// TODO: [ ] 删除initialized，增加initialState props
// TODO: [ ] 实现控制列显示隐藏
// TODO: [ ] 实现控制列移动顺序
// TODO: [ ] 支持缓存table的状态（条件是否已展开，列的排序等）
// TODO: [ ] 支持预设本地排序（本地排序时需要重新分配 _index 值）

import { BulbFilled, LoadingOutlined } from '@ant-design/icons';
import { Alert, Button, Divider, message, PaginationProps, Space, Spin, Tabs, TabsProps, Tooltip } from 'antd';
import { SortOrder } from 'antd/es/table/interface';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import { TableProps } from 'antd/lib/table';
import { TableRowSelection } from 'antd/lib/table/interface';
import classNames from 'classnames';
import CloseIcon from 'common/assets/icons/icon-close-fill.svg?react';
import FoldIcon from 'common/assets/icons/icon-fold.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import i18n, { getFormattedMessages } from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import moment from 'moment';
import React, { CSSProperties, Fragment, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import DisabledTrigger from '../DisabledTrigger';
import PowerSearchPanel, {
  IPowerSearchPanelProps,
  IPowerSearchPanelRef,
  PowerQuickSearchPanel,
} from '../PowerSearchPanel';
import TableWrap, { PaginationStyle, Sorter, TableWrapColumnsType, TableWrapColumnType } from '../TableWrap';
import { cachePrefix, defaultDataFields, defaultPaginationStyle, defaultSorterValues } from './config';
import FilterSaveModal from './FilterSaveModal';

import './index.css';

const loadingIcon = <LoadingOutlined className="ant-loading-icon" spin />;

// interface IColumnCheckboxListProps {
//   columns?: PowerTableColumnsType;
// }

// const ColumnCheckboxList: React.FC<IColumnCheckboxListProps> = (props) => {
//   const { columns } = props;
//   const [treeData, setTreeData] = useState<Record<string, any>[]>([]);
//   useEffect(() => {
//     if (columns && columns.length > 0) {
//       const treeData = columns.map((aColumn, index) => ({
//         key: index,
//         title: aColumn.title,
//         isLeft: true,
//       }));
//       setTreeData(treeData);
//     }
//   }, [columns]);
//
//   return (
//     <Tree
//       itemHeight={24}
//       draggable
//       checkable
//       blockNode
//       showLine={false}
//       treeData={treeData}
//     />
//   );
// };

export type SearchFieldsConfig = IPowerSearchPanelProps['fieldsConfig'];

export interface RequestParams {
  currentPage?: number;
  pageSize?: number;
  orderByField?: string;
  orderByMethod?: string;
  [propName: string]: any;
}

export interface ITabStatus {
  code: string;
  name: React.ReactNode;
  ignore?: boolean;
}

export interface IPowerTableInnerRef {
  /**
   * Load data by call `request` function.
   */
  load(): void;
  /**
   * Reset all things
   */
  reset(resetItems?: ResetItem[]): void;
  /**
   * Set active tab key
   */
  setActiveTabKey: (key: string) => void;
  /**
   * Set `PowerSearchPanel` fields value.
   */
  setSearchPanelFieldsValue(values: Record<string, any>): void;
  /**
   * Set searchPanelVisible.
   */
  setSearchPanelVisible(visible: boolean): void;
}

export type PowerTableColumnsType = TableWrapColumnsType<Record<string, any>>;

export type PowerTableColumnType = TableWrapColumnType<Record<string, any>>;

export type DataFields = {
  /**
   * Enable page field.
   * @default enablePage
   */
  enablePage?: string;
  /**
   * Current page field.
   * @default currentPage
   */
  currentPage?: string;
  /**
   * Page size field.
   * @default pageSize
   */
  pageSize?: string;
  /**
   * Order by field field.
   * @default orderByField
   */
  orderByField?: string;
  /**
   * Order by method field.
   * @default orderByMethod
   */
  orderByMethod?: string;
  /**
   * Has next field.
   * @default hasNext
   */
  hasNext?: string;
  /**
   * Total pages field.
   * @default totalPages
   */
  totalPages?: string;
  /**
   * Total records field.
   * @default totalRecords
   */
  totalRecords?: string;
  /**
   * Data field.
   * @default data
   */
  data?: string;
};

export type SorterValues = {
  ascend: string;
  descend: string;
};

export type SavedFilterItem = {
  name: string;
  params: Record<string, any>;
};

export type ResetItem = 'PAGINATION' | 'DATA' | 'SORTER' | 'SELECTION' | 'SEARCH_PANEL' | 'TABS';

export interface IPowerTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<IPowerTableInnerRef | undefined>;
  /**
   * Data fetch request
   */
  request?: (params: RequestParams, tabActiveKey: string) => Promise<any>;
  /**
   * Auto call request at mounted
   */
  autoLoad?: boolean;
  /**
   * Current component's wrap div style
   */
  style?: CSSProperties;
  /**
   * Table's rowKey props
   */
  rowKey?: TableProps<any>['rowKey'];
  /**
   * Table rowSelection.type
   */
  rowSelectionType?: 'checkbox' | 'radio';
  /**
   * Table rowSelection.selectedRowKeys
   */
  selectedRowKeys?: TableRowSelection<any>['selectedRowKeys'];
  /**
   * Table rowSelection.onChange
   */
  onRowSelectionChange?: TableRowSelection<any>['onChange'];
  /**
   * Table rowSelection.getCheckboxProps
   */
  rowSelectionGetCheckboxProps?: TableRowSelection<any>['getCheckboxProps'];
  /**
   * TableWrap's props
   */
  tableProps?: TableProps<any>;
  /**
   * Columns config
   */
  columns?: PowerTableColumnsType;
  /**
   * Table's components
   */
  components?: TableProps<any>['components'];
  /**
   * Saved filters
   */
  savedFilters?: SavedFilterItem[];
  /**
   * On filter delete
   */
  onFilterDelete?: (record: Record<string, any>) => Promise<any>;
  /**
   * Filter saveable (Allow to save filters to localstorage)
   */
  filterSaveable?: boolean;
  /**
   * On filter save trigger
   */
  onFilterSave?: (data: Record<string, any>) => Promise<any>;
  /**
   * Table setting tool visible (Allow to change table size, move column,
   * hide column, and others)
   * default: false
   */
  settingToolVisible?: boolean;
  /**
   * Quick search bar fields config
   */
  quickSearchFieldsConfig?: IPowerSearchPanelProps['fieldsConfig'];
  /**
   * Quick search bar submit button visible
   */
  quickSearchPanelSubmitButtonVisible?: boolean;
  /**
   * Quick search bar fields flex wrap
   */
  quickSearchPanelFieldsWrap?: boolean;
  /**
   * PowerSearchPanel collapsible
   */
  searchPanelCollapsible?: boolean;
  /**
   * PowerSearchPanel's props
   */
  // searchPanelProps?: IPowerSearchPanelProps;
  /**
   * PowerSearchPanel default visible
   */
  searchPanelVisible?: boolean;
  /**
   * PowerSearchPanel's fields config
   */
  searchFieldsConfig?: IPowerSearchPanelProps['fieldsConfig'];
  /**
   * Search panel initial values
   */
  searchPanelInitialValues?: Record<string, any>;
  /**
   * PowerSearchPanel's fields onFinish
   */
  // onSearch?: (values: Record<string, any>) => void;
  /**
   * List of status that will be displayed using the Tabs component.
   */
  tabStatus?: ITabStatus[];
  /**
   * Status tabs's defaultActiveKey.
   */
  tabDefaultActiveKey?: string;
  /**
   * Tabs's tabBarExtraContent.
   */
  tabBarExtraContent?: TabsProps['tabBarExtraContent'];
  /**
   * Status tabs's onChange.
   */
  tabsOnChange?: (activeKey: string) => void;
  /**
   * Contents between PowerSearchPanel and Table.
   */
  middleContents?: React.ReactNode;
  /**
   * Is initialized? (to preload dependencies)
   */
  initialized?: boolean;
  /**
   * Default page size (default to 10)
   */
  defaultPageSize?: number;
  /**
   * Enable pagination?
   */
  pagination?: boolean;
  /**
   * Pagination wrapper's className
   */
  paginationWrapClassName?: string;
  /**
   * Pagination tool style
   * - simple
   *   > simple pagination tool with page up, page down, page size,
   *   > and page number.
   *
   * - full
   *   > full pagination tool with page up, page down, page size,
   *   > total page number and total record number.
   * @default simple
   */
  paginationStyle?: PaginationStyle;
  /**
   * pagination sticky?
   */
  // paginationSticky?: boolean;
  /**
   * pagination extra content
   */
  paginationExtraContent?: React.ReactNode;
  /**
   * Pagination's props
   */
  fullPaginationProps?: PaginationProps;
  /**
   * Pagination field
   */
  dataFields?: DataFields;
  /**
   * Sorter values mapping
   */
  sorterValues?: SorterValues;
  /**
   * Default sorter
   */
  defaultSorter?: Sorter;
  /**
   * Loading
   */
  loading?: boolean;
  /**
   * Refresh button visible (default to true)
   */
  refreshBtnVisible?: boolean;
  /**
   * Refresh button on click
   */
  onRefreshBtnClick?: () => void;
  /**
   * Left toolbar that display above the table
   */
  leftToolbar?: React.ReactNode | React.ReactNode[];
  /**
   * Right toolbar that display above the table
   */
  rightToolbar?: React.ReactNode | React.ReactNode[];
  /**
   * Selection toolbar that display above the table when has selected rows
   */
  selectionToolbar?: React.ReactNode | React.ReactNode[];
  simplePaginationRightToolBar?: React.ReactNode | React.ReactNode[];
  /**
   * Enable caching
   */
  enableCache?: boolean;
  /**
   * The key value of the cache
   */
  cacheKey?: string;
  /**
   * Enable disabled trigger
   */
  enableDisabledTrigger?: boolean;
  /**
   * Disabled field name, default to 'disabled'
   */
  disabledFieldName?: string;
  /**
   * Extra reset callback function
   */
  extraSearchReset?: () => void;
}

// const tagColors = [
//   'magenta',
//   'red',
//   'volcano',
//   'orange',
//   'gold',
//   'lime',
//   'green',
//   'cyan',
//   'blue',
//   'geekblue',
//   'purple',
// ];
//
// const randomTagColor = () =>
//   tagColors[Math.floor(Math.random() * tagColors.length)];

// let defaultDataFields = {
//   enablePage: 'enablePage',
//   currentPage: 'currentPage',
//   pageSize: 'pageSize',
//   orderByField: 'orderByField',
//   orderByMethod: 'orderByMethod',
//   hasNext: 'hasNext',
//   totalPages: 'totalPages',
//   totalRecords: 'totalRecords',
//   data: 'data',
// };

// let defaultSorterValues: SorterValues = {
//   ascend: 'ASCEND',
//   descend: 'DESCEND',
// };

// /**
//  * Setup global PowerTable config
//  */
// export const initPowerTable = (
//   dataFields: DataFields,
//   sorterValues?: SorterValues,
// ) => {
//   defaultDataFields = {
//     ...defaultDataFields,
//     ...dataFields,
//   };
//   if (sorterValues) {
//     defaultSorterValues = {
//       ...defaultSorterValues,
//       ...sorterValues,
//     };
//   }
//   console.log('initPowerTable', dataFields, defaultDataFields);
// };

const savedFiltersTabKeyPrefix = 'SAVED_FILTERS_';

const PowerTable: React.FC<IPowerTableProps> = (props) => {
  const {
    autoLoad,
    filterSaveable,
    onFilterDelete,
    savedFilters,
    onFilterSave,
    settingToolVisible,
    enableCache,
    cacheKey,
    enableDisabledTrigger,
    disabledFieldName,
    initialized,
    columns,
    components,
    request,
    rowKey,
    rowSelectionType,
    selectedRowKeys,
    onRowSelectionChange,
    rowSelectionGetCheckboxProps,
    innerRef,
    style,
    tabStatus,
    tabDefaultActiveKey,
    tabBarExtraContent,
    tabsOnChange,
    middleContents,
    pagination,
    paginationWrapClassName,
    // paginationSticky,
    paginationExtraContent,
    fullPaginationProps,
    dataFields,
    sorterValues,
    // refreshBtnVisible,
    onRefreshBtnClick,
    defaultPageSize,
    defaultSorter,
    searchPanelInitialValues,
    searchPanelVisible: searchPanelDefaultVisible,
    searchFieldsConfig,
    quickSearchFieldsConfig,
    quickSearchPanelSubmitButtonVisible,
    quickSearchPanelFieldsWrap,
    leftToolbar,
    rightToolbar,
    selectionToolbar,
    simplePaginationRightToolBar,
    tableProps,
    extraSearchReset,
  } = props;
  // const sticky = tableProps?.sticky;
  const paginationStyle = props.paginationStyle || defaultPaginationStyle;
  const searchPanelCollapsible =
    typeof props.searchPanelCollapsible === 'boolean' ? props.searchPanelCollapsible : true;
  const refreshBtnVisible = typeof props.refreshBtnVisible === 'boolean' ? props.refreshBtnVisible : true;
  const [insideSelectedRowKeys, setInsideSelectedRowKeys] = useState<any>([]);
  const [searchPanelVisible, setSearchPanelVisible] = useState<boolean>(searchPanelDefaultVisible || false);
  const [data, setData] = useState<any[]>([]);
  const [tableColumns, setTableColumns] = useState<PowerTableColumnsType>(columns || []);
  const searchPanelValuesRef = useRef<Record<string, any>>(searchPanelInitialValues || {});
  const quickSearchPanelValuesRef = useRef<Record<string, any>>({});
  const [hasNext, setHasNext] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize || 10);
  const currentPageRef = useRef(1);
  const pageSizeRef = useRef(defaultPageSize || 10);
  const [sorter, setSorter] = useState<Sorter | null>(defaultSorter || null);
  const sorterRef = useRef<Sorter | null>(defaultSorter || null);
  const [filterSaveModalVisible, setFilterSaveModalVisible] = useState(false);
  const [filterSaveBtnDisabled, setFilterSaveBtnDisabled] = useState(true);
  const [filterSaveModalConfirmLoading, setFilterSaveModalConfirmLoading] = useState(false);
  const [disabled, setDisabled] = useState<boolean | null>(null);
  const disabledRef = useRef<boolean | null>(null);
  const defaultActiveTabKey = tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : '');
  const [activeTabKey, setActiveTabKey] = useState(defaultActiveTabKey);
  const activeTabKeyRef = useRef(defaultActiveTabKey);
  const searchPanelRef = useRef<IPowerSearchPanelRef>();
  const quickSearchPanelRef = useRef<IPowerSearchPanelRef>();
  // const [tableSize, setTableSize] = useState<SizeType>(
  const [tableSize] = useState<SizeType>(tableProps?.size || 'middle');
  const [total, setTotal] = useState(0);
  const lastParamsRef = useRef<Record<string, any>>({});
  const firstLoaded = useRef<boolean>(false);
  const cacheFirstLoaded = useRef<boolean>(!(enableCache && props.cacheKey));

  /**
   *  把字符串格式时间转换成moment对象
   */
  const formatDate = (date: string[] | undefined) => {
    let result;
    if (date && Array.isArray(date) && date.length === 2) {
      result = [moment(date[0]), moment(date[1])];
    }
    return result;
  };

  /**
   *  获取缓存数据
   */
  const loadCache = useCallback(() => {
    let result: Record<string, any> | undefined;

    function transformDate(dt: any) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
      if (dt && typeof dt === 'string' && dateRegex.test(dt)) {
        return moment(dt);
      }
      if (dt && Array.isArray(dt)) {
        return dt.map((item) => {
          return transformDate(item);
        });
      }
      return dt;
    }

    if (cacheKey) {
      const localObj = LocalStorageUtil.getItem(`${cachePrefix}${cacheKey}`);
      if (localObj) {
        if (localObj && localObj.searchParams) {
          Object.keys(localObj.searchParams).forEach((key) => {
            let val = localObj.searchParams[key];
            val = transformDate(val);
            localObj.searchParams[key] = val;
          });
        }
        result = localObj;
      }
    }
    return result;
  }, [cacheKey]);

  /**
   * 缓存查询数据
   */
  const saveCache = useCallback(
    (params: {
      searchParams?: Record<string, any>;
      tabsActiveKey?: string;
      pageSize?: number;
      currentPage?: number;
      sorter?: Sorter | null;
      searchPanelVisible?: boolean;
      disabled?: boolean | null;
    }) => {
      if (!cacheKey) return;
      const local = loadCache();
      LocalStorageUtil.setItem(`${cachePrefix}${cacheKey}`, {
        ...local,
        ...params,
      });
    },
    [cacheKey, loadCache],
  );

  /**
   * Call request with pagination params, sort params, and filters
   * @param params params that take priority
   * @param onlyParams only accept params as payload
   * @param tabKey current tab key that take priority
   * @returns Promise<boolean> return `true` when new page data is empty
   */
  const callRequest = useCallback(
    async (params: Record<string, any> = {}, onlyParams = false, tabKey?): Promise<boolean> => {
      const _dataFields = {
        ...defaultDataFields,
        ...dataFields,
      };
      const _sorterValues = {
        ...defaultSorterValues,
        ...sorterValues,
      };
      setLoading(true);
      let payload: Record<string, any> = {};
      if (!onlyParams) {
        payload = {
          ...searchPanelValuesRef.current,
          ...quickSearchPanelValuesRef.current,
        };
      }
      if (pagination) {
        payload[_dataFields.currentPage] = currentPageRef.current;
        payload[_dataFields.pageSize] = pageSizeRef.current;
        payload[_dataFields.enablePage] = true;
      }
      if (sorterRef.current && sorterRef.current.field && sorterRef.current.order) {
        payload[_dataFields.orderByField] = sorterRef.current.field;
        payload[_dataFields.orderByMethod] = sorterRef.current.order;
      }

      if (enableDisabledTrigger && typeof disabledRef.current === 'boolean') {
        payload[disabledFieldName || 'disabled'] = disabledRef.current;
      }

      payload = {
        ...payload,
        ...params,
      };

      // // upper case the sorter order method
      // if (payload[_dataFields.orderByMethod])
      //   payload[_dataFields.orderByMethod] =
      //     payload[_dataFields.orderByMethod].toUpperCase();

      // map sorterValues
      if (payload[_dataFields.orderByMethod]) {
        payload[_dataFields.orderByMethod] = _sorterValues[payload[_dataFields.orderByMethod].toLowerCase()];
      }
      // remove sorter params when one of those is empty
      if (!payload[_dataFields.orderByField] || !payload[_dataFields.orderByMethod]) {
        delete payload[_dataFields.orderByField];
        delete payload[_dataFields.orderByMethod];
      }

      function excludeAdditionalParams(params: Record<string, any>) {
        const newParams = {};
        const fields = (searchFieldsConfig || [])
          .concat(quickSearchFieldsConfig || [])
          .map((n) => n.name)
          .filter((n) => !!n);

        Object.keys(params).forEach((key) => {
          const val = params[key];
          if (fields.includes(key)) {
            newParams[key] = val;
          }
        });
        return newParams;
      }

      // compare current params and last params.
      // if they are different, or tabKey has change then set the currentPage
      // to 1. Otherwise do nothing.
      // eslint-disable-next-line
      const { currentPage: _cp, pageSize: _ps, ...otherParams } = payload;
      if (
        (cacheFirstLoaded.current &&
          JSON.stringify(excludeAdditionalParams(lastParamsRef.current)) !==
            JSON.stringify(excludeAdditionalParams(otherParams))) ||
        tabKey
      ) {
        payload[_dataFields.currentPage] = 1;
        setCurrentPage(1);
        currentPageRef.current = 1;
        saveCache({ currentPage: 1 });
      }
      lastParamsRef.current = otherParams;
      cacheFirstLoaded.current = true;

      // TODO: call onSearch function
      // if (onSearch) {
      //   onSearch(payload);
      // }

      if (request) {
        let actualTabKey = tabKey || activeTabKeyRef.current;
        if (activeTabKeyRef.current && tabStatus && tabStatus.length > 0) {
          const tabItem = tabStatus.find((n) => n.code === activeTabKeyRef.current);
          if (tabItem?.ignore) {
            actualTabKey = null;
          }
        }
        try {
          const resp = await request(payload, actualTabKey);
          const hasNext = resp[_dataFields.hasNext];
          const data = resp[_dataFields.data];
          const total = resp[_dataFields.totalRecords];

          setData(data);
          setHasNext(typeof hasNext === 'boolean' ? hasNext : false);
          setLoading(false);
          setTotal(total || 0);
          return data.length === 0;
        } catch (e) {}
      }
      setLoading(false);
      return false;
    },
    [
      enableDisabledTrigger,
      disabledFieldName,
      dataFields,
      sorterValues,
      setLoading,
      request,
      pagination,
      saveCache,
      tabStatus,
      quickSearchFieldsConfig,
      searchFieldsConfig,
    ],
  );

  const selectionOnChange = (selectedRowKeys, selectedRows, info) => {
    setInsideSelectedRowKeys(selectedRowKeys);
    if (onRowSelectionChange) {
      onRowSelectionChange(selectedRowKeys, selectedRows, info);
    }
  };

  const calcFilterSaveBtnDisabled = useCallback((params: Record<string, any>) => {
    let result = true;
    const entries = Object.entries(params);
    if (entries.length > 0 && entries.some((n) => n[1] !== null && n[1] !== undefined && n[1] !== '')) {
      result = false;
    }
    return result;
  }, []);

  const powerSearchPanelOnFinish = (values) => {
    searchPanelValuesRef.current = values;

    const quickSearchPanelValues = quickSearchPanelRef.current?.getFieldsValue();
    const trimmedQuickSearchPanelValues = Object.fromEntries(
      Object.entries(quickSearchPanelValues ?? {}).map(([key, value]) => [
        key,
        typeof value === 'string' ? value.trim() : value,
      ]),
    );
    quickSearchPanelValuesRef.current = trimmedQuickSearchPanelValues;
    const params = {
      ...values,
      ...trimmedQuickSearchPanelValues,
    };

    setFilterSaveBtnDisabled(calcFilterSaveBtnDisabled(params));

    saveCache({ searchParams: { ...params } });
    callRequest(params);
    // if (onSearch) {
    //   onSearch(values);
    // }
  };

  const powerSearchPanelOnReset = () => {
    quickSearchPanelValuesRef.current = {};
    quickSearchPanelRef.current?.reset();
    setActiveTabKey(defaultActiveTabKey);
    activeTabKeyRef.current = defaultActiveTabKey;
    setDisabled(null);
    disabledRef.current = null;
    saveCache({ tabsActiveKey: defaultActiveTabKey, searchParams: {}, pageSize: defaultPageSize, currentPage: 1 });
    if (extraSearchReset) {
      extraSearchReset();
    }
  };

  const searchPanelOnFieldsChange = () => {
    if (activeTabKey.startsWith(savedFiltersTabKeyPrefix)) {
      setActiveTabKey(tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : ''));
      activeTabKeyRef.current = tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : '');
    }
    setFilterSaveBtnDisabled(true);
  };

  const quickSearchPanelOnFinish = (values) => {
    quickSearchPanelValuesRef.current = values;
    const searchPanelValues = searchPanelRef.current?.getFieldsValue();
    searchPanelValuesRef.current = searchPanelValues;
    const params = {
      ...values,
      ...searchPanelValues,
    };

    setFilterSaveBtnDisabled(calcFilterSaveBtnDisabled(params));

    saveCache({ searchParams: { ...params } });
    callRequest(params);
    // if (onSearch) {
    //   onSearch(values);
    // }
  };

  const refreshBtnOnClick = () => {
    callRequest();
    if (onRefreshBtnClick) {
      onRefreshBtnClick();
    }
  };

  const _tabsOnChange = (activeKey) => {
    setActiveTabKey(activeKey);
    activeTabKeyRef.current = activeKey;
    saveCache({ tabsActiveKey: activeKey });
    const tabConfItem = tabStatus?.find((n) => n.code === activeKey);
    if (tabConfItem) {
      callRequest({}, false, activeKey);
    }
    if (savedFilters && activeKey.startsWith(savedFiltersTabKeyPrefix)) {
      setFilterSaveBtnDisabled(true);
      const index = activeKey.substring(activeKey.lastIndexOf('_') + 1);
      const filterItem = savedFilters[Number(index)];

      const mergeParams = { ...filterItem.params };
      const momentDate = formatDate(filterItem.params?.date); // 字符串时间转换为moment格式
      if (momentDate) mergeParams.date = momentDate;
      // TODO: Distinguish between two different parameters
      //       (quickSearchPanelValues and searchPanelValues)
      searchPanelValuesRef.current = mergeParams;
      quickSearchPanelValuesRef.current = mergeParams;

      searchPanelRef.current?.reset();
      quickSearchPanelRef.current?.reset();

      searchPanelRef.current?.setFieldsValue(mergeParams);
      quickSearchPanelRef.current?.setFieldsValue(mergeParams);

      saveCache({ searchParams: mergeParams, tabsActiveKey: activeKey });
      callRequest(mergeParams, true, activeKey);
    }
    if (tabsOnChange) {
      tabsOnChange(activeKey);
    }
  };

  const paginationOnChange = (_currentPage, _pageSize) => {
    setCurrentPage(_currentPage);
    currentPageRef.current = _currentPage;
    setPageSize(_pageSize);
    pageSizeRef.current = _pageSize;
    const isNext = _currentPage > currentPage;
    const _dataFields = {
      ...defaultDataFields,
      ...dataFields,
    };
    saveCache({
      pageSize: _pageSize,
      currentPage: _currentPage,
    });
    callRequest({
      [_dataFields.currentPage]: _currentPage,
      [_dataFields.pageSize]: _pageSize,
    }).then((isEmpty) => {
      if (isNext && isEmpty) {
        message.warning(getFormattedMessages('global.notFoundDataInNextPage'));
        const returnPage = _currentPage - 1;
        setCurrentPage(returnPage);
        currentPageRef.current = returnPage;
        saveCache({ currentPage: returnPage });
        callRequest({
          [_dataFields.currentPage]: returnPage,
        });
      }
    });
  };

  // const tableOnChange = (pagination, filters, sorter, extra, action) => {
  const tableOnChange: TableProps<any>['onChange'] = (pagination, filters, sorter) => {
    const _sorter: any = {};
    const _dataFields = {
      ...defaultDataFields,
      ...dataFields,
    };
    if (Array.isArray(sorter)) {
      _sorter.field = sorter[0].field;
      _sorter.order = sorter[0].order;
    } else {
      _sorter.field = sorter.field;
      _sorter.order = sorter.order;
    }
    const params = {
      [_dataFields.orderByField]: _sorter.field,
      [_dataFields.orderByMethod]: _sorter.order,
      ...filters,
    };
    const sorterObj = {
      field: _sorter.field,
      order: defaultSorterValues[_sorter.order],
    };
    const sorterActivated = !!(sorterObj.field && sorterObj.order);
    if (sorterActivated) {
      setSorter(sorterObj);
      sorterRef.current = sorterObj;
    } else {
      setSorter(null);
      sorterRef.current = null;
    }

    saveCache({
      sorter: sorterActivated ? sorterObj : null,
    });

    callRequest(params);
  };

  /**
   * reset all things
   */
  const reset = (resetItems: ResetItem[] = ['PAGINATION', 'DATA', 'SORTER', 'SELECTION', 'SEARCH_PANEL', 'TABS']) => {
    // pagination
    if (resetItems.includes('PAGINATION')) {
      setCurrentPage(1);
      currentPageRef.current = 1;
      setHasNext(false);
      setPageSize(defaultPageSize || 10);
      pageSizeRef.current = defaultPageSize || 10;
      setTotal(0);
    }

    // data
    if (resetItems.includes('DATA')) {
      setData([]);
    }

    // sorter
    if (resetItems.includes('SORTER')) {
      setSorter(defaultSorter || null);
      sorterRef.current = defaultSorter || null;
    }

    // selection
    if (resetItems.includes('SELECTION')) {
      setInsideSelectedRowKeys([]);
    }

    // search panel
    if (resetItems.includes('SEARCH_PANEL')) {
      searchPanelValuesRef.current = searchPanelInitialValues || {};
      quickSearchPanelValuesRef.current = searchPanelInitialValues || {};
      searchPanelRef.current?.reset();
      quickSearchPanelRef.current?.reset();
    }

    // tabs
    if (resetItems.includes('TABS')) {
      setActiveTabKey(tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : ''));
      activeTabKeyRef.current = tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : '');
    }
  };

  // const getFilterToolbar = () => (
  //   <div
  //     style={{
  //       marginBottom: 12,
  //       display: 'flex',
  //       justifyContent: 'space-between',
  //       alignItems: 'start',
  //     }}
  //   >
  //     <div>
  //       <Tag style={{ padding: '4px 8px' }} color="blue" closable>
  //         状态: &quot;新单&quot;或&quot;处理中&quot;
  //       </Tag>
  //       <Tag style={{ padding: '4px 8px' }} color="blue" closable>
  //         编码包含&quot;abc&quot;
  //       </Tag>
  //       <Tag style={{ padding: '4px 8px' }} color="blue" closable>
  //         日期: 2021-01-01~2021-10-21
  //       </Tag>
  //     </div>
  //     <Space>
  //       <Button type="text" icon={<ClearOutlined />}>
  //         清除所有条件
  //       </Button>
  //     </Space>
  //   </div>
  // );

  const rowSelectionConfig = rowSelectionType
    ? {
        rowSelection: {
          fixed: true,
          type: rowSelectionType,
          onChange: selectionOnChange,
          selectedRowKeys: selectedRowKeys || insideSelectedRowKeys,
          getCheckboxProps: rowSelectionGetCheckboxProps,
        },
      }
    : {};

  const filterSaveBtnOnClick = () => {
    setFilterSaveModalVisible(true);
  };

  const filterSaveModalOnOk = async (values) => {
    setFilterSaveModalConfirmLoading(true);
    if (onFilterSave) {
      try {
        await onFilterSave({
          ...values,
          params: lastParamsRef.current,
        });

        setFilterSaveBtnDisabled(true);
        message.success(i18n.t('global.saveSuccess'));

        setFilterSaveModalVisible(false);
      } catch (e) {}
      setFilterSaveModalConfirmLoading(false);
    }
  };

  const filterDeleteOnClick = (e, record) => {
    e.preventDefault();
    e.stopPropagation();
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDeleteFilter'),
      content: record.name,
      transitionName: '',
      maskTransitionName: '',
      okType: 'danger',
      onOk: async () => {
        if (onFilterDelete) {
          try {
            await onFilterDelete(record);
            // if (tabDefaultActiveKey) {
            setActiveTabKey(tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : ''));
            activeTabKeyRef.current =
              tabDefaultActiveKey || (tabStatus && tabStatus.length > 0 ? tabStatus[0].code : '');
            // }
            searchPanelValuesRef.current = searchPanelInitialValues || {};
            quickSearchPanelValuesRef.current = searchPanelInitialValues || {};
            callRequest({});
          } catch (e) {}
        }
      },
    });
  };

  const searchPanelToggleBtnOnClick = () => {
    setSearchPanelVisible(!searchPanelVisible);
    saveCache({ searchPanelVisible: !searchPanelVisible });
  };

  const disabledTriggerOnChange = useCallback(
    (val) => {
      setDisabled(val);
      disabledRef.current = val;
      callRequest();
      saveCache({ disabled: val });
    },
    [callRequest, saveCache],
  );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      callRequest();
    },
    reset: (resetItems: ResetItem[]) => {
      reset(resetItems);
    },
    setActiveTabKey: (key) => {
      setActiveTabKey(key);
      activeTabKeyRef.current = key;
    },
    setSearchPanelFieldsValue: (values) => {
      searchPanelRef.current?.setFieldsValue(values);
    },
    setSearchPanelVisible: (visible) => {
      setSearchPanelVisible(visible);
    },
  }));

  useEffect(() => {
    setLoading(!!props.loading);
  }, [props.loading]);

  useEffect(() => {
    // 此hack函数为了解决ant的table的sticky横向滚动条及真实滚动条重叠问题
    function hack() {
      window.scrollBy(0, 1);
      window.scrollBy(0, -1);
    }
    hack();
  }, [data]);

  useEffect(() => {
    if (columns) {
      const nweColumns: PowerTableColumnsType = columns.map((item) => {
        if (item.valueType !== 'index') {
          let sortOrder: SortOrder = null;
          switch (sorter?.order) {
            case 'ASCEND':
              sortOrder = 'ascend';
              break;
            case 'DESCEND':
              sortOrder = 'descend';
              break;
            default:
              break;
          }
          if (sortOrder && sorter?.field === item.dataIndex) {
            return {
              sortOrder,
              ...item,
            };
          }
          return item;
        }
        return item;
      });
      setTableColumns(nweColumns);
    }
  }, [columns, enableCache, cacheKey, sorter]);

  useEffect(() => {
    if (enableCache && cacheKey) {
      const localCacheData = loadCache();
      if (localCacheData) {
        const { tabsActiveKey, pageSize, currentPage, searchPanelVisible } = localCacheData;
        const searchParams = {
          ...searchPanelInitialValues,
          ...localCacheData.searchParams,
        };
        let sorter: any;
        if (localCacheData.sorter || localCacheData.sorter === null) {
          sorter = localCacheData.sorter;
        } else {
          sorter = defaultSorter;
        }
        if (typeof localCacheData.disabled === 'boolean' || localCacheData.disabled === null) {
          setDisabled(localCacheData.disabled);
          disabledRef.current = localCacheData.disabled;
        }
        // const sorter = localCacheData.sorter;
        // const sorter = {
        //   // ...defaultSorter,
        //   ...localCacheData.sorter,
        // };
        const sorterActivated = !!(sorter && sorter.field && sorter.order);
        setSorter(sorterActivated ? sorter : null);
        sorterRef.current = sorterActivated ? sorter : null;
        setSearchPanelVisible(searchPanelVisible || searchPanelDefaultVisible || false);
        setActiveTabKey(tabsActiveKey || activeTabKey || '');
        activeTabKeyRef.current = tabsActiveKey || activeTabKey || '';
        setPageSize(pageSize || defaultPageSize || 10);
        pageSizeRef.current = pageSize || defaultPageSize || 10;
        setCurrentPage(currentPage || 1);
        currentPageRef.current = currentPage || 1;
        searchPanelValuesRef.current = searchParams;
        quickSearchPanelValuesRef.current = searchParams;
        searchPanelRef.current?.setFieldsValue(searchParams);
        quickSearchPanelRef.current?.setFieldsValue(searchParams);
      }
    }
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (autoLoad && !firstLoaded.current) {
      callRequest();
      firstLoaded.current = true;
    }
  }, [autoLoad, callRequest]);

  const barVisible = !!(
    quickSearchFieldsConfig ||
    (searchFieldsConfig && searchPanelCollapsible) ||
    // filterSaveable ||
    selectionToolbar ||
    leftToolbar ||
    rightToolbar ||
    settingToolVisible ||
    refreshBtnVisible
  );

  return (
    <div
      className={classNames({
        'power-table': true,
        'pagination-visible': !!pagination,
      })}
      style={style}
    >
      {typeof initialized !== 'boolean' || initialized ? (
        <>
          <div>
            {tabStatus && tabStatus.length > 0 && (
              <Tabs
                defaultActiveKey={tabDefaultActiveKey}
                size="small"
                activeKey={activeTabKey}
                animated={false}
                onChange={_tabsOnChange}
                tabBarExtraContent={tabBarExtraContent}
                items={tabStatus
                  .map(({ code, name }) => ({
                    label: name,
                    key: code,
                  }))
                  .concat(
                    savedFilters && savedFilters.length > 0
                      ? savedFilters.map((record, index) => {
                          return {
                            label: (
                              <span className="flex items-center">
                                <span>{record.name}</span>
                                <button
                                  aria-label="Delete filter"
                                  type="button"
                                  className="-my-1 ml-1 rounded-md p-1 hover:bg-slate-100 focus:bg-slate-100 focus:outline-none"
                                  onClick={(e) => filterDeleteOnClick(e, record)}
                                >
                                  <CloseIcon className="h-4 w-4 fill-slate-500" />
                                </button>
                              </span>
                            ),
                            key: `${savedFiltersTabKeyPrefix}${index}`,
                          };
                        })
                      : [],
                  )}
              />
            )}
          </div>
          {barVisible && (
            <div
              style={{
                marginBottom: 12,
                display: 'flex',
                justifyContent: 'space-between',
                flexWrap: 'wrap',
                gap: 8,
              }}
            >
              <div style={{ display: 'flex', flex: '1 1 0%', alignItems: 'center', gap: 8 }}>
                {Array.isArray(leftToolbar) ? (
                  <Space>
                    {leftToolbar.map((item, index) => (
                      // eslint-disable-next-line
                      <Fragment key={index}>{item}</Fragment>
                    ))}
                  </Space>
                ) : (
                  leftToolbar
                )}
                {quickSearchFieldsConfig && leftToolbar && <Divider type="vertical" />}
                {quickSearchFieldsConfig && (
                  <PowerQuickSearchPanel
                    innerRef={quickSearchPanelRef}
                    onFieldsChange={searchPanelOnFieldsChange}
                    onFinish={quickSearchPanelOnFinish}
                    fieldsConfig={quickSearchFieldsConfig}
                    initialValues={searchPanelInitialValues}
                    fieldsWrap={quickSearchPanelFieldsWrap}
                    submitButtonVisible={quickSearchPanelSubmitButtonVisible}
                  />
                )}
                <Space>{/* toggle filter button + filters save button */}</Space>
                {(quickSearchFieldsConfig || leftToolbar) && !!selectionToolbar && <Divider type="vertical" />}
                {selectionToolbar && insideSelectedRowKeys.length === 0 && rowSelectionType && (
                  <Alert
                    style={{ paddingTop: 4, paddingBottom: 4 }}
                    message={i18n.t('global.selectToOperateTips')}
                    type="info"
                    icon={<BulbFilled />}
                    showIcon
                    closable
                  />
                )}
                {selectionToolbar &&
                  insideSelectedRowKeys.length > 0 &&
                  (Array.isArray(selectionToolbar) ? (
                    <Space>
                      {selectionToolbar.map((item, index) => (
                        // eslint-disable-next-line
                        <Fragment key={index}>{item}</Fragment>
                      ))}
                      <Alert
                        style={{ paddingTop: 4, paddingBottom: 4 }}
                        message={i18n.t('global.selectedDataNum', {
                          num: insideSelectedRowKeys.length,
                        })}
                        type="info"
                        showIcon
                      />
                    </Space>
                  ) : (
                    selectionToolbar
                  ))}
              </div>
              <div>
                {/* <Input.Search autoFocus placeholder="输入单号按回车搜索..." /> */}
                {/* <Button icon={<SearchOutlined />} /> */}
                <Space>
                  {enableDisabledTrigger && <DisabledTrigger value={disabled} onChange={disabledTriggerOnChange} />}
                  {Array.isArray(rightToolbar) ? (
                    <Space>
                      {rightToolbar.map((item, index) => (
                        // eslint-disable-next-line
                        <Fragment key={index}>{item}</Fragment>
                      ))}
                    </Space>
                  ) : (
                    rightToolbar
                  )}
                  {searchFieldsConfig && (
                    <div>
                      {searchPanelCollapsible && (
                        <Button
                          className={classNames(
                            `filter-toggle-btn with-text ${searchPanelVisible ? 'bg-lead-slate/10' : ''}`,
                          )}
                          icon={
                            <span className="flex h-full w-full items-center justify-center">
                              <FoldIcon className="fill-lead-slate" />
                            </span>
                          }
                          type={searchPanelVisible ? 'text' : 'default'}
                          onClick={searchPanelToggleBtnOnClick}
                        />
                      )}
                    </div>
                  )}
                  {/* <Dropdown */}
                  {/*   trigger={['click']} */}
                  {/*   overlay={ */}
                  {/*     <Menu onClick={() => {}}> */}
                  {/*       <Menu.Item key="1">1st menu item</Menu.Item> */}
                  {/*       <Menu.Item key="2">2nd menu item</Menu.Item> */}
                  {/*       <Menu.Ite'bm key="3">3rd menu item</Menu.Item> */}
                  {/*     </Menu> */}
                  {/*   } */}
                  {/*   placemen"bbut="bottomRight" */}
                  {/* > */}
                  {/*   <Button */}
                  {/*     icon={<SortAscendingOutlined />}>排序</Button> */}
                  {/* </Dropdown> */}
                  {/* {settingToolVisible && ( */}
                  {/*   <Popover */}
                  {/*     trigger={['click']} */}
                  {/*     placement="bottomRight" */}
                  {/*     // title="setting pane" */}
                  {/*     overlayInnerStyle={{ padding: 0 }} */}
                  {/*     content={ */}
                  {/*       <div> */}
                  {/*         <Radio.Group */}
                  {/*           style={{ marginBottom: 8 }} */}
                  {/*           size="small" */}
                  {/*           buttonStyle="solid" */}
                  {/*           onChange={(e) => { */}
                  {/*             setTableSize(e.target.value); */}
                  {/*           }} */}
                  {/*           defaultValue={tableSize} */}
                  {/*         > */}
                  {/*       <Radio.Button value="small">紧凑</Radio.Button> */}
                  {/*       <Radio.Button value="middle">中等</Radio.Button> */}
                  {/*       <Radio.Button value="large">松散</Radio.Button> */}
                  {/*         </Radio.Group> */}
                  {/*         <ColumnCheckboxList columns={columns} /> */}
                  {/*       </div> */}
                  {/*     } */}
                  {/*   > */}
                  {/*     <Button icon={<SettingOutlined />} /> */}
                  {/*   </Popover> */}
                  {/* )} */}
                  {refreshBtnVisible && (
                    <Button
                      type="default"
                      loading={loading}
                      onClick={refreshBtnOnClick}
                      icon={<RefreshIcon className="fill-lead-dark" />}
                    />
                  )}
                </Space>
              </div>
            </div>
          )}
          {/* {searchPanelProps && searchPanelVisible && ( */}
          {searchFieldsConfig && (
            <div className={searchPanelVisible ? '' : 'hide-search-panel'}>
              <PowerSearchPanel
                innerRef={searchPanelRef}
                style={{ marginBottom: 16 }}
                onReset={powerSearchPanelOnReset}
                onFieldsChange={searchPanelOnFieldsChange}
                onFinish={powerSearchPanelOnFinish}
                fieldsConfig={searchFieldsConfig}
                initialValues={searchPanelInitialValues}
                addictionButtons={
                  filterSaveable && (
                    <Tooltip
                      placement="topRight"
                      title={
                        filterSaveBtnDisabled
                          ? i18n.t('global.pleaseSelectFiltersAndSearch')
                          : i18n.t('global.saveFilters')
                      }
                    >
                      <Button disabled={filterSaveBtnDisabled} className="ml-2" onClick={filterSaveBtnOnClick}>
                        {i18n.t('global.save')}
                      </Button>
                    </Tooltip>
                  )
                }
              />
            </div>
          )}
          {middleContents}
          {/* )} */}
          {/* {getFilterToolbar()} */}
          <TableWrap
            tableProps={{
              rowKey: rowKey || 'id',
              loading: { indicator: loadingIcon, tip: i18n.t('global.loading'), spinning: loading },
              columns: tableColumns,
              pagination: false,
              dataSource: data,
              components,
              size: tableSize,
              onChange: tableOnChange,
              showSorterTooltip: false,
              // className: 'border-b border-slate-200 shadow sm:rounded-lg',
              // className: 'shadow z-50 mb-1 sm:rounded-lg',
              ...rowSelectionConfig,
              ...tableProps,
            }}
            pagination={pagination}
            paginationWrapClassName={paginationWrapClassName}
            paginationStyle={paginationStyle}
            simplePaginationProps={{
              hasNext,
              currentPage,
              pageSize,
              defaultPageSize,
              rightToolbar: simplePaginationRightToolBar,
              onChange: paginationOnChange,
            }}
            fullPaginationProps={{
              current: currentPage,
              defaultCurrent: 1,
              defaultPageSize,
              pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              total,
              onChange: paginationOnChange,
              ...fullPaginationProps,
            }}
            paginationExtraContent={paginationExtraContent}
            // paginationSticky={paginationSticky}
          />
        </>
      ) : (
        <Spin
          style={{
            marginLeft: '50%',
            marginTop: 100,
            marginBottom: 100,
            transform: 'translateX(-50%)',
          }}
          indicator={
            <>
              <LoadingOutlined style={{ fontSize: 24 }} spin />
              <span>{i18n.t('global.initializing')}</span>
            </>
          }
          delay={200}
        />
      )}
      <FilterSaveModal
        open={filterSaveModalVisible}
        confirmLoading={filterSaveModalConfirmLoading}
        onOk={filterSaveModalOnOk}
        onCancel={() => setFilterSaveModalVisible(false)}
      />
    </div>
  );
};

export default PowerTable;
