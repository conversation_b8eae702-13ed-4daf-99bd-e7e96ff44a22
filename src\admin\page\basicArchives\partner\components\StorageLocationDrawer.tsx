import { Form, Input, InputNumber } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import React, { useEffect, useMemo, useRef } from 'react';

import { IWarehouseItem } from '../data';

interface StorageLocationDrawerProps {
  type: 'READY' | 'SAMPLE';
  open: boolean;
  confirmLoading: boolean;
  current: Partial<IWarehouseItem> | undefined;
  onSubmit: (values: IWarehouseItem) => void;
  onCancel: () => void;
  onDelete: (values: Record<string, any> | undefined) => void;
}

const StorageLocationDrawer: React.FC<StorageLocationDrawerProps> = (props) => {
  const [form] = Form.useForm();
  const { type, onSubmit, onCancel, onDelete, current, open, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (current) {
      const values = current;
      form.setFieldsValue(values);
    }
    const times = setTimeout(() => {
      if (!open) return;
      if (current) {
        nameInputRef.current.focus();
      } else {
        codeInputRef.current.focus();
      }
    }, 200);
    return () => {
      clearTimeout(times);
      form.resetFields();
    };
  }, [open, current, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values) => {
    if (onSubmit) {
      onSubmit(values);
    }
  };
  const onClose = () => {
    if (onCancel) onCancel();
  };

  const onRecover = () => {
    if (current) {
      form.setFieldsValue(current);
      nameInputRef.current.focus();
    } else {
      form.resetFields();
      codeInputRef.current.focus();
    }
  };

  const title = useMemo(() => {
    if (type === 'SAMPLE') {
      return current ? `${i18n.t('global.editSampleLocation')} [${current.code}]` : i18n.t('global.newSampleLocation');
    }
    return current ? `${i18n.t('global.editStorageLocation')} [${current.code}]` : i18n.t('global.newStorageLocation');
  }, [type, current]);

  return (
    <Drawer
      title={title}
      open={open}
      destroyOnClose
      maskClosable={false}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={!!current}
          onDelete={() => {
            onDelete(current);
          }}
          onRecover={onRecover}
        />
      }
    >
      <Form layout="vertical" form={form} onFinish={handleFinish}>
        <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
          <Input ref={codeInputRef} readOnly={!!current} maxLength={20} />
        </Form.Item>
        <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
          <Input ref={nameInputRef} maxLength={50} />
        </Form.Item>

        <Form.Item label={i18n.t('global.rowIndex')} name="rowIndex">
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label={i18n.t('global.columnIndex')} name="columnIndex">
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label={i18n.t('global.pickSort')} name="pickSort">
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label={i18n.t('global.upSort')} name="upSort">
          <InputNumber min={1} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default StorageLocationDrawer;
