import { DatePicker, Input, Spin } from 'antd';
import * as <PERSON><PERSON>b<PERSON>rder<PERSON><PERSON> from 'common/api/factory/BfobOrder';
import * as FobOrder<PERSON>pi from 'common/api/factory/FobOrder';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerProSelect from 'common/components/Select/PartnerSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useCommonFn } from './common';

const FobOrder: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const [initialized, setInitialized] = useState<boolean>(false);
  const [permission] = usePermission('A:F:OB');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const navigate = useNavigate();
  const { confirm } = useCommonFn();

  let defaultPartnerValue: string[] = [];
  if (currentUser.type === 'FACTORY') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    async (params: Record<string, any>, tabActiveKey: string) => {
      if (params.created) {
        params.createdStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      delete params.created;

      if (params.code) {
        params.code = `%${params.code}%`;
      }

      if (tabActiveKey === 'ALL') {
        params.status = ['NEW', 'PROCESSING', 'FINISHED'];
      } else if (tabActiveKey === 'WAITING_DELIVERY') {
        params.status = ['NEW', 'PROCESSING'];
      } else {
        params.status = [tabActiveKey];
      }
      let res;
      if (currentUser.mode === 'BINDING') {
        res = BfobOrderApi.List({
          // orderByField: 'modified',
          // orderByMethod: 'DESCEND',
          ...params,
        });
      } else {
        res = FobOrderApi.List({
          // orderByField: 'modified',
          // orderByMethod: 'DESCEND',
          ...params,
        });
      }
      return res;
    },
    // eslint-disable-next-line
    [],
  );

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    try {
      await confirm(record.id, record.code);
      powerTableRef.current?.load();
    } catch (e) {}
  };

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.orderCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'fmOrderCode',
      label: i18n.t('global.fmOrderCode'),
      inputComponent: <Input />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'partnerIds',
      label: i18n.t('global.shipper'),
      inputComponent: <PartnerProSelect types={['FACTORY']} multiple sourceType="PERMISSION" />,
    },
    {
      name: 'toPartnerIds',
      label: i18n.t('global.receiver'),
      inputComponent: <PartnerProSelect types={['WAREHOUSE', 'SHOP']} multiple />,
    },
    {
      name: 'orderTypeId',
      label: i18n.t('global.orderType'),
      inputComponent: <OrderTypeSelect type="OB" categoryCode="FACTORY" valueField="id" />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.fmOrderCode'),
      width: 220,
      dataIndex: 'fmOrderCode',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                PROCESSING: 'blue',
                FINISHED: 'green',
                CANCELED: 'slate',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.delivery'),
      dataIndex: 'partnerName',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.receiver'),
      dataIndex: 'toPartnerName',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  actionColumn.actionConfig?.push({
    tooltip: i18n.t('global.confirm'),
    icon: <DoubleCheckIcon className="fill-lead-green" />,
    isDisabled: (record) => !(record.status === 'PROCESSING' && confirmPermission),
    onClick: (record) => {
      confirmBtnOnClick(record);
    },
  });

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  const searchPanelInitialValues: any = {
    disabled: 'false',
    created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
    partnerIds: defaultPartnerValue,
  };

  return (
    <div>
      <AppHeader />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={searchPanelInitialValues}
          refreshBtnVisible
          searchPanelVisible={false}
          searchPanelCollapsible
          enableCache
          cacheKey="FOB_ORDER"
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            {
              code: 'NEW',
              name: i18n.t('global.newOrder'),
            },
            {
              code: 'PROCESSING',
              name: i18n.t('global.processing'),
            },
            {
              code: 'FINISHED',
              name: i18n.t('global.finished'),
            },
            {
              code: 'CANCELED',
              name: i18n.t('global.cancelled'),
            },
            // {
            //   code: 'WAITING_DELIVERY',
            //   name: i18n.t('global.waitingForDelivery'),
            // },
          ]}
          defaultPageSize={10}
          pagination
          autoLoad
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: (record) => ({
              onClick: () => {
                navigate(`/app/fob/detail/${record.id}`);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default FobOrder;
