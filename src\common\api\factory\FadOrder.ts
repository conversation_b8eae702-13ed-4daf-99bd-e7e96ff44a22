import { request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/create',
    method: 'post',
    data,
  });

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/list',
    method: 'post',
    data,
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/get',
    method: 'post',
    data,
  });

export const Boxs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/boxes',
    method: 'post',
    data,
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fm-order/rfids',
    method: 'post',
    data,
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/confirm',
    method: 'post',
    data,
  });

export const Disagree = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/disagree',
    method: 'post',
    data,
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/logs',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/factory/fad-order/batch-confirm',
    method: 'post',
    data,
  });
