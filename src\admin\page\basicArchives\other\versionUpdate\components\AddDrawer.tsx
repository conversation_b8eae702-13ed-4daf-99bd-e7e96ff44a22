/**
 * Create by codegen at 2022-07-26
 * 应用版本管理 - 新增
 */
import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { DatePicker, Form, Input, message, Radio, Upload } from 'antd';
import { baseUrl, getToken } from 'common/api/Base';
import Drawer from 'common/components/Drawer';
import DictSelect from 'common/components/Select/DictSelect';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

interface AddModalProps {
  visible?: boolean;
  clientDefault?: string;
  confirmLoading?: boolean;
  softwareUpdateType?: Record<string, any>[];
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

// const { Option } = Select;
const { Dragger } = Upload;

const AddDrawer: React.FC<AddModalProps> = (props) => {
  const {
    visible,
    clientDefault,
    confirmLoading,
    // softwareUpdateType,
    onOk,
    onCancel,
  } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);
  const [radioKey, setRadioKey] = useState(1);
  const [source, setSource] = useState<string>('');
  const [fileName, setFileName] = useState('');
  // const softwareUpdateType = useDict(['SOFTWARE_UPDATE_TYPE']);

  const [permission] = usePermission('A:BASE:OTHER:O:VU');
  const relPermission = permission.codes.includes('REL');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      values.source = source;
      values.appointmentUpdateTag = radioKey === 3;
      values.defaultTag = radioKey === 2;
      values.fileName = fileName;
      onOk(values);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    maxCount: 1,
    action: `${baseUrl}/api/file/f/file/upload`,
    headers: {
      authorization: getToken(),
    },
    onChange(info) {
      const { status, name, response } = info.file;
      if (status === 'done') {
        const startIndex = name.lastIndexOf('_') + 1;
        const lastIndex = name.lastIndexOf('.');
        form.setFieldsValue({
          releaseVersion: name.slice(startIndex, lastIndex),
        });
        setSource(response);
        setFileName(name);
        message.success(`${info.file.name} ${i18n.t('global.uploadSuccess')}`);
      } else if (status === 'error') {
        message.error(`${info.file.name} ${i18n.t('global.uploadError')}`);
      }
    },
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setRadioKey(1);
    }
  }, [form, visible]);

  useEffect(() => {
    let timerId: any = null;
    if (visible) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    form.setFieldsValue({ client: clientDefault });
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [visible, clientDefault, form]);

  return (
    <Drawer
      title={i18n.t('global.newVersion')}
      width={700}
      open={visible}
      okButtonProps={{
        loading: confirmLoading,
      }}
      onOk={modalOnOk}
      onClose={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      <Form
        layout="vertical"
        form={form}
        initialValues={{
          appointmentUpdateDate: moment(),
        }}
        onFinish={formOnFinish}
      >
        <Form.Item name="client" label={i18n.t('global.client')}>
          <DictSelect />
        </Form.Item>
        <Form.Item name="releaseVersion" label={i18n.t('global.versionNumber')} rules={canNotBeNullRules}>
          <Input ref={inputRef} />
        </Form.Item>
        <Form.Item name="content" label={i18n.t('global.updateContent')} rules={canNotBeNullRules}>
          <Input.TextArea rows={3} />
        </Form.Item>
        <Form.Item label={i18n.t('global.applicationFile')} rules={canNotBeNullRules}>
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{i18n.t('global.uploadTip')}</p>
            <p className="ant-upload-hint">
              {i18n.t('global.supportType')}
              ：.rar, .zip, .apk
            </p>
          </Dragger>
        </Form.Item>
        <Form.Item name="remark" label={i18n.t('global.remark')}>
          <Input />
        </Form.Item>
        {relPermission && (
          <Form.Item label={i18n.t('global.release')}>
            <Radio.Group onChange={(e) => setRadioKey(e.target.value)} value={radioKey}>
              <Radio value={1}>{i18n.t('global.notYetReleased')}</Radio>
              <Radio value={2}>{i18n.t('global.publishNow')}</Radio>
              <Radio value={3}>{i18n.t('global.bookAnAppointment')}</Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {radioKey === 3 && (
          <Form.Item name="appointmentUpdateDate" label={i18n.t('global.updateTime')}>
            <DatePicker showTime />
          </Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default AddDrawer;
