import { Button, Dropdown, Input, message, Space } from 'antd';
import * as SampleApi from 'common/api/sample/Sample';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import ProdCodeRowText from 'common/components/Text/prodCodeRowText';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useMemo, useReducer, useRef, useState } from 'react';

import SampleBrandSelect from 'common/components/Select/SampleBrandSelect';
import SamplePriCategorySelect from 'common/components/Select/SamplePriCategorySelect';
import SampleSubCategorySelect from 'common/components/Select/SampleSubCategorySelect';
import SampleYearSelect from 'common/components/Select/SampleYearSelect';
import SampleSeriesSelect from 'common/components/Select/SampleSeriesSelect';
import { initialState, SampleContext, reducer } from './reducer';
import AddDrawer from './components/AddDrawer';
import DetailDrawer from './components/DetailDrawer';
import ImportImgModal from './components/ImportImgModal';
import ImportModal from './components/ImportModal';

const Sample = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [current, setCurrent] = useState<any>({});
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importImgModalVisible, setImportImgModalVisible] = useState(false);
  const [addModalConfirmLoading, setAddModalConfirmLoading] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);

  const [permission] = usePermission('A:BASE:SAMPLE');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const importPermission = permission.codes.includes('IMPORT');
  const deletePermission = permission.codes.includes('DELETE');

  const editBtnOnClick = (record: Record<string, any>) => {
    dispatch({ type: 'setSampleId', payload: record.id });
    setCurrent(record);
    setDetailDrawerVisible(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'smBrandIdList',
      label: i18n.t('global.brand'),
      inputComponent: <SampleBrandSelect mode="multiple" />,
    },
    {
      name: 'smPriCategoryIdList',
      label: i18n.t('global.priCategory'),
      inputComponent: <SamplePriCategorySelect mode="multiple" />,
    },
    {
      name: 'smSubCategoryIdList',
      label: i18n.t('global.subCategory'),
      inputComponent: <SampleSubCategorySelect mode="multiple" />,
    },
    {
      name: 'smYearIdList',
      label: i18n.t('global.yearCode'),
      inputComponent: <SampleYearSelect mode="multiple" />,
    },
    {
      name: 'smSeriesIdList',
      label: i18n.t('global.series'),
      inputComponent: <SampleSeriesSelect mode="multiple" />,
    },
  ];

  const tableColumns: any = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      width: 200,
      ellipsis: true,
      render: (code, record) => <ProdCodeRowText prodCode={code} disturbTag={record.disturbTag} />,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      valueType: 'text',
      minWidth: 200,
      auto: true,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: i18n.t('global.source'),
      dataIndex: 'source',
      sorter: true,
      valueType: 'text',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.band'),
      dataIndex: 'band',
      valueType: 'text',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.brand'),
      width: 150,
      valueType: 'codeName',
      codeDataIndex: 'smBrandCode',
      nameDataIndex: 'smBrandName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.colorGroup'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'smColorGrpCode',
      nameDataIndex: 'smColorGrpName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.gender'),
      width: 120,
      dataIndex: 'gender',
      valueType: 'text',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.sizeGroup'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'smSizeGrpCode',
      nameDataIndex: 'smSizeGrpName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.series'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'smSeriesCode',
      nameDataIndex: 'smSeriesName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.year'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'smYearCode',
      nameDataIndex: 'smYearName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.priCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'smPriCategoryCode',
      nameDataIndex: 'smPriCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.subCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'smSubCategoryCode',
      nameDataIndex: 'smSubCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.designer'),
      width: 150,
      dataIndex: 'designer',
      valueType: 'text',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.costPrice'),
      dataIndex: 'costPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.smRetailPrice'),
      dataIndex: 'retailPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.barcodeTagPrice'),
      dataIndex: 'tagPrice',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.listedDate'),
      dataIndex: 'listedDate',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  const onImportantFinish = () => {
    message.success(i18n.t('global.importProcessing'));
    powerTableRef.current?.load();
  };

  const onImportantImgFinish = () => {};

  const fetchData = useCallback(async (params: Record<string, any>) => {
    return SampleApi.List(params);
  }, []);

  const addBtnOnClick = () => {
    setAddModalVisible(true);
  };

  const importBtnOnClick = () => {
    setImportModalVisible(true);
  };

  const importImgBtnOnClick = () => {
    setImportImgModalVisible(true);
  };

  const importModalOnCancel = () => {
    setImportModalVisible(false);
  };

  const importImgModalOnCancel = () => {
    setImportImgModalVisible(false);
    powerTableRef.current?.load();
  };

  const addModalOnCancel = () => {
    setAddModalVisible(false);
  };

  const addModalOnSubmit = async (values) => {
    setAddModalConfirmLoading(true);
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      await SampleApi.Create(values);
      setAddModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAddModalConfirmLoading(false);
  };

  const detailDrawerOnClose = () => {
    setDetailDrawerVisible(false);
    dispatch({ type: 'setSkuList', payload: [] });
    dispatch({ type: 'setSampleId', payload: '' });
    dispatch({ type: 'setDisabled', payload: null });
    dispatch({ type: 'setSampleRecord', payload: null });
    powerTableRef.current?.load();
  };

  const sampleContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <SampleContext.Provider value={sampleContextValue}>
      <div>
        <AppHeader
          toolbar={
            <Space>
              {importPermission && (
                <Dropdown.Button
                  menu={{
                    items: [
                      {
                        label: i18n.t('global.importImages'),
                        key: 'IMAGES',
                        disabled: !importPermission,
                      },
                    ],
                    onClick: () => importImgBtnOnClick(),
                  }}
                  onClick={importBtnOnClick}
                  trigger={['click']}
                >
                  {i18n.t('global.import')}
                </Dropdown.Button>
              )}
              {createPermission && (
                <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
                  {i18n.t('global.new')}
                </Button>
              )}
            </Space>
          }
        />
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          enableDisabledTrigger
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          enableCache
          cacheKey="SAMPLE"
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: editPermission
              ? (record) => ({
                  onClick: () => {
                    editBtnOnClick(record);
                  },
                })
              : undefined,
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
        <AddDrawer
          modalProps={{
            open: addModalVisible,
            maskClosable: false,
          }}
          conformLoading={addModalConfirmLoading}
          onCancel={addModalOnCancel}
          onSubmit={addModalOnSubmit}
        />
        <ImportModal
          modalProps={{
            open: importModalVisible,
            onCancel: importModalOnCancel,
            maskClosable: false,
          }}
          onFinish={onImportantFinish}
          onGoBack={() => setImportModalVisible(false)}
        />
        <ImportImgModal
          modalProps={{
            open: importImgModalVisible,
            onCancel: importImgModalOnCancel,
            maskClosable: false,
          }}
          onFinish={onImportantImgFinish}
        />
        <DetailDrawer
          open={detailDrawerVisible}
          title={
            <>
              {i18n.t('global.sampleDetail')}
              {current ? ` [${current.code}]` : ''}
            </>
          }
          onClose={detailDrawerOnClose}
        />
      </div>
    </SampleContext.Provider>
  );
};

export default Sample;
