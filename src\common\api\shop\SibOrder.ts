import { IRequestConfig, request, RequestPayloadType, RequestReturnType } from '../Base';

export const Create = (data: RequestPayloadType, config?: IRequestConfig): RequestReturnType =>
  request({
    method: 'post',
    data,
    url: '/api/shop/sib-order/create',
    ...config,
  });

export const List = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/list',
    data,
    method: 'post',
  });

export const PreBoxes = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/pre-boxes',
    data,
    method: 'post',
  });

export const PreBoxRfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/pre-box-rfids',
    data,
    method: 'post',
  });

export const Get = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/get',
    data,
    method: 'post',
  });

export const Confirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/confirm',
    data,
    method: 'post',
  });

export const Boxes = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/boxes',
    data,
    method: 'post',
  });

export const Lines = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/lines',
    data,
    method: 'post',
  });

export const Cancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/cancel',
    data,
    method: 'post',
  });

export const Rfids = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/rfids',
    data,
    method: 'post',
  });

export const Logs = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/logs',
    method: 'post',
    data,
  });

export const FastReceipt = (data: RequestPayloadType): RequestReturnType =>
  request({
    method: 'post',
    data,
    url: '/api/shop/sib-order/fast-receipt',
  });

export const Update = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/update',
    method: 'post',
    data,
  });

export const Reset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/reset',
    method: 'post',
    data,
  });

export const ConfirmBox = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/box-confirm',
    method: 'post',
    data,
  });

export const BoxReset = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/box-reset',
    method: 'post',
    data,
  });

export const Save = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/box-save',
    method: 'post',
    data,
  });

export const InboundData = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/inbound-data',
    method: 'post',
    data,
  });

export const BatchCancel = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/batch-cancel',
    method: 'post',
    data,
  });

export const BatchConfirm = (data: RequestPayloadType): RequestReturnType =>
  request({
    url: '/api/shop/sib-order/batch-confirm',
    method: 'post',
    data,
  });
