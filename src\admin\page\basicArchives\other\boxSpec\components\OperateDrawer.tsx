import { Form, Input, InputNumber } from 'antd';
import * as BoxSpecApi from 'common/api/core/BoxSpec';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { IBoxSpecItem } from '../data';

interface OperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  boxSpecId: string | undefined;
  onSubmit: (values: IBoxSpecItem) => void;
  onCancel: () => void;
}

const OperateDrawer: React.FC<OperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, boxSpecId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);

  const [permission] = usePermission('A:BASE:OTHER:BOX_SPEC');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchBoxSpec = useCallback(async () => {
    setLoading(true);
    try {
      const boxSpec: any = await BoxSpecApi.Get({
        id: boxSpecId,
      });
      form.setFieldsValue(boxSpec);
      setCurrent(boxSpec);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [boxSpecId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (boxSpecId) fetchBoxSpec();
      setTimeout(() => {
        if (boxSpecId) {
          nameInputRef.current?.focus();
        } else {
          codeInputRef.current?.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [visible, boxSpecId, fetchBoxSpec, form]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as IBoxSpecItem);
    }
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editBoxSpec')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newBoxSpec')
  );

  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await BoxSpecApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchBoxSpec();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      open={visible}
      onClose={onClose}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!boxSpecId}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input ref={codeInputRef} readOnly={!!current} maxLength={20} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input maxLength={30} ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.alias')} name="alias">
            <Input />
          </Form.Item>
          <Form.Item label={i18n.t('global.capacity')} name="capacity" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.length')} name="length" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.width')} name="width" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.height')} name="height" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.volume')} name="volume" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.weightUnit')} name="weight" rules={canNotBeNullRules}>
            <InputNumber maxLength={10} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  );
};
export default OperateDrawer;
