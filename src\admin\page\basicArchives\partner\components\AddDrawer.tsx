import { IPartnerItem } from 'admin/page/basicArchives/partner/data';
import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import OperateForm, { OperateFormInnerRef } from './OperateForm';

interface IAddDrawerProps {
  type: string;
  open?: boolean;
  onSubmit: (values: IPartnerItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<IAddDrawerProps> = (props) => {
  const { type, onCancel, open, onSubmit } = props;
  const [form] = Form.useForm();
  const operateFormRef = useRef<OperateFormInnerRef>();

  const titleMap = {
    WAREHOUSE: i18n.t('global.newWarehouse'),
    SHOP: i18n.t('global.newShop'),
    FACTORY: i18n.t('global.newFactory'),
    SUPPLIER: i18n.t('global.newSupplier'),
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      if (values.area) {
        switch (values.area.length) {
          case 1:
            values.province = values.area[0];
            break;
          case 2:
            values.province = values.area[0];
            values.city = values.area[1];
            break;
          case 3:
            values.province = values.area[0];
            values.city = values.area[1];
            values.town = values.area[2];
            break;
          default:
            break;
        }
      }
      delete values.area;
      if (values.telNumber && values.telArea) {
        values.tel = `+(${values.telArea})${values.telNumber}`;
      } else if (values.telArea) {
        values.tel = `+(${values.telArea})`;
      } else {
        values.tel = values.telNumber;
      }
      if (values.mobileNumber && values.mobileArea) {
        values.mobile = `+(${values.mobileArea})${values.mobileNumber}`;
      } else if (values.mobileArea) {
        values.mobile = `+(${values.mobileArea})`;
      } else {
        values.mobile = values.mobileNumber;
      }
      delete values.telArea;
      delete values.mobileArea;
      delete values.telNumber;
      delete values.mobileNumber;
      onSubmit(values as IPartnerItem);
    }
  };

  const onFinishFailed = () => {};

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        operateFormRef.current?.focusCodeInput();
      }, 300);
    } else {
      form.resetFields();
    }
  }, [form, open]);

  return (
    <Drawer
      title={titleMap[type]}
      destroyOnClose
      width={800}
      okText={i18n.t('global.apply')}
      open={open}
      onClose={onCancel}
      onOk={handleSubmit}
    >
      <OperateForm
        innerRef={operateFormRef}
        type={type}
        form={form}
        name="addForm"
        initialValues={{
          allowTakeStock: true,
          returnTag: false,
        }}
        onFinish={handleFinish}
        onFinishFailed={onFinishFailed}
        allowTakeStockVisible
      />
    </Drawer>
  );
};

export default AddDrawer;
