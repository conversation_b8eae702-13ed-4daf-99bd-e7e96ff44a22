import { Descriptions, Divider, Form, Space, Statistic, Tabs } from 'antd';
import * as BfadOrderApi from 'common/api/factory/BfadOrder';
import * as FadOrderApi from 'common/api/factory/FadOrder';
import CloseCircleLineIcon from 'common/assets/icons/icon-close-circle-line.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import Button from 'common/components/Button';
import RefreshButton from 'common/components/Button/Refresh';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import { IPowerTableInnerRef } from 'common/components/PowerTable';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import BoxRfidsTable from './components/BoxRfidsTable';
import DeOrderConfirmModal from './components/ConfirmModal';
import RejectModal from './components/RejectModal';

const FadOrderDetail: React.FC = () => {
  const params: any = useParams();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [orderRecord, setOrderRecord] = useState<{
    [key: string]: any;
  }>({});
  const [deOrderConfirmModalVisible, setDeOrderConfirmModalVisible] = useState<boolean>(false);
  const [rejectModalVisible, setRejectModalVisible] = useState<boolean>(false);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [selectedFmOrderBoxIds, setSelectedFmOrderBoxIds] = useState<string[]>([]);
  const [selectedRfidsCount, setSelectedRfidsCount] = useState<number>(0);
  const [logs, setLogs] = useState<any[]>([]);
  const [permission] = usePermission('A:F:FAD');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const boxRfidsTableRef = useRef<IPowerTableInnerRef>();

  const [form] = Form.useForm();

  const reserveDeliveryBoxesMsg = i18n.t('global.reserveDeliveryBoxesMsg', {
    numtotal: selectedFmOrderBoxIds.length,
    numcount: selectedRfidsCount,
  });
  const fetchOrderData = useCallback(async () => {
    if (!params || !params.id) return;
    setLoading(true);
    try {
      let rec;
      if (currentUser.mode === 'BINDING') {
        rec = await BfadOrderApi.Get({ id: params.id });
      } else {
        rec = await FadOrderApi.Get({ id: params.id });
      }
      setOrderRecord(rec);
    } catch (err) {}
    setLoading(false);
    // eslint-disable-next-line
  }, [params]);

  const fetchLogs = async () => {
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfadOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      } else {
        res = await FadOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      }
      setLogs(res.data);
    } catch (e) {}
  };

  const refresh = () => {
    boxRfidsTableRef.current?.load();
    fetchOrderData();
  };

  const onSave = async (values) => {
    values.fmOrderBoxIds = selectedFmOrderBoxIds;
    setConfirmLoading(true);
    try {
      if (currentUser.mode === 'BINDING') {
        await BfadOrderApi.Confirm(values);
      } else {
        await FadOrderApi.Confirm(values);
      }
      setDeOrderConfirmModalVisible(false);
      NoticeUtil.success();
      refresh();
    } catch (e) {}
    setConfirmLoading(false);
  };

  const onSelectBox = (boxIds, rfidsCount) => {
    setSelectedFmOrderBoxIds(boxIds);
    setSelectedRfidsCount(rfidsCount);
  };

  const handleRejectModalSave = async (data) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BfadOrderApi.Disagree(data);
      } else {
        await FadOrderApi.Disagree(data);
      }
      NoticeUtil.success();
      setRejectModalVisible(false);
      refresh();
    } catch (e) {}
  };

  useEffect(() => {
    if (logModalVisible) {
      fetchLogs();
    }
    // eslint-disable-next-line
  }, [logModalVisible]);

  useEffect(() => {
    if (!params || !params.id) return;
    refresh();
    // eslint-disable-next-line
  }, [params]);

  return (
    <div>
      <AppHeader
        title={loading ? i18n.t('global.loading') : orderRecord.code}
        toolbar={
          <Space>
            <RefreshButton size="middle" onClick={refresh} />
            <Button
              type="default"
              icon={<ListIcon className="fill-lead-dark" />}
              onClick={() => setLogModalVisible(true)}
            >
              {i18n.t('global.log')}
            </Button>
            {orderRecord.status === 'NEW' && confirmPermission && (
              <>
                <Button
                  type="danger"
                  icon={<CloseCircleLineIcon className="fill-white" />}
                  onClick={() => setRejectModalVisible(true)}
                >
                  {i18n.t('global.reject')}
                </Button>
                <Button
                  type="primary"
                  icon={<DoubleCheckIcon className="fill-white" />}
                  className="global-blue"
                  onClick={() => setDeOrderConfirmModalVisible(true)}
                  disabled={selectedFmOrderBoxIds.length === 0}
                >
                  {i18n.t('global.confirm')} ({selectedFmOrderBoxIds.length})
                </Button>
              </>
            )}
            <Divider type="vertical" style={{ margin: 0 }} />
          </Space>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <SendReceiveLayout
            className="flex-auto"
            left={
              <PartnerViewer
                partnerType="FACTORY"
                label={i18n.t('global.delivery')}
                partnerCode={orderRecord.partnerCode}
                partnerName={orderRecord.partnerName}
                warehouseCode={orderRecord.warehouseCode}
                warehouseName={orderRecord.warehouseName}
              />
            }
            right={
              <PartnerViewer
                partnerType={orderRecord.toPartnerType}
                label={i18n.t('global.receiver')}
                partnerCode={orderRecord.toPartnerCode}
                partnerName={orderRecord.toPartnerName}
                warehouseCode={orderRecord.toWarehouseCode}
                warehouseName={orderRecord.toWarehouseName}
              />
            }
          />
          <div className="flex flex-initial items-center gap-x-6">
            {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
            {/* <Statistic title={i18n.t('global.recQty')} value={orderRecord.recQty} /> */}
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {orderRecord.fmOrderCode && (
              <Descriptions.Item label={i18n.t('global.fmOrderCode')}>{orderRecord.fmOrderCode}</Descriptions.Item>
            )}
            {orderRecord.prodCode && (
              <Descriptions.Item label={i18n.t('global.productCode')}>{orderRecord.prodCode}</Descriptions.Item>
            )}
            {orderRecord.syncStatusDesc && (
              <Descriptions.Item label={i18n.t('global.syncStatus')}>{orderRecord.syncStatusDesc}</Descriptions.Item>
            )}
            {orderRecord.expTime && (
              <Descriptions.Item label={i18n.t('global.expTime')}>{orderRecord.expTime}</Descriptions.Item>
            )}
            {orderRecord.modified && (
              <Descriptions.Item label={i18n.t('global.modified')}>
                {moment(orderRecord.modified).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>
                {moment(orderRecord.created).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {orderRecord.remark && (
              <Descriptions.Item label={i18n.t('global.remark')}>{orderRecord.remark}</Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          size="small"
          defaultActiveKey="BOX"
          items={[
            {
              label: (
                <>
                  {i18n.t('global.fadBox')} ({orderRecord.boxQty || 0})
                </>
              ),
              key: 'BOX',
              children: (
                <BoxRfidsTable
                  powerTableRef={boxRfidsTableRef}
                  fmOrderId={orderRecord.fmOrderId}
                  orderStatus={orderRecord.status}
                  orderId={params ? params.id : undefined}
                  onSelectBox={onSelectBox}
                />
              ),
            },
          ]}
        />
        <DeOrderConfirmModal
          title={reserveDeliveryBoxesMsg}
          visible={deOrderConfirmModalVisible}
          onCancel={() => {
            setDeOrderConfirmModalVisible(false);
            form.resetFields();
          }}
          form={form}
          orderRecord={orderRecord}
          confirmLoading={confirmLoading}
          onSave={onSave}
        />
      </div>
      <RejectModal
        visible={rejectModalVisible}
        fadOrderId={params.id}
        onSave={handleRejectModalSave}
        onCancel={() => setRejectModalVisible(false)}
      />
      <LogDrawer
        open={logModalVisible}
        orderId={orderRecord.id}
        onClose={() => setLogModalVisible(false)}
        data={logs}
      />
    </div>
  );
};

export default FadOrderDetail;
