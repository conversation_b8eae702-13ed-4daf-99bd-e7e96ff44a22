import { Timeline, Spin } from 'antd';
import CheckboxCircleFillIcon from 'common/assets/icons/icon-checkbox-circle-fill.svg?react';
import CloseCircleFillIcon from 'common/assets/icons/icon-close-circle-fill.svg?react';
import i18n from 'common/utils/I18n';
import React, { useEffect, useState } from 'react';
import * as WorkflowTaskApi from 'common/api/core/WorkflowTask';
import Empty from 'common/components/Empty';

import Drawer from 'common/components/Drawer';

interface IWorkflowTaskLogDrawer {
  open: boolean;
  task: any;
  onClose: () => void;
}

const WorkflowTaskLogDrawer: React.FC<IWorkflowTaskLogDrawer> = (props) => {
  const { open, onClose, task } = props;

  const title = task?.orderCode ? `${i18n.t('global.log')} [${task.orderCode}]` : i18n.t('global.log');

  const [logList, setLogList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchData = async () => {
    if (!task.id) return;
    try {
      setLoading(true);
      const res = await WorkflowTaskApi.LogList({
        workFlowTaskId: task.id,
        enablePage: false,
        orderByField: 'created',
        orderByMethod: 'DESCEND',
      });

      setLogList(res.data);
    } catch {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (task?.id) fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [task?.id]);

  return (
    <Drawer className="log-drawer" open={open} width={800} title={title} onClose={onClose} footer={false}>
      <Spin spinning={loading}>
        {logList.length === 0 ? (
          <div className="mt-[20%]">
            <Empty />
          </div>
        ) : (
          <Timeline mode="left">
            {logList.map((n: any) => {
              const propsObj: any = {};
              if (n.success) {
                propsObj.dot = <CheckboxCircleFillIcon className="h-5 w-5 fill-lead-green" />;
              } else {
                propsObj.dot = <CloseCircleFillIcon className="h-5 w-5 fill-lead-red" />;
              }
              return (
                <Timeline.Item key={n.id} {...propsObj}>
                  <div className="mb-2 font-bold">{n.created}</div>
                  <div className="flex gap-x-5">
                    <div className="flex flex-1 flex-col gap-y-1">
                      <span>{n.message}</span>
                    </div>
                  </div>
                </Timeline.Item>
              );
            })}
          </Timeline>
        )}
      </Spin>
    </Drawer>
  );
};

export default WorkflowTaskLogDrawer;
