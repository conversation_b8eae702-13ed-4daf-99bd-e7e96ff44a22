import { faCheck, faImage, faPenToSquare, faShare, faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Badge, Button, Space, Tag, Tooltip, Typography } from 'antd';
import i18n from 'common/utils/I18n';
import ImageWrap from 'common/components/ImageWrap';
import React, { useEffect, useState } from 'react';

import styles from './albumItem.module.css';

const { Paragraph, Text } = Typography;

export interface AlbumItemProps {
  current: Record<string, any>;
  /**
   * 是否显示门店名称,不传默认不显示
   * */
  isPartner?: boolean;
  /**
   * 控制是否显示遮罩层, 不传默认显示
   * */
  isLoading?: boolean;
  /**
   * 控制是否选中
   * */
  isSelectIcon?: boolean;
  /**
   * 控制是否全部不显示Badge,默认显示默认项
   * */
  isBadge?: boolean;
  /**
   * 控制是否显示转为公共相册按钮
   * */
  isConvert?: boolean;
  /**
   * 权限
   * */
  permission?: Record<string, any>;
  defaultOnClick?: (values: Record<string, any>) => void;
  editOnClick?: (values: Record<string, any>) => void;
  convertOnClick?: (values: Record<string, any>) => void;
  deleteOnClick?: (values: Record<string, any>) => void;
  selectOnClick?: (values: Record<string, any>) => void;
  photoOnClick?: (values: Record<string, any>) => void;
}
const AlbumItem: React.FC<AlbumItemProps> = (props) => {
  const {
    current,
    isPartner: _isPartner,
    isLoading: _isLoading,
    permission,
    isSelectIcon,
    isBadge: _isBadge,
    isConvert: _isConvert,
    defaultOnClick,
    editOnClick,
    convertOnClick,
    deleteOnClick,
    selectOnClick,
    photoOnClick,
  } = props;
  const [maskingVisible, setMaskingVisible] = useState<boolean>(false);
  const isLoading = _isLoading ?? true;
  const isPartner = _isPartner ?? false;
  const isBadge = _isBadge ?? true;
  const isConvert = _isConvert ?? true;
  const [opacity, setOpacity] = useState(0);
  const btnOnClick = (type) => {
    switch (type) {
      case 'DEFAULT':
        if (defaultOnClick) defaultOnClick(current);
        break;
      case 'EDIT':
        if (editOnClick) editOnClick(current);
        break;
      case 'PHOTO':
        if (photoOnClick) photoOnClick(current);
        break;
      case 'CONVERT':
        if (convertOnClick) convertOnClick(current);
        break;
      case 'DEL':
        if (deleteOnClick) deleteOnClick(current);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    setMaskingVisible(false);
  }, [current]);

  const convertPermission = permission?.convertPermission && isConvert && !current.disable;

  const contentNode: React.ReactNode = (
    // eslint-disable-next-line
    <div
      className={styles.content}
      onMouseEnter={() => {
        setOpacity(0);
        setTimeout(() => {
          setOpacity(1);
        }, 80);
        setMaskingVisible(true);
      }}
      onMouseLeave={() => {
        setOpacity(0);
        setMaskingVisible(false);
      }}
      onClick={() => selectOnClick && selectOnClick(current)}
    >
      <ImageWrap
        className={styles.image}
        style={{ height: isPartner ? 140 : 160 }}
        fileName={current.source}
        preview={false}
      />
      <div className={styles.footer} style={{ height: isPartner ? 60 : 40 }}>
        {isPartner && (
          <Tooltip title={current.partnerName}>
            <Text
              className="text-lead-slate"
              style={{
                width: 160,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {current.partnerName}
            </Text>
          </Tooltip>
        )}
        <div className={styles.album}>
          <Paragraph className="text-lead-slate" style={{ flex: 1, margin: 0 }} ellipsis={{ rows: 1 }}>
            {current.name}
          </Paragraph>
          <Text type="secondary" style={{ width: 30, textAlign: 'right' }}>
            {current.num}
          </Text>
        </div>
      </div>
      {isLoading && maskingVisible && (
        <div className={`${styles.masking}`} style={{ opacity }}>
          <div className={styles.operation}>
            <Space direction="horizontal" wrap align="center" size="middle" style={{ justifyContent: 'center' }}>
              {permission?.defaultPermission && !current.defaultTag && (
                <Tooltip title={i18n.t('global.default')} z-index={99}>
                  <Button
                    type="primary"
                    className={styles.defaultBtn}
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faCheck} />}
                    onClick={() => btnOnClick('DEFAULT')}
                  />
                </Tooltip>
              )}
              {permission?.editPermission && (
                <Tooltip title={i18n.t('global.edit')} z-index={99}>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faPenToSquare} />}
                    onClick={() => btnOnClick('EDIT')}
                  />
                </Tooltip>
              )}
              {permission?.photoPermission && (
                <Tooltip title={i18n.t('global.image')} z-index={99}>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faImage} />}
                    onClick={() => btnOnClick('PHOTO')}
                  />
                </Tooltip>
              )}
              {convertPermission && (
                <Tooltip title={i18n.t('global.turnPublicAlbum')} z-index={99}>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faShare} />}
                    onClick={() => btnOnClick('CONVERT')}
                  />
                </Tooltip>
              )}
              {permission?.deletePermission && (
                <Tooltip title={i18n.t('global.delete')} z-index={99}>
                  <Button
                    type="primary"
                    danger
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faTrashCan} />}
                    onClick={() => btnOnClick('DEL')}
                  />
                </Tooltip>
              )}
            </Space>
          </div>
          <Paragraph ellipsis={{ rows: 3 }}>{current.remark}</Paragraph>
        </div>
      )}
      {!isLoading && (
        <div className={styles.icon}>
          {isSelectIcon && <FontAwesomeIcon className={`${styles.radioIcon} text-lead-red`} icon={faCheck} />}
        </div>
      )}
      {current?.disable && <Tag className={styles.disable}>{i18n.t('global.disabled')}</Tag>}
    </div>
  );

  return (
    <div>
      {isBadge && current.defaultTag ? (
        <Badge.Ribbon text={i18n.t('global.default')} color="green">
          {contentNode}
        </Badge.Ribbon>
      ) : (
        <div>{contentNode}</div>
      )}
    </div>
  );
};

export default AlbumItem;
