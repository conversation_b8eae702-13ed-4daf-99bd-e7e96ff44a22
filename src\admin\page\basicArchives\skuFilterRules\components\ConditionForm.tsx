import { Form, Input, Select } from 'antd';
import i18n from 'common/utils/I18n';
import React from 'react';
import type { FormProps } from 'antd';
import { filterModeMap } from '../common';

type FormComponentProps = { codeInputRef: any } & FormProps;

const ConditionForm: React.FC<FormComponentProps> = ({ codeInputRef, ...args }) => {
  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const filterModeOptions = Object.keys(filterModeMap).map((key) => ({
    label: filterModeMap[key],
    value: key,
  }));

  return (
    <Form {...args}>
      <Form.Item label={i18n.t('global.filterMode')} name="filterMode" rules={canNotBeNullRules}>
        <Select ref={codeInputRef} options={filterModeOptions} />
      </Form.Item>
      <Form.Item label={i18n.t('global.epcLength')} name="length">
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.priority')} name="priority">
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.startPosition')} name="startPos">
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.startPositionStr')} name="startStr">
        <Input />
      </Form.Item>
      <Form.Item label={i18n.t('global.suffixCharacters')} name="suffix">
        <Input />
      </Form.Item>
    </Form>
  );
};

export default ConditionForm;
