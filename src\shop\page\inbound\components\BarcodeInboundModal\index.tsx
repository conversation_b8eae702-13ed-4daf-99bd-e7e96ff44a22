import { Col, message, Row, Space, Spin } from 'antd';
import { ModalProps } from 'antd/es/modal';
import * as <PERSON>ku<PERSON><PERSON> from 'common/api/core/Sku';
import * as BsibOrder<PERSON>pi from 'common/api/shop/BsibOrder';
import * as <PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/api/shop/SibOrder';
import ExchangeIcon from 'common/assets/icons/icon-exchange.svg?react';
import BarcodeInput from 'common/components/BarcodeInput';
import Button from 'common/components/Button';
import FlashStatistic from 'common/components/FlashStatistic';
import Modal from 'common/components/Modal';
import useSetting from 'common/hooks/useSetting';
import useSpeech from 'common/hooks/useSpeech';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { offWindowBeforeUnload, onWindowBeforeUnload } from 'common/utils';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import BarcodeAliasModal from 'common/components/BarcodeAliasModal';
import useSearchAlias from 'common/components/BarcodeAliasModal/hook/useSearchAlias';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { ITableBaseItem } from '../../data';
import DataViewTable from './DataViewTable';
import ErrorHandlerModal from './ErrorHandleModal';

message.config({
  maxCount: 3,
});

interface ITableDataItem extends ITableBaseItem {
  barcode: string;
  qty: number;
  operateQty: number;
  availableQty: number;
  actQty: number;
  alias?: any[];
  source?: string;
  brandName?: string;
}

interface IBarcodeInboundModalProps {
  sibOrderPreBoxId: string;
  sibOrderBoxId: string;
  modalProps?: ModalProps;
  onOk: () => void;
  sibOrderId?: string;
  sibOrderBoxCode: string;
  onOperateModeChange: () => void;
  sibOrderBoxStatus: string;
  localTag: boolean;
  innerRef: any;
}

interface INormalDataItem {
  boxCode: string;
  barcode: string;
  qty: number;
  operateMode: string;
}

interface IErrorDataItem {
  barcode: string;
  qty: number;
  operateQty: number;
  msg: string;
}

const BarcodeInboundModal: React.FC<IBarcodeInboundModalProps> = (props) => {
  const {
    innerRef,
    sibOrderPreBoxId,
    modalProps,
    onOk,
    sibOrderId,
    sibOrderBoxCode,
    onOperateModeChange,
    sibOrderBoxId,
    localTag,
    sibOrderBoxStatus,
  } = props;
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [normalData, setNormalData] = useState<INormalDataItem[]>([]);
  const [tableData, setTableData] = useState<ITableDataItem[]>([]);
  const [skuFetching, setSkuFetching] = useState(false);
  const [errorData, setErrorData] = useState<IErrorDataItem[]>([]);
  const [errorHandleModalVisible, setErrorHandleModalVisible] = useState<boolean>(false);
  const operateMode = 'SKU';

  const {
    barcodeAliasModalOpen,
    barcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  } = useSearchAlias();

  const isOperate = useRef(false);
  const normalQtyRef = useRef<any>();
  const errorQtyRef = useRef<any>();
  const barcodeInputRef = useRef<any>();
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const {
    ENABLE_BARCODE_SCAN_CUTOFF: enableBarcodeScanCutoff,
    BARCODE_CUTOFF_BEFORE_DIGITS: barcodeCutoffBeforeDigits,
    BARCODE_CUTOFF_AFTER_DIGITS: barcodeCutoffAfterDigits,
    SIB_BARCODE_PACKING_THRESHOLD: barcodePackingThreshold,
  } = useSetting([
    { code: 'ENABLE_BARCODE_SCAN_CUTOFF', valueType: 'BOOLEAN' },
    { code: 'BARCODE_CUTOFF_BEFORE_DIGITS', valueType: 'NUMBER' },
    { code: 'BARCODE_CUTOFF_AFTER_DIGITS', valueType: 'NUMBER' },
    { code: 'SIB_BARCODE_PACKING_THRESHOLD', valueType: 'NUMBER' },
  ]);

  const { playSound, stopSound } = useSpeech();

  const playAndNotify = useCallback(
    (text: string, type: 'success' | 'error' | 'warn' | 'info', val?: string) => {
      NoticeUtil[type](val ? `${text} ${val}` : text);
      playSound({ text });
    },
    [playSound],
  );

  const baseItem = (data?) => ({
    prodCode: data?.prodCode || '',
    colorCode: data?.colorCode || '',
    colorName: data?.colorName || '',
    sizeCode: data?.sizeCode || '',
    sizeName: data?.sizeName || '',
    skuName: data?.skuName || '',
    specCode: data?.specCode || '',
    specName: data?.specName || '',
  });

  useImperativeHandle(innerRef, () => ({
    getIsOperate: () => isOperate.current,
  }));

  const fetchPreBoxRfidsData = useCallback(async () => {
    const data: ITableDataItem[] = [];
    setLoading(true);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.PreBoxRfids({
          boxId: sibOrderPreBoxId,
          sibOrderId,
          enablePage: false,
        });
      } else {
        result = await SibOrderApi.PreBoxRfids({
          boxId: sibOrderPreBoxId,
          sibOrderId,
          enablePage: false,
        });
      }

      if (result.data.length > 0) {
        result.data.forEach((item) => {
          const dataItem = data.find((n) => n.barcode === item.barcode);
          if (dataItem) {
            dataItem.qty += item.qty;
          } else {
            data.push({
              barcode: item.barcode,
              qty: item.qty,
              availableQty: 0,
              operateQty: 0,
              actQty: 0,
              ...baseItem(item),
            });
          }
        });
      }
    } catch (e) {}
    setLoading(false);
    return data || [];
    // eslint-disable-next-line
  }, [sibOrderId, sibOrderPreBoxId]);

  const fetchBoxRfidsData = useCallback(
    async (data) => {
      const tableData: ITableDataItem[] = data;
      setLoading(true);
      try {
        const params: any = {
          sibOrderId,
          enablePage: false,
        };
        if (sibOrderBoxId) {
          params.sibBoxId = sibOrderBoxId;
        }
        let res;
        if (currentUser.mode === 'BINDING') {
          res = await BsibOrderApi.Rfids(params);
        } else {
          res = await SibOrderApi.Rfids(params);
        }

        if (res.data.length > 0) {
          const normalData: INormalDataItem[] = [];
          res.data.forEach((item) => {
            if (sibOrderPreBoxId && item.boxId !== sibOrderPreBoxId) {
              return;
            }
            const tableDataItem = tableData.find((n) => n.barcode === item.barcode);
            if (tableDataItem) {
              if (sibOrderBoxId) {
                tableDataItem.actQty = item.qty;
                tableDataItem.operateQty += item.qty;
              }
            } else {
              tableData.push({
                barcode: item.barcode,
                qty: 0,
                availableQty: 0,
                actQty: item.qty,
                operateQty: item.qty,
                ...baseItem(item),
              });
            }
          });
          tableData.forEach((item) => {
            if (item.operateQty > 0) {
              normalData.push({
                barcode: item.barcode,
                boxCode: sibOrderBoxCode,
                qty: item.operateQty,
                operateMode,
              });
            }
          });
          setNormalData(normalData);
        }
        let linesRes;
        const linesPayload = {
          sibOrderId,
          enablePage: false,
        };

        if (currentUser.mode === 'BINDING') {
          linesRes = await BsibOrderApi.Lines(linesPayload);
        } else {
          linesRes = await SibOrderApi.Lines(linesPayload);
        }

        tableData.forEach((item) => {
          const linesItem = linesRes.data.find((n) => n.barcode === item.barcode);
          const orderAvailableQty = linesItem.qty - linesItem.actQty;
          item.availableQty = item.qty - item.actQty;
          if (orderAvailableQty < item.availableQty) {
            item.availableQty = orderAvailableQty;
          }
        });
      } catch (e) {}
      setLoading(false);
      return tableData || [];
    },
    [currentUser.mode, sibOrderBoxCode, sibOrderBoxId, sibOrderId, sibOrderPreBoxId],
  );

  const fetchOrderLinesData = useCallback(async () => {
    let data: ITableDataItem[] = [];
    setLoading(true);
    try {
      let result;
      const payload = {
        sibOrderId,
        enablePage: false,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.Lines(payload);
      } else {
        result = await SibOrderApi.Lines(payload);
      }

      if (result.data.length > 0) {
        result.data.forEach((item) => {
          let actQty = 0;
          if (!sibOrderBoxId) {
            actQty = item.actQty;
          }
          data.push({
            barcode: item.barcode,
            qty: item.qty,
            availableQty: 0,
            actQty,
            operateQty: 0,
            ...baseItem(item),
          });
        });
      }
    } catch (e) {}
    data = data.filter((n) => n.qty !== 0);
    setLoading(false);
    return data || [];
  }, [currentUser.mode, sibOrderBoxId, sibOrderId]);

  const screenReaderDataByRoles = (inputBarcode, size, tableIndex) => {
    if (skuFetching) {
      const text = i18n.t('global.lastBarcodeProcessingTips');
      playAndNotify(text, 'warn');
      return;
    }

    // 当找到记录时，使用表格中的实际条码；否则使用输入的条码
    const barcode = tableIndex !== -1 ? tableData[tableIndex].barcode : inputBarcode;

    if (tableIndex !== -1 && tableData[tableIndex].rfidTag) {
      const text = i18n.t('global.prodOnlyForRFID');
      playAndNotify(text, 'warn');
      return;
    }
    const operateSize = size;
    const normalQty = normalData.find((n) => n.barcode === barcode)?.qty || 0;

    const pushNormalData = (count) => {
      const index = normalData.findIndex((n) => n.barcode === barcode);
      if (index !== -1) {
        normalData[index].qty = count;
        if (count === 0) {
          normalData.splice(index, 1);
        }
      } else if (count !== 0) {
        normalData.push({
          barcode,
          qty: count,
          boxCode: sibOrderBoxCode,
          operateMode,
        });
      }
      if (tableData[tableIndex].qty === 0 && count === 0) {
        tableData.splice(tableIndex, 1);
      } else {
        tableData.unshift(...tableData.splice(tableIndex, 1));
      }
      setNormalData(JSON.parse(JSON.stringify(normalData)));
      setTableData(JSON.parse(JSON.stringify(tableData)));
      const text = i18n.t('global.scanSuccess');
      playAndNotify(text, 'success');
      isOperate.current = true;
    };

    const addTableItem = async () => {
      const hide = message.loading(i18n.t('global.loading'), 0);
      setSkuFetching(true);
      try {
        const res: any = await SkuApi.FindByAlias({
          alias: [inputBarcode],
        });
        searchAliasByBarcode({
          barcode: inputBarcode,
          operateSize,
          searchData: res.data,
          callback(foundBarcode, operateSize, index) {
            if (index === -1) {
              const text = i18n.t('global.notFoundBarcode');
              playAndNotify(text, 'warn', `[${inputBarcode}]`);
            } else {
              const barcodeItem = res.data[index];
              if (barcodeItem.rfidTag) {
                const text = i18n.t('global.prodOnlyForRFID');
                playAndNotify(text, 'warn');
                setSkuFetching(false);
                hide();
                return;
              }
              // 使用 barcodeItem.barcode 作为实际条码
              const actualBarcode = barcodeItem.barcode;
              tableData.unshift({
                barcode: actualBarcode,
                qty: 0,
                availableQty: 0,
                actQty: 0,
                operateQty: operateSize,
                ...baseItem(barcodeItem),
                skuName: barcodeItem.name,
                rfidTag: barcodeItem.rfidTag,
                disturbTag: barcodeItem.disturbTag,
              });
              normalData.push({
                barcode: actualBarcode,
                qty: operateSize,
                boxCode: sibOrderBoxCode,
                operateMode,
              });
              setNormalData(JSON.parse(JSON.stringify(normalData)));
              setTableData(JSON.parse(JSON.stringify(tableData)));
              const text = i18n.t('global.scanSuccess');
              playAndNotify(text, 'success');
            }
          },
        });
      } catch (e) {}
      hide();
      setSkuFetching(false);
      isOperate.current = true;
    };

    if (operateSize > 0) {
      if (tableIndex !== -1) {
        if (!localTag) {
          if (sibOrderPreBoxId) {
            if (normalQty + operateSize > tableData[tableIndex].qty) {
              const text = i18n.t('global.readedQtyExcessOrderQty');
              playAndNotify(text, 'warn', `[${barcode}]`);
            } else {
              pushNormalData(normalQty + operateSize);
            }
          } else if (operateSize + tableData[tableIndex].operateQty > tableData[tableIndex].availableQty) {
            const text = i18n.t('global.readedQtyExcessRemainingQty');
            playAndNotify(text, 'warn', `[${barcode}]`);
          } else {
            pushNormalData(normalQty + operateSize);
          }
        } else {
          pushNormalData(normalQty + operateSize);
        }
      } else if (localTag) {
        addTableItem();
      } else {
        const text = i18n.t('global.barcodeNotInPreBoxRange');
        playAndNotify(text, 'warn', `[${barcode}]`);
      }
    } else if (tableIndex !== -1) {
      if (normalQty < Math.abs(operateSize)) {
        const text = i18n.t('global.deleteQtyExcess');
        playAndNotify(text, 'warn');
      } else {
        pushNormalData(normalQty + operateSize);
      }
    } else {
      const text = i18n.t('global.deleteQtyExcess');
      playAndNotify(text, 'warn');
    }
  };

  const saveBtnOnClick = async () => {
    try {
      setSaving(true);

      const payload = {
        data: normalData,
        sibOrderId,
      };
      if (currentUser.mode === 'BINDING') {
        await BsibOrderApi.Save(payload);
      } else {
        await SibOrderApi.Save(payload);
      }
      const text = i18n.t('global.saveSuccess');
      playAndNotify(text, 'success');
      if (onOk) onOk();
    } catch (err: any) {
      let responseData: any[] = [];
      if (err.response?.data?.detailMsg) {
        responseData = JSON.parse(err.response.data.detailMsg);
      }
      if (responseData.length > 0) {
        const text = i18n.t('global.pleaseHandleOverAndError');
        playAndNotify(text, 'warn');
        const responseErrorData: any[] = responseData.filter(
          (item) => item.type === 'CROSS_ERROR' || item.type === 'OTHER_ERROR',
        );
        const newErrorData: IErrorDataItem[] = [];
        responseErrorData.forEach((item) => {
          newErrorData.push({
            barcode: item.barcode,
            qty: item.errQty,
            operateQty: 0,
            msg: item.message,
          });
        });
        setErrorData(newErrorData);

        responseErrorData.forEach((item) => {
          const normalDataIndex = normalData.findIndex((n) => n.barcode === item.barcode);
          if (normalDataIndex !== -1) {
            normalData[normalDataIndex].qty -= item.errQty;
          }
          if (normalData[normalDataIndex].qty === 0) {
            normalData.splice(normalDataIndex, 1);
          }
        });
        setNormalData(JSON.parse(JSON.stringify(normalData)));
      }
    }
    setSaving(false);
  };

  const barcodeInputOnPressEnter = (barcode, operateSize) => {
    if (!operateSize) {
      const text = i18n.t('global.pleaseInputOperateQty');
      playAndNotify(text, 'warn');
      return;
    }
    if (operateSize === 0) {
      const text = i18n.t('global.operateQtyError');
      playAndNotify(text, 'warn');
      return;
    }

    if (barcode) {
      searchAliasByBarcode({ barcode, searchData: tableData, operateSize, callback: screenReaderDataByRoles });
    }
  };

  const resetBtnOnClick = () => {
    const text = i18n.t('global.confirmDiscard');
    playSound({ text });
    NoticeUtil.confirm({
      content: text,
      onOk: () => {
        setNormalData([]);
        stopSound();
        barcodeInputRef.current?.reset();
      },
    });
  };

  const dataViewTableOnDelete = (data) => {
    setNormalData(normalData.filter((n) => n.barcode !== data));
    setTableData(tableData.filter((n) => n.barcode !== data));
  };

  const dataViewTableOnReset = (data) => {
    const tableItem = tableData.find((n) => n.barcode === data);
    if (tableItem) {
      if (sibOrderBoxId) {
        tableItem.availableQty += tableItem.actQty;
      }
      tableItem.operateQty = 0;
    }
    setTableData(JSON.parse(JSON.stringify(tableData)));
    setNormalData(normalData.filter((n) => n.barcode !== data));
  };

  const fetchSkuInfoByBarcodeList = async (data) => {
    const tableData: ITableDataItem[] = data;
    if (tableData.length > 0) {
      const barcodeList = tableData.map((n) => n.barcode);
      try {
        const result: any = await SkuApi.FindByBarcodeList({
          barcodeList,
        });
        result.forEach((item) => {
          const tableItem = tableData.find((n) => n.barcode === item.barcode);
          if (tableItem) {
            tableItem.alias = item.alias;
            tableItem.source = item.source;
            tableItem.brandName = item.brandName;

            tableItem.disturbTag = item.disturbTag;
            tableItem.rfidTag = item.rfidTag;
          }
        });
      } catch (e) {}
    }
    return tableData;
  };

  const initOrderData = useCallback(async () => {
    let tableData: ITableDataItem[] = [];
    try {
      if (sibOrderPreBoxId) {
        tableData = await fetchPreBoxRfidsData();
      } else if (!localTag) {
        tableData = await fetchOrderLinesData();
      }
      if (!localTag || sibOrderBoxId) {
        tableData = await fetchBoxRfidsData(tableData);
      }
      tableData = await fetchSkuInfoByBarcodeList(tableData);
      setTableData(tableData);
    } catch (e) {}
  }, [sibOrderPreBoxId, localTag, sibOrderBoxId, fetchPreBoxRfidsData, fetchOrderLinesData, fetchBoxRfidsData]);

  const errorHandlerModalOnDelete = (data) => {
    data.forEach((item) => {
      if (item.operateQty !== 0) {
        const errorDataIndex = errorData.findIndex((n) => n.barcode === item.barcode);
        errorData[errorDataIndex].qty -= item.operateQty;
        if (errorData[errorDataIndex].qty === 0) {
          errorData.splice(errorDataIndex, 1);
        }
      }
    });
    setErrorData(JSON.parse(JSON.stringify(errorData)));
    setErrorHandleModalVisible(false);
  };

  useEffect(() => {
    if (normalData.length > 0) {
      normalQtyRef.current?.remind();
    }

    tableData.forEach((item) => {
      item.operateQty = normalData.find((n) => item.barcode === n.barcode)?.qty || 0;
    });
    setTableData(tableData.filter((n) => !(n.qty === 0 && n.operateQty === 0)));
    // eslint-disable-next-line
  }, [normalData]);

  useEffect(() => {
    if (errorData.length > 0) {
      errorQtyRef.current?.remind();
    }
  }, [errorData]);

  useEffect(() => {
    if (errorData.length > 0) {
      const text = i18n.t('global.detectedErrorMsg', { errorQty: errorData.length });
      playAndNotify(text, 'warn');
    }
    // eslint-disable-next-line
  }, [errorData, playAndNotify]);

  useEffect(() => {
    if (modalProps?.open === true) {
      initOrderData();
      onWindowBeforeUnload();
    } else {
      offWindowBeforeUnload();
      isOperate.current = false;
      setNormalData([]);
      setTableData([]);
      setLoading(false);
      setSaving(false);
      stopSound();
    }
  }, [initOrderData, modalProps?.open, stopSound]);

  const handleBarcodeAliasModalOnOk = (selectedRows) => {
    barcodeAliasModalOnOk({ selectedRows, searchData: tableData, callback: screenReaderDataByRoles });
  };

  const modalTitle = (
    <div className="flex h-6">
      <div className="flex items-center gap-x-2">
        <span>
          {i18n.t('global.barcodeInbound')}
          {sibOrderBoxCode ? ` [${sibOrderBoxCode}]` : ''}
        </span>
        {sibOrderBoxStatus === 'NEW' && (
          <Button type="ghost" icon={<ExchangeIcon className="fill-lead-slate" />} onClick={onOperateModeChange} />
        )}
      </div>
    </div>
  );

  return (
    <Modal
      fullScreen
      footer={false}
      title={modalTitle}
      centered
      maskClosable={false}
      destroyOnClose
      {...modalProps}
      onCancel={() => {
        const commonLogic = () => {
          const fakeMouseEvent: any = {};
          if (modalProps?.onCancel) {
            modalProps.onCancel(fakeMouseEvent);
          }
        };
        if (isOperate.current) {
          const text = i18n.t('global.confirmDiscard');
          playSound({ text });
          NoticeUtil.confirm({
            content: text,
            onOk: () => {
              commonLogic();
            },
          });
        } else {
          commonLogic();
        }
      }}
      className="operate-modal"
    >
      <Spin spinning={loading}>
        <div className="flex h-full flex-col gap-y-4">
          <DataViewTable
            className="flex-auto"
            leftTBar={
              <Space size="large">
                <BarcodeInput
                  range={barcodePackingThreshold ? [-barcodePackingThreshold, barcodePackingThreshold] : undefined}
                  innerRef={barcodeInputRef}
                  onPressEnter={barcodeInputOnPressEnter}
                  enableBarcodeScanCutoff={enableBarcodeScanCutoff}
                  barcodeCutoffBeforeDigits={barcodeCutoffBeforeDigits}
                  barcodeCutoffAfterDigits={barcodeCutoffAfterDigits}
                />
              </Space>
            }
            data={tableData}
            normalData={normalData}
            onDelete={dataViewTableOnDelete}
            onReset={dataViewTableOnReset}
            localTag={localTag}
          />
          <Row justify="space-between" align="bottom">
            <Col>
              <Space>
                <Button
                  type="success"
                  size="large"
                  loading={saving}
                  onClick={saveBtnOnClick}
                  disabled={!(normalData.length > 0)}
                >
                  {i18n.t('global.save')}
                </Button>
                {normalData.length > 0 && (
                  <Button danger size="large" onClick={resetBtnOnClick}>
                    {i18n.t('global.reset')}
                  </Button>
                )}
              </Space>
            </Col>
            <Col>
              <Space style={{ marginLeft: 16 }} size="large">
                <FlashStatistic
                  innerRef={normalQtyRef}
                  title={i18n.t('global.normal')}
                  value={normalData.reduce((prev, next) => prev + next.qty, 0)}
                  type="success"
                />
                {errorData.length > 0 && (
                  <FlashStatistic
                    type="error"
                    innerRef={errorQtyRef}
                    title={i18n.t('global.exceptQty')}
                    value={errorData.reduce((prev, next) => prev + next.qty, 0)}
                    onClick={() => {
                      setErrorHandleModalVisible(true);
                    }}
                  />
                )}
              </Space>
            </Col>
          </Row>
        </div>
      </Spin>
      <ErrorHandlerModal
        data={errorData}
        onDelete={errorHandlerModalOnDelete}
        modalProps={{
          onCancel: () => setErrorHandleModalVisible(false),
          visible: errorHandleModalVisible,
        }}
      />
      <BarcodeAliasModal
        open={barcodeAliasModalOpen}
        onCancel={barcodeAliasModalOnCancel}
        onOk={handleBarcodeAliasModalOnOk}
        data={barcodeAliasData}
        selectRowFiledId="barcode"
      />
    </Modal>
  );
};

export default BarcodeInboundModal;
