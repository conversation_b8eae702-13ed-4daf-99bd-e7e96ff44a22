import { Form, Input } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useContext, useEffect, useRef, useState } from 'react';
import * as FqcIssuesApi from 'common/api/factory/FqcIssues';
import * as BfqcIssuesApi from 'common/api/factory/BfqcIssues';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';

interface EditIssuesDrawerProps {
  visible: boolean;
  current: Partial<any> | undefined;
  onOk: () => void;
  onClose: () => void;
}

const EditIssuesDrawer: React.FC<EditIssuesDrawerProps> = (props) => {
  const { onOk, onClose, current, visible } = props;

  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const [permission] = usePermission('A:BASE:ISSUES');
  const issuesEditPermission = permission.codes.includes('ISSUESEDIT');
  const issuesDeletePermission = permission.codes.includes('ISSUESDELETE');

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const onSave = async () => {
    setLoading(true);
    try {
      if (!issuesEditPermission) return;

      const values = await form.validateFields();
      const payload = {
        ...values,
        id: current?.id,
      };

      if (currentUser.mode === 'BINDING') {
        await BfqcIssuesApi.Update(payload);
      } else {
        await FqcIssuesApi.Update(payload);
      }

      NoticeUtil.success();
      setLoading(false);
      if (onOk) onOk();
    } catch (e) {
      setLoading(false);
    }
  };

  const deleteBtnOnClick = async () => {
    if (!current) return;

    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current.code} - ${current.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BfqcIssuesApi.Del({ ids: [current.id] });
          } else {
            await FqcIssuesApi.Del({ ids: [current.id] });
          }
          if (onOk) onOk();
          NoticeUtil.success();
        } catch (err) {}
      },
    });
  };

  useEffect(() => {
    if (current && visible) {
      const values = {
        status: !current?.disabled,
        ...current,
      };
      form.setFieldsValue(values);
      nameInputRef.current?.focus();
    } else {
      form.resetFields();
    }
  }, [visible, current, form]);

  const onCancel = () => {
    if (onClose) onClose();
  };

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  return (
    <Drawer
      title={`${i18n.t('global.qualityCheckIssuesGroupDetails')}${current ? ` [${current.name}]` : ''}`}
      destroyOnClose
      open={visible}
      onClose={onCancel}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          recoverPermission={false}
          onApply={onSave}
          applyPermission={issuesEditPermission}
          deletePermission={issuesDeletePermission}
          onDelete={deleteBtnOnClick}
        />
      }
    >
      <Form form={form} layout="vertical" name="basic">
        <Form.Item label={i18n.t('global.code')} name="code" rules={canNotBeNullRules}>
          <Input ref={codeInputRef} maxLength={20} />
        </Form.Item>
        <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
          <Input maxLength={50} />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditIssuesDrawer;
