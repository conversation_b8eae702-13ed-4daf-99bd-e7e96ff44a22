/**
 * Create by codegen at 2022-07-13
 * 数据字典 - 新增
 */
import { Col, Form, Input, InputNumber, Row, Switch } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

interface AddDictDataDrawerProps {
  visible?: boolean;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

const AddDictDataDrawer: React.FC<AddDictDataDrawerProps> = (props) => {
  const { visible, confirmLoading, onOk, onCancel } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  useEffect(() => {
    let timerId: any = null;
    if (visible) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [visible]);

  return (
    <Drawer
      title={i18n.t('global.newDictData')}
      width={700}
      open={visible}
      okButtonProps={{
        loading: confirmLoading,
      }}
      onOk={modalOnOk}
      onClose={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
              <Input ref={inputRef} maxLength={30} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="name" label={i18n.t('global.name')} rules={canNotBeNullRules}>
              <Input maxLength={50} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="i18nName" label={i18n.t('global.internationalizedName')}>
              <Input maxLength={100} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="sort" label={i18n.t('global.sort')}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="remark" label={i18n.t('global.remark')}>
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.default')} name="defaultTag" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={i18n.t('global.status')} name="status" valuePropName="checked">
              <Switch />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default AddDictDataDrawer;
