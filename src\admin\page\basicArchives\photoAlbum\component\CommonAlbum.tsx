/**
 * 公共相册
 * */
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Space, Tabs } from 'antd';

import * as PhotoAlbumApi from 'common/api/core/PhotoAlbum';
import SimplePagination from 'common/components/SimplePagination';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useState } from 'react';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';

import AddCommonAlbumModal from './AddCommonAlbumModal';
import AlbumItem from './AlbumItem';
import EditAlbumModal from './EditAlbumModal';
import UploadPhoto from './UploadPhoto';

import styles from './shopAlbum.module.css';

const { TabPane } = Tabs;
const CommonAlbum = () => {
  const [tabsList, setTabsList] = useState<Record<string, any>[]>([]);
  const [tabsKey, setTabsKey] = useState<string>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [hasNext, setHasNext] = useState(false);
  const [photoAlbumData, setPhotoAlbumData] = useState<Record<string, any>[]>([]);
  const [current, setCurrent] = useState<Record<string, any>>();
  const [addCommonAlbumModalVisible, setAddCommonAlbumModalVisible] = useState(false);
  const [adding, setAdding] = useState(false);
  const [editAlbumModalVisible, setEditAlbumModalVisible] = useState(false);
  const [editIng, setEditIng] = useState(false);
  const [uploadPhotoVisible, setUploadPhotoVisible] = useState(false);

  const [permission] = usePermission('A:BASE:ALBUM:COMMON');
  const addPermission = permission.codes.includes('CREATE');
  const defaultPermission = permission.codes.includes('DEFAULT');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');
  const photoPermission = permission.codes.includes('PHOTO');
  const uploadPermission = permission.codes.includes('PHOTO:UPLOAD');
  const deletePhoPermission = permission.codes.includes('PHOTO:DELETE');

  const fetchTabs = useCallback(async () => {
    try {
      const res: any = await PhotoAlbumApi.ScenesList({});
      if (res.data.length > 0) {
        setTabsList(res.data);
        setTabsKey(res.data[0].code);
      }
    } catch (e) {}
  }, []);

  const fetchData = useCallback(async () => {
    const params: Record<string, any> = {
      firstFileUrlQuery: true,
      numQuery: true,
      enablePage: true,
      currentPage,
      pageSize,
      orderByField: 'default_tag',
      orderByMethod: 'DESCEND',
    };
    try {
      const res: any = await PhotoAlbumApi.List(params);
      setCurrentPage(Number(res.currentPage));
      setPageSize(res.pageSize);
      setHasNext(res.hasNext);
      setPhotoAlbumData(res.data);
    } catch (e) {}
  }, [currentPage, pageSize]);

  const tabsOnChange = (key) => {
    setTabsKey(key);
  };

  const paginationOnChange = (page, pageSize) => {
    setCurrentPage(page === 0 ? 1 : page);
    setPageSize(pageSize);
  };

  const addAlbumModalOnOk = async (record) => {
    setAdding(true);
    try {
      await PhotoAlbumApi.Create({
        ...record,
        disable: false,
      });
      setAddCommonAlbumModalVisible(false);
      NoticeUtil.success();
      fetchData();
    } catch (e) {}
    setAdding(false);
  };

  const defaultOnClick = async (record) => {
    const { disable, id, name, remark, sort } = record;
    try {
      await PhotoAlbumApi.Update({
        id,
        disable,
        name,
        remark,
        sort,
        defaultTag: true,
      });
      NoticeUtil.success();
      fetchData();
    } catch (e) {}
  };

  const editOnClick = (record) => {
    setCurrent(record);
    setEditAlbumModalVisible(true);
  };

  const photoOnClick = (record) => {
    setCurrent(record);
    setUploadPhotoVisible(true);
  };

  const editAlbumModalOnOk = async (record) => {
    setEditIng(true);
    try {
      await PhotoAlbumApi.Update({
        ...record,
        id: current?.id,
      });
      NoticeUtil.success();
      setEditAlbumModalVisible(false);
      fetchData();
    } catch (e) {}
    setEditIng(false);
  };

  const deleteOnClick = (record) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDeleteAlbum'),
      content: `${record.name}`,
      okType: 'danger',
      maskTransitionName: '',
      transitionName: '',
      onOk: async () => {
        try {
          await PhotoAlbumApi.Delete({
            id: record.id,
          });
          NoticeUtil.success();
          fetchData();
        } catch (e) {}
      },
    });
  };

  // 获取当前登录仓库相册
  useEffect(() => {
    if (tabsKey) {
      fetchData();
    }
    // eslint-disable-next-line
  }, [tabsKey, currentPage, pageSize]);

  // 获取tabs标签页数据
  useEffect(() => {
    fetchTabs();
    // eslint-disable-next-line
  }, []);

  // const showTotal: PaginationProps['showTotal'] = (total) => i18n.t('global.totalDataNum', { num: total });

  return (
    <>
      <div className={styles.content}>
        <Tabs defaultActiveKey="1" onChange={tabsOnChange}>
          {tabsList.map((item) => (
            <TabPane tab={item.name} key={item.code} />
          ))}
        </Tabs>
        <Space size={[20, 20]} wrap>
          {addPermission && (
            <Button className={styles.button} onClick={() => setAddCommonAlbumModalVisible(true)}>
              <FontAwesomeIcon icon={faPlus} /> <br />
              {i18n.t('global.addAlbum')}
            </Button>
          )}
          {photoAlbumData.map((item) => (
            <AlbumItem
              key={item.id}
              current={item}
              permission={{
                defaultPermission,
                editPermission,
                deletePermission,
                photoPermission,
              }}
              isConvert={false}
              defaultOnClick={defaultOnClick}
              editOnClick={editOnClick}
              photoOnClick={photoOnClick}
              deleteOnClick={deleteOnClick}
            />
          ))}
        </Space>
      </div>
      <div className={styles.sticky}>
        <SimplePagination
          currentPage={currentPage}
          defaultPageSize={10}
          pageSize={pageSize}
          hasNext={hasNext}
          onChange={paginationOnChange}
        />
      </div>
      <AddCommonAlbumModal
        current={{
          tabsKey,
        }}
        visible={addCommonAlbumModalVisible}
        confirmLoading={adding}
        onOk={addAlbumModalOnOk}
        onCancel={() => setAddCommonAlbumModalVisible(false)}
      />
      <EditAlbumModal
        editType="COMMON"
        current={current}
        visible={editAlbumModalVisible}
        confirmLoading={editIng}
        onOk={editAlbumModalOnOk}
        onCancel={() => setEditAlbumModalVisible(false)}
      />
      <UploadPhoto
        type="COMMON"
        current={current}
        visible={uploadPhotoVisible}
        permission={{
          uploadPermission,
          deletePhoPermission,
        }}
        onCancel={() => {
          setUploadPhotoVisible(false);
          fetchData();
        }}
      />
    </>
  );
};

export default CommonAlbum;
