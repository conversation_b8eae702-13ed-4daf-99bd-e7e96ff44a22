import { Descriptions, Statistic, Tabs } from 'antd';
import * as B<PERSON>b<PERSON><PERSON>r<PERSON><PERSON> from 'common/api/shop/BsobOrder';
import * as SobOrder<PERSON><PERSON> from 'common/api/shop/SobOrder';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import CloseCircleLineIcon from 'common/assets/icons/icon-close-circle-line.svg?react';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import Button from 'common/components/Button';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import RemarkEditModal from 'common/components/RemarkEditModal';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useMemo, useReducer, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';

import * as PrintOrderApi from 'common/api/core/PrintOrder';
import PrintModal from './components/PrintModal';
import { useCommonFn } from './common';
import BoxTable, { BoxTableInnerRef } from './components/BoxTable';
import LineTable, { LineTableInnerRef } from './components/LineTable';
import { DetailContext, initialState, reducer } from './DetailReducer';

const Detail: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { orderRecord, remarkModalOpen, logDrawerOpen, logs, printTaskAddModalOpen } = state;
  const params = useParams();
  const navigate = useNavigate();

  const [permission] = usePermission('A:S:OB');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');
  const resetPermission = permission.codes.includes('RESET');

  const [printTaskPermission] = usePermission('A:TAG:PT');
  const printPermission = printTaskPermission.codes.includes('CREATE');

  const { confirm, cancel, reset } = useCommonFn();
  const boxTableRef = useRef<BoxTableInnerRef>();
  const lineTableRef = useRef<LineTableInnerRef>();

  const setting = useSetting([{ code: 'RFID_RULES_DEPEND_ON_FIELD', valueType: 'STRING' }]);

  const { RFID_RULES_DEPEND_ON_FIELD: dependField } = setting;

  const fetchOrderData = useCallback(async () => {
    dispatch({ type: 'setLoading', payload: true });
    try {
      let rec: any;
      if (currentUser.mode === 'BINDING') {
        rec = await BsobOrderApi.Get({ id: params.id });
      } else {
        rec = await SobOrderApi.Get({ id: params.id });
      }
      rec.availableQty = rec.qty - rec.actQty;
      dispatch({ type: 'setOrderRecord', payload: rec });
    } catch (err) {}
    dispatch({ type: 'setLoading', payload: false });
    return {};
  }, [params.id, currentUser.mode]);

  const refresh = async () => {
    await fetchOrderData();
    boxTableRef.current?.load();
    lineTableRef.current?.load();
  };

  const confirmBtnOnClick = async () => {
    try {
      await confirm(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const cancelBtnOnClick = async () => {
    try {
      await cancel(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const resetBtnOnclick = async () => {
    try {
      await reset(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const remarkEditModalOnSubmit = async (values) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BsobOrderApi.Update({
          id: params.id,
          mode: orderRecord.mode,
          ...values,
        });
      } else {
        await SobOrderApi.Update({
          id: params.id,
          mode: orderRecord.mode,
          ...values,
        });
      }

      NoticeUtil.success();
      refresh();
    } catch {}
    dispatch({ type: 'setRemarkEditModalOpen', payload: false });
  };

  const fetchLogs = useCallback(async () => {
    try {
      let res: any;
      const payload = {
        enablePage: false,
        orderByField: 'created',
        orderByMethod: 'DESCEND',
        orderId: orderRecord.id,
      };
      if (currentUser.mode === 'BINDING') {
        res = await BsobOrderApi.Logs(payload);
      } else {
        res = await SobOrderApi.Logs(payload);
      }

      dispatch({ type: 'setLogs', payload: res.data });
    } catch (e) {}
  }, [currentUser.mode, orderRecord.id]);

  const logBtnOnClick = useCallback(() => {
    dispatch({ type: 'setLogDrawerOpen', payload: true });
    fetchLogs();
  }, [fetchLogs]);

  const onPrintModalSave = useCallback(
    async (value) => {
      try {
        const res: any = await PrintOrderApi.Generate(value);
        NoticeUtil.success();
        dispatch({ type: 'setPrintTaskAddModalOpen', payload: false });
        navigate(`/app/print-task/detail/${res.id}`);
      } catch (e) {}
    },
    [navigate],
  );

  const subTitle = useMemo(() => {
    return (
      <Tag
        color={
          {
            NEW: 'red',
            PROCESSING: 'blue',
            FINISHED: 'green',
            CANCELED: 'slate',
          }[orderRecord.status]
        }
      >
        {orderRecord.statusDesc}
      </Tag>
    );
  }, [orderRecord.status, orderRecord.statusDesc]);

  useEffect(() => {
    LocalStorageUtil.setItem('RFID_RULES_DEPEND_ON_FIELD', dependField);
  }, [dependField]);

  useEffect(() => {
    fetchOrderData();
    // eslint-disable-next-line
  }, [params.id]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <DetailContext.Provider value={detailContextValue}>
      <AppHeader
        title={orderRecord.code}
        subTitle={subTitle}
        toolbar={
          <div className="flex gap-x-2">
            <Button icon={<RefreshIcon className="fill-lead-dark" />} loading={state.loading} onClick={refresh} />
            <Button type="default" icon={<ListIcon className="fill-lead-dark" />} onClick={logBtnOnClick}>
              {i18n.t('global.log')}
            </Button>
            {/* 取消按钮是否可见：有权限且状态不是已完成和已取消 */}
            {cancelPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
              <Button type="danger" icon={<CloseCircleLineIcon className="fill-white" />} onClick={cancelBtnOnClick}>
                {i18n.t('global.cancel')}
              </Button>
            )}
            {/* 重置按钮是否可见：有权限且状态为处理中 */}
            {resetPermission && orderRecord.status === 'PROCESSING' && (
              <Button type="warning" icon={<ResetIcon className="fill-white" />} onClick={resetBtnOnclick}>
                {i18n.t('global.reset')}
              </Button>
            )}
            {confirmPermission && orderRecord.status === 'PROCESSING' && (
              <Button
                type="primary"
                icon={<DoubleCheckIcon className="fill-white" />}
                className="global-blue"
                onClick={confirmBtnOnClick}
              >
                {i18n.t('global.confirm')}
              </Button>
            )}
            {printPermission && !orderRecord.printOrderCode && (
              <Button
                type="primary"
                icon={<AddFillIcon className="fill-white" />}
                onClick={() => dispatch({ type: 'setPrintTaskAddModalOpen', payload: true })}
              >
                {i18n.t('global.createPrintTask')}
              </Button>
            )}
          </div>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <SendReceiveLayout
            className="flex-auto"
            left={
              <PartnerViewer
                partnerType="SHOP"
                partnerCode={orderRecord.partnerCode}
                partnerName={orderRecord.partnerName}
                warehouseCode={orderRecord.warehouseCode}
                warehouseName={orderRecord.warehouseName}
                label={i18n.t('global.from')}
              />
            }
            right={
              <PartnerViewer
                partnerType="WAREHOUSE"
                partnerCode={orderRecord.toPartnerCode}
                partnerName={orderRecord.toPartnerName}
                warehouseCode={orderRecord.toWarehouseCode}
                warehouseName={orderRecord.toWarehouseName}
                label={i18n.t('global.to')}
              />
            }
          />
          <div className="flex flex-initial items-center gap-x-6">
            {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
            <Statistic title={i18n.t('global.packedQty')} value={orderRecord.actQty} />
            <Statistic
              title={i18n.t('global.diffQty')}
              value={orderRecord.qty > 0 ? orderRecord.qty - orderRecord.actQty : 0}
            />
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {(orderRecord.sourceCode || orderRecord.fmOrderCode) && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>
                <AdditionCodeViewer codes={orderRecord.sourceCode} />
              </Descriptions.Item>
            )}
            {orderRecord.orderTypeName && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.orderTypeName}</Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>{orderRecord.created}</Descriptions.Item>
            )}
            {((!orderRecord.remark && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED') ||
              orderRecord.remark) && (
              <Descriptions.Item label={i18n.t('global.remark')} contentStyle={{ alignItems: 'center' }}>
                {orderRecord.remark}
                {modifyRemarkPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
                  <>
                    <Button
                      type="link"
                      size="small"
                      icon={<EditBoxLineIcon className="fill-lead-orange" />}
                      onClick={() => dispatch({ type: 'setRemarkEditModalOpen', payload: true })}
                      className="-my-px ml-2"
                    />
                    <RemarkEditModal
                      current={orderRecord}
                      onSubmit={remarkEditModalOnSubmit}
                      onCancel={() => dispatch({ type: 'setRemarkEditModalOpen', payload: false })}
                      open={remarkModalOpen}
                      title={i18n.t('global.modifyRemark')}
                    />
                  </>
                )}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          defaultActiveKey="0"
          size="small"
          items={[
            {
              key: '0',
              label: `${i18n.t('global.outboundBox')} (${orderRecord.boxCount || 0})`,
              children: <BoxTable innerRef={boxTableRef} onRefresh={refresh} />,
            },
            {
              key: '1',
              label: i18n.t('global.details'),
              children: <LineTable innerRef={lineTableRef} />,
            },
          ]}
        />
      </div>
      <PrintModal
        onSave={onPrintModalSave}
        onCancel={() => {
          dispatch({ type: 'setPrintTaskAddModalOpen', payload: false });
        }}
        visible={printTaskAddModalOpen}
      />
      <LogDrawer
        open={logDrawerOpen}
        orderId={orderRecord.id}
        onClose={() => dispatch({ type: 'setLogDrawerOpen', payload: false })}
        data={logs}
      />
    </DetailContext.Provider>
  );
};

export default Detail;
