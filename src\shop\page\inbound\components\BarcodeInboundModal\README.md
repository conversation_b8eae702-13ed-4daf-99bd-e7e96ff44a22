## 业务流程
单据类型区分本地单 / 非本地单
- 本地单：允许装箱系统内任何条码，包含任意数量
- 非本地单：系统外部来源单或者系统其他端产生的单据，通常会带有预装箱。

### 本地单装箱流程
输入条码/条码别名通过`SkuApi.FindByAlias`查找系统内的条码（也可通过这样判断条码是否在系统内），条码已经存在表格中则更新操作数，没存在的话就新增一条表格数据。

操作一列仅有删除一个操作，删除这个已输入的条码，包括清空已操作数

异常处理：保存后接口返回，通过 `detailMsg` 拿对应异常的条码存入异常表格弹窗里面的进行处理。

处理所有异常后才能保存

### 非本地单装箱流程
**按单装箱 / 按预装箱装箱**
非本地单会存在单据明细，输入条码/条码别名不再需要请求`SkuApi.FindByAlias`去查找系统，而是根据单据明细里面列表来判断条码是否在单据明细范围内（包括查找明细里面的别名）。

操作一列仅有重置一个操作，清空这个已输入的条码的操作数

异常处理：无（？）接口似乎没有返回的，但可以兼容处理

## 请求流程

### 本地单
**按单据装箱** / **按物理箱装箱，且是新箱** => 直接空明细，不需要再请求
**编辑箱** => 请求箱明细，回显相关数据
`promiseList = [箱明细数据]`

### 非本地单
**按单据装箱** => 请求单据明细数据
**按预装箱装箱** => 请求预装箱明细
**编辑箱** => 根据单据/预装箱类型请求不同明细 => 请求箱明细，回显相关数据
`promiseList = [单据明细/预装箱明细, 箱明细数据]`

## 扫描数据流程

### 本地单
扫描当前 tablelines 包括别名，searchAliasByBarcode

- index === -1，不在本地明细，通过 SkuApi.FindByAlias(条码可能)，再次通过 searchAliasBarcode 搜索


先判断是否已经扫过，扫过的话进入条码别名hook，然后设置其操作数
没有扫过，请求 fetchalias 接口来获取条码信息，并新增到表中