import { message, Typography } from 'antd';
import { apiBaseEventBus } from 'common/api/Base';
import ArrowRightSLineIcon from 'common/assets/icons/icon-arrow-right-s-line.svg?react';
import RedErrorIcon from 'common/assets/icons/icon-red-error.svg?react';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { throttle } from 'common/utils';
import i18n from 'common/utils/I18n';
import React, { ReactNode, useCallback, useContext, useEffect } from 'react';

import styles from 'common/app/Entry.module.css';

const isDevMode = !import.meta.env.PROD;

const { Text } = Typography;
export interface EntryProps {
  children?: React.ReactNode;
}
const Entry: React.FC<EntryProps> = (props) => {
  const { children } = props;
  const { state } = useContext<TGlobalContext>(GlobalContext);

  const showMessage = useCallback((description: ReactNode) => {
    return message.error({
      icon: <span />,
      content: description,
      duration: 3,
    });
  }, []);

  useEffect(() => {
    if (!state.config || !state.config.baseUrl) return;

    const showErrorMessage = throttle((description: ReactNode) => {
      showMessage(description);
    }, 500);

    const onResponseError = async (error: any) => {
      const { response } = error;
      if (
        // ERR_CANCELED 代表手动终止请求
        error.code !== 'ERR_CANCELED' &&
        error.config?.throwError !== false
      ) {
        if (!response || error.code === 'ERR_NETWORK') {
          showErrorMessage(
            <div className="flex items-center gap-1">
              <RedErrorIcon className="fill-lead-red text-base" />
              <span>{i18n.t('global.networkErrorMessage')}</span>
            </div>,
          );
        } else if (response && response.status !== undefined && response.status !== null) {
          let message: any = error.code;
          let detailMsg: any = '';
          let hasDetailMsg = false;
          let errorCode = '';
          const url = response?.config?.url || '';

          if (response.data) {
            // 处理responseType未blob情况返回json的情况
            if (response.config && response.config.responseType === 'blob') {
              try {
                const errorObjText = await response.data.text();
                response.data = JSON.parse(errorObjText);
              } catch (e) {}
            }

            if (response.data.message) {
              message = response.data.message;
            }
            if (response.data.msg) {
              message = response.data.msg;
            }
            hasDetailMsg = !!response.data.detailMsg;
            detailMsg = response.data.detailMsg;
            errorCode = response.data.errorCode;
          }

          switch (response.status) {
            case 503:
              message = i18n.t('global.serviceUnavailableForNow');
              break;
            case 403:
              message = i18n.t('global.noPermission');
              break;
            case 401:
              // message = i18n.t('global.identityExpiredMessage');
              apiBaseEventBus.emit('tokenExpired', message);
              break;
            default:
              break;
          }

          if (isDevMode) {
            message = (
              <>
                {message} <Text type="danger">{response.status}</Text>
              </>
            );
          }

          try {
            const detailMsgObj = JSON.parse(detailMsg);
            detailMsg = (
              <>
                {detailMsgObj.map((n) => {
                  const index = n.message.match(/^[0-9]/);
                  const msg = n.message.slice(n.message.indexOf(':') + 1, n.message.length);
                  return (
                    <span title={`${index ? `#${index}：` : ''}${msg}`}>
                      {`${index ? `#${index}` : ''}`} {msg}
                    </span>
                  );
                })}
              </>
            );
          } catch (e) {}

          // 以下错误编码不显式抛出错误
          // 考虑到工厂条码装箱时，需要显示提示错误，其他地方也提示这个错误影响不大及错误指向性明确，暂处理成显示提示错误
          // const excludeCodes = ['DATA_VALIDATE_EXCEPTION'];
          const excludeCodes: string[] = [];
          const excludeStatus = [401];
          if (!excludeCodes.includes(errorCode) && !excludeStatus.includes(response.status)) {
            if (hasDetailMsg) {
              showErrorMessage(
                <details className={styles.details}>
                  <summary className={`text-lead-dark ${styles.summary}`}>
                    <div className="flex items-center justify-center">
                      <RedErrorIcon className="fill-lead-red text-base" />
                      <span className="ml-1 mr-1">{message}</span>
                      <ArrowRightSLineIcon className={`fill-lead-dark ${styles.right_icon}`} />
                    </div>
                    <div>{isDevMode && <Text code>{url}</Text>}</div>
                  </summary>
                  <div className="flex flex-col">{detailMsg}</div>
                </details>,
              );
            } else {
              showErrorMessage(
                <>
                  <div className="flex items-center justify-center">
                    <RedErrorIcon className="fill-lead-red text-base" />
                    <span className="ml-1 mr-1">{message}</span>
                    <ArrowRightSLineIcon className={`fill-lead-dark ${styles.right_icon}`} />
                  </div>
                  <div>{isDevMode && <Text code>{url}</Text>}</div>
                </>,
              );
            }
          }
        }
      }
    };

    apiBaseEventBus.on('responseError', onResponseError);

    return () => {
      apiBaseEventBus.off('responseError', onResponseError);
    };
  }, [state.config, showMessage]);

  return <div>{children}</div>;
};

export default Entry;
