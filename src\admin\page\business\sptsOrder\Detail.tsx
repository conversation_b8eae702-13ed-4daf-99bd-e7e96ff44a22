import { Badge, Card, Col, Descriptions, Popover, Row, Space, Statistic, Tabs, Tooltip } from 'antd';
import * as SpTsOrderApi from 'common/api/sample/SpTsOrder';
import ArrowGoBackLineIcon from 'common/assets/icons/icon-arrow-go-back-line.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import FailCircleIcon from 'common/assets/icons/icon-fail-circle.svg?react';
import UploadCloudIcon from 'common/assets/icons/icon-upload-cloud.svg?react';
import SettingLineIcon from 'common/assets/icons/icon-settings-line.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import StartIcon from 'common/assets/icons/icon-start.svg?react';
import Button from 'common/components/Button';
import ConfirmButton from 'common/components/Button/Confirm';
import ExportButton from 'common/components/Button/Export';
import RefreshButton from 'common/components/Button/Refresh';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer from 'common/components/PartnerViewer';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import RemarkEditModal from 'common/components/RemarkEditModal';
import SearchInput from 'common/components/SearchInput';
import Tag from 'common/components/Tag';
import useDidUpdateEffect from 'common/hooks/useDidUpdateEffect';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import { saveAs } from 'file-saver';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { exportExcel } from 'common/utils/Excel';
import SetRangeModal from './components/SetRangeModal';
import SnapImportModal from './components/SnapImportModal';
import { useCommonFn } from './common';

import GroupTypeRadioButton from './components/GroupTypeRadioButton';
import LineTable from './components/LineTable';
import { ITsOrderItem } from './data';

type LineGroupType = 'BY_PROD' | 'BY_SKU' | 'BY_TAG';

const defaultGroupType = 'BY_PROD';

const WtsOrderDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [tsOrderRecord, setTsOrderRecord] = useState<ITsOrderItem | { [key: string]: any }>({});
  const params: { id?: string } = useParams();

  const [exportLineBtnLoading, setExportLineBtnLoading] = useState(false);
  const [showByTag, setShowByTag] = useState(true);
  const [linePaneProdCode, setLinePaneProdCode] = useState<string>();
  const [linePaneGroupType, setLinePaneGroupType] = useState<LineGroupType>(defaultGroupType);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [logs, setLogs] = useState<any[]>([]);
  const [currencyData, setCurrencyData] = useState<any>();
  const [showUniqueCode, setShowUniqueCode] = useState<boolean>(false);
  const [remarkEditModalVisible, setRemarkEditModalVisible] = useState<boolean>(false);
  const [snapImportModalVisible, setSnapImportModalVisible] = useState(false);
  const [tsOrderRangeList, setTsOrderRangeList] = useState<Record<string, any>[]>([]);
  const [rangeModalCurrent, setRangeModalCurrent] = useState<any>({});
  const [rangeType, setRangeType] = useState<string>();
  const [setRangeModalVisible, setSetRangeModalVisible] = useState(false);
  const [setRangeModalLoading, setSetRangeModalLoading] = useState<boolean>(false);
  const [diffLinePaneGroupType, setDiffLinePaneGroupType] = useState<LineGroupType>(defaultGroupType);
  const [diffLinePaneProdCode, setDiffLinePaneProdCode] = useState<string>();
  const [exportDiffLineBtnLoading, setExportDiffLineBtnLoading] = useState(false);

  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const { reset, start, confirm, cancel } = useCommonFn();

  const [permission] = usePermission('A:P:TSO');
  const startPermission = permission.codes.includes('START');
  const resetPermission = permission.codes.includes('RESET');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const cancelPermission = permission.codes.includes('CANCEL');
  const exportLinePermission = permission.codes.includes('EXPORT_LINE');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');
  const importSnapPermission = permission.codes.includes('IMPORT_SNAP');
  const setRangePermission = permission.codes.includes('SET_RANGE');
  const realTimePermission = permission.codes.includes('REALTIME');

  const { CURRENCY: currency } = useSetting([{ code: 'CURRENCY', valueType: 'STRING' }]);
  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }

  const linesTableRefFns = useRef<IPowerTableInnerRef>();
  const diffLinesTableRefFns = useRef<IPowerTableInnerRef>();
  const errorLinesTableRefFns = useRef<IPowerTableInnerRef>();

  const getTsOrderRange = useCallback(async () => {
    if (!params || !params.id) return;
    try {
      const tsOrderRange: any = await SpTsOrderApi.GetRange({
        id: params.id,
      });
      setTsOrderRangeList(tsOrderRange);
    } catch (e) {}
    // eslint-disable-next-line
  }, [params]);

  const fetchTsOrder = useCallback(async () => {
    if (!params || !params.id) return;
    setLoading(true);
    try {
      const rec: any = await SpTsOrderApi.Get({
        id: params.id,
      });
      rec.amt = Number(rec.amt);
      rec.snapAmt = Number(rec.snapAmt);

      // 抽盘单时获取盘点范围
      if (rec.mode === 'RANDOM') {
        getTsOrderRange();
      }
      setRangeType(rec.rangeType);

      setTsOrderRecord(rec);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
    // eslint-disable-next-line
  }, [params, getTsOrderRange]);

  const fetchErrorLines = (powerTableParams) => {
    if (!params || !params.id) {
      return Promise.resolve({ data: [] });
    }

    const payload = {
      id: params.id,
      prodCode: linePaneProdCode,
      ...powerTableParams,
    };

    return SpTsOrderApi.ErrorLines(payload);
  };

  const exportErrorLineBtnOnClick = async () => {
    const dataColumns: any = [
      {
        display: 'EPC',
        dataIndex: 'epc',
        type: 'STRING',
      },
      {
        display: i18n.t('global.barcode'),
        dataIndex: 'barcode',
        type: 'STRING',
      },
      {
        display: i18n.t('global.productCode'),
        dataIndex: 'prodCode',
        type: 'STRING',
      },
      {
        display: i18n.t('global.brand'),
        dataIndex: 'brandName',
        type: 'STRING',
      },
      {
        display: i18n.t('global.name'),
        dataIndex: 'skuName',
        type: 'STRING',
      },
      {
        display: i18n.t('global.color'),
        dataIndex: 'colorName',
        type: 'STRING',
      },
      {
        display: i18n.t('global.size'),
        dataIndex: 'sizeName',
        type: 'STRING',
      },
      {
        display: i18n.t('global.specs'),
        dataIndex: 'specName',
        type: 'STRING',
      },
      {
        display: i18n.t('global.count'),
        dataIndex: 'qty',
        type: 'STRING',
      },
      {
        display: i18n.t('global.reason'),
        dataIndex: 'message',
        type: 'STRING',
      },
    ];
    const errorLineDataResult = await fetchErrorLines({
      enablePage: false,
    });
    const errorLineData = errorLineDataResult.data ?? [];
    exportExcel(dataColumns, errorLineData, i18n.t('global.tsOrderErrorLine'));
  };

  const fetchLines = (powerTableParams) => {
    if (!params || !params.id) {
      return Promise.resolve({ data: [] });
    }
    const payload = {
      id: params.id,
      smCode: linePaneProdCode,
      ...powerTableParams,
    };
    switch (linePaneGroupType) {
      case 'BY_PROD':
        return SpTsOrderApi.SampleLines(payload);
      case 'BY_SKU':
        return SpTsOrderApi.Lines(payload);
      case 'BY_TAG':
        return SpTsOrderApi.RfidLines({
          orderId: params.id,
          smCode: linePaneProdCode,
          ...powerTableParams,
        });
      default:
        break;
    }

    return Promise.resolve({ data: [] });
  };

  const fetchLogs = useCallback(async () => {
    try {
      const res: any = await SpTsOrderApi.Logs({
        enablePage: false,
        orderByField: 'created',
        orderByMethod: 'DESCEND',
        orderId: tsOrderRecord.id,
      });
      setLogs(res.data);
    } catch (e) {}
    // eslint-disable-next-line
  }, [tsOrderRecord.id]);

  const refresh = useCallback(() => {
    // 1. 获取盘点单单头
    fetchTsOrder();
    // 2. 获取盘点单明细
    linesTableRefFns.current?.load();
    // eslint-disable-next-line
  }, [fetchTsOrder, linePaneProdCode, linePaneGroupType]);

  const timeRef = useRef();

  const cancelButtonOnClick = async () => {
    try {
      await cancel(tsOrderRecord.id, tsOrderRecord.code);
      refresh();
    } catch (e) {}
  };
  const resetButtonOnClick = async () => {
    try {
      await reset(tsOrderRecord.id, tsOrderRecord.code);
      refresh();
    } catch (e) {}
  };
  const startButtonOnClick = async () => {
    try {
      await start(tsOrderRecord.id, tsOrderRecord.code);
      refresh();
    } catch (e) {}
  };
  const confirmButtonOnClick = async () => {
    try {
      await confirm(tsOrderRecord.id, tsOrderRecord.code);
      refresh();
    } catch (e) {}
  };
  const modifyRemark = () => {
    setRemarkEditModalVisible(true);
  };
  const remarkOnCancel = () => {
    setRemarkEditModalVisible(false);
  };
  const remarkEditModalOnSubmit = async (values) => {
    try {
      await SpTsOrderApi.Update({
        id: params.id,
        ...values,
      });

      NoticeUtil.success();
      refresh();
    } catch {}
    setRemarkEditModalVisible(false);
  };

  useEffect(() => {
    if (logModalVisible) {
      fetchLogs();
    }
  }, [fetchLogs, logModalVisible]);

  useDidUpdateEffect(() => {
    linesTableRefFns.current?.load();
    // eslint-disable-next-line
  }, [linePaneGroupType]);

  useEffect(
    () => () => {
      clearInterval(timeRef.current);
    },
    [],
  );

  useEffect(() => {
    setShowUniqueCode(LocalStorageUtil.getItem('WTS_ORDER_SHOW_UNICODE'));
  }, []);

  useEffect(() => {
    refresh();
    // eslint-disable-next-line
  }, []);

  const currencyPrefix = (
    <Tooltip title={currencyData && currencyData.code} placement="leftTop">
      {currencyData && currencyData.symbol}
    </Tooltip>
  );

  const fetchDiffLines = (powerTableParams) => {
    if (!params || !params.id) {
      return Promise.resolve({ data: [] });
    }
    const payload = {
      id: params.id,
      onlyDiff: true,
      prodCode: diffLinePaneProdCode,
      ...powerTableParams,
    };
    if (currentUser.mode === 'BINDING') {
      switch (diffLinePaneGroupType) {
        case 'BY_PROD':
          return SpTsOrderApi.SampleLines(payload);
        case 'BY_SKU':
          return SpTsOrderApi.Lines(payload);
        default:
          break;
      }
    } else {
      switch (diffLinePaneGroupType) {
        case 'BY_PROD':
          return SpTsOrderApi.SampleLines(payload);
        case 'BY_SKU':
          return SpTsOrderApi.Lines(payload);
        default:
          break;
      }
    }

    return Promise.resolve({ data: [] });
  };

  const exportLineBtnOnClick = async (byProd = false, onlyDiff = false) => {
    if (onlyDiff) {
      setExportDiffLineBtnLoading(true);
    } else {
      setExportLineBtnLoading(true);
    }
    const payload = {
      id: params.id,
      enablePage: false,
      onlyDiff,
    };
    try {
      let resp: any;
      const requestConf = {
        timeout: 600000, // 10分钟
      };
      if (byProd) {
        resp = await SpTsOrderApi.ExportSampleLines(payload, requestConf);
      } else {
        resp = await SpTsOrderApi.ExportOrderLines(payload, requestConf);
      }

      saveAs(
        resp,
        `${onlyDiff ? i18n.t('global.tsOrderDiffLine') : i18n.t('global.tsOrderLine')}(${
          byProd ? i18n.t('global.byPriceTotal') : i18n.t('global.byBarcodeTotal')
        }) - ${tsOrderRecord.code}.xlsx`,
      );
    } catch (e) {}
    if (onlyDiff) {
      setExportDiffLineBtnLoading(false);
    } else {
      setExportLineBtnLoading(false);
    }
  };

  const importButtonOnClick = () => {
    setSnapImportModalVisible(true);
  };

  const realTimeButtonOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmGetRealTimeInventory'),
      content: tsOrderRecord.code || '',
      onOk: async () => {
        try {
          await SpTsOrderApi.InventoryCountImport({
            orderId: tsOrderRecord.id,
          });

          NoticeUtil.success();
          refresh();
        } catch (e) {}
      },
    });
  };

  const snapImportModalOnCancel = () => {
    setSnapImportModalVisible(false);
  };

  const setRangeButtonOnClick = async () => {
    const hide = NoticeUtil.loading();
    try {
      const tsOrderRange: any = await SpTsOrderApi.GetRange({
        id: params.id,
      });
      setRangeModalCurrent([
        ...tsOrderRange.map((n) => ({
          ...n,
          propType: rangeType,
        })),
      ]);
      setSetRangeModalVisible(true);
    } catch (e) {}
    hide();
  };

  const tsOrderSetRangeModalOnSubmit = async (value) => {
    try {
      setSetRangeModalLoading(true);
      await SpTsOrderApi.SetRange(
        {
          propIds: value.map((n) => n.propId),
          rangeType: value[0].propType,
          orderId: params.id,
        },
        {
          timeout: 600000, // 10分钟
        },
      );

      NoticeUtil.success();
      refresh();
      setSetRangeModalLoading(false);
      setSetRangeModalVisible(false);
    } catch (e) {
      setSetRangeModalLoading(false);
    }
  };

  const tsOrderSetRangeModalOnCancel = () => {
    setSetRangeModalVisible(false);
  };

  const items = [
    {
      label: i18n.t('global.line'),
      key: 'LINE',
      children: (
        <>
          <Row justify="space-between" style={{ marginBottom: 16 }}>
            <Col>
              <Space>
                <GroupTypeRadioButton
                  defaultValue={defaultGroupType}
                  onChange={(e) => setLinePaneGroupType(e.target.value)}
                  showByTag={showByTag}
                />
                <SearchInput
                  placeholder={i18n.t('global.searchByProd')}
                  onChange={(e) => setLinePaneProdCode(e.target.value)}
                  onSearch={() => {
                    linesTableRefFns.current?.load();
                  }}
                  onPressEnter={() => {
                    linesTableRefFns.current?.load();
                  }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                {/* {linePaneGroupType === 'BY_TAG' && ( */}
                {/*  <Checkbox */}
                {/*    defaultChecked={showUniqueCode} */}
                {/*    onChange={(e) => { */}
                {/*      setShowUniqueCode(e.target.checked); */}
                {/*      LocalStorageUtil.setItem('WTS_ORDER_SHOW_UNICODE', e.target.checked); */}
                {/*    }} */}
                {/*  > */}
                {/*    {i18n.t('global.showUnicode')} */}
                {/*  </Checkbox> */}
                {/* )} */}
                {linePaneGroupType !== 'BY_TAG' && (
                  <ExportButton
                    disabled={!exportLinePermission}
                    loading={exportLineBtnLoading}
                    onClick={() => exportLineBtnOnClick(linePaneGroupType === 'BY_PROD')}
                  />
                )}
                <RefreshButton onClick={() => linesTableRefFns.current?.load()} />
              </Space>
            </Col>
          </Row>
          <LineTable
            innerRef={linesTableRefFns}
            lineType={linePaneGroupType}
            request={fetchLines}
            showUniqueCode={showUniqueCode}
          />
        </>
      ),
    },
    {
      label: i18n.t('global.diff'),
      key: 'DIFF',
      children: (
        <>
          <Row justify="space-between" style={{ marginBottom: 16 }}>
            <Col>
              <Space>
                <GroupTypeRadioButton
                  defaultValue={defaultGroupType}
                  showByTag={showByTag}
                  onChange={(e) => setDiffLinePaneGroupType(e.target.value)}
                />
                <SearchInput
                  placeholder={i18n.t('global.searchByProd')}
                  onChange={(e) => setDiffLinePaneProdCode(e.target.value)}
                  onSearch={() => {
                    diffLinesTableRefFns.current?.load();
                  }}
                  onPressEnter={() => {
                    diffLinesTableRefFns.current?.load();
                  }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <ExportButton
                  disabled={!exportLinePermission}
                  loading={exportDiffLineBtnLoading}
                  onClick={() => exportLineBtnOnClick(diffLinePaneGroupType === 'BY_PROD', true)}
                />
                <RefreshButton onClick={() => diffLinesTableRefFns.current?.load()} />
              </Space>
            </Col>
          </Row>
          <LineTable
            innerRef={diffLinesTableRefFns}
            lineType={diffLinePaneGroupType}
            request={fetchDiffLines}
            showUniqueCode={showUniqueCode}
          />
        </>
      ),
    },
    {
      label: i18n.t('global.error'),
      key: 'ERROR',
      children: (
        <>
          <Space style={{ marginBottom: 16, float: 'right' }}>
            <ExportButton disabled={!exportLinePermission} onClick={() => exportErrorLineBtnOnClick()} />
            <RefreshButton onClick={() => errorLinesTableRefFns.current?.load()} />
          </Space>
          <LineTable
            innerRef={errorLinesTableRefFns}
            lineType="BY_ERROR"
            request={fetchErrorLines}
            showUniqueCode={showUniqueCode}
          />
        </>
      ),
    },
  ];

  const linesCard = (
    <Card bodyStyle={{ paddingTop: 8, paddingBottom: 0 }}>
      <Tabs
        animated={false}
        defaultActiveKey="LINE"
        onChange={(val) => {
          if (val === 'LINE') {
            setShowByTag(true);
          } else {
            setShowByTag(false);
          }
        }}
        items={items}
      />
    </Card>
  );

  const RangeModalContent = useMemo(
    () => (
      <SetRangeModal
        confirmLoading={setRangeModalLoading}
        current={rangeModalCurrent}
        visible={setRangeModalVisible}
        onSubmit={tsOrderSetRangeModalOnSubmit}
        onCancel={tsOrderSetRangeModalOnCancel}
      />
    ),
    // eslint-disable-next-line
    [rangeModalCurrent, setRangeModalVisible],
  );

  const popTableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'propCode',
      key: 'propCode',
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'propName',
      key: 'propName',
    },
  ];

  const PopContent = (
    <div
      style={{
        width: 400,
      }}
    >
      <PowerTable
        initialized
        rowKey="id"
        columns={popTableColumns}
        defaultPageSize={10}
        refreshBtnVisible={false}
        pagination={false}
        autoLoad
        tableProps={{
          dataSource: tsOrderRangeList,
          sticky: true,
          size: 'small',
        }}
      />
    </div>
  );

  const cancelButtonVisible =
    (tsOrderRecord.status === 'NEW' || tsOrderRecord.status === 'PROCESSING') && cancelPermission;
  const startButtonVisible = tsOrderRecord.status === 'NEW' && startPermission;
  const confirmButtonVisible = tsOrderRecord.status === 'PROCESSING' && confirmPermission;
  const resetButtonVisible = tsOrderRecord.status === 'PROCESSING' && resetPermission;
  const importButtonVisible = tsOrderRecord.status === 'NEW' && importSnapPermission;
  const setRangeButtonVisible = tsOrderRecord.status === 'NEW' && tsOrderRecord.mode === 'RANDOM' && setRangePermission;
  const realTimeButtonVisible =
    (tsOrderRecord.status === 'NEW' || tsOrderRecord.status === 'PROCESSING') && realTimePermission;

  return (
    <div>
      <AppHeader
        title={loading ? i18n.t('global.loading') : tsOrderRecord.code}
        subTitle={
          loading ? null : (
            <Tag
              color={
                {
                  NEW: 'red',
                  PROCESSING: 'blue',
                  FINISHED: 'green',
                  CANCELED: 'slate',
                }[tsOrderRecord.status]
              }
            >
              {tsOrderRecord.statusDesc}
            </Tag>
          )
        }
        toolbar={
          <Space>
            <RefreshButton onClick={refresh} />
            <Button
              type="default"
              icon={<ListIcon className="fill-lead-dark" />}
              onClick={() => setLogModalVisible(true)}
            >
              {i18n.t('global.log')}
            </Button>
            {importButtonVisible && (
              <Tooltip title={i18n.t('global.importSnap')}>
                <Button
                  icon={<UploadCloudIcon className="fill-lead-dark" />}
                  type="default"
                  onClick={importButtonOnClick}
                />
              </Tooltip>
            )}
            {realTimeButtonVisible && (
              <Tooltip title={i18n.t('global.getRealTimeInventory')}>
                <Button
                  icon={<UploadCloudIcon className="fill-lead-dark" />}
                  type="default"
                  onClick={realTimeButtonOnClick}
                />
              </Tooltip>
            )}
            {setRangeButtonVisible && (
              <Tooltip title={i18n.t('global.setStockRange')}>
                <Button
                  type="default"
                  icon={<SettingLineIcon className="fill-lead-dark" />}
                  onClick={setRangeButtonOnClick}
                />
              </Tooltip>
            )}
            {cancelButtonVisible && (
              <Button type="danger" icon={<FailCircleIcon className="fill-white" />} onClick={cancelButtonOnClick}>
                {i18n.t('global.cancel')}
              </Button>
            )}
            {resetButtonVisible && (
              <Button type="warning" icon={<ArrowGoBackLineIcon className="fill-white" />} onClick={resetButtonOnClick}>
                {i18n.t('global.reset')}
              </Button>
            )}
            {startButtonVisible && (
              <Button type="success" icon={<StartIcon className="fill-white" />} onClick={startButtonOnClick}>
                {i18n.t('global.startTs')}
              </Button>
            )}
            {confirmButtonVisible && <ConfirmButton onClick={confirmButtonOnClick} />}
          </Space>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <div className="flex-auto">
            <PartnerViewer
              partnerType="WAREHOUSE"
              label={i18n.t('global.warehouse')}
              partnerCode={tsOrderRecord.partnerCode}
              partnerName={tsOrderRecord.partnerName}
              warehouseCode={tsOrderRecord.warehouseCode}
              warehouseName={tsOrderRecord.warehouseName}
            />
          </div>
          <div className="flex flex-initial items-center gap-x-6">
            <Statistic title={i18n.t('global.mode')} value={tsOrderRecord.modeDesc} className="uppercase" />
            <Statistic title={i18n.t('global.status')} value={tsOrderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {tsOrderRecord.sourceCode && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>{tsOrderRecord.sourceCode}</Descriptions.Item>
            )}
            {typeof tsOrderRecord.qty === 'number' && (
              <Descriptions.Item label={i18n.t('global.stockCount')}>{tsOrderRecord.qty || 0}</Descriptions.Item>
            )}
            {typeof tsOrderRecord.amt === 'number' && (
              <Descriptions.Item label={i18n.t('global.stockCost')}>
                <span className="mr-1">{currencyPrefix}</span>
                {tsOrderRecord.amt || 0}
              </Descriptions.Item>
            )}
            <Descriptions.Item label={i18n.t('global.startTime')}>
              {moment(tsOrderRecord.startTime).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            {tsOrderRecord.mode === 'RANDOM' && (
              <Descriptions.Item label={i18n.t('global.range')} style={{ cursor: 'pointer' }}>
                <Popover
                  content={PopContent}
                  title={i18n.t('global.lineRange')}
                  trigger="hover"
                  arrowPointAtCenter
                  placement="topLeft"
                >
                  <div>
                    {{
                      BRAND: i18n.t('global.brand'),
                      PRI_CATEGORY: i18n.t('global.priCategory'),
                      SUB_CATEGORY: i18n.t('global.subCategory'),
                      YEAR: i18n.t('global.year'),
                      SERIES: i18n.t('global.series'),
                      SAMPLE: i18n.t('product'),
                    }[tsOrderRecord.rangeType] || tsOrderRecord.rangeTypeDesc}
                    <Badge count={tsOrderRangeList.length} offset={[4, -4]} />
                  </div>
                </Popover>
              </Descriptions.Item>
            )}

            <Descriptions.Item label={i18n.t('global.remark')}>
              <span className="flex items-center">
                {tsOrderRecord.remark}
                {modifyRemarkPermission &&
                  tsOrderRecord.status !== 'FINISHED' &&
                  tsOrderRecord.status !== 'CANCELED' && (
                    <>
                      <Button
                        type="link"
                        size="small"
                        icon={<EditBoxLineIcon className="fill-lead-orange" />}
                        onClick={() => modifyRemark()}
                      />
                      <RemarkEditModal
                        current={tsOrderRecord}
                        onSubmit={remarkEditModalOnSubmit}
                        onCancel={remarkOnCancel}
                        open={remarkEditModalVisible}
                        title={i18n.t('global.modifyRemark')}
                      />
                    </>
                  )}
              </span>
            </Descriptions.Item>
          </Descriptions>
        </div>
      </div>
      <div style={{ paddingTop: 16 }}>
        {/* {summaryCard} */}
        {linesCard}
      </div>
      {RangeModalContent}
      <SnapImportModal
        wTsOrderId={params ? params.id : undefined}
        onOk={() => refresh()}
        modalProps={{
          open: snapImportModalVisible,
          onCancel: snapImportModalOnCancel,
        }}
      />
      <LogDrawer
        open={logModalVisible}
        orderId={tsOrderRecord.id}
        onClose={() => setLogModalVisible(false)}
        data={logs}
      />
    </div>
  );
};

export default WtsOrderDetail;
