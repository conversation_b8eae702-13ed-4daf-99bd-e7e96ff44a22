import { ModalProps } from 'antd/es/modal';
import * as SampleTagApi from 'common/api/sample/SampleRfid';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useState } from 'react';

interface ImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onFinish: () => void;
}

const ImportModal: React.FC<ImportModalProps> = (props) => {
  const { modalProps, onGoBack, onFinish } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();

  const template: TemplateItem[] = [
    {
      dataIndex: 'smEpc',
      display: 'EPC',
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'smTid',
      display: 'TID',
      type: 'STRING',
    },
    {
      dataIndex: 'smBarcode',
      display: i18n.t('global.barcode'),
      type: 'STRING',
    },
    {
      dataIndex: 'partnerCode',
      display: i18n.t('global.warehouseCode'),
      type: 'STRING',
    },
    {
      dataIndex: 'partnerType',
      display: i18n.t('global.warehouseType'),
      type: 'STRING',
      remark: i18n.t('global.partnerTypeTips'),
    },
  ];

  const onImport = async (data) => {
    setProgressStatus('active');
    try {
      setProgressPercent(0);
      await SampleTagApi.Imports(
        {
          data,
        },
        {
          throwError: false,
          // @ts-ignore
          timeout: window.globalConfig.sampleImportTimeout.value,
          onUploadProgress: (progressEvent: any) => {
            const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
            setProgressPercent(percentCompleted);
          },
        },
      );
      onFinish();
      setProgressPercent(100);
      setProgressStatus('success');
    } catch (e) {
      setProgressStatus('exception');
      throw e;
    }
  };

  return (
    <Modal width={960} title={i18n.t('global.import')} footer={false} destroyOnClose {...modalProps}>
      <Importer
        moduleName={i18n.t('global.tag')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
