import { Card, Empty, Input, Spin } from 'antd';
import * as <PERSON><PERSON><PERSON> from 'common/api/core/Permission';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import { getMenuName } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useState } from 'react';

import PermissionTree from './PermissionTree';

const { Search } = Input;

export interface IPermissionPanelPropsRef {
  recover: () => void;
}
interface IPermissionPanelProps {
  roleId: string | undefined;
  client: string | undefined;
  onCheckChange?: (subClient: string, rootCode: string, allSelectedKeys: any[]) => void;
  innerRef: (ref: IPermissionPanelPropsRef) => void;
}

/**
 * 根据扁平的列表获取所有末尾子叶节点编码
 * @param selectedSourceData
 * @param rootCode
 */
const getKeysByRootCode: (subClient: string, selectedSourceData: any[], rootCode: string) => any[] = (
  subClient,
  selectedSourceData,
  rootCode,
) => {
  let result: any[] = [];
  const loopChild: (parentCode: string) => void = (parentCode) => {
    const child: any[] = selectedSourceData.filter((n) => n.parentCode === parentCode && n.subClient === subClient);
    child.forEach((n) => {
      loopChild(n.code);
    });
    if (child.length === 0 && parentCode !== rootCode) {
      result.push(parentCode);
    }
  };
  loopChild(rootCode);
  if (result.length === 0 && selectedSourceData.some((n) => n.code === rootCode && n.subClient === subClient)) {
    result = [rootCode];
  }
  return result;
};

/**
 * 根据扁平的列表生成树结构数据
 */
const generateTreeData: (allSourceData: any[], selectedSourceData: any[]) => any[] = (
  allSourceData,
  selectedSourceData,
) => {
  const result: any[] = [];
  allSourceData.forEach((item) => {
    if (item.parentCode) return;
    result.push({
      ...item,
      key: item.code,
      code: item.code,
      title: getMenuName(item.name, item.i18nName),
      subClient: item.subClient,
    });
  });

  const getChildren: (parentCode: string, subClient: string) => any[] = (parentCode: string, subClient: string) => {
    const child: any[] = [];
    allSourceData.forEach((item) => {
      if (item.parentCode === parentCode && item.subClient === subClient) {
        child.push({
          ...item,
          key: item.code,
          code: item.code,
          title: getMenuName(item.name, item.i18nName),
        });
      }
    });
    if (child.length > 0) {
      child.forEach((n) => {
        n.children = getChildren(n.key, subClient);
      });
    }
    return child;
  };
  result.forEach((item) => {
    item.checkedKeys = getKeysByRootCode(item.subClient, selectedSourceData, item.key);
    item.children = getChildren(item.key, item.subClient);
  });
  return result;
};

/**
 * 已选中父级，但是其子集一个未选中,移除父级
 * */
const getRoleData: (allList: Record<string, any>[], roleList: Record<string, any>[]) => void = (allList, roleList) => {
  // @ts-ignore
  const selectKeys: any[] = [...new Set(roleList.map((n) => n.code))];
  allList.forEach((item) => {
    if (!selectKeys.includes(item.code)) return;
    // 有权限，筛选对应子集
    const filterData = allList.filter((n) => n.parentCode === item.code);
    if (filterData.length === 0) return;
    // 有子集，判断是否有一个子集有权限
    if (!filterData.some((item) => selectKeys.includes(item.code))) {
      // 没有一个子集有权限，去除父级, 这里使用splice修改原数组，删除不了
      // eslint-disable-next-line
      for (let i = roleList.length - 1; i >= 0; i--) {
        if (roleList[i].code === item.code) roleList.splice(i, 1);
      }
      (function loop(currentItem) {
        // 1. 获取当前这一项的父级数据
        // 2. 获取该父级的所有子级数据, 判断是否有子集数据，没有，不处理
        // 3. 判断子集是否存在已勾选
        // 4. 如果存在，不处理
        // 5. 否则从已选数据中去除该节点
        // @ts-ignore
        const roleKeys: any[] = [...new Set(roleList.map((n) => n.code))];
        const parent = allList.find((item) => item.code === currentItem.parentCode);
        if (!parent) return;
        const allChildren = allList.filter((item) => item.parentCode === parent.code);
        if (allChildren.length === 0) return;
        const childrenHasChecked = allChildren.some((child) => roleKeys.includes(child.code));
        if (childrenHasChecked) return;
        // eslint-disable-next-line
        for (let i = roleList.length - 1; i >= 0; i--) {
          if (roleList[i].code === parent.code) {
            roleList.splice(i, 1);
            loop(parent);
          }
        }
      })(item);
    }
  });
};

const PermissionPanel: React.FC<IPermissionPanelProps> = (props) => {
  const { roleId, client, onCheckChange, innerRef } = props;
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [treeDataGroupByRoot, setTreeDataGroupByRoot] = useState<any[]>([]);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser, services } = state;

  const fetchData = useCallback(async () => {
    if (!roleId || !client) return;
    setLoading(true);
    const servicesCodes = services.map((n) => {
      if (n.serCode.startsWith('B')) {
        return n.serCode.slice(1);
      }
      return n.serCode;
    });
    try {
      // 1. 获取所有菜单并装配好要渲染的数据
      const allPermissionResp: any = await RoleApi.List({
        enablePage: false,
        client,
        mode: currentUser.mode === 'BINDING' ? 'BIND' : 'WRITE',
      });
      // 2. 获取当前角色的所有已勾选菜单，并装配至勾选的数据里
      const rolePermissionResp: any = await RoleApi.RolePermissionList({
        enablePage: false,
        roleId,
        client,
      });
      // 3.根据获取的已启用的服务进行筛选
      const allPermissionData = allPermissionResp.data.filter((item) => servicesCodes.includes(item.module));
      // 4.检测已勾选菜单的子集菜单是否勾选一个，一个未勾选的话，把其父级菜单从已勾选参数中移除
      getRoleData(allPermissionData, rolePermissionResp.data);

      const newTreeDataGroupByRoot = generateTreeData(allPermissionData, rolePermissionResp.data);
      setTreeDataGroupByRoot(newTreeDataGroupByRoot);
    } catch (e) {}
    setLoading(false);
  }, [client, roleId, currentUser, services]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line
  }, [roleId, client]);

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      fetchData();
    },
  }));

  const onChange = (e) => {
    const { value } = e.target;
    setSearchValue(value);
  };

  return (
    <div className="permission-content">
      <Spin spinning={loading}>
        {treeDataGroupByRoot.length > 0 && (
          <>
            <Search
              className="search-input"
              autoFocus
              style={{ marginBottom: 16 }}
              placeholder={i18n.t('global.searchTips')}
              onChange={onChange}
            />
            <div className="menu-content">
              {treeDataGroupByRoot.map((mItem, index) => {
                const key = `${mItem}-${index}`;
                return (
                  <Card className="menu-card" title={`${index + 1}. ${mItem.title}`} key={key}>
                    <PermissionTree
                      searchValue={searchValue}
                      defaultCheckedKeys={mItem.checkedKeys}
                      // treeData={mItem.children}
                      treeData={[mItem]}
                      checkChange={(allSelectedKeys) => {
                        if (onCheckChange) {
                          onCheckChange(mItem.subClient, mItem.code, allSelectedKeys);
                        }
                      }}
                    />
                  </Card>
                );
              })}
            </div>
          </>
        )}
        {treeDataGroupByRoot.length === 0 && (
          <div style={{ padding: '48px 0' }}>
            <Empty />
          </div>
        )}
      </Spin>
    </div>
  );
};

export default PermissionPanel;
