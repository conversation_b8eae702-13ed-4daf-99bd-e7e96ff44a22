import { ModalProps } from 'antd/es/modal';
import * as FqcIssuesApi from 'common/api/factory/FqcIssues';
import * as BfqcIssuesApi from 'common/api/factory/BfqcIssues';
import Importer, { ImporterProps, TemplateItem } from 'common/components/Importer';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useContext, useState } from 'react';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';

interface IImportModalProps {
  modalProps?: ModalProps;
  onGoBack: () => void;
  onOk: () => void;
}

const ImportModal: React.FC<IImportModalProps> = (props) => {
  const { modalProps, onGoBack, onOk } = props;
  const [progressStatus, setProgressStatus] = useState<ImporterProps['progressStatus']>('normal');
  const [progressPercent, setProgressPercent] = useState<ImporterProps['progressPercent']>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const template: TemplateItem[] = [
    {
      dataIndex: 'groupCode',
      display: i18n.t('global.issuesGroupCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'groupDisabled',
      display: i18n.t('global.issuesGroupDisabled'),
      type: 'BOOLEAN',
      required: true,
    },
    {
      dataIndex: 'groupName',
      display: i18n.t('global.issuesGroupName'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'groupRemark',
      display: i18n.t('global.issuesGroupRemark'),
      type: 'STRING',
    },
    {
      dataIndex: 'code',
      display: i18n.t('global.issuesCode'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'name',
      display: i18n.t('global.issuesName'),
      type: 'STRING',
      required: true,
    },
    {
      dataIndex: 'remark',
      display: i18n.t('global.issuesRemark'),
      type: 'STRING',
    },
    {
      dataIndex: 'sort',
      display: i18n.t('global.issuesGroupSort'),
      type: 'NUMBER',
    },
  ];

  const onImport = async (data) => {
    setConfirmLoading(true);
    setProgressStatus('active');
    try {
      setProgressPercent(0);

      if (currentUser.mode === 'BINDING') {
        await BfqcIssuesApi.Import(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      } else {
        await FqcIssuesApi.Import(
          {
            data,
          },
          {
            throwError: false,
            timeout: 300000,
            onUploadProgress: (progressEvent: any) => {
              const percentCompleted = Math.floor((progressEvent.loaded * 100) / progressEvent.total);
              setProgressPercent(percentCompleted);
            },
          },
        );
      }

      if (onOk) onOk();
      setProgressPercent(100);
      setProgressStatus('success');
      setConfirmLoading(false);
    } catch (e: any) {
      setProgressStatus('exception');
      setConfirmLoading(false);
      throw e;
    }
  };

  return (
    <Modal
      width={960}
      title={i18n.t('global.ImportQualityCheckIssues')}
      confirmLoading={confirmLoading}
      footer={false}
      destroyOnClose
      {...modalProps}
    >
      <Importer
        moduleName={i18n.t('global.qualityCheckIssues')}
        template={template}
        onImport={onImport}
        onGoBack={onGoBack}
        progressPercent={progressPercent}
        progressStatus={progressStatus}
      />
    </Modal>
  );
};

export default ImportModal;
