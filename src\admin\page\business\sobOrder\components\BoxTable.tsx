import { Input } from 'antd';
import * as BsobOrder<PERSON><PERSON> from 'common/api/shop/BsobOrder';
import * as Sob<PERSON>rder<PERSON><PERSON> from 'common/api/shop/SobOrder';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import BoxSpecToolTip from 'common/components/BoxSpecToolTip';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import BoxStatusSelect from 'common/components/Select/BoxStatusSelect';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  onRefresh?: () => void;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId, localTag } = orderRecord;
  const { onRefresh, innerRef } = props;
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);
  const [currentBox, setCurrentBox] = useState<Record<string, any>>({});

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:S:OB');
  const boxResetPermission = permission.codes.includes('BOX_RESET');
  const boxDeletePermission = permission.codes.includes('DELETE_BOX');

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);
  const { SHOW_RFID_EXT_FLAG: showRfidExt } = useSetting([{ code: 'SHOW_RFID_EXT_FLAG', valueType: 'BOOLEAN' }]);
  const { SO_SAVE_BOX_ENABLE_BOX_SPEC: enableBoxSpec } = useSetting([
    { code: 'SO_SAVE_BOX_ENABLE_BOX_SPEC', valueType: 'BOOLEAN' },
  ]);

  const fetchData = useCallback(
    async (params) => {
      const payload: any = {
        ...params,
        sobOrderId: orderRecord.id,
      };
      let rec;
      if (currentUser.mode === 'BINDING') {
        rec = await BsobOrderApi.Boxes(payload);
      } else if (currentUser.mode === 'WRITE') {
        rec = await SobOrderApi.Boxes(payload);
      }
      return rec;
    },
    [currentUser.mode, orderRecord.id],
  );

  const _onRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const resetBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmResetBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BsobOrderApi.BoxReset({
              boxCode: record.boxCode,
              sobOrderId: orderId,
            });
          } else {
            await SobOrderApi.BoxReset({
              boxCode: record.boxCode,
              sobOrderId: orderId,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const deleteBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDeleteBox'),
      content: record.code,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BsobOrderApi.BoxDelete({
              id: record.id,
            });
          } else {
            await SobOrderApi.BoxDelete({
              id: record.id,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const openDetail = async (record) => {
    setCurrentBox(record);
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewDrawerLoading(true);
    try {
      let result;
      const payload = {
        sobBoxId: record.id,
        sobOrderId: orderId,
        enablePage: false,
        showRfidExtFlag: showRfidExt,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsobOrderApi.Rfids(payload);
      } else {
        result = await SobOrderApi.Rfids(payload);
      }

      // setCurrentBoxDetailRfidData(result.data.filter((n) => n.epc !== null));
      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewDrawerLoading(false);
  };

  // const abnormalSkuListRequest = useCallback(
  //   (params: Record<string, any>) => {
  //     params.sobOrderId = orderId;
  //     params.sobBoxId = currentBoxId;
  //     let result;
  //     if (currentUser.mode === 'BINDING') {
  //       result = BsobOrderApi.AbnormalLines(params);
  //     } else {
  //       result = SobOrderApi.AbnormalLines(params);
  //     }
  //     return result;
  //   },
  //   // eslint-disable-next-line
  //   [orderId, currentBoxId],
  // );

  // const abnormalEpcListRequest = useCallback(
  //   (params: Record<string, any>) => {
  //     params.sobOrderId = orderId;
  //     params.sobBoxId = currentBoxId;
  //     let result;
  //     if (currentUser.mode === 'BINDING') {
  //       result = BsobOrderApi.AbnormalTags(params);
  //     } else {
  //       result = SobOrderApi.AbnormalTags(params);
  //     }
  //     return result;
  //   },
  //   // eslint-disable-next-line
  //   [orderId, currentBoxId],
  // );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  let columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      sorter: true,
      ellipsis: true,
      minWidth: 180,
      auto: true,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      dataIndex: 'operateMode',
      valueType: 'codeName',
      codeDataIndex: 'operateMode',
      nameDataIndex: 'operateModeDesc',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.packedQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  if (enableBoxSpec) {
    columns.splice(3, 0, {
      title: i18n.t('global.boxSpec'),
      width: 200,
      render: (text, record) => <BoxSpecToolTip current={record} />,
    });
  }

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: <BoxStatusSelect placeholder={i18n.t('global.status')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    valueType: 'action',
    fixed: 'right',
    align: 'center',
    actionConfig: [],
  };

  if (boxResetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        record.wholeTag ||
        (orderRecord.status !== 'NEW' && orderRecord.status !== 'PROCESSING') ||
        record.status !== 'UPLOADED',
      onClick: resetBtnOnClick,
    });
  }

  if (boxDeletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        record.status === 'FINISHED' || orderRecord.status === 'FINISHED' || orderRecord.status === 'CANCELED',
      onClick: deleteBtnOnClick,
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  if (localTag) {
    columns = columns.filter((n) => n.dataIndex !== 'qty');
  }

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={20}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        pagination
        request={fetchData}
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        quickSearchPanelFieldsWrap
        refreshBtnVisible
      />
      <BoxDetailsPreviewDrawer
        title={
          <div className="flex gap-x-2">
            {i18n.t('global.boxLine')} [{currentBox.boxCode}]
            <div className="flex">
              {currentBox.boxSpecCode && (
                <>
                  [<BoxSpecToolTip current={currentBox} />]
                </>
              )}
            </div>
          </div>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxTable;
