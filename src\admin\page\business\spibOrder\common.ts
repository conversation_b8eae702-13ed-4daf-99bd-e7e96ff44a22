import * as SpibOrderApi from 'common/api/sample/SpibOrder';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback } from 'react';

export const useCommonFn = () => {
  const confirm = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          onOk: async () => {
            try {
              await SpibOrderApi.Confirm({
                id: orderId,
              });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [],
  );

  // const fastReceipt = useCallback(
  //   (orderId: string, orderCode?: string): Promise<any> =>
  //     new Promise((res, rej) => {
  //       NoticeUtil.confirm({
  //         title: i18n.t('global.confirmFastReceipt'),
  //         content: orderCode || '',
  //         onOk: async () => {
  //           try {
  //               await SpibOrderApi.FastReceipt({ id: orderId });
  //
  //             NoticeUtil.success();
  //             res(true);
  //           } catch (e) {
  //             rej(e);
  //           }
  //         },
  //       });
  //     }),
  //   // eslint-disable-next-line
  //   [],
  // );

  const cancel = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              await SpibOrderApi.Cancel({ id: orderId });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  const reset = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmResetOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              await SpibOrderApi.Reset({ id: orderId });

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  return { confirm, cancel, reset };
};
