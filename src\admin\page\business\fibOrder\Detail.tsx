import { Button, Card, Descriptions, Space, Statistic, Tabs } from 'antd';
import * as BfibOrderApi from 'common/api/factory/BfibOrder';
import * as FibOrderApi from 'common/api/factory/FibOrder';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import RefreshButton from 'common/components/Button/Refresh';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer, { SendReceiveLayout } from 'common/components/PartnerViewer';
import { IPowerTableInnerRef } from 'common/components/PowerTable';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import BoxRfidsTable from './components/BoxRfidsTable';
import LineRfidsTable from './components/LineRfidsTable';

const Detail: React.FC = () => {
  const params: any = useParams();
  const [loading, setLoading] = useState(false);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;

  const [orderRecord, setOrderRecord] = useState<{
    [key: string]: any;
  }>({});
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [logs, setLogs] = useState<any[]>([]);

  const boxRfidsTableRef = useRef<IPowerTableInnerRef>();
  const lineRfidsTableRef = useRef<IPowerTableInnerRef>();

  const fetchOrderData = async () => {
    setLoading(true);
    try {
      let rec;
      if (currentUser.mode === 'BINDING') {
        rec = await BfibOrderApi.Get({
          id: params.id,
        });
      } else {
        rec = await FibOrderApi.Get({
          id: params.id,
        });
      }
      setOrderRecord(rec);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!params || !params.id) return;
    fetchOrderData();
    boxRfidsTableRef.current?.load();
    // eslint-disable-next-line
  }, [params]);

  const refresh = () => {
    fetchOrderData();
    boxRfidsTableRef.current?.load();
    lineRfidsTableRef.current?.load();
  };

  const fetchLogs = async () => {
    try {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = await BfibOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      } else {
        res = await FibOrderApi.Logs({
          enablePage: false,
          orderByField: 'created',
          orderByMethod: 'DESCEND',
          orderId: orderRecord.id,
        });
      }
      setLogs(res.data);
    } catch (e) {}
  };
  useEffect(() => {
    if (logModalVisible) {
      fetchLogs();
    }
    // eslint-disable-next-line
  }, [logModalVisible]);

  return (
    <Card bordered={false} bodyStyle={{ paddingTop: 0 }}>
      <AppHeader
        title={loading ? i18n.t('global.loading') : orderRecord.code}
        toolbar={
          <Space>
            <RefreshButton size="middle" onClick={refresh} />
            <Button
              type="default"
              icon={<ListIcon className="fill-lead-dark" />}
              onClick={() => setLogModalVisible(true)}
            >
              {i18n.t('global.log')}
            </Button>
          </Space>
        }
      />

      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <SendReceiveLayout
            className="flex-auto"
            left={
              <PartnerViewer
                label={i18n.t('global.delivery')}
                partnerType={orderRecord.fromPartnerType}
                partnerCode={orderRecord.fromPartnerCode}
                partnerName={orderRecord.fromPartnerName}
                warehouseCode={orderRecord.fromWarehouseCode}
                warehouseName={orderRecord.fromWarehouseName}
              />
            }
            right={
              <PartnerViewer
                label={i18n.t('global.receiver')}
                partnerType="FACTORY"
                partnerCode={orderRecord.partnerCode}
                partnerName={orderRecord.partnerName}
                warehouseCode={orderRecord.warehouseCode}
                warehouseName={orderRecord.warehouseName}
              />
            }
          />
          <div className="flex flex-initial items-center gap-x-6">
            {!orderRecord.localTag && <Statistic title={i18n.t('global.count')} value={orderRecord.qty} />}
            <Statistic title={i18n.t('global.inboundQty')} value={orderRecord.actQty} />
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {orderRecord.prodCode && (
              <Descriptions.Item label={i18n.t('global.productCode')}>{orderRecord.prodCode}</Descriptions.Item>
            )}
            {orderRecord.sourceCode && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>{orderRecord.sourceCode}</Descriptions.Item>
            )}
            {orderRecord.fmOrderCode && (
              <Descriptions.Item label={i18n.t('global.fmOrderCode')}>{orderRecord.fmOrderCode}</Descriptions.Item>
            )}
            {orderRecord.typeDesc && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.typeDesc}</Descriptions.Item>
            )}
            {orderRecord.amt && (
              <Descriptions.Item label={i18n.t('global.orderAmount')}>{orderRecord.amt}</Descriptions.Item>
            )}
            {orderRecord.actAmt && (
              <Descriptions.Item label={i18n.t('global.realAmount')}>{orderRecord.actAmt}</Descriptions.Item>
            )}
            {orderRecord.typeDesc && (
              <Descriptions.Item label={i18n.t('global.type')}>{orderRecord.typeDesc}</Descriptions.Item>
            )}
            {orderRecord.modified && (
              <Descriptions.Item label={i18n.t('global.modified')}>
                {moment(orderRecord.modified).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>
                {moment(orderRecord.created).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {orderRecord.remark && (
              <Descriptions.Item label={i18n.t('global.remark')}>{orderRecord.remark}</Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          size="small"
          defaultActiveKey="BOX"
          items={[
            {
              label: (
                <>
                  {i18n.t('global.inboundBox')} ({orderRecord.boxCount})
                </>
              ),
              key: 'BOX',
              children: (
                <BoxRfidsTable powerTableRef={boxRfidsTableRef} orderId={params && params.id ? params.id : undefined} />
              ),
            },
            {
              label: i18n.t('global.line'),
              key: 'RFIDS',
              children: (
                <LineRfidsTable
                  orderId={params && params.id ? params.id : undefined}
                  powerTableRef={lineRfidsTableRef}
                />
              ),
            },
          ]}
        />
      </div>
      <LogDrawer
        open={logModalVisible}
        orderId={orderRecord.id}
        onClose={() => setLogModalVisible(false)}
        data={logs}
      />
    </Card>
  );
};

export default Detail;
