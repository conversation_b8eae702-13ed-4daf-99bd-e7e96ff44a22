import { CheckCircleOutlined, CloseCircleOutlined, InboxOutlined } from '@ant-design/icons';
import { Col, Collapse, List, message, Progress, Radio, Row, Typography, Upload } from 'antd';
import { ModalProps } from 'antd/es/modal';
import * as SkuImgApi from 'common/api/sample/SampleSkuImg';
import * as FileApi from 'common/api/file/Img';
import ArrowDownSLineIcon from 'common/assets/icons/icon-arrow-down-s-line.svg?react';
import ArrowRightSLineIcon from 'common/assets/icons/icon-arrow-right-s-line.svg?react';
import InformationFillIcon from 'common/assets/icons/icon-information-fill.svg?react';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import * as MathUtils from 'common/utils/Math';
import React, { useEffect, useRef, useState } from 'react';

const { Dragger } = Upload;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface IImportImgModalProps {
  modalProps: ModalProps;
  onFinish: () => void;
  handleSaveImg?: (source: any) => void;
}

interface IImgDataListProps {
  index: number;
  smCode: string;
  smColorCode?: string;
  sortId: number;
  defaultTag: boolean;
  source: string;
}

interface IUploadImgListProps {
  name: string;
  status: 'saving' | 'success' | 'error';
  source: 'string';
  msg?: 'string';
}

const ImportImgModal: React.FC<IImportImgModalProps> = (props) => {
  const { modalProps, onFinish, handleSaveImg } = props;
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [fileCount, setFileCount] = useState<number>(0);
  const [fileUploadCount, setFileUploadCount] = useState<number>(0);
  const [uploadImgList, setUploadImgList] = useState<IUploadImgListProps[]>([]);
  const [status, setStatus] = useState<string>('all');

  const imgDataList = useRef<IImgDataListProps[]>([]);

  const onOk = async () => {
    setConfirmLoading(true);
    try {
      await SkuImgApi.Imports({ data: imgDataList.current }, false);
      message.success(i18n.t('global.success'));
      if (onFinish) {
        onFinish();
      }
    } catch (e: any) {
      JSON.parse(e.response.data.detailMsg).forEach((item: any) => {
        const errorIndex = Number(item.message.split(':')[0]);
        const errorSource = imgDataList.current.find((n) => n.index === errorIndex)?.source;
        const uploadImgListIndex = uploadImgList.findIndex((n) => n.source === errorSource);
        if (uploadImgListIndex !== -1) {
          uploadImgList[uploadImgListIndex].status = 'error';
          uploadImgList[uploadImgListIndex].msg = item.message;
        }
        uploadImgList.unshift(...uploadImgList.splice(uploadImgListIndex, 1));
        imgDataList.current = imgDataList.current.filter((n) => n.index !== errorIndex);
      });
      setUploadImgList(JSON.parse(JSON.stringify(uploadImgList)));
    }
    setConfirmLoading(false);
  };

  // const backBtnOnClick = () => {
  //   setFileCount(0);
  //   setFileUploadCount(0);
  //   setUploadImgList([]);
  //   setStatus('all');
  //   imgDataList.current = [];
  // };

  const saveImg = async (option) => {
    setConfirmLoading(true);
    const formData = new FormData();
    formData.append('file', option.file);
    const fileName = option.file.name.replace(/\b(.png|.jpeg|.jpg|.bmp)\b/g, '');
    const fileDataItem = fileName.split('_');
    const uploadImgItem: any = uploadImgList.find((n) => n.name === option.file.name);
    try {
      const res: any = await FileApi.Upload(formData);
      if (handleSaveImg) {
        handleSaveImg(res);
      }
      if (fileDataItem.length === 2) {
        imgDataList.current.push({
          index: imgDataList.current.length,
          smCode: fileDataItem[0],
          sortId: Number(fileDataItem[1]),
          defaultTag: Number(fileDataItem[1]) === 0,
          source: res,
        });
      } else if (fileDataItem.length === 3) {
        imgDataList.current.push({
          index: imgDataList.current.length,
          smCode: fileDataItem[0],
          smColorCode: fileDataItem[1],
          sortId: Number(fileDataItem[2]),
          defaultTag: Number(fileDataItem[2]) === 0,
          source: res,
        });
      }
      if (uploadImgItem) {
        if (fileDataItem.length === 2 || fileDataItem.length === 3) {
          uploadImgItem.status = 'success';
          uploadImgItem.source = res;
        } else {
          uploadImgItem.status = 'error';
          uploadImgItem.source = res;
          uploadImgItem.msg = i18n.t('fileNameError');
        }
      }
      setFileUploadCount(imgDataList.current.length);
    } catch (e) {
      if (uploadImgItem) {
        uploadImgItem.status = 'error';
      }
    }
    if (!uploadImgList.some((n) => n.status === 'saving')) {
      onOk();
    }
    setUploadImgList(JSON.parse(JSON.stringify(uploadImgList)));
    setConfirmLoading(false);
  };

  const radioOnChange = (e) => {
    setStatus(e.target.value);
  };

  const ImportProps: any = {
    name: 'file',
    multiple: true,
    customRequest: saveImg,
    showUploadList: false,
    beforeUpload: (file, fileList) => {
      setFileCount(fileList.length);
      const currentFileList = fileList.map((n) => {
        if (n.size > 10 * 1024 * 1024) {
          return {
            name: n.name,
            status: 'error',
            source: '',
            msg: i18n.t('global.overSizeError'),
          };
        }
        if (n.type) {
          if (n.type === 'image/png' || n.type === 'image/jpeg' || n.type === 'image/bmp') {
            return { name: n.name, status: 'saving', source: '' };
          }
        }
        return {
          name: n.name,
          status: 'error',
          source: '',
          msg: i18n.t('global.typeError'),
        };
      });
      setUploadImgList([...uploadImgList, ...currentFileList]);
      return true;
    },
  };

  useEffect(() => {
    if (!modalProps?.open) {
      setConfirmLoading(false);
      imgDataList.current = [];
      setFileCount(0);
      setFileUploadCount(0);
      setUploadImgList([]);
    }
  }, [modalProps?.open]);

  const article = (
    <>
      <Row justify="space-between" style={{ marginTop: 15 }}>
        <Col>
          <Title level={5}>{i18n.t('global.explain')}: </Title>
        </Col>
      </Row>
      <Paragraph>1. _: {i18n.t('global.underCode')}</Paragraph>
      <Paragraph>
        2. <Text code>{'{SAMPLE_CODE}'}</Text>: {i18n.t('global.equalSampleCode')}
      </Paragraph>
      <Paragraph>
        3. <Text code>{'{COLOR_CODE}'}</Text>: {i18n.t('global.equalColorCode')}
      </Paragraph>
      <Paragraph>
        4. <Text code>{'{SORT_ID}'}</Text>: {i18n.t('global.equalSort')}
      </Paragraph>
      <Title level={5}>{i18n.t('global.rule')}: </Title>
      <Paragraph>
        1. {i18n.t('global.samplePicture')}: <Text code>{'{SAMPLE_CODE}_{SORT_ID}'}.jpg</Text>
      </Paragraph>
      <Paragraph>
        2. {i18n.t('global.skuImage')}: <Text code>{'{SAMPLE_CODE}_{COLOR_CODE}_{SORT_ID}'}.jpg</Text>
      </Paragraph>
      <Title level={5}>{i18n.t('global.example')}: </Title>
      <Paragraph>{i18n.t('global.importSampleImgRule')}</Paragraph>
      <Paragraph>
        <Text code>SAMPLE123_00.jpg</Text> {/* eslint-disable-next-line max-len */}
        {i18n.t('global.representSample')}: SAMPLE123, {i18n.t('global.sort')}: 00, {i18n.t('global.defaultImg')}
      </Paragraph>
      <Paragraph>
        <Text code>SAMPLE123_01.jpg</Text> {i18n.t('global.representSample')}: SAMPLE123, {i18n.t('global.sort')}: 01
      </Paragraph>
      <Paragraph>{i18n.t('global.importSkuImgRule')}</Paragraph>
      <Paragraph>
        <Text code>SAMPLE123_10_00.jpg</Text> {/* eslint-disable-next-line max-len */}
        {i18n.t('global.representSample')}: SAMPLE123, {i18n.t('global.color')}: 10, {i18n.t('global.sort')}: 00,{' '}
        {i18n.t('global.defaultImg')}
      </Paragraph>
      <Paragraph>
        <Text code>SAMPLE123_10_01.jpg</Text> {/* eslint-disable-next-line max-len */}
        {i18n.t('global.representSample')}: SAMPLE123, {i18n.t('global.color')}: 10, {i18n.t('global.sort')}: 01
      </Paragraph>
    </>
  );

  return (
    <Modal
      title={i18n.t('global.importImages')}
      destroyOnClose
      width={700}
      confirmLoading={confirmLoading}
      {...modalProps}
      footer={false}
      // okButtonProps={{ disabled: imgDataList.current.length === 0 }}
    >
      {uploadImgList.length === 0 || uploadImgList.some((n) => n.status === 'saving') ? (
        <>
          <Dragger style={{ marginBottom: 16 }} {...ImportProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{i18n.t('global.importTip')}</p>
            <p className="ant-upload-hint">
              {i18n.t('global.supportType')}: <Text code>.png</Text>
              <Text code>.jpg</Text>
              <Text code>.bmp</Text>
            </p>
          </Dragger>
          {fileCount > 0 && (
            <Progress
              format={() => `(${fileUploadCount}/${fileCount})`}
              percent={(MathUtils.accDiv(fileUploadCount, fileCount) as any).toFixed(4) * 100}
              style={{ marginBottom: 16 }}
            />
          )}
          <Collapse
            expandIcon={(panelProps) =>
              panelProps.isActive ? (
                <ArrowDownSLineIcon className="fill-lead-slate" />
              ) : (
                <ArrowRightSLineIcon className="fill-lead-slate" />
              )
            }
            expandIconPosition="right"
          >
            <Panel
              header={
                <>
                  <InformationFillIcon className="mr-2 inline-block fill-lead-orange" />
                  {i18n.t('global.help')}
                </>
              }
              key="1"
            >
              {article}
            </Panel>
          </Collapse>
        </>
      ) : (
        <>
          <Radio.Group defaultValue="all" onChange={radioOnChange}>
            <Radio.Button value="all">{i18n.t('global.all')}</Radio.Button>
            <Radio.Button value="success">
              <CheckCircleOutlined style={{ marginRight: 5, color: '#52c41a' }} />
              {i18n.t('global.succeed')}({uploadImgList.filter((n) => n.status === 'success').length})
            </Radio.Button>
            <Radio.Button value="error">
              <CloseCircleOutlined style={{ marginRight: 5, color: '#ff4d4f' }} />
              {i18n.t('global.failed')} ({uploadImgList.filter((n) => n.status === 'error').length})
            </Radio.Button>
          </Radio.Group>
          <List
            size="small"
            bordered
            dataSource={uploadImgList.filter((n) => {
              if (status === 'all') {
                return true;
              }
              return n.status === status;
            })}
            style={{ height: 200, overflowY: 'auto', marginTop: 10 }}
            renderItem={(item) => (
              <>
                {item.status === 'success' && (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <div>
                          <CheckCircleOutlined style={{ marginRight: 5, color: '#52c41a' }} />
                          {item?.name}
                        </div>
                      }
                    />
                  </List.Item>
                )}
                {item.status === 'error' && (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <div>
                          <CloseCircleOutlined style={{ marginRight: 5, color: '#ff4d4f' }} />
                          {item?.name}
                        </div>
                      }
                    />
                    <div>{item.msg}</div>
                  </List.Item>
                )}
              </>
            )}
          />
        </>
      )}
    </Modal>
  );
};

export default ImportImgModal;
