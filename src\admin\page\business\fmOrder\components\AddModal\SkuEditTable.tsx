import { Button, Form, Input, InputNumber, Space, Switch } from 'antd';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, { IPowerTableInnerRef } from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';

import useDict from 'common/hooks/useDict';
import { TableDataContext, UPDATE_SKU } from './TableData';

const EditableContext = React.createContext<any>(1);

interface Item {
  key: string;
  barcode: string;
  colorName: string;
  sizeName: string;
  specName: string;
  qty: string;
}

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  type?: 'number' | 'text';
  children: React.ReactNode;
  dataIndex: string;
  record: Item;
  handleSave: (record: Item) => void;
  detail: any;
}

interface EditableRowProps {
  index: number;
}

const EditableRow: React.FC<EditableRowProps> = (props) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        {/* @ts-ignore */}
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  detail,
  type,
  ...restProps
}) => {
  const [currentInput, setCurrentInput] = useState<any>();
  const inputRef = (input) => setCurrentInput(input);
  const form = useContext(EditableContext);

  const pleaseMsg = i18n.t('global.pleaseEnter');
  useEffect(() => {
    if (detail) {
      const index = detail.findIndex((n) => n.barcode === record.barcode);

      if (index !== -1) {
        form.setFieldsValue(detail[index]);
      } else {
        form.setFieldsValue({ qty: '', price: '', ratio: '' });
      }
    }
    // eslint-disable-next-line
  }, [detail, record]);

  useEffect(() => {
    if (currentInput && dataIndex === 'name') {
      currentInput.focus();
    }
  }, [currentInput, dataIndex]);

  const save = async () => {
    try {
      const values = await form.validateFields();
      handleSave({ ...record, ...values });
    } catch (errInfo) {}
  };
  let childNode = children;
  if (editable && type === 'number') {
    childNode = (
      <Form.Item
        style={{ marginBottom: 0 }}
        name={dataIndex}
        rules={[
          {
            required: true,
            message: pleaseMsg + title,
          },
        ]}
      >
        <InputNumber ref={inputRef} onPressEnter={save} onBlur={save} min={0} max={1000000} />
      </Form.Item>
    );
  }

  if (editable && type === 'text') {
    childNode = (
      <Form.Item
        style={{ marginBottom: 0 }}
        name={dataIndex}
        rules={[
          {
            required: false,
          },
        ]}
      >
        <Input className="w-[180px]" onPressEnter={save} onBlur={save} />
      </Form.Item>
    );
  }
  return <td {...restProps}>{childNode}</td>;
};

interface ISkuEditTable {
  // prodId?: string;
  currentSku?: any;
  onDelete?: (n) => void;
  selectBarCode: () => void;
}

interface IDetail {
  barcode: string;
  prodCode: string;
  price: string;
  qty: number;
  ratio: number;
}

const SkuEditTable: React.FC<ISkuEditTable> = (props) => {
  const { currentSku, onDelete, selectBarCode } = props;
  const [skuData, setSkuData] = useState<any[]>([]);
  const [detail, setDetail] = useState<IDetail[]>([]);
  const [barcodeQty, setBarcodeQty] = useState<number>(1);
  const { saveContextData } = useContext(TableDataContext);
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const fmBoxCustomFieldList = useDict(['FM_BOX_CUSTOM_FIELD']);
  const [skuColumns, setSkuColumns] = useState<any[]>([]);
  const [isMatch, setIsMatch] = useState(false);

  const handleDelete = (record) => {
    const newSkuData = JSON.parse(JSON.stringify(skuData));
    const newDetail = JSON.parse(JSON.stringify(detail));
    newSkuData.forEach((item, index) => item.barcode === record.barcode && newSkuData.splice(index, 1));
    newDetail.forEach((item, index) => item.barcode === record.barcode && newDetail.splice(index, 1));

    saveContextData({ type: UPDATE_SKU, sku: newDetail });
    setSkuData(newSkuData);
    setDetail(newDetail);
    if (onDelete) {
      onDelete(record);
    }
  };

  const popTableColumnRender = (text, record) =>
    skuData.length >= 1 ? (
      <div>
        <Button icon={<DeleteBinLineIcon className="fill-lead-red" />} onClick={() => handleDelete(record)} />
      </div>
    ) : null;

  const handleColumns = useCallback(() => {
    const res = [
      {
        title: i18n.t('global.barcode'),
        dataIndex: 'barcode',
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'prodCode',
        ellipsis: true,
        width: 150,
      },
      {
        title: i18n.t('global.colors'),
        dataIndex: 'colorName',
        width: 120,
      },
      {
        title: i18n.t('global.sizes'),
        dataIndex: 'sizeName',
        width: 120,
      },
      {
        title: i18n.t('global.specs'),
        dataIndex: 'specName',
        width: 150,
      },
      {
        title: i18n.t('global.barcodeRetailPrice'),
        dataIndex: 'price',
        editable: true,
        type: 'number',
        width: 150,
      },
      {
        title: i18n.t('global.count'),
        dataIndex: 'qty',
        editable: true,
        type: 'number',
        width: 120,
      },
    ];

    if (isMatch) {
      res.splice(7, 0, {
        title: i18n.t('global.prodRatioValue'),
        dataIndex: 'ratio',
        editable: true,
        type: 'number',
        width: 120,
      });
    }

    const newData: any[] = fmBoxCustomFieldList
      // .filter((item) => !item.disable)
      .map((item) => ({
        dataIndex: item.code,
        title: item.name,
        editable: true,
        type: 'text',
        width: 200,
      }));

    const list: any = res.concat(newData);

    list.push({
      dataIndex: 'operation',
      render: popTableColumnRender,
      width: 40,
    });

    setSkuColumns(list);
  }, [popTableColumnRender, fmBoxCustomFieldList, isMatch]);

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  const handleSave = (row) => {
    const newDetail = JSON.parse(JSON.stringify(detail));
    const index = newDetail.findIndex((n) => n.barcode === row.barcode);

    const ids = fmBoxCustomFieldList.map((item) => item.code);

    ids.push('boxCode', 'epc', 'operateMode');

    ids.forEach((element) => {
      if (row?.[element]) {
        newDetail[index][element] = row?.[element];
      }
    });

    newDetail[index].qty = row.qty ? row.qty : 0;
    newDetail[index].price = row.price ? row.price : '';
    newDetail[index].ratio = row.ratio ? row.ratio : 0;

    setDetail(newDetail);
    saveContextData({ type: UPDATE_SKU, sku: newDetail });
  };

  const modifyBarcodeQty = () => {
    detail.forEach((item) => {
      item.qty = barcodeQty;
    });
    setDetail(JSON.parse(JSON.stringify(detail)));
  };

  const columns: any = skuColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        detail,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        type: col?.type,
        handleSave,
      }),
    };
  });

  useEffect(() => {
    if (currentSku && currentSku.length > 0) {
      const newDetail: IDetail[] = [];
      const newSkuData: any[] = [];
      currentSku.forEach((item) => {
        const index = detail.findIndex((n) => n.barcode === item.barcode);
        if (index !== -1) {
          newDetail.push({
            barcode: item.barcode,
            prodCode: item.prodCode,
            price: detail[index].price,
            qty: detail[index].qty,
            ratio: detail[index].ratio,
          });
          newSkuData.push({
            ...item,
            price: detail[index].price,
            qty: detail[index].qty,
            ratio: detail[index].ratio,
          });
        } else {
          newDetail.push({
            barcode: item.barcode,
            prodCode: item.prodCode,
            price: item.retailPrice,
            qty: 0,
            ratio: 0,
          });
          newSkuData.push({
            ...item,
            price: item.retailPrice,
            qty: 0,
            ratio: 0,
          });
        }
      });
      saveContextData({ type: UPDATE_SKU, sku: newDetail });
      setDetail(newDetail);
      setSkuData(newSkuData);
    } else {
      setDetail([]);
      setSkuData([]);
    }
    // eslint-disable-next-line
  }, [currentSku]);

  useEffect(() => {
    if (fmBoxCustomFieldList) handleColumns();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fmBoxCustomFieldList, isMatch]);

  return (
    skuColumns.length > 0 && (
      <PowerTable
        columns={columns}
        rowKey="_index"
        tableProps={{
          dataSource: skuData,
          scroll: { x: 1200, y: 400 },
          size: 'small',
        }}
        innerRef={powerTableRef}
        style={{ overflowY: 'auto' }}
        components={components}
        pagination={false}
        refreshBtnVisible={false}
        leftToolbar={
          <Button type="primary" onClick={selectBarCode}>
            {i18n.t('global.selectBarCode')}
          </Button>
        }
        rightToolbar={
          <Space>
            <div className="flex items-center">
              <span className="text-base">{i18n.t('global.matchRatio')}：</span>
              <Switch checked={isMatch} onChange={(checked) => setIsMatch(checked)} />
            </div>
            <InputNumber
              style={{ width: 150 }}
              min={1}
              value={barcodeQty}
              onChange={(val: any) => {
                setBarcodeQty(val <= 0 ? 1 : val);
              }}
              prefix={<span style={{ color: '#8c8c8c' }}>{i18n.t('global.count')} :</span>}
            />
            <Button type="primary" onClick={modifyBarcodeQty}>
              {i18n.t('global.modify')}
            </Button>
          </Space>
        }
      />
    )
  );
};

export default SkuEditTable;
