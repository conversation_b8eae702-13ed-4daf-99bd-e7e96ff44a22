const webPermissionData: any = {
  currentPage: 0,
  data: [
    {
      client: 'WEB',
      code: 'CORE',
      module: 'CORE',
      name: 'web核心',
      parentCode: '',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'CORE:BASE',
      module: 'CORE',
      name: '基础档案',
      parentCode: 'CORE',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'CORE:BASE:PARTNER',
      module: 'CORE',
      name: '合作伙伴',
      parentCode: 'CORE:BASE',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'CORE:BASE:PARTNER:WAREHOUSE:EDIT',
      module: 'CORE',
      name: '仓库',
      parentCode: 'CORE:BASE:PARTNER',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'W_A',
      module: 'WAREHOUSE',
      name: 'web仓库后台',
      parentCode: '',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'W_A:BUSINESS_ORDER',
      module: 'WAREHOUSE',
      name: '业务单据',
      parentCode: 'W_A',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'W_A:BUSINESS_ORDER:IB',
      module: 'WAREHOUSE',
      name: '入库单',
      parentCode: 'W_A:BUSINESS_ORDER',
      permissionCode: '',
      type: 'MENU',
    },
    {
      client: 'WEB',
      code: 'W_A:BUSINESS_ORDER:OB:NEW',
      module: 'WAREHOUSE',
      name: '新增',
      parentCode: 'W_A:BUSINESS_ORDER:OB',
      permissionCode: '',
      type: 'ITEM',
    },
    {
      client: 'WEB',
      code: 'W_A:BUSINESS_ORDER:OB',
      module: 'WAREHOUSE',
      name: '出库单',
      parentCode: 'W_A:BUSINESS_ORDER',
      permissionCode: '',
      type: 'MENU',
    },
  ],
  hasNext: true,
  pageSize: 0,
};

webPermissionData.data.forEach((item, index) => {
  item.id = index;
  item.created = '2020-11-05 07:05:09';
  item.modified = '2020-11-05 08:05:09';
});

export { webPermissionData };
