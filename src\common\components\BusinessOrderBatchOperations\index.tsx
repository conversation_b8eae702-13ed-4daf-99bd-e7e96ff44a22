import React from 'react';
import { Button } from 'antd';
import i18n from 'common/utils/I18n';

export interface BusinessOrderBatchOperationsProps {
  selectedIds: string[];
  clearAllSelection: () => void;
  confirmPermission: boolean;
  cancelPermission: boolean;
  btnOnClick: (type: 'confirm' | 'cancel') => void;
}

const BusinessOrderBatchOperations: React.FC<BusinessOrderBatchOperationsProps> = (props) => {
  const { selectedIds, clearAllSelection, confirmPermission, cancelPermission, btnOnClick } = props;

  return (
    <div className="flex items-center gap-2">
      {selectedIds.length > 0 && (
        <div className="flex items-center gap-2">
          {i18n.t('global.selectedDataNum', { num: selectedIds.length })}
          <Button type="link" onClick={clearAllSelection}>
            {i18n.t('global.clear')}
          </Button>
        </div>
      )}
      {confirmPermission && (
        <Button disabled={selectedIds.length === 0} onClick={() => btnOnClick('confirm')}>
          {i18n.t('global.batchConfirm')}
        </Button>
      )}
      {cancelPermission && (
        <Button disabled={selectedIds.length === 0} onClick={() => btnOnClick('cancel')}>
          {i18n.t('global.batchCancel')}
        </Button>
      )}
    </div>
  );
};
export default BusinessOrderBatchOperations;
