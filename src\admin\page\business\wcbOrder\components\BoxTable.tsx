import { Input } from 'antd';
import * as BwcbOrderApi from 'common/api/warehouse/BwcbOrder';
import * as WcbOrderApi from 'common/api/warehouse/WcbOrder';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId } = orderRecord;
  const { innerRef } = props;

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(
    async (params) => {
      let res;
      const payload = {
        ...params,
        id: orderRecord.id,
      };
      if (!payload.boxCode) delete payload.boxCode;
      if (currentUser.mode === 'BINDING') {
        res = await BwcbOrderApi.Lines(payload);
      } else {
        res = await WcbOrderApi.Lines(payload);
      }
      return res;
    },
    [currentUser.mode, orderRecord.id],
  );

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      ellipsis: true,
      width: 200,
    },
    {
      title: i18n.t('global.boxIndex'),
      dataIndex: 'boxIndex',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.remark'),
      valueType: 'text',
      dataIndex: 'remark',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      auto: true,
      minWidth: 150,
    },
    {
      title: i18n.t('global.modified'),
      dataIndex: 'modified',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
  ];

  return (
    <PowerTable
      initialized
      rowKey="id"
      innerRef={powerTableRef}
      columns={columns}
      defaultPageSize={20}
      // tableProps={{
      //   sticky: true,
      //   onRow: (record) => ({
      //     onClick: () => {
      //       openDetail(record);
      //     },
      //   }),
      // }}
      pagination
      // request={(data) => request(data, dataType)}
      request={fetchData}
      quickSearchPanelSubmitButtonVisible
      quickSearchFieldsConfig={quickSearchFieldsConfig}
      refreshBtnVisible
    />
  );
};

export default BoxTable;
