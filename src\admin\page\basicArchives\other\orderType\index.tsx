import OperateDrawer from 'admin/page/basicArchives/other/orderType/components/OperateDrawer';
import { Button, Select } from 'antd';
import * as OrderTypeApi from 'common/api/core/OrderType';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import { IOrderTypeItem } from './data';

const { Option } = Select;

const OrderType: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<Partial<IOrderTypeItem> | undefined>({});
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const [permission] = usePermission('A:BASE:OTHER:ORDER_TYPE');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await OrderTypeApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const addBtnOnClick = () => {
    setVisible(true);
    setCurrent(undefined);
  };

  const fetchData = useCallback(async (params: Record<string, any>) => {
    let data: any = [];
    try {
      data = await OrderTypeApi.List(params);
    } catch {}
    return { data };
  }, []);

  const handleSubmit = async (values: IOrderTypeItem) => {
    const id = current ? current.id : '';

    setConfirmLoading(true);
    try {
      if (id) {
        /**
         * TODO 编辑接口不支持修改状态disabled，需接口修改
         */
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await OrderTypeApi.Update({
          // @ts-ignore
          id,
          ...values,
        });
      } else {
        values.disabled = false;
        values.code = values.code.trim();
        values.name = values.name.trim();
        await OrderTypeApi.Create(values);
      }
      setConfirmLoading(false);
      setVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setCurrent(undefined);
    powerTableRef.current?.load();
  };

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'type',
      label: i18n.t('global.type'),
      inputComponent: (
        <Select placeholder={i18n.t('global.type')}>
          <Option value="IB">{i18n.t('global.inboundOrder')}</Option>
          <Option value="OB">{i18n.t('global.outboundOrder')}</Option>
        </Select>
      ),
    },
    {
      name: 'os',
      label: i18n.t('global.systemPresets'),
      inputComponent: (
        <Select placeholder={i18n.t('global.systemPresets')}>
          <Option value="true">{i18n.t('global.yes')}</Option>
          <Option value="false">{i18n.t('global.no')}</Option>
        </Select>
      ),
    },
    {
      name: 'categoryCode',
      label: i18n.t('global.categoryCodeList'),
      inputComponent: (
        <Select>
          <Option value="WAREHOUSE">{i18n.t('global.warehouse')}</Option>
          <Option value="SHOP">{i18n.t('global.shop')}</Option>
          <Option value="FACTORY">{i18n.t('global.factory')}</Option>
        </Select>
      ),
    },
  ];
  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      ellipsis: true,
      width: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      width: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.systemPresets'),
      dataIndex: 'os',
      valueType: 'boolean',
      width: 190,
    },
    {
      title: i18n.t('global.type'),
      dataIndex: 'typeDesc',
      ellipsis: true,
      width: 180,
    },
    {
      title: i18n.t('global.categoryCodeList'),
      render: (text, record) => (
        <span>
          {record.categories.length > 0 &&
            record.categories
              .map((n) => {
                switch (n.code) {
                  case 'WAREHOUSE':
                    return i18n.t('global.warehouse');
                  case 'SHOP':
                    return i18n.t('global.shop');
                  case 'FACTORY':
                    return i18n.t('global.factory');
                  default:
                    return '';
                }
              })
              .join(',')}
        </span>
      ),
      width: 190,
      ellipsis: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      sorter: true,
      minWidth: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        rightToolbar={[
          createPermission && (
            <Button type="primary" icon={<AddFillIcon className="fill-white" />} onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        settingToolVisible
        pagination={false}
        autoLoad
        enableCache
        cacheKey="ORDER_TYPE"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  setCurrent(record);
                  setVisible(true);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <OperateDrawer
        visible={visible}
        confirmLoading={confirmLoading}
        orderTypeId={current ? current.id : ''}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default OrderType;
