import { createContext } from 'react';

import { ISampleItem, ISkuItem } from './data';

export const initialState: {
  skuList: ISkuItem[];
  sampleRecord: ISampleItem | null;
  disabled: boolean | null;
  sampleId: string;
} = {
  skuList: [],
  sampleRecord: null,
  disabled: null,
  sampleId: '',
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'setSkuList'; payload: ISkuItem[] }
  | { type: 'setSampleRecord'; payload: ISampleItem | null }
  | { type: 'setDisabled'; payload: boolean | null }
  | { type: 'setSampleId'; payload: string };

export function reducer(state: TStateType, action: TActionType): TStateType {
  switch (action.type) {
    case 'setSkuList':
      return { ...state, skuList: action.payload };
    case 'setSampleRecord':
      return { ...state, sampleRecord: action.payload };
    case 'setDisabled':
      return { ...state, disabled: action.payload };
    case 'setSampleId':
      return { ...state, sampleId: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TSampleContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const SampleContext = createContext<TSampleContext>({
  state: initialState,
  dispatch: () => undefined,
});
