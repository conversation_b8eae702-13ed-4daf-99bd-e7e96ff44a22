import { Table } from 'antd';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import * as SizeApi from 'common/api/core/Size';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ColumnsType } from 'antd/lib/table';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';

export interface SummaryTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface ISummaryTableProps {
  fmOrderId: string;
  // 新单无数据，不用发起请求
  notFetch: boolean;
  innerRef?: React.MutableRefObject<SummaryTableInnerRef | undefined>;
}

type ItemType = {
  actQty: number;
  boxId: string;
  boxCode: string;
  prodId: string;
  prodCode: string;
  sizeId: string;
  sizeCode: string;
  sizeName: string;
  sizeGrpId: string;
  sizeGrpCode: string;
  sizeGrpName: string;
  colorId: string;
  colorCode: string;
  colorName: string;
  [key: string]: any;
};

type SizeItem = {
  colId: number;
  rowId: number;
  sizeId: string;
  sizeCode: string;
  sizeName: string;
  sizeGrpId: string;
  sizeGrpCode: string;
  sizeGrpName: string;
  [key: string]: any;
};

const SIZE_ITEM_WIDTH = 100;

const SummaryTable: React.FC<ISummaryTableProps> = (props) => {
  const { fmOrderId, notFetch, innerRef } = props;

  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const [loading, setLoading] = useState(false);
  const [width, setWidth] = useState(900);
  const columnLength = useRef(0);

  // 箱明细数据
  const [dataScoure, setDataScoure] = useState<ItemType[]>([]);
  // 尺码组 Map，「尺码ID：尺码集合」，相当于坐标轴的「Y轴」
  const [sizeGroupMap, setSizeGroupMap] = useState<Map<string, Set<SizeItem>>>(new Map());
  // 尺码列 Map，「custom_ID：尺码列集合」，相当于坐标轴的「X轴」
  const [sizeHeaderMap, setSizeHeaderMap] = useState<Map<string, SizeItem[]>>(new Map());

  const headerCloumns: ColumnsType<ItemType> = useMemo(
    () => [
      {
        title: i18n.t('global.boxCode'),
        dataIndex: 'boxCode',
        width: 200,
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'prodCode',
        width: 200,
      },
      {
        title: i18n.t('global.color'),
        dataIndex: 'colorName',
        width: 200,
      },
      {
        title: i18n.t('global.sizeGroup'),
        dataIndex: 'sizeGrpName',
        width: 200,
      },
    ],
    [],
  );

  // key:「箱ID_款ID_顏色ID_尺码组ID」
  const getKey = (item: ItemType) => `${item.boxId}_${item.prodId}_${item.colorId}_${item.sizeGrpId}`;

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      // 获取 箱明细 数据
      let res;

      if (currentUser.mode === 'BINDING') {
        res = await BfmOrderApi.SumQuery({ fmOrderId, enablePage: false });
      } else {
        res = await FmOrderApi.SumQuery({ fmOrderId, enablePage: false });
      }

      // 没有箱数据，退出
      if (!res || res?.data?.length === 0) {
        setLoading(false);
        return;
      }

      const { data }: { data: ItemType[] } = res;

      // 给箱数据去重，累加「尺码」数量
      const rearrangementDataMap = new Map();

      data.forEach((element) => {
        const itemKey = `${element.boxCode}_${element.prodId}_${element.colorId}_${element.sizeId}`;
        const mapItem = rearrangementDataMap.get(itemKey);
        if (mapItem) {
          rearrangementDataMap.set(itemKey, {
            ...mapItem,
            actQty: mapItem.actQty + element.actQty,
            count: mapItem.count + element.actQty,
          });
        } else {
          rearrangementDataMap.set(itemKey, { ...element, count: element.actQty || 0 });
        }
      });

      const newData = [...rearrangementDataMap.values()];

      const prodIds = newData.map((item) => item.prodId);

      // 根据「款号」获取「尺码组内的所有尺码」数据
      const { data: sizeData }: { data: SizeItem[] } = await SizeApi.ListByProd({ prodIds, enablePage: false });

      // 根据 sizeGrpId + sizeId 去重
      const uniqueSizeData = Array.from(
        new Map(
          sizeData.map((item) => [
            `${item.sizeGrpId}_${item.sizeId}`, // 使用复合键
            item,
          ]),
        ).values(),
      );

      // 合并的「尺码数量」的箱明细数据，key：箱ID_款ID_顏色ID_尺码组ID
      const currentDataScoureMap: Map<string, ItemType> = new Map();

      // 合并箱明细数据
      for (let i = 0; i < newData.length; i++) {
        const element = newData[i];

        // 如果数据存在，则需要累加装箱数量
        const item = currentDataScoureMap.get(getKey(element));
        if (item) {
          currentDataScoureMap.set(getKey(element), {
            ...item,
            [element.sizeId]: element.actQty,
            count: element.actQty + item.count,
          });
        } else {
          currentDataScoureMap.set(getKey(element), { ...element, [element.sizeId]: element.actQty });
        }
      }

      setDataScoure([...currentDataScoureMap.values()]);

      // 通过 「箱明细数据」初始化「已有的尺码组Map」
      const sizeListMap: Map<string, Set<SizeItem>> = new Map();

      [...currentDataScoureMap.values()].forEach((item) => {
        if (!sizeListMap.get(item.sizeGrpId)) sizeListMap.set(item.sizeGrpId, new Set([]));
      });

      // 「合并」所有的「尺码」数据
      for (let i = 0; i < uniqueSizeData.length; i++) {
        const element = uniqueSizeData[i];

        sizeListMap.get(element.sizeGrpId)?.add({
          colId: element.colId,
          rowId: element.rowId,
          sizeId: element.sizeId,
          sizeCode: element.sizeCode,
          sizeName: element.sizeName,
          sizeGrpId: element.sizeGrpId,
          sizeGrpCode: element.sizeGrpCode,
          sizeGrpName: element.sizeGrpName,
        });
      }
      // 尺码列通过「colId升序」排序
      const mapKeys = [...sizeListMap.keys()];

      [...sizeListMap.values()].forEach((element, index) => {
        sizeListMap.set(mapKeys[index], new Set([...element].sort((a, b) => a.colId - b.colId)));
      });

      setSizeGroupMap(sizeListMap);

      /* 
        「Y轴数据」转换为「X轴数据」

        Y轴
        SG_001 = [S_011, S_012]
        SG_002 = [S_021, S_022, S_023]

        坐标图
        SG_002  S_021  S_022  S_023
        SG_001  S_011  S_012
                cst_0  cst_1  cst_2
        
        转换成X轴
        custom_0 = [S_011, S_021],
        custom_1 = [S_012, S_022],
        custom_2 = [S_023],
      */
      const yAxisData = [...sizeListMap.values()];

      const xAxisDataMap = new Map();

      // y 转换成 x
      yAxisData.forEach((yAxisElement) => {
        [...yAxisElement].forEach((yAxisItemElement, index) => {
          if (xAxisDataMap.get(`custom_${index}`)) {
            xAxisDataMap.set(`custom_${index}`, [...xAxisDataMap.get(`custom_${index}`), yAxisItemElement]);
          } else {
            xAxisDataMap.set(`custom_${index}`, [yAxisItemElement]);
          }
        });
      });

      setSizeHeaderMap(xAxisDataMap);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }, [currentUser.mode, fmOrderId]);

  useEffect(() => {
    if (!notFetch) fetchData();
  }, [notFetch, fetchData]);

  const reset = () => {
    setWidth(900);
    setDataScoure([]);
    setSizeGroupMap(new Map());
    setSizeHeaderMap(new Map());
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      reset();
      fetchData();
    },
  }));

  const columns = useMemo(() => {
    const arr: ColumnsType<ItemType> = JSON.parse(JSON.stringify(headerCloumns));

    arr.push({
      dataIndex: '_size_group_placeholder',
      width: SIZE_ITEM_WIDTH,
    });

    sizeHeaderMap.forEach((item, key) => {
      const keys = [...item].map((item) => item.sizeId);

      arr.push({
        title: key,
        dataIndex: key,
        width: SIZE_ITEM_WIDTH,
        render: (_, record) => (
          <div style={{ textAlign: 'right' }}>
            {Math.max.apply(
              null,
              keys.map((i) => record?.[i] || 0),
            ) || 0}
          </div>
        ),
      });
    });

    arr.push({
      title: i18n.t('global.packedQty'),
      dataIndex: 'count',
      width: SIZE_ITEM_WIDTH,
      render: (_, record) => <div style={{ textAlign: 'right' }}>{record?.count || 0}</div>,
    });

    const widthCount = arr.reduce((accumulator, currentValue) => accumulator + Number(currentValue.width || 0), 0);
    setWidth(widthCount);
    columnLength.current = arr.length;

    return arr;
  }, [headerCloumns, sizeHeaderMap]);

  const customHeader = useMemo(() => {
    const defaultHeader = JSON.parse(JSON.stringify(headerCloumns));

    // 尺码组列最长长度
    let maxLength = 0;

    let inHeaderSizeList: any[] = [];
    const outsideHeaderSizeList: SizeItem[][] = [];

    [...sizeGroupMap.values()].forEach((item, index) => {
      const arrItem = [...item];

      if (arrItem.length > maxLength) maxLength = arrItem.length;

      if (index === 0) inHeaderSizeList = arrItem;
      else {
        outsideHeaderSizeList.push(arrItem);
      }
    });

    const sizeItemStyle: React.CSSProperties = { width: SIZE_ITEM_WIDTH, textAlign: 'right' };

    return (
      <div style={{ width: '100%' }}>
        <table className="ant-table-thead" style={{ width: '100%', tableLayout: 'auto' }}>
          {outsideHeaderSizeList.map((item) => (
            <tr style={{ width }} key={item[0].sizeGrpId}>
              {/* 占位 */}
              {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
              <th style={{ width: 200 }} colSpan={4} />

              {/* 尺码组名称 */}
              <th style={sizeItemStyle} key={item[0].sizeGrpId}>
                {item[0].sizeGrpName}
              </th>

              {item.map((i) => (
                <th style={sizeItemStyle} key={i.sizeId}>
                  {i.sizeName}
                </th>
              ))}

              {/* 尺码组长度不够的占位th */}
              {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
              <th style={{ width: SIZE_ITEM_WIDTH }} colSpan={maxLength - item.length + 1} />
            </tr>
          ))}

          {/* 表头 */}
          <tr style={{ width }}>
            {/* 固定字段 */}
            {defaultHeader.map((i) => (
              <th style={{ width: i.width }} key={i.dataIndex}>
                {i.title}
              </th>
            ))}

            {/* 尺码组名称 */}
            {inHeaderSizeList.length !== 0 && (
              <th style={sizeItemStyle} key={inHeaderSizeList[0].sizeGrpId}>
                {inHeaderSizeList[0].sizeGrpName}
              </th>
            )}

            {/* 尺码item */}
            {inHeaderSizeList.map((i) => (
              <th style={sizeItemStyle} key={i.sizeId}>
                {i.sizeName}
              </th>
            ))}

            {/* 尺码组长度不够的占位th */}
            {maxLength - inHeaderSizeList.length > 0 && (
              // eslint-disable-next-line jsx-a11y/control-has-associated-label
              <th style={{ width: SIZE_ITEM_WIDTH }} colSpan={maxLength - inHeaderSizeList.length} />
            )}

            <th style={sizeItemStyle} key="count">
              {i18n.t('global.packedQty')}
            </th>
          </tr>
        </table>
      </div>
    );
  }, [headerCloumns, sizeGroupMap, width]);

  return (
    <div style={{ width: '100%', overflow: 'scroll' }}>
      <div style={{ width: '100%', minWidth: width, overflow: 'auto hidden' }}>
        <Table
          loading={loading}
          columns={columns}
          dataSource={dataScoure}
          pagination={false}
          className="custom_header_table"
          showHeader={false}
          title={() => customHeader}
          summary={(currentData) => {
            const total = currentData.reduce((accumulator, currentValue) => accumulator + currentValue.count, 0);
            return (
              <>
                <th className="p-4">{i18n.t('global.summation')}</th>
                {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
                <th className="p-4" colSpan={columnLength.current - 2} />
                <th className="p-4 text-right">{total}</th>
              </>
            );
          }}
        />
      </div>
    </div>
  );
};

export default SummaryTable;
