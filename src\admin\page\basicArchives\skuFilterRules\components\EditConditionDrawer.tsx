import { Form } from 'antd';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useEffect, useRef, useState } from 'react';
import * as RfidFilterApi from 'common/api/core/RfidFilter';
import ConditionForm from './ConditionForm';

interface EditConditionDrawerProps {
  open: boolean;
  current: Partial<any> | undefined;
  onOk: () => void;
  onClose: () => void;
}

const EditConditionDrawer: React.FC<EditConditionDrawerProps> = (props) => {
  const { onOk, onClose, current, open } = props;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const onSave = async () => {
    setLoading(true);
    try {
      const values = await form.validateFields();

      const payload = {
        ...values,
        rfidFilterConditionId: current?.id,
        rfidFilterTypeId: current?.rfidFilterTypeId,
      };

      await RfidFilterApi.ConditionUpdate(payload);

      NoticeUtil.success();
      setLoading(false);
      if (onOk) onOk();
    } catch (e) {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (current && open) {
      form.setFieldsValue(current);
      nameInputRef.current?.focus();
    } else {
      form.resetFields();
    }
  }, [open, current, form]);

  const onCancel = () => {
    if (onClose) onClose();
  };

  return (
    <Drawer
      title={i18n.t('global.editTagFilterRule')}
      destroyOnClose
      open={open}
      onClose={onCancel}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading,
          }}
          recoverPermission={false}
          onApply={onSave}
          applyPermission
          deletePermission={false}
          cancelPermission
          onCancel={onClose}
        />
      }
    >
      <ConditionForm codeInputRef={codeInputRef} form={form} layout="vertical" name="basic" />
    </Drawer>
  );
};

export default EditConditionDrawer;
