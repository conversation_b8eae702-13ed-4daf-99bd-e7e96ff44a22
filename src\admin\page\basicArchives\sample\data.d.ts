import IBase from 'common/types/IBase';
import IExt from 'common/types/IExt';

export interface ISampleItem extends IBase {
  band: string;
  code: string;
  costPrice: number;
  created: string;
  defaultImg: string;
  disabled: boolean;
  disturbTag: boolean;
  ext: IExt;
  gender: string;
  id: string;
  listedDate: string;
  modified: string;
  name: string;
  remark: string;
  retailPrice: string;
  smBrandCode: string;
  smBrandId: string;
  smBrandName: string;
  smColorGrpCode: string;
  smColorGrpId: string;
  smColorGrpName: string;
  smPriCategoryCode: string;
  smPriCategoryId: string;
  smPriCategoryName: string;
  smSeriesCode: string;
  smSeriesId: string;
  smSeriesName: string;
  smSizeGrpCode: string;
  smSizeGrpId: string;
  smSizeGrpName: string;
  smSubCategoryCode: string;
  smSubCategoryId: string;
  smSubCategoryName: string;
  smYearCode: string;
  smYearId: string;
  smYearName: string;
  source: string;
  tagPrice: number;
  version: number;
}

export interface ISkuItem extends IBase {
  created: string;
  disabled: boolean;
  id: string;
  modified: string;
  remark: string;
  smBarcode: string;
  smCode: string;
  smColorCode: string;
  smColorId: string;
  smColorName: string;
  smId: string;
  smName: string;
  smSizeCode: string;
  smSizeId: string;
  smSizeName: string;
  smSkuName: string;
  version: number;
}
