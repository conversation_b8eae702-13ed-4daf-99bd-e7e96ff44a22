import { Descriptions, Tabs } from 'antd';
import * as BwcbOrderApi from 'common/api/warehouse/BwcbOrder';
import * as WcbOrderApi from 'common/api/warehouse/WcbOrder';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import EditBoxLineIcon from 'common/assets/icons/icon-edit-box-line.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import Button from 'common/components/Button';
import RemarkEditModal from 'common/components/RemarkEditModal';
import Tag from 'common/components/Tag';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useMemo, useReducer, useRef } from 'react';
import { useParams } from 'react-router-dom';

import { useCommonFn } from './common';
import BoxTable, { BoxTableInnerRef } from './components/BoxTable';
import { DetailContext, initialState, reducer } from './DetailReducer';

const Detail: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { orderRecord, remarkModalOpen } = state;
  const params = useParams();
  const boxTableRef = useRef<BoxTableInnerRef>();
  const { confirm } = useCommonFn();

  const [permission] = usePermission('A:W:CB');
  const confirmPermission = permission.codes.includes('CONFIRM');
  const modifyRemarkPermission = permission.codes.includes('MODIFY_REMARK');

  const subTitle = useMemo(() => {
    return (
      <div className="flex items-center gap-2">
        <Tag
          color={
            {
              NEW: 'red',
              PROCESSING: 'blue',
              FINISHED: 'green',
              CANCELED: 'slate',
            }[orderRecord.status]
          }
        >
          {orderRecord.statusDesc}
        </Tag>
        <Tag
          color={
            {
              NOT_REVIEWED: 'slate',
              REVIEWED: 'green',
            }[orderRecord.reviewStatus]
          }
        >
          {orderRecord.reviewStatusDesc}
        </Tag>
      </div>
    );
  }, [orderRecord.status, orderRecord.statusDesc, orderRecord.reviewStatus, orderRecord.reviewStatusDesc]);

  const fetchOrderData = useCallback(async () => {
    dispatch({ type: 'setLoading', payload: true });
    try {
      let rec: any;
      if (currentUser.mode === 'BINDING') {
        rec = await BwcbOrderApi.Get({ id: params.id });
      } else {
        rec = await WcbOrderApi.Get({ id: params.id });
      }
      rec.availableQty = rec.qty - rec.actQty;
      dispatch({ type: 'setOrderRecord', payload: rec });
    } catch (err) {}
    dispatch({ type: 'setLoading', payload: false });
    return {};
  }, [params.id, currentUser.mode]);

  const refresh = async () => {
    await fetchOrderData();
    boxTableRef.current?.load();
  };

  const confirmBtnOnClick = async () => {
    try {
      await confirm(orderRecord.id, orderRecord.code);
      refresh();
    } catch (e) {}
  };

  const remarkEditModalOnSubmit = async (values) => {
    try {
      if (currentUser.mode === 'BINDING') {
        await BwcbOrderApi.UpdateRemark({
          id: params.id,
          ...values,
        });
      } else {
        await WcbOrderApi.UpdateRemark({
          id: params.id,
          ...values,
        });
      }

      NoticeUtil.success();
      refresh();
    } catch {}
    dispatch({ type: 'setRemarkEditModalOpen', payload: false });
  };

  useEffect(() => {
    fetchOrderData();
    // eslint-disable-next-line
    }, [params.id]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <DetailContext.Provider value={detailContextValue}>
      <AppHeader
        title={orderRecord.code}
        subTitle={subTitle}
        toolbar={
          <div className="flex gap-x-2">
            <Button icon={<RefreshIcon className="fill-lead-dark" />} loading={state.loading} onClick={refresh} />{' '}
            {confirmPermission && orderRecord.status === 'PROCESSING' && (
              <Button
                type="primary"
                icon={<DoubleCheckIcon className="fill-white" />}
                className="global-blue"
                onClick={confirmBtnOnClick}
              >
                {i18n.t('global.confirm')}
              </Button>
            )}
          </div>
        }
      />
      <div className="rounded-md border border-lead-light-slate px-5 pt-4">
        <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
          {orderRecord.syncStatus && orderRecord.syncStatusDesc && (
            <Descriptions.Item label={i18n.t('global.syncStatus')}>{orderRecord.syncStatusDesc}</Descriptions.Item>
          )}
          {typeof orderRecord.syncVer === 'number' && (
            <Descriptions.Item label={i18n.t('global.syncVersion')}>{orderRecord.syncVer}</Descriptions.Item>
          )}
          {typeof orderRecord.archived === 'boolean' && (
            <Descriptions.Item label={i18n.t('global.archived')}>
              {orderRecord.archived ? i18n.t('global.yes') : i18n.t('global.no')}
            </Descriptions.Item>
          )}
          {orderRecord.boxQty && (
            <Descriptions.Item label={i18n.t('global.boxQty')}>{orderRecord.boxQty}</Descriptions.Item>
          )}
          {((!orderRecord.remark && orderRecord.status !== 'FINISHED') || orderRecord.remark) && (
            <Descriptions.Item label={i18n.t('global.remark')} contentStyle={{ alignItems: 'center' }}>
              {orderRecord.remark}

              {modifyRemarkPermission && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED' && (
                <>
                  <Button
                    type="link"
                    size="small"
                    icon={<EditBoxLineIcon className="fill-lead-orange" />}
                    onClick={() => dispatch({ type: 'setRemarkEditModalOpen', payload: true })}
                    className="-my-px ml-2"
                  />
                  <RemarkEditModal
                    current={orderRecord}
                    onSubmit={remarkEditModalOnSubmit}
                    onCancel={() => dispatch({ type: 'setRemarkEditModalOpen', payload: false })}
                    open={remarkModalOpen}
                    title={i18n.t('global.modifyRemark')}
                  />
                </>
              )}
            </Descriptions.Item>
          )}
          {orderRecord.makeDate && (
            <Descriptions.Item label={i18n.t('global.makeDate')}>{orderRecord.makeDate}</Descriptions.Item>
          )}
          {orderRecord.modified && (
            <Descriptions.Item label={i18n.t('global.modified')}>{orderRecord.modified}</Descriptions.Item>
          )}
          {orderRecord.created && (
            <Descriptions.Item label={i18n.t('global.created')}>{orderRecord.created}</Descriptions.Item>
          )}
        </Descriptions>
      </div>
      <div className="mt-5">
        <Tabs
          defaultActiveKey="0"
          size="small"
          items={[
            {
              key: '1',
              label: `${i18n.t('global.box')} (${orderRecord.boxQty || 0})`,
              children: <BoxTable innerRef={boxTableRef} />,
            },
          ]}
        />
      </div>
    </DetailContext.Provider>
  );
};

export default Detail;
