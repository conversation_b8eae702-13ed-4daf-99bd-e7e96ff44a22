import { DatePicker, Input, Space } from 'antd';
import * as SampleTagApi from 'common/api/sample/SampleRfid';
import ImportButton from 'common/components/Button/Import';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import { usePermission } from 'common/utils/Permission';
import React, { useRef, useState } from 'react';

import ImportModal from './components/ImportModal';

const SampleTag: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [importModalVisible, setImportModalVisible] = useState(false);

  const [permission] = usePermission('A:BASE:SAMPLETAG');
  const createPermission = permission.codes.includes('IMPORT');

  const fetchData = (params) => {
    if (params.created) {
      params.createdStart = params.created[0].startOf('day');
      params.createdEnd = params.created[1].endOf('day');
    }
    delete params.created;

    if (params.smBarcode) {
      params.smBarcode = params.smBarcode.trim();
    }
    if (params.smEpc) {
      params.smEpc = params.smEpc.trim();
    }
    return SampleTagApi.List(params);
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smEpc',
      label: 'EPC',
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smBarcode',
      label: i18n.t('global.barcode'),
      inputComponent: <Input />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: 'EPC',
      dataIndex: 'smEpc',
      valueType: 'text',
      ellipsis: true,
      tooltip: true,
      sorter: true,
      auto: true,
      minWidth: 330,
    },
    {
      title: 'TID',
      dataIndex: 'smTid',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 260,
      tooltip: true,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'smCode',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 150,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'smName',
      valueType: 'text',
      ellipsis: true,
      sorter: false,
      width: 180,
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'smBarcode',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
      width: 180,
    },
    {
      title: i18n.t('global.color'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'smColorCode',
      nameDataIndex: 'smColorName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.size'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'smSizeCode',
      nameDataIndex: 'smSizeName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.brand'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'smBrandCode',
      nameDataIndex: 'smBrandName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.labelSource'),
      ellipsis: true,
      sorter: false,
      width: 150,
      codeDataIndex: 'source',
      nameDataIndex: 'sourceDesc',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.operationSourceType'),
      codeDataIndex: 'sourcePartnerType',
      nameDataIndex: 'sourcePartnerTypeDesc',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
      ellipsis: true,
      width: 350,
    },
    {
      title: i18n.t('global.operationSource'),
      ellipsis: true,
      width: 300,
      codeDataIndex: 'sourcePartnerCode',
      nameDataIndex: 'sourcePartnerName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.operatingPositionType'),
      dataIndex: 'partnerTypeDesc',
      codeDataIndex: 'partnerType',
      nameDataIndex: 'partnerTypeDesc',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
      ellipsis: true,
      width: 250,
    },
    {
      title: i18n.t('global.operatingPosition'),
      ellipsis: true,
      width: 250,
      codeDataIndex: 'partnerCode',
      nameDataIndex: 'partnerName',
      valueType: 'codeName',
      codeNameStyle: 'ONLY_NAME',
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.lastModified'),
      dataIndex: 'modified',
      valueType: 'dateTime',
      ellipsis: true,
      sorter: true,
      width: 200,
    },
  ];

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {createPermission && (
              <ImportButton
                type="primary"
                onClick={() => {
                  setImportModalVisible(true);
                }}
              />
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="SAMPLE_SKU_TAG"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <ImportModal
        modalProps={{
          open: importModalVisible,
          onCancel: () => {
            setImportModalVisible(false);
          },
          maskClosable: false,
        }}
        onFinish={() => {
          powerTableRef.current?.load();
        }}
        onGoBack={() => setImportModalVisible(false)}
      />
    </div>
  );
};
export default SampleTag;
