import { Button, DatePicker, Form, Input, Result } from 'antd';
import classNames from 'classnames';
import * as SptsOrderApi from 'common/api/sample/SpTsOrder';
import Drawer from 'common/components/Drawer';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useMemo, useReducer } from 'react';
import { useNavigate } from 'react-router-dom';

import { AddDrawerContext, initialState, reducer } from './AddDrawerReducer';

interface AddDrawerProps {
  open?: boolean;
  onRefresh?: () => void;
  onClose?: () => void;
}

const disabledDate = (current) => current && current < moment().startOf('day');
const AddDrawer: React.FC<AddDrawerProps> = (props) => {
  const { open, onClose } = props;
  const onRefresh = useMemo(() => props.onRefresh || (() => {}), [props.onRefresh]);
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { currentStep, success, errorMsg, saving, result } = state;
  const navigate = useNavigate();

  const initVal: any = {
    startTime: moment().startOf('day'),
    mode: 'ALL',
  };
  if (currentUser.type === 'WAREHOUSE') {
    initVal.partner = {
      partner: currentUser.partnerId,
      warehouse: '',
    };
  }

  const [form] = Form.useForm();

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const submit = async () => {
    await form.validateFields();
    const payload = { ...form.getFieldsValue(), mode: 'ALL' };

    dispatch({ type: 'setSaving', payload: true });
    try {
      const resp: any = await SptsOrderApi.Create(payload, {
        throwError: false,
      });
      dispatch({
        type: 'setResult',
        payload: {
          id: resp.id,
          code: resp.code,
        },
      });
      dispatch({ type: 'setSuccess', payload: true });
      onRefresh();
    } catch (e: any) {
      dispatch({ type: 'setErrorMsg', payload: e?.response?.data?.message || '' });
      dispatch({ type: 'setSuccess', payload: false });
    }
    dispatch({ type: 'setCurrentStep', payload: 1 });
    dispatch({ type: 'setSaving', payload: false });
  };

  useEffect(() => {
    if (!open) {
      dispatch({ type: 'reset' });
      form.resetFields();
    }
  }, [open, form]);

  const onRetry = useCallback(() => {
    dispatch({ type: 'setCurrentStep', payload: 0 });
  }, []);

  const onOpenOrder = useCallback(() => {
    if (result?.id) {
      navigate(`/app/spts/${result?.id}`);
    }
  }, [result?.id, navigate]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <AddDrawerContext.Provider value={detailContextValue}>
      <Drawer
        title={i18n.t('global.newInventory')}
        subTitle={
          {
            0: i18n.t('global.writeBasicInfo'),
            1: i18n.t('global.result'),
          }[currentStep]
        }
        open={open}
        onClose={onClose}
        destroyOnClose
        footer={
          currentStep !== 1 ? (
            <div className="flex gap-x-2 px-6 pb-6">
              {currentStep === 0 && (
                <Button type="primary" loading={saving} onClick={submit}>
                  {i18n.t('global.ok')}
                </Button>
              )}
              <Button onClick={props.onClose}>{i18n.t('global.cancel')}</Button>
            </div>
          ) : (
            false
          )
        }
      >
        <div className="flex flex-col">
          <Form
            form={form}
            name="basic"
            layout="vertical"
            // onFinish={formOnFinish}
            style={{
              display: currentStep === 0 ? 'block' : 'none',
            }}
            className={classNames({
              hidden: currentStep !== 0,
            })}
            initialValues={initVal}
          >
            <Form.Item name="partnerId" label={i18n.t('global.warehouse')} rules={canNotBeNullRules}>
              <PartnerSelect types={['WAREHOUSE']} sourceType="PERMISSION" />
            </Form.Item>
            <Form.Item name="startTime" label={i18n.t('global.startTime')} rules={canNotBeNullRules}>
              <DatePicker disabledDate={disabledDate} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item label={i18n.t('global.remark')} name="remark">
              <Input.TextArea rows={3} />
            </Form.Item>
          </Form>
          <div
            className={classNames({
              hidden: currentStep !== 1,
            })}
          >
            {success && (
              <Result
                status="success"
                title={i18n.t('global.createdDone')}
                subTitle={`${i18n.t('global.orderCode')}: ${result?.code}`}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onOpenOrder}>
                    {i18n.t('global.openOrder')}
                  </Button>,
                ]}
              />
            )}
            {!success && (
              <Result
                status="error"
                title={i18n.t('global.createdFail')}
                subTitle={errorMsg}
                extra={[
                  <Button key="1" onClick={onClose}>
                    {i18n.t('global.close')}
                  </Button>,
                  <Button type="primary" key="2" onClick={onRetry}>
                    {i18n.t('global.retry')}
                  </Button>,
                ]}
              />
            )}
          </div>
        </div>
      </Drawer>
    </AddDrawerContext.Provider>
  );
};

export default AddDrawer;
