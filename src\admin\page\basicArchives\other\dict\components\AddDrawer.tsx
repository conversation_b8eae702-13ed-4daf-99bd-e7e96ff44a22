/**
 * Create by codegen at 2022-07-13
 * 数据字典 - 新增
 */
import { Form, Input, Modal } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

interface AddDrawerProps {
  visible?: boolean;
  confirmLoading?: boolean;
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

const AddDrawer: React.FC<AddDrawerProps> = (props) => {
  const { visible, confirmLoading, onOk, onCancel } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      onOk(values);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  useEffect(() => {
    let timerId: any = null;
    if (visible) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [visible]);

  return (
    <Modal
      title={i18n.t('global.newDictionary')}
      open={visible}
      confirmLoading={confirmLoading}
      onOk={modalOnOk}
      onCancel={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
          <Input ref={inputRef} maxLength={30} />
        </Form.Item>
        <Form.Item name="name" label={i18n.t('global.name')} rules={canNotBeNullRules}>
          <Input maxLength={50} />
        </Form.Item>
        <Form.Item label={i18n.t('global.remark')} name="remark">
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddDrawer;
