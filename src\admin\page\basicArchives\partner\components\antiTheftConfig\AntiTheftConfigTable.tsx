import { IWarehouseItem } from 'admin/page/basicArchives/partner/data';
import { Button, Checkbox, Col, Row, Switch } from 'antd';
// import * as WarehouseApi from 'common/api/core/Warehouse';
import * as AntiTheftApi from 'common/api/core/AntiTheft';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import PartnerSelectorModal from 'common/components/PartnerSelectorModal';
import ShopAntiTheftSelect from 'common/components/Select/ShopAntiTheftSelect';
import * as PartnerA<PERSON> from 'common/api/core/Partner';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PartnerSettingDrawer from './PartnerSettingDrawer';
import { businessTypeOption } from './common';

export interface AntiTheftConfigRef {
  submit(): void;
  refresh(): void;
}

export interface IAntiTheftConfigTableProps {
  partnerRecord?: Record<string, any>;
  innerRef?: React.MutableRefObject<AntiTheftConfigRef | undefined>;
}

interface IAntiTheftGroupTProps {
  id: string;
  disabled: boolean;
  ownerPartnerId: string;
  targetPartnerId: string;
  targetPartnerCode: string;
  targetPartnerName: string;
  businessTypeList?: string[];
}

const AntiTheftConfigTable: React.FC<IAntiTheftConfigTableProps> = (props) => {
  const { partnerRecord, innerRef } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const recordItem = useRef<Partial<IWarehouseItem> | undefined>();
  const [loading, setLoading] = useState(false);
  const dataSourceMap = useRef<Map<string, IAntiTheftGroupTProps>>(new Map());
  const [partnerSelectorModalVisible, setPartnerSelectorModalVisible] = useState(false);
  const [defaultSelected, setDefaultSelected] = useState<string[]>([partnerRecord?.id]);
  const [partnerSettingVisible, setPartnerSettingVisible] = useState(false);
  const [selectBusinessTypeKeys, setSelectBusinessTypeKeys] = useState<string[]>([]);
  const [antiTheft, setAntiTheft] = useState<string>(partnerRecord?.enableAntiTheft);
  const [dataSource, setDataSource] = useState<any[]>([]);

  const [permission] = usePermission('A:BASE:PARTNER');
  const itemPermission = permission.codes.includes('ANTI_THEFT_PARTNER');

  const request = useCallback(
    () =>
      AntiTheftApi.ShareGroupList({
        ownerPartnerId: partnerRecord?.id,
        type: partnerRecord?.type,
      }),
    [partnerRecord],
  );

  const fetchData = useCallback(async () => {
    setLoading(true);

    try {
      const res: any = await request();
      let data: any[] = [];

      const myself = res.find((i) => i.targetPartnerId === partnerRecord?.id);
      const nowDefaultSelected = res.map((i) => i.targetPartnerId);

      if (myself) {
        setSelectBusinessTypeKeys(myself?.businessTypeList || []);
      } else {
        nowDefaultSelected.unshift(partnerRecord?.id);
      }

      data = res.filter((item: any) => item !== myself);
      setDefaultSelected(nowDefaultSelected);

      dataSourceMap.current = new Map(data.map((i) => [i.id, i]));
      setLoading(false);
      setDataSource(data);
    } catch (error) {
      setLoading(false);
    }
  }, [partnerRecord, request]);

  const onSearch = useCallback(
    async (value: string) => {
      try {
        if (value && value !== partnerRecord?.code) {
          const res = [...dataSourceMap.current.values()];
          const filteredData = res.filter((item) => item.targetPartnerCode?.includes(value));

          setDataSource(filteredData);
        } else if (value === partnerRecord?.code) {
          setDataSource([]);
        } else if (!value) {
          setDataSource([...dataSourceMap.current.values()]);
        }
      } catch (error) {}
    },
    [partnerRecord],
  );

  const onSwitchChange = useCallback(async (record: Record<string, any>, bool: boolean) => {
    const item = dataSourceMap.current.get(record.id);

    if (item) {
      dataSourceMap.current.set(record.id, { ...item, disabled: !bool });
    }
  }, []);

  const openPartnerSettingModal = (record: Record<string, any>) => {
    let newRecord = record;

    const item = dataSourceMap.current.get(record.id);

    if (item && item?.businessTypeList) {
      newRecord = { ...newRecord, businessTypeList: item.businessTypeList };
    }

    recordItem.current = newRecord;
    setPartnerSettingVisible(true);
  };

  const settingOnSubmit = (values: Record<string, any>) => {
    setPartnerSettingVisible(false);

    const item = dataSourceMap.current.get(values.id);

    if (item) {
      dataSourceMap.current.set(values.id, { ...item, businessTypeList: values.businessTypeList });
    }

    recordItem.current = undefined;
  };

  const deletePartner = async (record: Record<string, any>) => {
    const item = dataSourceMap.current.get(record.id);

    if (item) {
      dataSourceMap.current.delete(record.id);
      setDataSource([...dataSourceMap.current.values()]);
      setDefaultSelected([...dataSourceMap.current.keys()]);
    }
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.codeOrAccount'),
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} onSearch={onSearch} />
      ),
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.code'),
      dataIndex: 'targetPartnerCode',
      valueType: 'text',
      minWidth: 150,
      tooltip: true,
    },
    {
      title: i18n.t('global.antiTheft'),
      dataIndex: 'disabled',
      width: 150,
      render: (_, record) => {
        return itemPermission ? (
          <Switch defaultChecked={!record.disabled} onChange={(bool) => onSwitchChange(record, bool)} />
        ) : (
          <>
            {record.disabled && <span className="text-lead-normal">{i18n.t('global.disabled')}</span>}
            {!record.disabled && <span className="text-lead-normal">{i18n.t('global.enabled')}</span>}
          </>
        );
      },
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (itemPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.edit'),
      icon: <ListIcon className="fill-lead-blue" />,
      onClick: openPartnerSettingModal,
    });

    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: deletePartner,
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  const openPartnerSelectorModal = () => {
    setPartnerSelectorModalVisible(true);
  };

  const partnerSelectorModalOnOk = async (values: Record<string, any>[]) => {
    if (!partnerRecord) return;

    const ids = values.map((i) => i.id);

    // 获取其他仓id
    const otherIds = ids.filter((i) => i !== partnerRecord.id);

    // 先删除dataSourceMap中不存在的数据
    const dataSourceList = [...dataSourceMap.current.values()];

    dataSourceList.forEach((i) => {
      if (!ids.includes(i.targetPartnerId)) {
        dataSourceMap.current.delete(i.id);
      }
    });

    // 添加新数据
    otherIds.forEach((i) => {
      if (!dataSourceList.find((item) => item.targetPartnerId === i)) {
        const item: any = values.find((item) => item.id === i);
        dataSourceMap.current.set(i, {
          targetPartnerId: i,
          disabled: false,
          businessTypeList: [],
          id: i,
          ownerPartnerId: partnerRecord.id,
          targetPartnerCode: item.code,
          targetPartnerName: item.name,
        });
      }
    });

    setDataSource([...dataSourceMap.current.values()]);
    setDefaultSelected(ids);
    setPartnerSelectorModalVisible(false);
  };
  const partnerSelectorModalOnCancel = () => {
    setPartnerSelectorModalVisible(false);
  };

  const onCheckboxChange = (keys: string[]) => {
    setSelectBusinessTypeKeys(keys);
  };

  useImperativeHandle(innerRef, () => ({
    submit: async () => {
      if (!partnerRecord) return;

      try {
        const submitType = PartnerApi.Update({
          ...partnerRecord,
          id: partnerRecord.id,
          enableAntiTheft: antiTheft,
        });

        const values = [...dataSourceMap.current.values()];

        const data = values.map((i) => ({
          targetPartnerId: i.targetPartnerId,
          disabled: i.disabled,
          businessTypeList: i?.businessTypeList || [],
        }));

        data.push({
          targetPartnerId: partnerRecord.id,
          disabled: false,
          businessTypeList: selectBusinessTypeKeys,
        });

        const submitGroup = AntiTheftApi.ShareGroupSave({
          ownerPartnerId: partnerRecord.id,
          data,
        });

        await Promise.all([submitType, submitGroup]);
        NoticeUtil.success();

        setAntiTheft('');
        setSelectBusinessTypeKeys([]);
        setDefaultSelected([partnerRecord.id]);
      } catch (error) {}
    },
    refresh: () => {
      setAntiTheft(partnerRecord?.enableAntiTheft);
      setDataSource([]);
      setDefaultSelected([partnerRecord?.id]);
      setSelectBusinessTypeKeys([]);
      fetchData();
    },
  }));

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <div className="mb-8">
        <div className="mb-2 text-base font-bold">{i18n.t('global.currentWarehouse')}</div>

        <div className="flex items-center">
          <ShopAntiTheftSelect className="w-40" value={antiTheft} onChange={setAntiTheft} />
        </div>
        <div>
          <Checkbox.Group className="mt-4 w-full" value={selectBusinessTypeKeys} onChange={onCheckboxChange}>
            <Row gutter={[8, 8]}>
              {businessTypeOption.map((i) => (
                <Col span={6}>
                  <Checkbox value={i.value} disabled={!itemPermission}>
                    {i.label}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
      </div>

      <div className="mb-2 text-base font-bold">{i18n.t('global.sharedAntiTheftConfig')}</div>

      <PowerTable
        initialized
        rowKey="targetPartnerId"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        defaultPageSize={20}
        settingToolVisible
        pagination={false}
        autoLoad
        loading={loading}
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          dataSource,
        }}
        refreshBtnVisible={false}
        rightToolbar={[
          itemPermission && (
            <Button
              className="ml-4"
              icon={<AddFillIcon className="fill-white" />}
              type="primary"
              onClick={openPartnerSelectorModal}
            >
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
      />

      <PartnerSettingDrawer current={recordItem.current} open={partnerSettingVisible} onSubmit={settingOnSubmit} />

      <PartnerSelectorModal
        partnerType={partnerRecord?.type}
        onOk={partnerSelectorModalOnOk}
        modalProps={{
          open: partnerSelectorModalVisible,
          onCancel: partnerSelectorModalOnCancel,
        }}
        multiple
        defaultSelected={defaultSelected}
      />
    </div>
  );
};

export default AntiTheftConfigTable;
