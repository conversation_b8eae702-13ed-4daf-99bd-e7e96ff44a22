import * as BfobOrderApi from 'common/api/factory/BfobOrder';
import * as FobOrderApi from 'common/api/factory/FobOrder';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback, useContext } from 'react';

export const useCommonFn = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const confirm = useCallback(
    async (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode,
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BfobOrderApi.Confirm({ id: orderId });
              } else {
                await FobOrderApi.Confirm({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  return { confirm };
};
