import { Tabs } from 'antd';
import i18n from 'common/utils/I18n';
import React, { useImperativeHandle, useRef, useState } from 'react';

import PermissionPanel, { IPermissionPanelPropsRef } from './PermissionPanel';

import styles from './Permission.module.css';

interface PermissionPropsRef {
  recover: () => void;
}
export interface PermissionProps {
  current?: Record<string, any>;
  onChange?: (value: Record<string, any>) => void;
  innerRef?: React.MutableRefObject<PermissionPropsRef | undefined>;
}
const Permission: React.FC<PermissionProps> = (props) => {
  const { current, onChange, innerRef } = props;
  const [selectedKeysGroupByClient, setSelectedKeysGroupByClient] = useState<any>({});
  const permissionPanelRefObj = useRef<Record<string, IPermissionPanelPropsRef>>({});
  const [activeKey, setActiveKey] = useState('WEB');

  const clientTypeList = [
    {
      code: 'WEB',
      name: i18n.t('global.web'),
    },
    {
      code: 'ANDROID',
      name: i18n.t('global.android'),
    },
    {
      code: 'WINDOWS',
      name: i18n.t('global.windows'),
      disabled: true,
    },
    {
      code: 'GLOBAL',
      name: i18n.t('global.globals'),
    },
  ];

  const permissionTreeOnCheckChange = (client, subClient, rootCode, allSelectedKeys) => {
    const selectedKeysGroupByClientCopied = JSON.parse(JSON.stringify(selectedKeysGroupByClient));
    if (!selectedKeysGroupByClientCopied[client]) {
      selectedKeysGroupByClientCopied[client] = {
        [subClient]: allSelectedKeys,
      };
    } else {
      selectedKeysGroupByClientCopied[client][subClient] = allSelectedKeys;
    }
    if (onChange) onChange(selectedKeysGroupByClientCopied);
    setSelectedKeysGroupByClient(selectedKeysGroupByClientCopied);
  };

  useImperativeHandle(innerRef, () => ({
    recover: () => {
      setActiveKey('WEB');
      Object.values(permissionPanelRefObj.current).forEach((ref) => {
        if (ref && ref.recover) ref.recover();
      });
    },
  }));

  return (
    <div className={styles.rolePermissionDrawer}>
      <div className="drawer-inner-box">
        <Tabs
          defaultActiveKey="1"
          tabPosition="left"
          animated={false}
          activeKey={activeKey}
          onChange={(e) => setActiveKey(e)}
          items={clientTypeList.map((item) => {
            return {
              label: <span>{item.name}</span>,
              key: item.code,
              children: (
                <PermissionPanel
                  innerRef={(ref) => {
                    permissionPanelRefObj.current[item.code] = ref;
                  }}
                  roleId={current?.id}
                  client={item.code}
                  onCheckChange={(subClient, rootCode, allSelectedKeys) => {
                    permissionTreeOnCheckChange(item.code, subClient, rootCode, allSelectedKeys);
                  }}
                />
              ),
            };
          })}
        />
      </div>
    </div>
  );
};

export default Permission;
