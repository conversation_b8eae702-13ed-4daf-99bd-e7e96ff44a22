import * as <PERSON><PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON> from 'common/api/factory/BfibOrder';
import * as FibOrder<PERSON><PERSON> from 'common/api/factory/FibOrder';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, { PowerTableColumnsType, SearchFieldsConfig } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useState } from 'react';

interface IBoxRfidsTableProps {
  orderId: string;
  powerTableRef: any;
}

const BoxRfidsTable: React.FC<IBoxRfidsTableProps> = (props) => {
  const { orderId, powerTableRef } = props;
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewModalLoading, setBoxDetailsPreviewModalLoading] = useState<boolean>(false);
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);

  const boxCodeOnClick = async (record) => {
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewModalLoading(true);
    setCurrentBoxCode(record.boxCode);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BfibOrderApi.Rfids({
          boxId: record.boxId,
          fibOrderId: orderId,
          enablePage: false,
        });
      } else {
        result = await FibOrderApi.Rfids({
          boxId: record.boxId,
          fibOrderId: orderId,
          enablePage: false,
        });
      }
      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewModalLoading(false);
  };

  const fetchData = useCallback(
    async (params: Record<string, any>) => {
      let result = { data: [] };
      try {
        if (currentUser.mode === 'BINDING') {
          result = await BfibOrderApi.Boxs({
            ...params,
            fibOrderId: orderId,
          });
        } else {
          result = await FibOrderApi.Boxs({
            ...params,
            fibOrderId: orderId,
          });
        }
      } catch (e) {}
      return result;
    },
    // eslint-disable-next-line
    [orderId],
  );

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <SearchInput placeholder={i18n.t('global.boxCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.box'),
      dataIndex: 'boxCode',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      valueType: 'status',
      statusConfig: {
        NEW: { status: 'Red', desc: i18n.t('global.newBox') },
        FINISHED: { status: 'Green', desc: i18n.t('global.finished') },
      },
      minWidth: 150,
      auto: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        request={fetchData}
        pagination
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              boxCodeOnClick(record);
            },
          }),
        }}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewModalLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxRfidsTable;
