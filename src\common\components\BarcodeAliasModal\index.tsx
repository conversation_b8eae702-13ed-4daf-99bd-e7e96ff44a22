import { faArrowDown, faArrowUp } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Modal, Typography, Image } from 'antd';
import * as FileApi from 'common/api/file/Img';
import Button from 'common/components/Button';
import AliasViewer from 'common/components/AliasViewer';
import PowerTable, { IPowerTableInnerRef, PowerTableColumnsType } from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const { Text } = Typography;

interface IBarcodeAliasModalProps {
  open: boolean;
  data: any[];
  selectRowFiledId: string;
  onOk?: (selectedRows: Record<string, any>[]) => void;
  onCancel?: () => void;
}

const BarcodeAliasModal: React.FC<IBarcodeAliasModalProps> = (props) => {
  const { open, data, selectRowFiledId, onOk, onCancel } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<string, any>[]>([]);
  const selectKey = useRef<string>();
  const isEventListenerReady = useRef<boolean>(false);

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.image'),
      width: 100,
      dataIndex: 'source',
      render: (text) => (
        <Image
          preview={
            text
              ? {
                  src: FileApi.Get(text),
                }
              : false
          }
          src={text ? FileApi.Get(text, 80, 80) : `img/noImage.svg`}
        />
      ),
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      width: 150,
    },
    {
      title: i18n.t('global.alias'),
      dataIndex: 'alias',
      align: 'center',
      width: 100,
      render: (value: string, record) => (
        <AliasViewer aliasData={(Array.isArray(record.alias) ? record.alias : record.aliasArr) || []} />
      ),
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 120,
    },
    {
      title: i18n.t('global.brand'),
      dataIndex: 'brandCode',
      codeDataIndex: 'brandCode',
      nameDataIndex: 'brandName',
      valueType: 'codeName',
      width: 120,
    },
    {
      title: i18n.t('global.color'),
      dataIndex: 'colorCode',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      valueType: 'codeName',
      width: 80,
    },
    {
      title: i18n.t('global.sizes'),
      dataIndex: 'sizeCode',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      valueType: 'codeName',
      width: 80,
    },
    {
      title: i18n.t('global.specs'),
      dataIndex: 'specCode',
      codeDataIndex: 'specCode',
      nameDataIndex: 'specName',
      valueType: 'codeName',
      width: 80,
    },
  ];

  const onSelect = (record) => {
    setSelectedRowKeys([record[selectRowFiledId]]);
    setSelectedRows([record]);
  };

  const handleOk = () => {
    if (onOk) {
      onOk(selectedRows);
    }
  };

  const handleClear = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    selectKey.current = '';
    isEventListenerReady.current = false;
  };

  const handleCancel = () => {
    handleClear();
    if (onCancel) {
      onCancel();
    }
  };

  const setSelectRow = useCallback(
    (record) => {
      setSelectedRows([record]);
      setSelectedRowKeys([record[selectRowFiledId]]);
      selectKey.current = record[selectRowFiledId];
    },
    [selectRowFiledId],
  );

  const onKeyDown = useCallback(
    (e) => {
      if (!data || !isEventListenerReady.current) return;

      e.stopPropagation();

      // 向上
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        const index = data.findIndex((n) => n[selectRowFiledId] === selectKey.current);
        if (index === 0) {
          const item = data.slice(-1)[0];
          // 当前选择为第一个时，按上键，选中最后一个
          setSelectRow(item);
          return;
        }
        // 当前选中为最后一个时，按上键，往上选择一个
        const item = data[index - 1];
        setSelectRow(item);
      }

      // 向下
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        const index = data.findIndex((n) => n[selectRowFiledId] === selectKey.current);
        // 当前选中为最后一个时，按下键，选中第一个
        if (index === data.length - 1) {
          setSelectRow(data[0]);
          return;
        }

        // 其他情况时，往后选择一个
        const item = data[index + 1];
        setSelectRow(item);
      }

      // 回车
      if (e.key === 'Enter') {
        e.preventDefault();
        const item = data.find((n) => n[selectRowFiledId] === selectKey.current);
        if (onOk && item) {
          onOk([item]);
          handleClear();
        }
      }
    },
    [data, onOk, selectRowFiledId, setSelectRow],
  );

  useEffect(() => {
    if (open) {
      // 当模态框打开且有数据时，自动选中第一条记录
      if (data && data.length > 0) {
        const firstItem = data[0];
        setSelectRow(firstItem);
      }

      document.addEventListener('keydown', onKeyDown);

      return () => {
        document.removeEventListener('keydown', onKeyDown);
      };
    }
    // 模态框关闭时清理状态
    handleClear();
  }, [open, onKeyDown, data, setSelectRow]);

  useEffect(() => {
    if (open && data && data.length > 0) {
      // 使用 requestAnimationFrame 来确保在下一个渲染周期激活事件监听器
      const frameId = requestAnimationFrame(() => {
        isEventListenerReady.current = true;
      });

      return () => {
        cancelAnimationFrame(frameId);
      };
    }
  }, [open, data]);

  return (
    <Modal
      width={1000}
      title={i18n.t('global.selectBarCode')}
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={
        <div className="flex items-center justify-between">
          <div>
            <Button type="primary" onClick={handleOk}>
              {i18n.t('global.ok')}
            </Button>
            <Button onClick={handleCancel}>{i18n.t('global.cancel')}</Button>
          </div>
          <div>
            <Text keyboard style={{ background: '#f1f3f5', color: '#8c8c8c' }}>
              Enter
            </Text>
            <span className="mr-4">{i18n.t('global.select')}</span>
            <Text keyboard style={{ background: '#f1f3f5', color: '#8c8c8c' }}>
              <FontAwesomeIcon icon={faArrowUp} />
            </Text>
            <Text keyboard style={{ background: '#f1f3f5', color: '#8c8c8c' }}>
              <FontAwesomeIcon icon={faArrowDown} />
            </Text>
            <span className="mr-4">{i18n.t('global.switch')}</span>
            <Text keyboard style={{ background: '#f1f3f5', color: '#8c8c8c' }}>
              esc
            </Text>
            <span className="mr-4">{i18n.t('global.close')}</span>
          </div>
        </div>
      }
    >
      <PowerTable
        initialized
        rowKey={selectRowFiledId}
        columns={columns}
        innerRef={powerTableRef}
        refreshBtnVisible={false}
        tableProps={{
          dataSource: data || [],
          size: 'small',
          scroll: {
            x: 0,
            y: 500,
          },
          rowSelection: {
            type: 'radio',
            selectedRowKeys,
            onSelect,
          },
          onRow: (record) => ({
            onClick: () => {
              onSelect(record);
            },
          }),
          rowKey: selectRowFiledId,
        }}
        onRowSelectionChange={(selectedRowKeys, selectedRows) => {
          setSelectedRowKeys(selectedRowKeys);
          setSelectedRows(selectedRows);
        }}
        style={{ padding: 0 }}
      />
    </Modal>
  );
};

export default BarcodeAliasModal;
