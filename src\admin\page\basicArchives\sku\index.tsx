import SkuPriceCard from 'admin/page/basicArchives/sku/components/SkuPriceCard';
import { DatePicker, Image, Input } from 'antd';
import * as ExtFieldApi from 'common/api/core/ExtField';
import * as Sku<PERSON>pi from 'common/api/core/Sku';
import * as <PERSON><PERSON><PERSON> from 'common/api/file/Img';
import AliasViewer from 'common/components/AliasViewer';
import PowerTable, { IPowerTableInnerRef, SearchFieldsConfig } from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const Sku: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.barcode'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'barcode',
      label: i18n.t('global.barcode'),
      inputComponent: <Input />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: any = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.image'),
      width: 100,
      dataIndex: 'source',
      render: (text) => (
        <Image
          preview={
            text
              ? {
                  src: FileApi.Get(text),
                }
              : false
          }
          src={text ? FileApi.Get(text, 100, 100) : 'img/noImage.svg'}
        />
      ),
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      sorter: 'true',
      width: 180,
      ellipsis: true,
    },
    {
      title: i18n.t('global.alias'),
      dataIndex: 'alias',
      align: 'center',
      width: 100,
      render: (value: string, record) => <AliasViewer aliasData={record.alias} />,
    },
    {
      title: i18n.t('global.skuName'),
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.sourceBarcode'),
      dataIndex: 'barcodeSourceCode',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.productName'),
      dataIndex: 'prodName',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.sourceNumber'),
      dataIndex: 'prodSourceCode',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.brand'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'brandCode',
      nameDataIndex: 'brandName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.color'),
      width: 130,
      valueType: 'codeName',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.size'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.spec'),
      width: 150,
      valueType: 'codeName',
      codeDataIndex: 'specCode',
      nameDataIndex: 'specName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.gender'),
      width: 120,
      valueType: 'codeName',
      codeDataIndex: 'genderCode',
      nameDataIndex: 'genderName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.priCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'priCategoryCode',
      nameDataIndex: 'priCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.subCategory'),
      width: 200,
      valueType: 'codeName',
      codeDataIndex: 'subCategoryCode',
      nameDataIndex: 'subCategoryName',
      ellipsis: true,
      enableWrap: true,
    },
    {
      title: i18n.t('global.retailPrice'),
      dataIndex: 'retailPrice',
      valueType: 'amount',
      width: 200,
      ellipsis: true,
    },
    {
      title: i18n.t('global.tagPrice'),
      dataIndex: 'tagPrice',
      valueType: 'amount',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.weight'),
      dataIndex: 'weight',
      valueType: 'number',
      width: 150,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const [columns, setColumns] = useState<any>(tableColumns);

  const fetchData = useCallback(async (params: Record<string, any>) => {
    if (params.barcode) {
      params.barcode = params.barcode.trim();
    }
    if (params.prodCode) {
      params.prodCode = params.prodCode.trim();
    }
    if (params.createDateRange) {
      params.createdStart = params.createDateRange[0].startOf('day');
      params.createdEnd = params.createDateRange[1].endOf('day');
    }
    delete params.createDateRange;
    let data: any = [];
    try {
      data = await SkuApi.List(params);
      data.data.forEach((item) => {
        Object.keys(item.prodExt).forEach((n) => {
          item[`prodExt-${n}`] = item.prodExt[n];
        });
      });
    } catch (e) {}
    return data;
  }, []);

  const [addColumnsWidth, setAddColumnsWidth] = useState<number>(0);
  const getExtField = async () => {
    const extFieldData = await ExtFieldApi.List({
      disabled: false,
      enablePage: false,
      type: 'PROD',
    });
    const addColumns: any = [];
    extFieldData.data.forEach((n) => {
      addColumns.push({
        title: n.name,
        dataIndex: `prodExt-${n.code}`,
        width: 140,
        ellipsis: true,
      });
    });
    setAddColumnsWidth(extFieldData.data.length * 140);
    setColumns(tableColumns.slice(0, -1).concat(addColumns).concat(tableColumns.slice(-1)));
  };

  useEffect(() => {
    getExtField();
    // eslint-disable-next-line
  }, []);

  const expandedRowRender = (record: Record<string, any>) => {
    return (
      <div className="flex flex-wrap gap-4">
        {record.skuPartnerPriceVOList &&
          record.skuPartnerPriceVOList.length > 0 &&
          record.skuPartnerPriceVOList.map((item) => <SkuPriceCard current={item} />)}
      </div>
    );
  };

  return (
    <div>
      <AppHeader />
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="SKU"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
          scroll: { x: 2480 + addColumnsWidth },
          expandable: {
            expandedRowRender,
            rowExpandable: (record) => record.skuPartnerPriceVOList && record.skuPartnerPriceVOList.length !== 0,
          },
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
    </div>
  );
};

export default Sku;
