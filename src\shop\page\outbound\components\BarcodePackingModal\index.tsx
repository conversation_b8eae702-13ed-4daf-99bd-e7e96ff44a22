import { Col, Row, Space, Spin } from 'antd';
import { ModalProps } from 'antd/es/modal';
import * as <PERSON>ku<PERSON><PERSON> from 'common/api/core/Sku';
import * as BsobOrder<PERSON>pi from 'common/api/shop/BsobOrder';
import * as Sob<PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/api/shop/SobOrder';
import ExchangeIcon from 'common/assets/icons/icon-exchange.svg?react';
import BarcodeInput from 'common/components/BarcodeInput';
import Button from 'common/components/Button';
// import BoxSpecSelect from 'common/components/BoxSpecSelect';
import FlashStatistic from 'common/components/FlashStatistic';
import Modal from 'common/components/Modal';
import useSetting from 'common/hooks/useSetting';
import useSpeech from 'common/hooks/useSpeech';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import { offWindowBeforeUnload, onWindowBeforeUnload } from 'common/utils';
import i18n from 'common/utils/I18n';
// import * as LocalStorageUtil from 'common/utils/LocalStorage';
import * as NoticeUtil from 'common/utils/Notice';
import BarcodeAliasModal from 'common/components/BarcodeAliasModal';
import useSearchAlias from 'common/components/BarcodeAliasModal/hook/useSearchAlias';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { ITableBaseItem } from '../../data';
import DataViewTable from './DataViewTable';
import ErrorHandlerModal from './ErrorHandleModal';

// const { Text } = Typography;

// const boxSpecStorageKey = 'OUTBOUND-BOX-SPEC';

interface IBarcodeOutboundModalProps {
  sobOrderId?: string;
  localTag: boolean;
  modalProps?: ModalProps;
  onOk?: () => void;
  onOperateModeChange: () => void;
  sobOrderBoxStatus: string;
  sobOrderBoxId: string;
  sobOrderBoxCode: string;
  innerRef: any;
}

// 数据结构
interface ITableDataItem extends ITableBaseItem {
  barcode: string;
  qty: number;
  operateQty: number;
  availableQty: number;
  actQty: number;
  alias?: any[];
  source?: string;
  brandName?: string;
}

interface INormalDataItem {
  barcode: string;
  qty: number;
}

interface IErrorDataItem {
  barcode: string;
  qty: number;
  operateQty: number;
  msg: string;
}

const BarcodeOutboundModal: React.FC<IBarcodeOutboundModalProps> = (props) => {
  const {
    innerRef,
    sobOrderId,
    modalProps,
    onOk,
    onOperateModeChange,
    sobOrderBoxStatus,
    sobOrderBoxId,
    sobOrderBoxCode,
    localTag,
  } = props;
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [tableData, setTableData] = useState<ITableDataItem[]>([]);
  const [normalData, setNormalData] = useState<INormalDataItem[]>([]);
  // const defaultBoxSpec: string = LocalStorageUtil.getItem(boxSpecStorageKey);
  const [skuFetching, setSkuFetching] = useState(false);
  const [errorData, setErrorData] = useState<IErrorDataItem[]>([]);
  const [errorHandleModalVisible, setErrorHandleModalVisible] = useState<boolean>(false);
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const isOperate = useRef(false);
  const normalQtyRef = useRef<any>();
  const errorQtyRef = useRef<any>();
  const barcodeInputRef = useRef<any>();

  const {
    barcodeAliasModalOpen,
    barcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  } = useSearchAlias();

  const {
    ENABLE_BARCODE_SCAN_CUTOFF: enableBarcodeScanCutoff,
    BARCODE_CUTOFF_BEFORE_DIGITS: barcodeCutoffBeforeDigits,
    BARCODE_CUTOFF_AFTER_DIGITS: barcodeCutoffAfterDigits,
    SOB_BARCODE_PACKING_THRESHOLD: barcodePackingThreshold,
  } = useSetting([
    { code: 'ENABLE_BARCODE_SCAN_CUTOFF', valueType: 'BOOLEAN' },
    { code: 'BARCODE_CUTOFF_BEFORE_DIGITS', valueType: 'NUMBER' },
    { code: 'BARCODE_CUTOFF_AFTER_DIGITS', valueType: 'NUMBER' },
    { code: 'SOB_BARCODE_PACKING_THRESHOLD', valueType: 'NUMBER' },
  ]);

  const { playSound, stopSound } = useSpeech();

  const playAndNotify = useCallback(
    (text: string, type: 'success' | 'error' | 'warn' | 'info', val?: string) => {
      NoticeUtil[type](val ? `${text} ${val}` : text);
      playSound({ text });
    },
    [playSound],
  );

  const baseItem = (data?) => ({
    prodCode: data?.prodCode || '',
    colorCode: data?.colorCode || '',
    colorName: data?.colorName || '',
    sizeCode: data?.sizeCode || '',
    sizeName: data?.sizeName || '',
    skuName: data?.skuName || '',
    specCode: data?.specCode || '',
    specName: data?.specName || '',
  });

  useImperativeHandle(innerRef, () => ({
    getIsOperate: () => isOperate.current,
  }));

  const operateMode = 'SKU';

  const screenReaderDataByRoles = async (inputBarcode, size, tableIndex) => {
    if (skuFetching) {
      const text = i18n.t('global.lastBarcodeProcessingTips');
      playAndNotify(text, 'warn');
      return;
    }

    // 当找到记录时，使用表格中的实际条码；否则使用输入的条码
    const barcode = tableIndex !== -1 ? tableData[tableIndex].barcode : inputBarcode;

    if (tableIndex !== -1 && tableData[tableIndex].rfidTag) {
      const text = i18n.t('global.prodOnlyForRFID');
      playAndNotify(text, 'warn');
      return;
    }
    const operateSize = size;
    const normalQty = normalData.find((n) => n.barcode === barcode)?.qty || 0;
    const pushNormalData = (count) => {
      const index = normalData.findIndex((n) => n.barcode === barcode);
      if (index !== -1) {
        normalData[index].qty = count;
        if (count === 0) {
          normalData.splice(index, 1);
        }
      } else if (count !== 0) {
        normalData.push({ barcode, qty: count });
      }

      if (tableData[tableIndex].qty === 0 && count === 0) {
        tableData.splice(tableIndex, 1);
      } else {
        // tableData[tableIndex].availableQty -= operateSize;
        tableData.unshift(...tableData.splice(tableIndex, 1));
      }
      setTableData(JSON.parse(JSON.stringify(tableData)));
      setNormalData(JSON.parse(JSON.stringify(normalData)));
      isOperate.current = true;
      const text = i18n.t('global.scanSuccess');
      playAndNotify(text, 'success');
    };

    const addTableItem = async () => {
      const hide = NoticeUtil.loading(i18n.t('global.loading'), 0);
      setSkuFetching(true);
      try {
        const res: any = await SkuApi.FindByAlias({
          alias: [inputBarcode],
        });
        searchAliasByBarcode({
          barcode: inputBarcode,
          operateSize,
          searchData: res.data,
          callback(foundBarcode, operateSize, index) {
            if (index === -1) {
              const text = i18n.t('global.notFoundBarcode');
              playAndNotify(text, 'warn', `[${inputBarcode}]`);
            } else {
              const barcodeItem = res.data[index];
              if (barcodeItem.rfidTag) {
                const text = i18n.t('global.prodOnlyForRFID');
                playAndNotify(text, 'warn');
                setSkuFetching(false);
                hide();
                return;
              }
              // 使用 barcodeItem.barcode 作为实际条码
              const actualBarcode = barcodeItem.barcode;
              tableData.unshift({
                barcode: actualBarcode,
                ...baseItem(barcodeItem),
                skuName: barcodeItem.name,
                qty: barcodeItem.qty,
                availableQty: Infinity,
                operateQty: operateSize,
                rfidTag: barcodeItem.rfidTag,
                disturbTag: barcodeItem.disturbTag,
                actQty: 0,
              });
              normalData.push({ barcode: actualBarcode, qty: operateSize });
              setTableData(JSON.parse(JSON.stringify(tableData)));
              setNormalData(JSON.parse(JSON.stringify(normalData)));
              const text = i18n.t('global.scanSuccess');
              playAndNotify(text, 'success');
            }
          },
        });
      } catch (e) {}
      hide();
      setSkuFetching(false);
      isOperate.current = true;
    };

    if (operateSize > 0) {
      if (localTag) {
        if (tableIndex !== -1) {
          pushNormalData(normalQty + operateSize);
        } else {
          addTableItem();
        }
      } else if (tableIndex !== -1) {
        if (operateSize + normalQty - tableData[tableIndex].actQty > tableData[tableIndex].availableQty) {
          const text = i18n.t('global.readedQtyExcessRemainingQty');
          playAndNotify(text, 'warn', `[${barcode}]`);
        } else {
          pushNormalData(normalQty + operateSize);
        }
      } else {
        const text = i18n.t('global.barcodeNotInOrder');
        playAndNotify(text, 'warn', `[${barcode}]`);
      }
    } else if (normalQty + operateSize < 0) {
      const text = i18n.t('global.deleteQtyExcess');
      playAndNotify(text, 'warn', `[${barcode}]`);
    } else {
      pushNormalData(normalQty + operateSize);
    }
  };

  const fetchSobOrderLineData = useCallback(async () => {
    const data: ITableDataItem[] = [];
    setLoading(true);
    try {
      let result;
      if (currentUser.mode === 'BINDING') {
        result = await BsobOrderApi.Lines({
          enablePage: false,
          sobOrderId,
        });
      } else {
        result = await SobOrderApi.Lines({
          enablePage: false,
          sobOrderId,
        });
      }

      result.data.forEach((item) => {
        data.push({
          barcode: item.barcode,
          qty: item.qty,
          operateQty: 0,
          ...baseItem(item),
          availableQty: item.qty ? item.qty - item.actQty : 0,
          actQty: 0,
        });
      });
    } catch (e) {}
    setLoading(false);
    return data || [];
    // eslint-disable-next-line
  }, [sobOrderId]);

  const fetchBoxRfidsData = useCallback(
    async (data) => {
      const tableData = data;
      setLoading(true);
      try {
        let res;
        if (currentUser.mode === 'BINDING') {
          res = await BsobOrderApi.Rfids({
            sobBoxId: sobOrderBoxId,
            sobOrderId,
            enablePage: false,
          });
        } else {
          res = await SobOrderApi.Rfids({
            sobBoxId: sobOrderBoxId,
            sobOrderId,
            enablePage: false,
          });
        }

        if (res.data.length > 0) {
          const normalData: any[] = [];
          res.data.forEach((item) => {
            const tableDataItem = tableData.find((n) => n.barcode === item.barcode);
            normalData.push({ barcode: item.barcode, qty: item.qty });
            if (tableDataItem) {
              tableDataItem.operateQty = item.qty;
              tableDataItem.actQty = item.qty;
            } else {
              tableData.push({
                barcode: item.barcode,
                qty: 0,
                operateQty: item.qty,
                ...baseItem(item),
                availableQty: item.qty ? item.qty - item.actQty : 0,
                actQty: item.actQty,
                notInRange: false,
              });
            }
          });
          setNormalData(normalData);
        }
      } catch (e) {}
      setLoading(false);
      return tableData || [];
    },
    // eslint-disable-next-line
    [sobOrderBoxId, sobOrderId],
  );

  const resetBtnOnClick = () => {
    const text = i18n.t('global.confirmDiscard');
    playSound({ text });
    NoticeUtil.confirm({
      content: text,
      onOk: () => {
        setNormalData([]);
        stopSound();
        barcodeInputRef.current?.reset();
      },
    });
  };

  const barcodeInputOnPressEnter = (barcode, operateSize) => {
    if (!operateSize) {
      const text = i18n.t('global.pleaseInputOperateQty');
      playAndNotify(text, 'warn');
      return;
    }
    if (operateSize === 0) {
      const text = i18n.t('global.operateQtyError');
      playAndNotify(text, 'warn');
      return;
    }
    if (barcode) {
      searchAliasByBarcode({ barcode, searchData: tableData, operateSize, callback: screenReaderDataByRoles });
    }
  };

  const saveBtnOnClick = async () => {
    try {
      setSaving(true);
      if (currentUser.mode === 'BINDING') {
        await BsobOrderApi.BoxSave({
          data: normalData,
          sobOrderId,
          operateMode,
          boxCode: sobOrderBoxCode,
        });
      } else {
        await SobOrderApi.BoxSave({
          data: normalData,
          sobOrderId,
          operateMode,
          boxCode: sobOrderBoxCode,
        });
      }

      const text = i18n.t('global.saveSuccess');
      playAndNotify(text, 'success');
      if (onOk) onOk();
    } catch (err: any) {
      let responseData: any[] = [];
      if (err.response?.data?.detailMsg) {
        responseData = JSON.parse(err.response.data.detailMsg);
      }
      if (responseData.length > 0) {
        const text = i18n.t('global.pleaseHandleOverAndError');
        playAndNotify(text, 'warn');
        const responseErrorData: any[] = responseData.filter(
          (item) => item.type === 'CROSS_ERROR' || item.type === 'OTHER_ERROR',
        );
        const newErrorData: IErrorDataItem[] = [];
        responseErrorData.forEach((item) => {
          newErrorData.push({
            barcode: item.barcode,
            qty: item.errQty,
            operateQty: 0,
            msg: item.message,
          });
        });
        setErrorData(newErrorData);

        responseErrorData.forEach((item) => {
          const normalDataItem = normalData.find((n) => n.barcode === item.barcode);
          if (normalDataItem) {
            normalDataItem.qty -= item.errQty;
          }
        });
        setNormalData(JSON.parse(JSON.stringify(normalData)));
      }
    }
    setSaving(false);
  };

  const fetchSkuInfoByBarcodeList = async (data) => {
    const tableData: ITableDataItem[] = data;
    if (tableData.length > 0) {
      const barcodeList = tableData.map((n) => n.barcode);
      try {
        const result: any = await SkuApi.FindByBarcodeList({
          barcodeList,
        });
        result.forEach((item) => {
          const tableItem = tableData.find((n) => n.barcode === item.barcode);
          if (tableItem) {
            tableItem.alias = item.alias;
            tableItem.source = item.source;
            tableItem.brandName = item.brandName;

            tableItem.disturbTag = item.disturbTag;
            tableItem.rfidTag = item.rfidTag;
          }
        });
      } catch (e) {}
    }
    return tableData;
  };

  const errorHandlerModalOnDelete = (data) => {
    data.forEach((item) => {
      if (item.operateQty !== 0) {
        const errorDataIndex = errorData.findIndex((n) => n.barcode === item.barcode);
        errorData[errorDataIndex].qty -= item.operateQty;
        if (errorData[errorDataIndex].qty === 0) {
          errorData.splice(errorDataIndex, 1);
        }
      }
    });
    setErrorData(JSON.parse(JSON.stringify(errorData)));
    setErrorHandleModalVisible(false);
  };

  const initOrderData = async () => {
    let tableData: ITableDataItem[] = [];
    try {
      if (!localTag) {
        tableData = await fetchSobOrderLineData();
      }
      if (sobOrderBoxId) {
        tableData = await fetchBoxRfidsData(tableData);
      }
      tableData = await fetchSkuInfoByBarcodeList(tableData);
      setTableData(tableData);
    } catch (e) {}
  };

  useEffect(() => {
    if (modalProps?.open === true) {
      initOrderData();
      onWindowBeforeUnload();
    } else {
      offWindowBeforeUnload();

      isOperate.current = false;
      setNormalData([]);
      setTableData([]);
      setLoading(false);
      setSaving(false);
      stopSound();
    }
    // eslint-disable-next-line
  }, [modalProps?.open]);

  useEffect(() => {
    if (normalData.length > 0) {
      normalQtyRef.current?.remind();
    }
    tableData.forEach((item) => {
      item.operateQty = normalData.find((n) => item.barcode === n.barcode)?.qty || 0;
    });
    setTableData(tableData.filter((n) => !(n.qty === 0 && n.operateQty === 0)));
    // eslint-disable-next-line
  }, [normalData]);

  useEffect(() => {
    if (errorData.length > 0) {
      errorQtyRef.current?.remind();
    }
  }, [errorData]);

  useEffect(() => {
    if (errorData.length > 0) {
      const text = i18n.t('global.detectedErrorMsg', { errorQty: errorData.length });
      playAndNotify(text, 'warn');
    }
    // eslint-disable-next-line
  }, [errorData, playAndNotify]);

  const handleBarcodeAliasModalOnOk = (selectedRows) => {
    barcodeAliasModalOnOk({ selectedRows, searchData: tableData, callback: screenReaderDataByRoles });
  };

  const modalTitle = (
    <div className="flex h-6">
      <span>
        {i18n.t('global.barcodePacking')}
        {sobOrderBoxCode ? ` [${sobOrderBoxCode}]` : ''}
      </span>
      {sobOrderBoxStatus === 'NEW' && (
        <Button
          type="ghost"
          style={{ marginLeft: 20 }}
          icon={<ExchangeIcon className="fill-lead-slate" />}
          onClick={onOperateModeChange}
        />
      )}
    </div>
  );

  return (
    <Modal
      fullScreen
      centered
      title={modalTitle}
      footer={false}
      maskClosable={false}
      {...modalProps}
      onCancel={() => {
        const commonLogic = () => {
          const fakeMouseEvent: any = {};
          if (modalProps?.onCancel) {
            modalProps.onCancel(fakeMouseEvent);
          }
        };
        if (isOperate.current) {
          const text = i18n.t('global.confirmDiscard');
          playSound({ text });
          NoticeUtil.confirm({
            content: text,
            onOk: () => {
              commonLogic();
            },
          });
        } else {
          commonLogic();
        }
      }}
      className="operate-modal"
    >
      <Spin spinning={loading}>
        <div className="flex h-full flex-col gap-y-4">
          <DataViewTable
            className="flex-auto"
            leftTBar={
              <Space>
                <div>
                  <BarcodeInput
                    range={barcodePackingThreshold ? [-barcodePackingThreshold, barcodePackingThreshold] : undefined}
                    innerRef={barcodeInputRef}
                    onPressEnter={barcodeInputOnPressEnter}
                    enableBarcodeScanCutoff={enableBarcodeScanCutoff}
                    barcodeCutoffBeforeDigits={barcodeCutoffBeforeDigits}
                    barcodeCutoffAfterDigits={barcodeCutoffAfterDigits}
                  />
                </div>
              </Space>
            }
            localTag={localTag}
            data={tableData}
          />
          <Row justify="space-between" align="bottom">
            <Col>
              <Space>
                <Button
                  type="success"
                  size="large"
                  loading={saving}
                  onClick={saveBtnOnClick}
                  disabled={!(normalData.length > 0)}
                >
                  {i18n.t('global.save')}
                </Button>
                {normalData.length > 0 && (
                  <Button danger size="large" onClick={resetBtnOnClick}>
                    {i18n.t('global.reset')}
                  </Button>
                )}
              </Space>
            </Col>
            <Col>
              <Space>
                <FlashStatistic
                  type="success"
                  innerRef={normalQtyRef}
                  title={i18n.t('global.normal')}
                  value={normalData.reduce((prev, next) => prev + next.qty, 0)}
                />
                {errorData.length > 0 && (
                  <FlashStatistic
                    type="error"
                    innerRef={errorQtyRef}
                    title={i18n.t('global.exceptQty')}
                    value={errorData.reduce((prev, next) => prev + next.qty, 0)}
                    onClick={() => {
                      setErrorHandleModalVisible(true);
                    }}
                  />
                )}
              </Space>
            </Col>
          </Row>
        </div>
      </Spin>
      <ErrorHandlerModal
        data={errorData}
        onDelete={errorHandlerModalOnDelete}
        modalProps={{
          onCancel: () => setErrorHandleModalVisible(false),
          open: errorHandleModalVisible,
        }}
      />
      <BarcodeAliasModal
        open={barcodeAliasModalOpen}
        onCancel={barcodeAliasModalOnCancel}
        onOk={handleBarcodeAliasModalOnOk}
        data={barcodeAliasData}
        selectRowFiledId="barcode"
      />
    </Modal>
  );
};

export default BarcodeOutboundModal;
