import i18n from 'common/utils/I18n';

// STOCK_IN("入库"),
// STOCK_OUT("出库"),
// INVENTORY_STOCK("盘点"),
// LEND_OUT("借出"),
// RETURN("归还"),
// DISPOSE("处置"),
// RETAIL("零售"),
// BIND("绑定"),
// ANTI_THEFT_COLLECT("防盗采集");

export type BusinessType =
  | 'INVENTORY_STOCK'
  | 'STOCK_IN'
  | 'STOCK_OUT'
  | 'LEND_OUT'
  | 'RETURN'
  | 'DISPOSE'
  | 'RETAIL'
  | 'BIND'
  | 'ANTI_THEFT_COLLECT';

export const businessTypeOption: { label: string; value: BusinessType }[] = [
  { label: i18n.t('global.inventory'), value: 'INVENTORY_STOCK' },
  { label: i18n.t('global.inbound'), value: 'STOCK_IN' },
  { label: i18n.t('global.outbound'), value: 'STOCK_OUT' },
  { label: i18n.t('global.retail'), value: 'RETAIL' },
  { label: i18n.t('global.borrow'), value: 'LEND_OUT' },
  { label: i18n.t('global.return'), value: 'RETURN' },
  { label: i18n.t('global.antiTheftCollection'), value: 'ANTI_THEFT_COLLECT' },
  { label: i18n.t('global.bind'), value: 'BIND' },
];
