import { QuestionCircleOutlined } from '@ant-design/icons';
import { Checkbox, Col, Form, Input, InputNumber, Radio, Row, Switch, Tooltip } from 'antd';
import * as ConfigApi from 'common/api/core/Config';
import SaveButton from 'common/components/Button/Save';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useState } from 'react';
import { Trans } from 'react-i18next';

interface IShopConfig {
  form?: any;
  onOk?: () => void;
  onFailed?: () => void;
}

const ShopConfig: React.FC<IShopConfig> = (props) => {
  const onOk = props.onOk || (() => {});
  const onFailed = props.onFailed || (() => {});
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [data, setData] = useState<any[]>([]);
  const [formItemIsShowMap, setFormItemIsShowMap] = useState<Record<string, boolean>>({});

  const [permission] = usePermission('A:SETTING');
  const editPermission = permission.codes.includes('EDIT_S_S');

  const fetchCurrentConfig = useCallback(async () => {
    setLoading(true);
    try {
      const formValues: { [key: string]: any } = {};
      const resp: any = await ConfigApi.List({
        enablePage: false,
        configTypes: ['SHOP'],
      });
      setData(resp.data);
      const showMap: Record<string, boolean> = {};
      resp.data.forEach((item) => {
        showMap[item.code] = true;
        formValues[item.code] = item.value === 'true' || item.value === 'false' ? item.value === 'true' : item.value;
      });
      if (formValues.APP_S_ORDER_DELETE_STATUS) {
        formValues.APP_S_ORDER_DELETE_STATUS = formValues.APP_S_ORDER_DELETE_STATUS.split(',');
      }
      form.setFieldsValue(formValues);
      setFormItemIsShowMap(showMap);
    } catch (e) {}
    setLoading(false);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    fetchCurrentConfig();
    // eslint-disable-next-line
  }, []);

  const onFinish = async (values) => {
    if (values.APP_S_ORDER_DELETE_STATUS.length > 0) {
      values.APP_S_ORDER_DELETE_STATUS = values.APP_S_ORDER_DELETE_STATUS.toString();
    } else {
      values.APP_S_ORDER_DELETE_STATUS = '';
    }
    const payload: { data: { id: string; value: any }[] } = {
      data: [],
    };
    Object.keys(values).forEach((key) => {
      let val = values[key];
      let id = '';

      const item = data.find((n) => n.code === key);
      if (item) id = item.id;
      if (typeof val === 'boolean') val = val.toString();

      payload.data.push({
        id,
        value: val,
      });
    });

    setSaving(true);
    try {
      await ConfigApi.Update(payload);
      fetchCurrentConfig();
      NoticeUtil.success(i18n.t('global.saveSuccess'));
      onOk();
    } catch (e) {
      onFailed();
    }
    setSaving(false);
  };

  const options = [
    { label: i18n.t('global.newOrder'), value: 'NEW' },
    {
      label: i18n.t('global.processing'),
      value: 'PROCESSING',
    },
    {
      label: i18n.t('global.finished'),
      value: 'FINISHED',
    },
    { label: i18n.t('global.cancelled'), value: 'CANCELED' },
  ];

  return (
    <Form form={form} layout="vertical" onFinish={onFinish} onFinishFailed={onFailed}>
      {!loading ? (
        <>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.general')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_S_ORDER_DELETE_DAYS && (
                <Form.Item name="APP_S_ORDER_DELETE_DAYS" label={i18n.t('global.appSOrderDeleteDays')}>
                  <InputNumber min={0} />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_S_ORDER_DELETE_STATUS && (
                <Form.Item name="APP_S_ORDER_DELETE_STATUS" label={i18n.t('global.appSOrderDeleteStatus')}>
                  <Checkbox.Group options={options} />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_PROD_INFO_EXPRESSION && (
                <Form.Item
                  name="APP_PROD_INFO_EXPRESSION"
                  label={
                    <>
                      {i18n.t('global.appStoreProdInfoExpression')}
                      <Tooltip
                        title={
                          <Trans components={[<br />, <br />]}>
                            {i18n.t('global.appStoreProdInfoExpressionTooltip')}
                          </Trans>
                        }
                      >
                        <QuestionCircleOutlined className="ml-1 text-blue-500" />
                      </Tooltip>
                    </>
                  }
                >
                  <Input />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SHOP_NO_ORDER_TYPE_REQUIRED && (
                <Form.Item
                  name="SHOP_NO_ORDER_TYPE_REQUIRED"
                  label={i18n.t('global.shopNoOrderTypeRequired')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SI_ACT_QTY_LESS_NOT_CONFIRM && (
                <Form.Item
                  name="SI_ACT_QTY_LESS_NOT_CONFIRM"
                  label={i18n.t('global.inboundActualQtyLessThanOrderQtyNotAllowConfirm')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SO_ACT_QTY_LESS_NOT_CONFIRM && (
                <Form.Item
                  name="SO_ACT_QTY_LESS_NOT_CONFIRM"
                  label={i18n.t('global.outboundActualQtyLessThanOrderQtyNotAllowConfirm')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.inbound')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SI_RFID_POSITION_VALID_MODE && (
                <Form.Item name="SI_RFID_POSITION_VALID_MODE" label={i18n.t('global.SIRFIDPositionValidMode')}>
                  <Radio.Group className="flex flex-wrap gap-y-2">
                    <Radio value="ALLOW_REMIND">
                      {i18n.t('global.allowOutboundWithNotification')}{' '}
                      <Tooltip title={i18n.t('global.allowRemindTooltipStore')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="ALLOW_NOT_REMIND">
                      {i18n.t('global.allowOutboundWithoutNotification')}{' '}
                      <Tooltip title={i18n.t('global.allowNotRemindTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="NOT_ALLOW">
                      {i18n.t('global.outboundNotAllowed')}{' '}
                      <Tooltip title={i18n.t('global.notAllowTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SO_GENERATE_SI && (
                <Form.Item name="SO_GENERATE_SI" label={i18n.t('global.SOGenerateSI')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.WO_GENERATE_SI && (
                <Form.Item name="WO_GENERATE_SI" label={i18n.t('global.WOGenerateSI')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.FO_GENERATE_SI && (
                <Form.Item name="FO_GENERATE_SI" label={i18n.t('global.FOGenerateSI')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.APP_SI_CREATE_BOX && (
                <Form.Item name="APP_SI_CREATE_BOX" label={i18n.t('global.appSICreateBox')} valuePropName="checked">
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SIB_BARCODE_PACKING_THRESHOLD && (
                <Form.Item
                  label={i18n.t('global.inboundBarcodePackingOperationThreshold', {
                    client: i18n.t('global.shop'),
                  })}
                  name="SIB_BARCODE_PACKING_THRESHOLD"
                >
                  <InputNumber min={1} />
                </Form.Item>
              )}
            </Col>
          </Row>
          <Row gutter={24} className="m-0 justify-between">
            <Col span={24}>
              <div className="mb-6 text-xl font-bold">{i18n.t('global.outbound')}</div>
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SO_RFID_POSITION_VALID_MODE && (
                <Form.Item name="SO_RFID_POSITION_VALID_MODE" label={i18n.t('global.SORFIDPositionValidMode')}>
                  <Radio.Group className="flex flex-wrap gap-y-2">
                    <Radio value="ALLOW_REMIND">
                      {i18n.t('global.allowRemind')}{' '}
                      <Tooltip title={i18n.t('global.allowRemindTooltipStore')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="ALLOW_NOT_REMIND">
                      {i18n.t('global.allowNotRemind')}{' '}
                      <Tooltip title={i18n.t('global.allowNotRemindTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                    <Radio value="NOT_ALLOW">
                      {i18n.t('global.notAllow')}{' '}
                      <Tooltip title={i18n.t('global.notAllowTooltip')}>
                        <QuestionCircleOutlined className="text-blue-500" />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SO_SAVE_BOX_ENABLE_BOX_SPEC && (
                <Form.Item
                  label={
                    <>
                      {i18n.t('global.enableBoxSpec')}
                      <Tooltip title={<div>{i18n.t('global.outboundNewBoxSelectSpec')}</div>}>
                        <QuestionCircleOutlined className="ml-1 text-blue-500" />
                      </Tooltip>
                    </>
                  }
                  name="SO_SAVE_BOX_ENABLE_BOX_SPEC"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )}
            </Col>
            <Col span={11}>
              {formItemIsShowMap.SOB_BARCODE_PACKING_THRESHOLD && (
                <Form.Item
                  label={i18n.t('global.outboundBarcodePackingOperationThreshold', {
                    client: i18n.t('global.shop'),
                  })}
                  name="SOB_BARCODE_PACKING_THRESHOLD"
                >
                  <InputNumber min={1} />
                </Form.Item>
              )}
            </Col>
          </Row>
          {!props.form && (
            <Form.Item className="sticky bottom-0 mb-0 w-full bg-white py-5 pl-3">
              <SaveButton htmlType="submit" loading={saving} disabled={!editPermission} />
            </Form.Item>
          )}
        </>
      ) : (
        <div className="flex min-h-[theme('spacing.96')] items-center justify-center">
          <Spin />
        </div>
      )}
    </Form>
  );
};

export default ShopConfig;
