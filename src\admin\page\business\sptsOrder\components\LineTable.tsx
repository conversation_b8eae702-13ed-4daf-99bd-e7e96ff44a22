import PowerTable, { IPowerTableProps, PowerTableColumnsType } from 'common/components/PowerTable';
import useSetting from 'common/hooks/useSetting';
import i18n from 'common/utils/I18n';
import React from 'react';

interface ITsOrderLineTableProps extends Omit<IPowerTableProps, 'tableColumns'> {
  lineType: 'BY_PROD' | 'BY_SKU' | 'BY_TAG' | 'BY_ERROR' | 'BY_RECORD';
  showUniqueCode?: boolean;
}

const TsOrderLineTable: React.FC<ITsOrderLineTableProps> = (props) => {
  const { lineType, showUniqueCode } = props;
  const { SHOW_RFID_EXT_FLAG: showRfidExt } = useSetting([{ code: 'SHOW_RFID_EXT_FLAG', valueType: 'BOOLEAN' }]);
  let tableColumns: PowerTableColumnsType = [];

  if (lineType === 'BY_PROD') {
    tableColumns = [
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'smCode',
        sorter: true,
        width: 150,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: i18n.t('global.productName'),
        dataIndex: 'smName',
        ellipsis: true,
        minWidth: 200,
        auto: true,
      },
      {
        title: i18n.t('global.bankStorageAmount'),
        dataIndex: 'snapQty',
        sorter: true,
        valueType: 'number',
        width: 250,
      },
      {
        title: i18n.t('global.stockCount'),
        dataIndex: 'qty',
        sorter: true,
        valueType: 'number',
        width: 200,
      },
      {
        title: i18n.t('global.diffQty'),
        dataIndex: 'diffQty',
        sorter: true,
        valueType: 'number',
        width: 250,
      },
      {
        title: i18n.t('global.snapCost'),
        dataIndex: 'snapAmt',
        sorter: true,
        valueType: 'amount',
        width: 250,
      },
      {
        title: i18n.t('global.stockCost'),
        dataIndex: 'amt',
        sorter: true,
        valueType: 'amount',
        width: 200,
      },
      {
        title: i18n.t('global.diffCost'),
        dataIndex: 'diffAmt',
        sorter: true,
        valueType: 'amount',
        width: 200,
      },
    ];
  } else if (lineType === 'BY_SKU') {
    tableColumns = [
      {
        title: i18n.t('global.barcode'),
        dataIndex: 'smBarcode',
        sorter: true,
        width: 180,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'smCode',
        sorter: true,
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.productName'),
        dataIndex: 'smName',
        minWidth: 200,
        auto: true,
        ellipsis: true,
      },
      {
        title: i18n.t('global.color'),
        dataIndex: 'smColorName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.size'),
        dataIndex: 'smSizeName',
        width: 100,
        ellipsis: true,
      },
      // {
      //   title: i18n.t('global.spec'),
      //   dataIndex: 'specName',
      //   width: 250,
      //   ellipsis: true,
      // },
      {
        title: i18n.t('global.barcodeRetailPrice'),
        dataIndex: 'retailPrice',
        valueType: 'amount',
        width: 150,
      },
      {
        title: i18n.t('global.bankStorageAmount'),
        dataIndex: 'snapQty',
        sorter: true,
        valueType: 'number',
        width: 250,
      },
      {
        title: i18n.t('global.stockCount'),
        dataIndex: 'qty',
        sorter: true,
        valueType: 'number',
        width: 200,
      },
      {
        title: i18n.t('global.diffQty'),
        dataIndex: 'diffQty',
        sorter: true,
        valueType: 'number',
        width: 250,
      },
      {
        title: i18n.t('global.snapCost'),
        dataIndex: 'snapAmt',
        sorter: true,
        valueType: 'amount',
        width: 250,
      },
      {
        title: i18n.t('global.stockCost'),
        dataIndex: 'amt',
        sorter: true,
        valueType: 'amount',
        width: 200,
      },
      {
        title: i18n.t('global.diffCost'),
        dataIndex: 'diffAmt',
        sorter: true,
        valueType: 'amount',
        width: 200,
      },
    ];
  } else if (lineType === 'BY_TAG') {
    tableColumns = [
      {
        title: i18n.t('global.barcode'),
        dataIndex: 'smBarcode',
        sorter: true,
        width: 180,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: 'EPC',
        dataIndex: 'smEpc',
        sorter: true,
        auto: true,
        minWidth: 180,
        ellipsis: true,
        fixed: 'left',
        valueType: 'text',
        tooltip: true,
      },
      // {
      //   title: i18n.t('global.uniqueCode'),
      //   dataIndex: 'unicode',
      //   sorter: true,
      //   width: 180,
      //   ellipsis: true,
      //   fixed: 'left',
      //   valueType: 'text',
      //   tooltip: true,
      // },
      // {
      //   title: i18n.t('global.partner'),
      //   dataIndex: 'rfidExt',
      //   width: 180,
      //   ellipsis: true,
      //   render: (text, record) => (
      //     <Tooltip
      //       placement="topLeft"
      //       title={
      //         <>
      //           {record.rfidExt?.factoryCode}
      //           <br />
      //           {record.rfidExt?.factoryName}
      //         </>
      //       }
      //     >
      //       <Text title={record.rfidExt?.factoryName}>{record.rfidExt?.factoryName}</Text>
      //     </Tooltip>
      //   ),
      // },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'smCode',
        sorter: true,
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.color'),
        dataIndex: 'smColorName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.size'),
        dataIndex: 'smSizeName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.position'),
        valueType: 'codeName',
        codeDataIndex: 'positionCode',
        nameDataIndex: 'positionName',
        dataIndex: 'positionName',
        width: 250,
        ellipsis: true,
      },
      // {
      //   title: i18n.t('global.spec'),
      //   dataIndex: 'specName',
      //   width: 250,
      //   ellipsis: true,
      // },
    ];
  } else if (lineType === 'BY_ERROR') {
    tableColumns = [
      {
        title: i18n.t('global.barcode'),
        dataIndex: 'barcode',
        sorter: true,
        width: 180,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: 'EPC',
        dataIndex: 'epc',
        sorter: true,
        auto: true,
        minWidth: 200,
        ellipsis: true,
        fixed: 'left',
        valueType: 'text',
        tooltip: true,
      },
      {
        title: i18n.t('global.brand'),
        dataIndex: 'brandName',
        sorter: true,
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'smCode',
        sorter: true,
        width: 180,
        ellipsis: true,
      },
      {
        title: i18n.t('global.color'),
        dataIndex: 'colorName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.size'),
        dataIndex: 'sizeName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.spec'),
        dataIndex: 'specName',
        width: 250,
        ellipsis: true,
      },
      {
        title: i18n.t('global.count'),
        dataIndex: 'qty',
        width: 200,
        valueType: 'number',
      },
      {
        title: i18n.t('global.reason'),
        dataIndex: 'message',
        width: 250,
        ellipsis: true,
      },
    ];
  } else if (lineType === 'BY_RECORD') {
    tableColumns = [
      {
        valueType: 'index',
        fixed: 'left',
      },
      {
        title: i18n.t('global.barcode'),
        dataIndex: 'barcode',
        sorter: true,
        width: 180,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: i18n.t('global.productCode'),
        dataIndex: 'smCode',
        sorter: true,
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.productName'),
        dataIndex: 'smName',
        minWidth: 200,
        auto: true,
        ellipsis: true,
      },
      {
        title: i18n.t('global.color'),
        dataIndex: 'colorName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.size'),
        dataIndex: 'sizeName',
        width: 100,
        ellipsis: true,
      },
      {
        title: i18n.t('global.spec'),
        dataIndex: 'specName',
        width: 250,
        ellipsis: true,
      },
      {
        title: i18n.t('global.stockCount'),
        dataIndex: 'qty',
        sorter: true,
        valueType: 'number',
        width: 200,
      },
      {
        title: i18n.t('global.scanCount'),
        dataIndex: 'scanQty',
        valueType: 'number',
        width: 200,
      },
      {
        title: i18n.t('global.manualCount'),
        dataIndex: 'manualQty',
        sorter: true,
        valueType: 'number',
        width: 200,
      },
    ];
  }

  if (!showUniqueCode) {
    tableColumns = tableColumns.filter((n) => n.dataIndex !== 'unicode');
  }

  if (!showRfidExt) {
    tableColumns = tableColumns.filter((n) => n.dataIndex !== 'rfidExt');
  }

  return (
    <PowerTable
      initialized
      rowKey="id"
      columns={tableColumns}
      defaultPageSize={10}
      refreshBtnVisible={false}
      pagination
      autoLoad
      tableProps={{
        sticky: true,
      }}
      // defaultSorter={{
      //   field: byProd ? 'smCode' : 'barcode',
      //   order: 'DESCEND',
      // }}
      {...props}
    />
  );
};

export default TsOrderLineTable;
