import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { faCheck, faEye, faTrashCan } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Space, Tooltip, Typography } from 'antd';
import * as FileApi from 'common/api/file/Img';
import i18n from 'common/utils/I18n';
import ImageWrap from 'common/components/ImageWrap';
import React, { useEffect, useState } from 'react';

import styles from './photoItem.module.css';

const { Paragraph } = Typography;

export interface PhotoItemProps {
  current: Record<string, any>;
  id: string | number;
  /**
   * 控制是否显示遮罩层, 不传默认显示
   * */
  isLoading?: boolean;
  /**
   * 控制是否选中
   * */
  isSelectIcon?: boolean;
  /**
   * 权限
   * */
  permission?: Record<string, any>;
  deleteOnClick?: (values: Record<string, any>) => void;
  selectOnClick?: (values: Record<string, any>) => void;
}
const PhotoItem: React.FC<PhotoItemProps> = (props) => {
  const { current, isLoading: _isLoading, isSelectIcon, permission, deleteOnClick, selectOnClick } = props;
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props.id,
    transition: {
      duration: 1000, // milliseconds
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });
  const [maskingVisible, setMaskingVisible] = useState<boolean>(false);
  const isLoading = _isLoading ?? true;
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const [opacity, setOpacity] = useState(0);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const previewOnClick = async () => {
    setImageVisible(true);
  };

  const deleteBtnOnClick = () => {
    if (deleteOnClick) deleteOnClick(current);
  };

  useEffect(() => {
    setMaskingVisible(false);
  }, [current]);

  return (
    // eslint-disable-next-line
    <div ref={setNodeRef}
      className={styles.content}
      style={style}
      {...attributes}
      {...listeners}
      onMouseEnter={() => {
        setOpacity(0);
        setTimeout(() => {
          setOpacity(1);
        }, 80);
        setMaskingVisible(true);
      }}
      onMouseLeave={() => {
        setOpacity(0);
        setMaskingVisible(false);
      }}
      onClick={() => selectOnClick && selectOnClick(current)}
    >
      <ImageWrap fileName={current.source} preview={false} />
      {isLoading && maskingVisible && (
        <div className={styles.masking} style={{ opacity }}>
          <div className={styles.operation}>
            <Space direction="horizontal" wrap align="center" size="middle" style={{ justifyContent: 'center' }}>
              <Tooltip title={i18n.t('global.preview')} z-index={99}>
                <Button
                  type="primary"
                  shape="circle"
                  icon={<FontAwesomeIcon icon={faEye} />}
                  onClick={previewOnClick}
                />
              </Tooltip>
              {permission?.deletePhoPermission && (
                <Tooltip title={i18n.t('global.delete')} z-index={99}>
                  <Button
                    type="primary"
                    danger
                    shape="circle"
                    icon={<FontAwesomeIcon icon={faTrashCan} />}
                    onClick={deleteBtnOnClick}
                  />
                </Tooltip>
              )}
            </Space>
          </div>
          <Paragraph style={{ width: '100%' }} ellipsis={{ rows: 2 }}>
            {current.fileName}
          </Paragraph>
        </div>
      )}
      {!isLoading && (
        <div className={`${styles.icon} border`}>
          {isSelectIcon && <FontAwesomeIcon className={`${styles.radioIcon} text-lead-red`} icon={faCheck} />}
        </div>
      )}
      <ImageWrap
        width={0}
        style={{ display: 'none' }}
        preview={{
          visible: imageVisible,
          src: FileApi.Get(current.source),
          onVisibleChange: (value) => {
            setImageVisible(value);
          },
        }}
      />
    </div>
  );
};

export default PhotoItem;
