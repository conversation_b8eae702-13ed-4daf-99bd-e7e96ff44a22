import { useState } from 'react';

interface ISearchAliasByBarcodeParams {
  barcode: string;
  searchData: any[];
  operateSize: number;
  callback: (barcode: string, operateSize: number, index: number) => void;
}

interface ISearchAliasModalOnOkProps {
  selectedRows: any[];
  searchData: any[];
  callback: (barcode: string, operateSize: number, index: number) => void;
}

const useSearchAlias = () => {
  const [barcodeAliasModalOpen, setBarcodeAliasModalOpen] = useState(false);
  const [barcodeAliasData, setBarcodeAliasData] = useState<any[]>([]);
  const [barcodePrevOperateSize, setBarcodePrevOperateSize] = useState(0);

  const searchAliasByBarcode = ({ barcode, searchData, operateSize, callback }: ISearchAliasByBarcodeParams) => {
    const resultIdxs: number[] = [];
    const _barcode = barcode.trim().toUpperCase();
    searchData.forEach((item, index) => {
      if (item.barcode === barcode) {
        resultIdxs.push(index);
      } else if (item.alias && item.alias.some((_alias) => _alias.code === barcode)) {
        resultIdxs.push(index);
      }
    });

    if (resultIdxs.length === 1) {
      callback(_barcode, operateSize, resultIdxs[0]);
    } else if (resultIdxs.length === 0) {
      callback(_barcode, operateSize, -1);
    } else {
      setBarcodePrevOperateSize(operateSize);
      setBarcodeAliasModalOpen(true);
      setBarcodeAliasData(resultIdxs.map((idx) => searchData[idx]));
    }
  };

  const barcodeAliasModalOnOk = ({ selectedRows, searchData, callback }: ISearchAliasModalOnOkProps) => {
    if (selectedRows && selectedRows.length > 0 && barcodePrevOperateSize > 0) {
      const selectedRow = selectedRows[0];
      const tableDataIndex = searchData.findIndex((n) => n.barcode === selectedRow.barcode);
      callback(selectedRow.barcode, barcodePrevOperateSize, tableDataIndex);
    }
    setBarcodeAliasModalOpen(false);
    setBarcodePrevOperateSize(0);
    setBarcodeAliasData([]);
  };

  const barcodeAliasModalOnCancel = () => {
    setBarcodeAliasModalOpen(false);
    setBarcodePrevOperateSize(0);
    setBarcodeAliasData([]);
  };

  return {
    barcodeAliasModalOpen,
    barcodeAliasData,
    setBarcodeAliasModalOpen,
    setBarcodeAliasData,
    searchAliasByBarcode,
    barcodeAliasModalOnOk,
    barcodeAliasModalOnCancel,
  };
};

export default useSearchAlias;
