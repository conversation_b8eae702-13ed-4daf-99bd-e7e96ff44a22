import IBase from 'common/types/IBase';

/**
 * 盘点模式
 */
export type TsOrderMode = 'ALL' | 'RANDOM';

/**
 * 盘点单状态
 */
export type TsOrderStatus = 'NEW' | 'PROCESSING' | 'FINISHED' | 'CANCELED';

/**
 * 盘点单同步状态
 */
export type TsOrderSyncStatus = 'NERVER' | 'FAIL' | 'SUCCESS';

/**
 * 盘点盈亏状态
 * profit 盈 loss： 亏 RIGHT： 准
 */
export type TsOrderProfitAndLossStatus = 'PROFIT' | 'LOSS' | 'RIGHT';

export interface ITsOrderItem extends IBase {
  amt: number;
  code: string;
  created: string;
  id: number;
  localTag: boolean;
  mode: string;
  modeDesc: string;
  modified: string;
  partnerCode: string;
  partnerId: number;
  partnerName: string;
  qty: number;
  remark: string;
  snapAmt: number;
  snapQty: number;
  startTime: string;
  status: string;
  statusDesc: string;
  syncStatus: string;
  syncStatusDesc: string;
  warehouseCode: string;
  warehouseId: number;
  warehouseName: string;
  rangeType: string;
  rangeTypeDesc: string;
  rangePropId: string;
  rangePropName: string;
  sourceCode?: string;
  fmOrderCode?: string;
}
