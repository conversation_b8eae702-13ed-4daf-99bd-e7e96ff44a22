import { Button, DatePicker, Input, Space } from 'antd';
import * as PartnerA<PERSON> from 'common/api/core/Partner';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import AppHeader from 'common/layout/AppHeader';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useRef, useState } from 'react';

import AddDrawer from './components/AddDrawer';
import EditDrawer from './components/EditDrawer';
import { ILogisticsItem } from './data';

const Logistics: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [editDrawerOpen, setEditDrawerOpen] = useState(false);
  const [editDrawerSaving, setEditDrawerSaving] = useState(false);
  const [addDrawerOpen, setAddDrawerOpen] = useState(false);
  const [addDrawerSaving, setAddDrawerSaving] = useState(false);
  const [current, setCurrent] = useState<Partial<ILogisticsItem> | undefined>({});
  const [permission] = usePermission('A:BASE:LOGISTICS');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DEL');

  const editBtnOnClick = (record: Record<string, any>) => {
    setCurrent(record);
    setEditDrawerOpen(true);
  };

  const addBtnOnClick = () => {
    setAddDrawerOpen(true);
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await PartnerApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  const addDrawerOnSubmit = async (values) => {
    values.type = 'LOGISTICS';
    values.allowTakeStock = false;
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      setAddDrawerSaving(true);
      const payload = values;
      if (typeof payload.disabled !== 'boolean') {
        payload.disabled = false;
      }
      await PartnerApi.Create(payload);

      setAddDrawerOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setAddDrawerSaving(false);
  };

  const editDrawerOnSubmit = async (values) => {
    values.type = 'LOGISTICS';
    values.allowTakeStock = false;
    values.code = values.code.trim();
    values.name = values.name.trim();
    try {
      setEditDrawerSaving(true);
      await PartnerApi.Update({
        ...current,
        ...values,
      });

      setEditDrawerOpen(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setEditDrawerSaving(false);
  };

  const addDrawerOnCancel = () => {
    setAddDrawerOpen(false);
    powerTableRef.current?.load();
  };

  const editDrawerOnCancel = () => {
    setCurrent(undefined);
    setEditDrawerOpen(false);
    powerTableRef.current?.load();
  };

  const fetchData = useCallback((params: Record<string, any>) => {
    const payload = { ...params };
    payload.type = ['LOGISTICS'];
    if (payload.createDateRange) {
      payload.createdStart = payload.createDateRange[0].startOf('day');
      payload.createdEnd = payload.createDateRange[1].endOf('day');
      delete payload.createDateRange;
    }
    return PartnerApi.List(payload);
  }, []);

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.codeOrAccount'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      sorter: true,
      auto: true,
      minWidth: 200,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      sorter: true,
      ellipsis: true,
      minWidth: 200,
      auto: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      sorter: true,
      valueType: 'dateTime',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }
  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {createPermission && (
              <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
                {i18n.t('global.new')}
              </Button>
            )}
          </Space>
        }
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={10}
        settingToolVisible
        pagination
        autoLoad
        enableCache
        cacheKey="LOGISTICS"
        tableProps={{
          sticky: {
            offsetHeader: 96,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  editBtnOnClick(record);
                },
              })
            : undefined,
        }}
        defaultSorter={{ field: 'created', order: 'DESCEND' }}
        request={fetchData}
      />
      <AddDrawer
        onSubmit={addDrawerOnSubmit}
        open={addDrawerOpen}
        onCancel={addDrawerOnCancel}
        saving={addDrawerSaving}
      />
      <EditDrawer
        current={current}
        onSubmit={editDrawerOnSubmit}
        open={editDrawerOpen}
        onCancel={editDrawerOnCancel}
        saving={editDrawerSaving}
      />
    </div>
  );
};

export default Logistics;
