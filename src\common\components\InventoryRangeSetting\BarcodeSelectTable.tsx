import * as SkuApi from 'common/api/core/Sku';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button } from 'antd';
import SelectionPopover from './SelectionPopover';
import ImportModal from './ImportModal';

export interface BarcodeSelectTableRef {
  clear: () => void;
}

interface BarcodeSelectTableProps {
  innerRef?: React.MutableRefObject<BarcodeSelectTableRef | undefined>;
  /**
   * 默认已勾选数据，单选时为id，多选时为id数组
   */
  defaultSelected?: string | string[];
  /**
   * 是否多选
   */
  multiple?: boolean;
  onChange?: (type: string, selectRowKeys: string[], selectRows: Record<string, any>[]) => void;
}

const BarcodeSelectTable: React.FC<BarcodeSelectTableProps> = (props) => {
  const { multiple, defaultSelected, onChange, innerRef } = props;
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [selectedRows, setSelectedRows] = useState<Record<string, any>[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [importModalOpen, setImportModalOpen] = useState(false);

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'barcode',
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.inputCodeThenEnterToSearch')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'barcode',
      width: 150,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 120,
    },
    {
      title: i18n.t('global.color'),
      dataIndex: 'colorName',
      width: 100,
      valueType: 'codeName',
      codeDataIndex: 'colorCode',
      nameDataIndex: 'colorName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.size'),
      dataIndex: 'sizeName',
      width: 100,
      valueType: 'codeName',
      codeDataIndex: 'sizeCode',
      nameDataIndex: 'sizeName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.gender'),
      dataIndex: 'genderName',
      width: 120,
    },
    {
      title: i18n.t('global.year'),
      dataIndex: 'yearName',
      width: 120,
    },
    {
      title: i18n.t('global.brand'),
      dataIndex: 'brandName',
      width: 150,
      valueType: 'codeName',
      codeDataIndex: 'brandCode',
      nameDataIndex: 'brandName',
      ellipsis: true,
    },
    {
      title: i18n.t('global.priCategory'),
      dataIndex: 'priCategoryName',
      width: 150,
    },
    {
      title: i18n.t('global.subCategory'),
      dataIndex: 'subCategoryName',
      width: 150,
    },
  ];

  const onSelectAll = (selected: boolean, selectedRows: Record<string, any>[], changeRows: Record<string, any>[]) => {
    if (multiple) {
      // @ts-ignore
      const newSelectedRecord: Record<string, any>[] = JSON.parse(JSON.stringify(selectedRows));
      if (selected) {
        changeRows.forEach((record) => {
          const existIndex = newSelectedRecord.findIndex((n) => n.id === record.id);
          if (existIndex === -1) {
            newSelectedRecord.push(record);
          }
        });
      } else {
        // const deleteIndex: number[] = [];
        // changeRows.forEach((record) => {
        //   const existIndex = newSelectedRecord.findIndex((n) => n.id === record.id);
        //   if (existIndex !== -1) {
        //     deleteIndex.push(existIndex);
        //   }
        // });
        // deleteIndex.forEach((item) => newSelectedRecord.splice(item, 1));
      }
      setSelectedRowKeys(newSelectedRecord.map((n) => n.id));
      setSelectedRows(newSelectedRecord);
    }
  };

  const onSelect = (record: Record<string, any>) => {
    if (multiple) {
      const newSelectedRecord: Record<string, any>[] = JSON.parse(JSON.stringify(selectedRows));
      const existIndex = newSelectedRecord.findIndex((n) => n.id === record.id);
      if (existIndex !== -1) {
        newSelectedRecord.splice(existIndex, 1);
      } else {
        newSelectedRecord.push(record);
      }
      setSelectedRowKeys(newSelectedRecord.map((n) => n.id));
      setSelectedRows(newSelectedRecord);
    } else {
      setSelectedRowKeys([record.id]);
      setSelectedRows([record]);
    }
  };

  const fetchData = async (params: Record<string, any>) => SkuApi.List({ ...params });

  useEffect(() => {
    if (defaultSelected) {
      const selectedRowKeys = multiple ? defaultSelected : [defaultSelected];
      if (selectedRowKeys.length === 0) return;
      // @ts-ignore
      setSelectedRowKeys(selectedRowKeys);
      SkuApi.List({ ids: selectedRowKeys, enablePage: false })
        .then((res) => {
          setSelectedRows(res.data);
        })
        .finally(() => {});
    }
    // eslint-disable-next-line
  }, [multiple, defaultSelected]);

  const allClearSelect = () => {
    setSelectedRows([]);
    setSelectedRowKeys([]);
  };

  useEffect(() => {
    if (onChange) onChange('SKU', selectedRowKeys, selectedRows);
    // eslint-disable-next-line
  }, [selectedRows, selectedRowKeys]);

  useEffect(() => {
    powerTableRef.current?.load();
  }, []);

  useImperativeHandle(innerRef, () => ({
    clear: () => allClearSelect(),
  }));

  const deleteColumnBtnOnClick = useCallback(
    (record: Record<string, any>) => {
      const newSelectedRecord = selectedRows.filter((n) => n.id !== record.id);
      setSelectedRowKeys(newSelectedRecord.map((n) => n.id));
      setSelectedRows(newSelectedRecord);
    },
    [selectedRows],
  );

  const onImportFinish = (selectedData: Record<string, any>[]) => {
    setSelectedRowKeys(selectedData.map((n) => n.id));
    setSelectedRows(selectedData);
    setImportModalOpen(false);
  };

  return (
    <div className="min-h-[500px]">
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        pagination
        request={fetchData}
        rightToolbar={
          <Button
            type="primary"
            onClick={() => {
              setImportModalOpen(true);
            }}
          >
            {i18n.t('global.import')}
          </Button>
        }
        paginationExtraContent={
          <SelectionPopover
            columns={columns}
            dataSource={selectedRows}
            onAllClearSelect={allClearSelect}
            onDeleteColumnBtnOnClick={deleteColumnBtnOnClick}
          />
        }
        tableProps={{
          size: 'small',
          scroll: {
            x: 0,
            y: 400,
          },
          rowSelection: {
            type: multiple ? 'checkbox' : 'radio',
            selectedRowKeys,
            onSelect,
            onSelectAll,
          },
          onRow: (record) => ({
            onClick: () => {
              onSelect(record);
            },
          }),
          rowKey: 'id',
        }}
        onRowSelectionChange={(selectedRowKeys: string[], selectedRows) => {
          setSelectedRowKeys(selectedRowKeys);
          setSelectedRows(selectedRows);
        }}
        style={{ padding: 0 }}
      />

      <ImportModal
        templateColText={i18n.t('global.barcode')}
        moduleName={i18n.t('global.barcode')}
        fetchListApi={SkuApi.List}
        modalProps={{
          open: importModalOpen,
          onCancel: () => setImportModalOpen(false),
          maskClosable: false,
        }}
        onFinish={onImportFinish}
        onGoBack={() => setImportModalOpen(false)}
      />
    </div>
  );
};

export default BarcodeSelectTable;
