import { Input } from 'antd';
import * as SpibOrderApi from 'common/api/sample/SpibOrder';
import SampleBoxDetailsPreviewDrawer from 'common/components/SampleBoxDetailsPreviewDrawer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import BoxStatusSelect from 'common/components/Select/BoxStatusSelect';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = state;
  const { id: orderId, localTag } = orderRecord;
  const { innerRef } = props;
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const fetchData = useCallback(
    async (params) => {
      const payload = {
        ...params,
        wibOrderId: orderRecord.id,
      };
      return SpibOrderApi.Boxes(payload);
    },
    [orderRecord.id],
  );

  const openDetail = async (record) => {
    setCurrentBoxCode(record.boxCode);
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewDrawerLoading(true);
    try {
      const payload = {
        wibBoxId: record.id,
        wibOrderId: orderId,
        enablePage: false,
      };
      const result: any = await SpibOrderApi.Rfids(payload);

      setCurrentBoxDetailRfidData(result.data);
      setBoxDetailsPreviewDrawerLoading(false);
    } catch (e) {
      setBoxDetailsPreviewDrawerLoading(false);
    }
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: <BoxStatusSelect placeholder={i18n.t('global.status')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  let columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      ellipsis: true,
      minWidth: 180,
      auto: true,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      dataIndex: 'operateModeDesc',
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  if (localTag) {
    columns = columns.filter((n) => n.dataIndex !== 'qty');
  }

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={20}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        pagination
        request={fetchData}
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible
      />
      <SampleBoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxTable;
