import { createContext } from 'react';

export type ShipperType = 'WAREHOUSE' | 'SHOP' | 'FACTORY';

export type Result = {
  id: string;
  code: string;
};

export type TState = {
  /** 加载中 */
  loading: boolean;
  /** 单据 */
  orderRecord: Record<string, any>;
  /** 备注编辑框是否显示 */
  remarkModalOpen: boolean;
  /** 入库箱数量 */
  outboundBoxCount: number;
  /** 日志弹窗是否显示 */
  logDrawerOpen: boolean;
  /** 日志数据 */
  logs: Record<string, any>[];
  /** 打印任务添加弹窗是否显示 */
  printTaskAddModalOpen: boolean;
};

export const initialState: TState = {
  loading: false,
  orderRecord: {},
  remarkModalOpen: false,
  outboundBoxCount: 0,
  logDrawerOpen: false,
  logs: [],
  printTaskAddModalOpen: false,
};

export type TStateType = typeof initialState;

export type TActionType =
  | { type: 'reset' }
  | { type: 'setTabsActiveKey'; payload: string }
  | { type: 'setLoading'; payload: boolean }
  | { type: 'setOrderRecord'; payload: Record<string, any> }
  | { type: 'setRemarkEditModalOpen'; payload: boolean }
  | { type: 'setOutboundBoxCount'; payload: number }
  | { type: 'setLogDrawerOpen'; payload: boolean }
  | { type: 'setLogs'; payload: Record<string, any>[] }
  | { type: 'setPrintTaskAddModalOpen'; payload: boolean };

export function reducer(state: TStateType, action: TActionType): TStateType {
  switch (action.type) {
    case 'reset':
      return { ...initialState };
    case 'setLoading':
      return { ...state, loading: action.payload };
    case 'setOrderRecord':
      return { ...state, orderRecord: action.payload };
    case 'setRemarkEditModalOpen':
      return { ...state, remarkModalOpen: action.payload };
    case 'setOutboundBoxCount':
      return { ...state, outboundBoxCount: action.payload };
    case 'setLogDrawerOpen':
      return { ...state, logDrawerOpen: action.payload };
    case 'setLogs':
      return { ...state, logs: action.payload };
    case 'setPrintTaskAddModalOpen':
      return { ...state, printTaskAddModalOpen: action.payload };
    default:
      throw new Error('Unhandled action');
  }
}

export type TDetailContext = {
  state: TStateType;
  dispatch: (action: TActionType) => void;
};

export const DetailContext = createContext<TDetailContext>({
  state: initialState,
  dispatch: () => undefined,
});
