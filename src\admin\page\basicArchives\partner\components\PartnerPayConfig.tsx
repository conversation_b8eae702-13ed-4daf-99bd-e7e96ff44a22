import { FormProps, Radio, Spin } from 'antd';
import * as DevConfigSettingApi from 'common/api/core/DevConfigSetting';
import i18n from 'common/utils/I18n';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Config, { ConfigPropsInnerRef } from 'admin/page/common/devConfigSetting/components/Config';

export interface PartnerPayConfigPropsRef {
  getValues(): void;
  refresh(): void;
}

interface PartnerPayConfigProps extends FormProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<PartnerPayConfigPropsRef | undefined>;
  payConfig?: string;
  partnerId?: string;
}

const PartnerPayConfig: React.FC<PartnerPayConfigProps> = (props) => {
  const { innerRef, payConfig, partnerId } = props;
  const [activeKey, setActiveKey] = useState('GLOBAL');
  const [tabsData, setTabsData] = useState<Record<string, any>[]>([]);
  const [configValues, setConfigValues] = useState<Record<string, any>>({});
  const [currentConfigValues, setCurrentConfigValues] = useState<Record<string, any>[]>([]);
  const [loading, setLoading] = useState(false);
  const payConfigRef = useRef<ConfigPropsInnerRef>();

  // 获取支付方式
  const fetchGetPay = useCallback(async () => {
    setLoading(true);
    try {
      // 1.获取全局的支付方式
      const global: any = await DevConfigSettingApi.GetPay({});
      // 2.获取当前门店的支付方式
      const current: any = await DevConfigSettingApi.GetPay({ partnerId });
      // 3.合并数据
      const merge = global.map((item) => {
        // 默认门店支付方式都为停用状态
        let result: Record<string, any> = { ...item, disabled: true };
        const findItem = current.find((n) => n.category === item.category);
        if (findItem) {
          result = { ...item, ...findItem };
        }
        return result;
      });
      setTabsData(merge);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [partnerId]);

  // 获取支付配置值
  const fetchConfigValues = useCallback(async () => {
    setLoading(true);
    try {
      // 1.获取系统的配置值
      const global: any = await DevConfigSettingApi.List({
        enablePage: false,
      });
      // 2.获取当前门店的配置值
      const current: any = await DevConfigSettingApi.List({ enablePage: false, partnerId });

      const values: Record<string, any> = {};
      if (global.data && global.data.length > 0) {
        global.data.forEach((item) => {
          const { category, configKey, configValue } = item;
          if (
            (category === 'PAY_ALI' || category === 'PAY_WX' || category === 'PAY_SMART' || category === 'PAY_ALLIN') &&
            !values[configKey]
          ) {
            values[configKey] = configValue;
          }
        });
      }
      if (current.data && current.data.length > 0) {
        current.data.forEach((item) => {
          const { configKey, configValue } = item;
          if (values[configKey]) values[configKey] = configValue;
        });
      }
      setCurrentConfigValues(current.data);
      setConfigValues(values);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [partnerId]);

  const refresh = () => {
    fetchGetPay();
    fetchConfigValues();
  };

  const handleChange = (key) => {
    setActiveKey(key);
  };

  useImperativeHandle(innerRef, () => ({
    getValues: async () => {
      let result: Record<string, any> = {
        payConfig: activeKey,
      };
      try {
        const resp: any = await payConfigRef.current?.getValues();
        resp.values.forEach((item) => {
          /** 当前门店有支付配置时，传id为编辑支付，无支付配置时，不传id，为创建支付 */
          const findItem = currentConfigValues.find((n) => n.configKey === item.configKey);
          if (findItem) item.id = findItem.id;
        });
        result = { ...result, ...resp };
      } catch (e) {}
      return result;
    },
    refresh: () => refresh(),
  }));

  useEffect(() => {
    if (payConfig) {
      setActiveKey(payConfig);
    }
  }, [payConfig]);

  useEffect(() => {
    if (partnerId) {
      refresh();
    }
    // eslint-disable-next-line
  }, [partnerId]);

  return (
    <Spin spinning={loading}>
      <Radio.Group
        className="mb-4"
        buttonStyle="solid"
        value={activeKey}
        onChange={(e) => handleChange(e.target.value)}
      >
        <Radio.Button value="GLOBAL">{i18n.t('global.globals')}</Radio.Button>
        <Radio.Button value="CUSTOM">{i18n.t('global.customize')}</Radio.Button>
      </Radio.Group>
      {activeKey === 'GLOBAL' ? (
        <div>{i18n.t('global.globalPaymentConfiguration')}</div>
      ) : (
        <Config
          innerRef={payConfigRef}
          tabsDataSource={tabsData}
          configValues={configValues}
          currentConfigType="PAY_CONFIG"
        />
      )}
    </Spin>
  );
};

export default PartnerPayConfig;
