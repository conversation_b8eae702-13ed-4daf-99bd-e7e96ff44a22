import { Descriptions, Statistic, Tabs } from 'antd';
import * as Bsr<PERSON>rder<PERSON><PERSON> from 'common/api/shop/BsrOrder';
import * as SrOrder<PERSON>pi from 'common/api/shop/SrOrder';
import ListIcon from 'common/assets/icons/icon-list.svg?react';
import RefreshIcon from 'common/assets/icons/icon-refresh.svg?react';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import Button from 'common/components/Button';
import LogDrawer from 'common/components/LogDrawer';
import PartnerViewer from 'common/components/PartnerViewer';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import React, { useCallback, useContext, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import LineTable, { LineTableInnerRef } from './components/LineTable';
import { DetailContext, initialState, reducer } from './DetailReducer';

const Detail: React.FC = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const [state, dispatch] = useReducer(reducer, initialState);
  const { orderRecord, logDrawerOpen, logs } = state;
  const params = useParams();

  const [currencyData, setCurrencyData] = useState<Record<string, any>>();
  const lineTableRef = useRef<LineTableInnerRef>();

  const { RFID_RULES_DEPEND_ON_FIELD: dependField, CURRENCY: currency } = useSetting([
    { code: 'CURRENCY', valueType: 'STRING' },
    { code: 'RFID_RULES_DEPEND_ON_FIELD', valueType: 'STRING' },
  ]);

  if (currency && !currencyData) {
    setCurrencyData(JSON.parse(currency));
  }

  const fetchOrderData = useCallback(async () => {
    dispatch({ type: 'setLoading', payload: true });
    try {
      let rec: any;
      if (currentUser.mode === 'BINDING') {
        rec = await BsrOrderApi.Get({ orderId: params.id });
      } else {
        rec = await SrOrderApi.Get({ orderId: params.id });
      }
      rec.availableQty = rec.qty - rec.actQty;
      dispatch({ type: 'setOrderRecord', payload: rec });
    } catch (err) {}
    dispatch({ type: 'setLoading', payload: false });
    return {};
  }, [params.id, currentUser.mode]);

  const refresh = async () => {
    await fetchOrderData();
    lineTableRef.current?.load();
  };

  const fetchLogs = useCallback(async () => {
    try {
      let res: any;
      const payload = {
        enablePage: false,
        orderByField: 'created',
        orderByMethod: 'DESCEND',
        orderId: orderRecord.orderId,
      };
      if (currentUser.mode === 'BINDING') {
        res = await BsrOrderApi.Logs(payload);
      } else {
        res = await SrOrderApi.Logs(payload);
      }

      dispatch({ type: 'setLogs', payload: res.data });
    } catch (e) {}
  }, [currentUser.mode, orderRecord.orderId]);

  const logBtnOnClick = useCallback(() => {
    dispatch({ type: 'setLogDrawerOpen', payload: true });
    fetchLogs();
  }, [fetchLogs]);

  const subTitle = useMemo(() => {
    return (
      <Tag
        color={
          {
            NEW: 'red',
            PROCESSING: 'blue',
            FINISHED: 'green',
            CANCELED: 'slate',
          }[orderRecord.status]
        }
      >
        {orderRecord.statusDesc}
      </Tag>
    );
  }, [orderRecord.status, orderRecord.statusDesc]);

  useEffect(() => {
    LocalStorageUtil.setItem('RFID_RULES_DEPEND_ON_FIELD', dependField);
  }, [dependField]);

  useEffect(() => {
    fetchOrderData();
    // eslint-disable-next-line
  }, [params.id]);

  const detailContextValue = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return (
    <DetailContext.Provider value={detailContextValue}>
      <AppHeader
        title={orderRecord.orderCode}
        subTitle={subTitle}
        toolbar={
          <div className="flex gap-x-2">
            <Button icon={<RefreshIcon className="fill-lead-dark" />} loading={state.loading} onClick={refresh} />
            <Button type="default" icon={<ListIcon className="fill-lead-dark" />} onClick={logBtnOnClick}>
              {i18n.t('global.log')}
            </Button>
          </div>
        }
      />
      <div className="rounded-md border border-lead-light-slate">
        <div className="flex gap-x-6 bg-lead-light-bg p-5">
          <PartnerViewer
            className="flex-auto"
            partnerType="SHOP"
            partnerCode={orderRecord.partnerCode}
            partnerName={orderRecord.partnerName}
            warehouseCode={orderRecord.warehouseCode}
            warehouseName={orderRecord.warehouseName}
            label={i18n.t('global.shop')}
          />
          <div className="flex flex-initial items-center gap-x-6">
            <Statistic
              title={i18n.t('global.payAmount')}
              value={Number(orderRecord.payAmount).toFixed(2)}
              prefix={currencyData?.symbol}
            />
            <Statistic title={i18n.t('global.retailQty')} value={orderRecord.srQty} />
            <Statistic
              title={i18n.t('global.retailAmount')}
              value={Number(orderRecord.srAmount).toFixed(2)}
              prefix={currencyData?.symbol}
            />
            <Statistic title={i18n.t('global.returnQty')} value={orderRecord.srtQty} />
            <Statistic
              title={i18n.t('global.returnAmount')}
              value={Number(orderRecord.srtAmount).toFixed(2)}
              prefix={currencyData?.symbol}
            />
            <Statistic title={i18n.t('global.status')} value={orderRecord.statusDesc} className="uppercase" />
          </div>
        </div>
        <div className="px-5 pt-4">
          <Descriptions size="default" column={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }}>
            {(orderRecord.sourceCode || orderRecord.orderCode) && (
              <Descriptions.Item label={i18n.t('global.sourceOrderCode')}>
                <AdditionCodeViewer codes={orderRecord.sourceCode} />
              </Descriptions.Item>
            )}
            {orderRecord.flowNumber && (
              <Descriptions.Item label={i18n.t('global.serialNumber')}>{orderRecord.flowNumber}</Descriptions.Item>
            )}
            {orderRecord.payPlatformDesc && (
              <Descriptions.Item label={i18n.t('global.payType')}>{orderRecord.payPlatformDesc}</Descriptions.Item>
            )}
            {orderRecord.payScenesDesc && (
              <Descriptions.Item label={i18n.t('global.payScenario')}>{orderRecord.payScenesDesc}</Descriptions.Item>
            )}
            {orderRecord.payDate && (
              <Descriptions.Item label={i18n.t('global.payDate')}>{orderRecord.payDate}</Descriptions.Item>
            )}
            {orderRecord.created && (
              <Descriptions.Item label={i18n.t('global.created')}>{orderRecord.created}</Descriptions.Item>
            )}
            {((!orderRecord.remark && orderRecord.status !== 'FINISHED' && orderRecord.status !== 'CANCELED') ||
              orderRecord.remark) && (
              <Descriptions.Item label={i18n.t('global.remark')} contentStyle={{ alignItems: 'center' }}>
                {orderRecord.remark}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      </div>
      <div className="mt-5">
        <Tabs
          defaultActiveKey="0"
          size="small"
          items={[
            {
              key: '1',
              label: i18n.t('global.details'),
              children: <LineTable innerRef={lineTableRef} />,
            },
          ]}
        />
      </div>
      <LogDrawer
        open={logDrawerOpen}
        orderId={orderRecord.id}
        onClose={() => dispatch({ type: 'setLogDrawerOpen', payload: false })}
        data={logs}
      />
    </DetailContext.Provider>
  );
};

export default Detail;
