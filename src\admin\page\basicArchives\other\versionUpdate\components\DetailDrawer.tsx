/**
 * Create by codegen at 2022-07-26
 * 应用版本管理 - 编辑
 */
import { InboxOutlined } from '@ant-design/icons';
import { Form, Input, message, Upload, UploadProps } from 'antd';
import { baseUrl, getToken } from 'common/api/Base';
import * as VersionUpdateApi from 'common/api/core/VersionUpdate';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import DictSelect from 'common/components/Select/DictSelect';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useEffect, useRef, useState } from 'react';

interface EditModalProps {
  visible?: boolean;
  clientDefault?: string;
  current?: Record<string, any>;
  confirmLoading?: boolean;
  softwareUpdateType?: Record<string, any>[];
  onOk?: (values: Record<string, any>) => void;
  onCancel?: () => void;
}

// const { Option } = Select;
const { Dragger } = Upload;

const DetailDrawer: React.FC<EditModalProps> = (props) => {
  const {
    visible,
    current,
    confirmLoading,
    // softwareUpdateType,
    onOk,
    onCancel,
  } = props;
  const [form] = Form.useForm();
  const inputRef = useRef<any>(null);
  const [fileName, setFileName] = useState<string>();
  // const [radioKey, setRadioKey] = useState(1);
  const [source, setSource] = useState<string>('');

  const [permission] = usePermission('A:BASE:OTHER:O:VU');
  const delPermission = permission.codes.includes('DEL');

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const modalOnOk = () => {
    form.submit();
  };

  const modalOnCancel = () => {
    if (onCancel) onCancel();
  };

  const formOnFinish = (values) => {
    if (onOk) {
      values.source = source || current?.source;
      // values.appointmentUpdateTag = radioKey === 3;
      // values.defaultTag = radioKey === 2;
      values.fileName = fileName || current?.fileName;
      values.id = current?.id;
      onOk(values);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    maxCount: 1,
    action: `${baseUrl}/api/file/f/file/upload`,
    headers: {
      authorization: getToken(),
    },
    onChange(info) {
      const { status, name, response } = info.file;
      if (status === 'done') {
        const startIndex = name.lastIndexOf('_') + 1;
        const lastIndex = name.lastIndexOf('.');
        form.setFieldsValue({
          releaseVersion: name.slice(startIndex, lastIndex),
        });
        setSource(response.data.name);
        setFileName(name);
        message.success(`${info.file.name} ${i18n.t('global.uploadSuccess')}`);
      } else if (status === 'error') {
        message.error(`${info.file.name} ${i18n.t('global.uploadError')}`);
      }
    },
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  useEffect(() => {
    if (visible && current) {
      form.setFieldsValue(current);
    }
  }, [current, visible, form]);

  useEffect(() => {
    let timerId: any = null;
    if (visible) {
      timerId = setTimeout(() => {
        inputRef.current?.focus();
      }, 200);
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [visible]);

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.releaseVersion} - ${current?.client}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await VersionUpdateApi.Delete({ ids: [current?.id] });
          NoticeUtil.success();
          modalOnCancel();
        } catch (e) {}
      },
    });
  };

  return (
    <Drawer
      title={i18n.t('global.versionDetail')}
      width={700}
      open={visible}
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={modalOnOk}
          deletePermission={delPermission}
          onDelete={deleteBtnOnClick}
          onCancel={modalOnCancel}
        />
      }
      onClose={modalOnCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
    >
      <Form layout="vertical" form={form} onFinish={formOnFinish}>
        <Form.Item name="client" label={i18n.t('global.client')}>
          <DictSelect disabled />
        </Form.Item>
        <Form.Item name="releaseVersion" label={i18n.t('global.versionNumber')} rules={canNotBeNullRules}>
          <Input ref={inputRef} />
        </Form.Item>
        <Form.Item name="content" label={i18n.t('global.updateContent')} rules={canNotBeNullRules}>
          <Input.TextArea rows={3} />
        </Form.Item>
        <Form.Item label={i18n.t('global.applicationFile')}>
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{i18n.t('global.uploadTip')}</p>
            <p className="ant-upload-hint">
              {i18n.t('global.supportType')}
              ：.rar, .zip, .apk
            </p>
          </Dragger>
        </Form.Item>
        <Form.Item name="remark" label={i18n.t('global.remark')}>
          <Input />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default DetailDrawer;
