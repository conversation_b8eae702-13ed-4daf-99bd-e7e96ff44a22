import { DatePicker, Input, Spin } from 'antd';
import * as B<PERSON>b<PERSON>rder<PERSON><PERSON> from 'common/api/factory/BfibOrder';
import * as FibOrder<PERSON><PERSON> from 'common/api/factory/FibOrder';
import AdditionCodeViewer from 'common/components/AdditionCodeViewer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import OrderTypeSelect from 'common/components/Select/OrderTypeSelect';
import PartnerProSelect from 'common/components/Select/PartnerSelect';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const FibOrder: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = state;
  const [initialized, setInitialized] = useState<boolean>(false);
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([{ code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER' }]);
  const navigate = useNavigate();

  let defaultPartnerValue: string[] = [];
  if (currentUser.type === 'FACTORY') {
    defaultPartnerValue = [currentUser.partnerId];
  }

  const fetchData = useCallback(
    (params: Record<string, any>, tabActiveKey: string) => {
      if (params.created) {
        params.createdStart = params.created[0].startOf('day');
        params.createdEnd = params.created[1].endOf('day');
      }
      delete params.created;

      if (params.code) {
        params.code = `%${params.code}%`;
      }
      if (tabActiveKey !== 'ALL') {
        params.status = [tabActiveKey];
      } else {
        params.status = ['NEW', 'FINISHED'];
      }
      let res;
      if (currentUser.mode === 'BINDING') {
        res = BfibOrderApi.List({
          // orderByField: 'modified',
          // orderByMethod: 'DESCEND',
          ...params,
        });
      } else {
        res = FibOrderApi.List({
          // orderByField: 'modified',
          // orderByMethod: 'DESCEND',
          ...params,
        });
      }
      return res;
    },
    // eslint-disable-next-line
    [],
  );

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.orderCode'),
      labelHidden: true,
      inputComponent: (
        <SearchInput placeholder={i18n.t('global.orderCodeOrSourceOrderCode')} autoFocus style={{ width: 280 }} />
      ),
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'fmOrderCode',
      label: i18n.t('global.fmOrderCode'),
      inputComponent: <Input />,
    },
    {
      name: 'prodCode',
      label: i18n.t('global.productCode'),
      inputComponent: <Input />,
    },
    {
      name: 'fromPartnerIds',
      label: i18n.t('global.shipper'),
      inputComponent: <PartnerProSelect types={['WAREHOUSE', 'SHOP']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'partnerIds',
      label: i18n.t('global.receiver'),
      inputComponent: <PartnerProSelect types={['FACTORY']} sourceType="PERMISSION" multiple />,
    },
    {
      name: 'orderTypeId',
      label: i18n.t('global.orderType'),
      inputComponent: <OrderTypeSelect type="IB" categoryCode="FACTORY" valueField="id" />,
    },
    {
      name: 'created',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const tableColumns: PowerTableColumnsType = [
    {
      title: i18n.t('global.orderCode'),
      dataIndex: 'code',
      valueType: 'text',
      // render: (text, record) => <LinkWrapV5 to={`/app/fib/detail/${record.id}`}>{text}</LinkWrapV5>,
      width: 180,
      fixed: 'left',
    },
    {
      title: i18n.t('global.additionCode'),
      width: 250,
      ellipsis: true,
      render: (text, record) => {
        return (
          <AdditionCodeViewer
            codes={[
              { label: i18n.t('global.fmOrderCode'), value: record.fmOrderCode },
              { label: i18n.t('global.sourceOrderCode'), value: record.sourceCode },
            ]}
          />
        );
      },
    },
    {
      title: i18n.t('global.orderType'),
      dataIndex: 'typeDesc',
      width: 180,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      valueType: 'status',
      statusConfig: {
        NEW: { status: 'Red', desc: i18n.t('global.newOrder') },
        FINISHED: { status: 'Green', desc: i18n.t('global.finished') },
      },
      width: 150,
    },
    {
      title: i18n.t('global.productCode'),
      dataIndex: 'prodCode',
      width: 140,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 120,
    },
    {
      title: i18n.t('global.delivery'),
      dataIndex: 'fromPartnerName',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.receiver'),
      dataIndex: 'partnerName',
      valueType: 'text',
      tooltip: true,
      ellipsis: {
        showTitle: false,
      },
      width: 230,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      minWidth: 200,
      auto: true,
      ellipsis: true,
    },
    {
      title: i18n.t('global.created'),
      dataIndex: 'created',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
  ];

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  return (
    <div>
      <AppHeader />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={tableColumns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={{
            created: [defaultSelectDate.startDate, defaultSelectDate.endDate],
            partnerIds: defaultPartnerValue,
          }}
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            {
              code: 'NEW',
              name: i18n.t('global.newOrder'),
            },
            {
              code: 'FINISHED',
              name: i18n.t('global.finished'),
            },
          ]}
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          enableCache
          cacheKey="FIB_ORDER"
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            onRow: (record) => ({
              onClick: () => {
                navigate(`/app/fib/detail/${record.id}`);
              },
            }),
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
    </div>
  );
};

export default FibOrder;
