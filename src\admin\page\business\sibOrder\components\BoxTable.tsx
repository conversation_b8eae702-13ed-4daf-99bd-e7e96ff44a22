import { Input } from 'antd';
import * as Bsib<PERSON>rder<PERSON><PERSON> from 'common/api/shop/BsibOrder';
import * as SibOrder<PERSON>pi from 'common/api/shop/SibOrder';
import DoubleCheckIcon from 'common/assets/icons/icon-double-check.svg?react';
import ResetIcon from 'common/assets/icons/icon-reset.svg?react';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import BoxStatusSelect from 'common/components/Select/BoxStatusSelect';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface BoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface BoxTableProps {
  onRefresh?: () => void;
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<BoxTableInnerRef | undefined>;
}

const BoxTable: React.FC<BoxTableProps> = (props) => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId, localTag } = orderRecord;
  const { onRefresh, innerRef } = props;
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);

  const powerTableRef = useRef<IPowerTableInnerRef>();

  const [permission] = usePermission('A:S:IB');
  const boxConfirmPermission = permission.codes.includes('BOX_CONFIRM');
  const boxResetPermission = permission.codes.includes('BOX_RESET');

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);
  const { SHOW_RFID_EXT_FLAG: showRfidExt } = useSetting([{ code: 'SHOW_RFID_EXT_FLAG', valueType: 'BOOLEAN' }]);

  const fetchData = useCallback(
    async (params) => {
      let res;
      const payload = {
        ...params,
        sibOrderId: orderRecord.id,
      };
      if (currentUser.mode === 'BINDING') {
        res = await BsibOrderApi.Boxes(payload);
      } else if (currentUser.mode === 'WRITE') {
        res = await SibOrderApi.Boxes(payload);
      }
      return res;
    },
    [currentUser.mode, orderRecord.id],
  );

  const _onRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const confirmBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmConfirmBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BsibOrderApi.ConfirmBox({
              id: record.id,
            });
          } else {
            await SibOrderApi.ConfirmBox({
              id: record.id,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const resetBtnOnClick = (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmResetBox'),
      content: record.boxCode,
      onOk: async () => {
        try {
          if (currentUser.mode === 'BINDING') {
            await BsibOrderApi.BoxReset({
              boxCode: record.boxCode,
              sibOrderId: orderId,
            });
          } else {
            await SibOrderApi.BoxReset({
              boxCode: record.boxCode,
              sibOrderId: orderId,
            });
          }
          NoticeUtil.success();
          _onRefresh();
        } catch (e) {}
      },
    });
  };

  const openDetail = async (record) => {
    setCurrentBoxCode(record.boxCode);
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewDrawerLoading(true);
    try {
      let result;
      const payload = {
        sibBoxId: record.id,
        sibOrderId: orderId,
        enablePage: false,
        showRfidExtFlag: showRfidExt,
      };
      if (currentUser.mode === 'BINDING') {
        result = await BsibOrderApi.Rfids(payload);
      } else {
        result = await SibOrderApi.Rfids(payload);
      }

      setCurrentBoxDetailRfidData(result.data);
    } catch (e) {}
    setBoxDetailsPreviewDrawerLoading(false);
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  let columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      dataIndex: 'boxCode',
      fixed: 'left',
      ellipsis: true,
      sorter: true,
      // valueType: 'link',
      minWidth: 180,
      auto: true,
      // onLinkClick: openDetail,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      dataIndex: 'operateMode',
      valueType: 'codeName',
      codeDataIndex: 'operateMode',
      nameDataIndex: 'operateModeDesc',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'status',
      inputComponent: <BoxStatusSelect placeholder={i18n.t('global.status')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    valueType: 'action',
    fixed: 'right',
    align: 'center',
    actionConfig: [],
  };

  if (boxConfirmPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.confirm'),
      icon: <DoubleCheckIcon className="fill-lead-green" />,
      isDisabled: (record) => record.status === 'FINISHED' || orderRecord.status !== 'PROCESSING',
      onClick: (record) => confirmBtnOnClick(record),
    });
  }

  if (boxResetPermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.reset'),
      icon: <ResetIcon className="fill-lead-red" />,
      isDisabled: (record) =>
        (orderRecord.status !== 'NEW' && orderRecord.status !== 'PROCESSING') || record.status !== 'UPLOADED',
      onClick: resetBtnOnClick,
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  if (localTag) {
    columns = columns.filter((n) => n.dataIndex !== 'qty');
  }

  return (
    <>
      <PowerTable
        initialized
        rowKey="id"
        innerRef={powerTableRef}
        columns={columns}
        defaultPageSize={20}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        pagination
        request={fetchData}
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        refreshBtnVisible
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default BoxTable;
