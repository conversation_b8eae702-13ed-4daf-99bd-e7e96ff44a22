import OperateDrawer from 'admin/page/basicArchives/other/extField/components/OperateDrawer';
import { Button, Tabs } from 'antd';
import * as ExtFieldApi from 'common/api/core/ExtField';
import AddFillIcon from 'common/assets/icons/icon-add-fill.svg?react';
import DeleteBinLineIcon from 'common/assets/icons/icon-delete-bin-line.svg?react';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
} from 'common/components/PowerTable';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

const ExtField: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [currentType, setCurrentType] = useState('PROD');
  const [permission] = usePermission('A:BASE:OTHER:EXT_FIELD');
  const createPermission = permission.codes.includes('CREATE');
  const editPermission = permission.codes.includes('EDIT');
  const deletePermission = permission.codes.includes('DELETE');
  const [current, setCurrent] = useState<Record<string, any> | undefined>(undefined);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [operationModalVisible, setOperationModalVisible] = useState(false);

  const editBtnOnClick = (record) => {
    setCurrent(record);
    setOperationModalVisible(true);
  };

  const fetchData = useCallback(
    async (params) => {
      const paylod: Record<string, any> = {
        enablePage: false,
        type: currentType,
      };
      if (typeof params.disabled === 'boolean') {
        paylod.disabled = params.disabled;
      }
      return ExtFieldApi.List(paylod);
    },
    [currentType],
  );

  const addBtnOnClick = () => {
    setOperationModalVisible(true);
  };

  const handleSubmit = async (values) => {
    const id = current ? current.id : '';
    setConfirmLoading(true);
    try {
      if (id) {
        if (typeof values.status === 'boolean') {
          values.disabled = !values.status;
          delete values.status;
        }
        await ExtFieldApi.Update({
          id: current?.id,
          ...values,
        });
      } else {
        values.code = values.code.trim();
        values.name = values.name.trim();
        await ExtFieldApi.Create({
          ...values,
          type: currentType,
        });
      }
      setOperationModalVisible(false);
      setCurrent(undefined);
      setConfirmLoading(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {
      setConfirmLoading(false);
    }
  };

  const deleteBtnOnClick = async (record: Record<string, any>) => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${record.code} - ${record.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await ExtFieldApi.Delete({ id: record.id });
          NoticeUtil.success();
          powerTableRef.current?.load();
        } catch (e) {}
      },
    });
  };

  useEffect(() => {
    if (currentType) {
      powerTableRef.current?.load();
    }
  }, [currentType]);

  const tableColumns = useMemo(() => {
    const arr: PowerTableColumnsType = [
      {
        title: i18n.t('global.status'),
        dataIndex: 'disabled',
        valueType: 'disabledStatus',
        ellipsis: true,
        sorter: true,
        width: 120,
      },
      {
        title: i18n.t('global.code'),
        dataIndex: 'code',
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.name'),
        dataIndex: 'name',
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.fieldType'),
        dataIndex: 'fieldTypeDesc',
        width: 150,
        ellipsis: true,
      },
      {
        title: i18n.t('global.sortId'),
        dataIndex: 'sortId',
        width: 120,
        ellipsis: true,
      },
      {
        title: i18n.t('global.remark'),
        dataIndex: 'remark',
        minWidth: 150,
        auto: true,
        ellipsis: true,
      },
      {
        title: i18n.t('global.created'),
        dataIndex: 'created',
        sorter: true,
        valueType: 'dateTime',
        width: 200,
      },
    ];

    if (currentType === 'PARTNER') {
      arr.splice(3, 0, {
        title: i18n.t('global.partnerType'),
        width: 150,
        valueType: 'codeName',
        codeDataIndex: 'subType',
        nameDataIndex: 'subTypeDesc',
        ellipsis: true,
      });
    }

    return arr;
  }, [currentType]);

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    align: 'center',
    fixed: 'right',
    valueType: 'action',
    actionConfig: [],
  };

  if (deletePermission) {
    actionColumn.actionConfig?.push({
      tooltip: i18n.t('global.delete'),
      icon: <DeleteBinLineIcon className="fill-lead-red" />,
      onClick: (record) => {
        deleteBtnOnClick(record);
      },
    });
  }

  if ((actionColumn.actionConfig ?? []).length > 0) tableColumns.push(actionColumn);

  return (
    <>
      <Tabs
        defaultActiveKey="1"
        size="small"
        onChange={(key) => setCurrentType(key)}
        items={[
          {
            label: i18n.t('global.product'),
            key: 'PROD',
          },
          {
            label: i18n.t('global.partner'),
            key: 'PARTNER',
          },
          {
            label: i18n.t('global.subWarehouse'),
            key: 'WAREHOUSE',
          },
          {
            label: i18n.t('global.employee'),
            key: 'EMP',
          },
        ]}
      />
      <PowerTable
        initialized
        rowKey="id"
        columns={tableColumns}
        innerRef={powerTableRef}
        enableDisabledTrigger
        rightToolbar={[
          createPermission && (
            <Button icon={<AddFillIcon className="fill-white" />} type="primary" onClick={addBtnOnClick}>
              {i18n.t('global.new')}
            </Button>
          ),
        ]}
        defaultPageSize={20}
        settingToolVisible
        // autoLoad 此处不进行自动加载，原因是上面的currentType改变时会触发load
        enableCache
        cacheKey="EXT_FIELD"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: editPermission
            ? (record) => ({
                onClick: () => {
                  editBtnOnClick(record);
                },
              })
            : undefined,
        }}
        request={fetchData}
      />
      <OperateDrawer
        currentType={currentType}
        visible={operationModalVisible}
        confirmLoading={confirmLoading}
        current={current}
        onSubmit={handleSubmit}
        onCancel={() => {
          setCurrent(undefined);
          setOperationModalVisible(false);
          powerTableRef.current?.load();
        }}
      />
    </>
  );
};

export default ExtField;
