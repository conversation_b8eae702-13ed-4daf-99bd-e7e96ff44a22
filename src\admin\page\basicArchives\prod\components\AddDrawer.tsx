import { OperateFormInnerRef } from 'admin/page/basicArchives/employee/components/OperateForm';
import OperateForm, { extFieldFormItemNamePreFix } from 'admin/page/basicArchives/prod/components/OperateForm';
import { DrawerProps, Form } from 'antd';
import Drawer from 'common/components/Drawer';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { IProdItem } from '../data';

interface IAddModalProps {
  modalProps?: DrawerProps;
  conformLoading?: boolean;
  onSubmit: (values: IProdItem) => void;
  onCancel: () => void;
}

const AddDrawer: React.FC<IAddModalProps> = (props) => {
  const { modalProps, conformLoading, onSubmit, onCancel } = props;
  const [form] = Form.useForm();
  const operateFormRef = useRef<OperateFormInnerRef>();

  const handleFinish = (values: { [key: string]: any }) => {
    const ext = {};
    Object.keys(values).forEach((key) => {
      if (key.startsWith(extFieldFormItemNamePreFix)) {
        ext[key.split(extFieldFormItemNamePreFix)[1]] = values[key];
      }
    });
    values.ext = ext;
    if (onSubmit) {
      onSubmit(values as IProdItem);
    }
  };

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  useEffect(() => {
    if (modalProps?.open) {
      setTimeout(() => {
        operateFormRef.current?.focusCodeInput();
      }, 300);
    } else {
      // desc: reset fields will destroy all your fields, and then mount again.
      form.resetFields();
    }
  }, [form, modalProps]);

  return (
    <Drawer
      title={i18n.t('global.newProduct')}
      destroyOnClose
      width={880}
      okButtonProps={{ loading: conformLoading }}
      okText={i18n.t('global.apply')}
      onOk={handleSubmit}
      onClose={() => {
        if (onCancel) onCancel();
        form.resetFields();
      }}
      {...modalProps}
    >
      <OperateForm innerRef={operateFormRef} form={form} onFinish={handleFinish} />
    </Drawer>
  );
};

export default AddDrawer;
