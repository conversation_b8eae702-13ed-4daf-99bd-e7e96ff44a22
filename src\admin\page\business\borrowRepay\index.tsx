import { DatePicker, Input, message, Space } from 'antd';
import * as SampleLrRecordApi from 'common/api/sample/SampleLrRecord';
import Button from 'common/components/Button';
import ExportButton from 'common/components/Button/Export';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import EmployeeSelect from 'common/components/Select/EmployeeSelect';
import Spin from 'common/components/Spin';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import AppHeader from 'common/layout/AppHeader';
import { exportExcel, TemplateItem } from 'common/utils/Excel';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import moment from 'moment/moment';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import DisposeModal from './components/DisposeModal';

const BorrowRepay: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [disposeModalOpen, setDisposeModalOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [selectRowsList, setSelectRowsList] = useState<Record<string, any>[]>([]);
  const [initialized, setInitialized] = useState(false);
  const { ORDER_DEF_QUERY_DAYS: queryDays } = useSetting([
    { code: 'ORDER_DEF_QUERY_DAYS', valueType: 'NUMBER', defaultValue: 7 },
  ]);
  const [isDisable, setIsDisable] = useState(true);
  const [count, setCount] = useState(0);
  const [activeKeys, setActiveKeys] = useState<string>('');
  const [searchParams, setSearchParams] = useState<Record<string, any>>({});

  const [permission] = usePermission('A:P:BR');
  const disposePermission = permission.codes.includes('DISPOSE');
  const exportPermission = permission.codes.includes('EXPORT');

  const fetchData = useCallback(async (params: any, tabActiveKey: string) => {
    if (params.createDateRange) {
      params.createdStart = params.createDateRange[0].startOf('day');
      params.createdEnd = params.createDateRange[1].endOf('day');
    }
    delete params.createDateRange;

    if (params.code) {
      params.code = `%${params.code}%`;
    }

    if (tabActiveKey !== 'ALL') {
      params.status = tabActiveKey;
      setSearchParams(params);
    }

    setActiveKeys(tabActiveKey);

    return SampleLrRecordApi.List(params);
  }, []);

  const disposeModalOnSubmit = async (values) => {
    setConfirmLoading(true);
    try {
      await SampleLrRecordApi.Dispose(values, { throwError: false });
      setConfirmLoading(false);
      NoticeUtil.success();
      setDisposeModalOpen(false);
      setSelectedRowKeys([]);
      setSelectRowsList([]);
      powerTableRef.current?.load();
    } catch (err: any) {
      let errorMsgList: any[] = [];
      if (err.response?.data?.detailMsg) {
        const excludeCodes = ['SAMPLE_DATA_VALIDATE_EXCEPTION'];
        if (excludeCodes.includes(err.response.data.errorCode)) {
          errorMsgList = JSON.parse(err.response.data.detailMsg);
        }
        if (excludeCodes.length > 0) {
          const newSelectRowsList = JSON.parse(JSON.stringify(selectRowsList));
          newSelectRowsList.forEach((item) => {
            const findItem = errorMsgList.find((n) => n.smEpc === item.smEpc);
            if (findItem) {
              item.msg = findItem.message;
            }
          });
          setSelectRowsList(newSelectRowsList);
        }
      } else {
        message.error(err.response?.message);
      }
      setConfirmLoading(false);
    }
  };

  const disposeModalOnDelete = (id, smEpc) => {
    setSelectedRowKeys(selectedRowKeys.filter((n) => n !== id));
    setSelectRowsList(selectRowsList.filter((n) => (n.id ? n.id !== id : n.smEpc !== smEpc)));
  };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      inputComponent: <SearchInput placeholder={i18n.t('global.code')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'smBarcode',
      label: i18n.t('global.barcode'),
      inputComponent: <Input />,
    },
    {
      name: 'smEpc',
      label: 'EPC',
      inputComponent: <Input />,
    },
    {
      name: 'disposerId',
      label: i18n.t('global.disposer'),
      inputComponent: <EmployeeSelect types={['WAREHOUSE']} />,
    },
    {
      name: 'lenderId',
      label: i18n.t('global.borrower'),
      inputComponent: <EmployeeSelect types={['WAREHOUSE']} />,
    },
    {
      name: 'returnerId',
      label: i18n.t('global.returner'),
      inputComponent: <EmployeeSelect types={['WAREHOUSE']} />,
    },
    {
      name: 'createDateRange',
      label: i18n.t('global.created'),
      inputComponent: <DatePicker.RangePicker />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.batchCode'),
      dataIndex: 'code',
      width: 220,
      fixed: 'left',
    },
    {
      title: 'EPC',
      dataIndex: 'smEpc',
      valueType: 'text',
      sorter: true,
      width: 330,
    },
    {
      title: i18n.t('global.barcode'),
      dataIndex: 'smBarcode',
      valueType: 'text',
      sorter: true,
      width: 250,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'smSkuName',
      valueType: 'text',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.disposeType'),
      dataIndex: 'disposeTypeCode',
      codeDataIndex: 'disposeTypeCode',
      nameDataIndex: 'disposeTypeName',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.disposeRemark'),
      dataIndex: 'disposeRemark',
      valueType: 'text',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.disposer'),
      dataIndex: 'disposerCode',
      codeDataIndex: 'disposerCode',
      nameDataIndex: 'disposerName',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.disposeDate'),
      dataIndex: 'disposeDate',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.lendRemark'),
      dataIndex: 'lendRemark',
      valueType: 'text',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.borrower'),
      dataIndex: 'lenderCode',
      codeDataIndex: 'lenderCode',
      nameDataIndex: 'lenderName',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.lendDate'),
      dataIndex: 'lendDate',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.returnRemark'),
      dataIndex: 'returnRemark',
      valueType: 'text',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.returner'),
      dataIndex: 'returnerCode',
      codeDataIndex: 'returnerCode',
      nameDataIndex: 'returnerName',
      valueType: 'codeName',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.returnDate'),
      dataIndex: 'returnDate',
      valueType: 'dateTime',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.planReturnDate'),
      dataIndex: 'planReturnDate',
      valueType: 'date',
      sorter: true,
      width: 200,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 120,
      fixed: 'right',
      render(text, record) {
        return (
          <Tag
            color={
              {
                LENDED: 'red',
                RETURNED: 'green',
                DISPOSED: 'blue',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
  ];

  const template: TemplateItem[] = useMemo(() => {
    return [
      {
        display: i18n.t('global.batchCode'),
        dataIndex: 'code',
      },
      {
        display: 'EPC',
        dataIndex: 'smEpc',
      },
      {
        display: i18n.t('global.barcode'),
        dataIndex: 'smBarcode',
      },
      {
        display: i18n.t('global.name'),
        dataIndex: 'smSkuName',
      },
      {
        display: i18n.t('global.disposeTypeCode'),
        dataIndex: 'disposeTypeCode',
      },
      {
        display: i18n.t('global.disposeType'),
        dataIndex: 'disposeTypeName',
      },
      {
        display: i18n.t('global.disposeRemark'),
        dataIndex: 'disposeRemark',
      },
      {
        display: i18n.t('global.disposerCode'),
        dataIndex: 'disposerCode',
      },
      {
        display: i18n.t('global.disposer'),
        dataIndex: 'disposerName',
      },
      {
        display: i18n.t('global.disposeDate'),
        dataIndex: 'disposeDate',
      },
      {
        display: i18n.t('global.lendRemark'),
        dataIndex: 'lendRemark',
      },
      {
        display: i18n.t('global.borrowerCode'),
        dataIndex: 'lenderCode',
      },
      {
        display: i18n.t('global.borrower'),
        dataIndex: 'lenderName',
      },
      {
        display: i18n.t('global.lendDate'),
        dataIndex: 'lendDate',
      },
      {
        display: i18n.t('global.returnRemark'),
        dataIndex: 'returnRemark',
      },
      {
        display: i18n.t('global.returnerCode'),
        dataIndex: 'returnerCode',
      },
      {
        display: i18n.t('global.returner'),
        dataIndex: 'returnerName',
      },
      {
        display: i18n.t('global.returnDate'),
        dataIndex: 'returnDate',
      },
      {
        display: i18n.t('global.planReturnDate'),
        dataIndex: 'planReturnDate',
      },
      {
        display: i18n.t('global.status'),
        dataIndex: 'statusDesc',
      },
    ];
  }, []);

  const defaultSelectDate = {
    startDate: moment()
      .startOf('day')
      .subtract(queryDays || 7, 'd'),
    endDate: moment().endOf('day'),
  };

  const searchPanelInitialValues = {
    createDateRange: [defaultSelectDate.startDate, defaultSelectDate.endDate],
  };

  useEffect(() => {
    if (queryDays != null) {
      setInitialized(true);
    }
  }, [queryDays]);

  useEffect(() => {
    setIsDisable(!selectRowsList.some((n) => n.status === 'LENDED' || n.status === 'RETURNED'));
    setCount(selectRowsList.filter((n) => n.status !== 'DISPOSED').length);
  }, [selectRowsList]);

  const exportBtnOnClick = useCallback(async () => {
    try {
      const resp: any = await SampleLrRecordApi.List({ ...searchParams, enablePage: false });
      exportExcel(template, resp.data, i18n.t('global.boxLine'));
    } catch {}
  }, [searchParams, template]);

  return (
    <div>
      <AppHeader
        toolbar={
          <Space>
            {disposePermission && (
              <Button disabled={isDisable} onClick={() => setDisposeModalOpen(true)}>
                {i18n.t('global.dispose')} {count > 0 ? `(${count})` : ''}
              </Button>
            )}
            {exportPermission && activeKeys !== 'ALL' && <ExportButton onClick={exportBtnOnClick} />}
          </Space>
        }
      />
      {initialized ? (
        <PowerTable
          initialized
          rowKey="id"
          columns={columns}
          innerRef={powerTableRef}
          quickSearchFieldsConfig={quickSearchFieldsConfig}
          searchFieldsConfig={searchFieldsConfig}
          searchPanelInitialValues={searchPanelInitialValues}
          defaultPageSize={10}
          settingToolVisible
          pagination
          autoLoad
          enableCache
          cacheKey="BORROW_REPAY"
          tabStatus={[
            {
              code: 'ALL',
              name: i18n.t('global.all'),
            },
            { code: 'LENDED', name: i18n.t('global.lended') },
            {
              code: 'RETURNED',
              name: i18n.t('global.returned'),
            },
            {
              code: 'DISPOSED',
              name: i18n.t('global.disposed'),
            },
          ]}
          tableProps={{
            sticky: {
              offsetHeader: 96,
            },
            rowSelection: {
              type: 'checkbox',
              selectedRowKeys,
              onSelectAll: (selected, selectedRows, changeRows) => {
                let newSelectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
                let newSelectRowsList = JSON.parse(JSON.stringify(selectRowsList));
                if (selected) {
                  changeRows.forEach((item) => {
                    if (!newSelectedRowKeys.includes(item.id)) {
                      newSelectedRowKeys.push(item.id);
                    }
                    if (!newSelectRowsList.some((n) => n.id === item.id)) {
                      newSelectRowsList.push(item);
                    }
                  });
                } else {
                  newSelectedRowKeys = newSelectedRowKeys.filter((item) => !changeRows.some((n) => item === n.id));
                  newSelectRowsList = newSelectRowsList.filter((item) => !changeRows.some((n) => item.id === n.id));
                }
                setSelectedRowKeys(newSelectedRowKeys);
                setSelectRowsList(newSelectRowsList);
              },
              onSelect: (record, selected) => {
                let newSelectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
                let newSelectRowsList = JSON.parse(JSON.stringify(selectRowsList));
                if (selected) {
                  newSelectedRowKeys.push(record.id);
                  if (!newSelectRowsList.some((n) => n.id === record.id)) {
                    newSelectRowsList.push(record);
                  }
                } else {
                  newSelectedRowKeys = newSelectedRowKeys.filter((item) => item !== record.id);
                  newSelectRowsList = newSelectRowsList.filter((item) => item.id !== record.id);
                }
                setSelectedRowKeys(newSelectedRowKeys);
                setSelectRowsList(newSelectRowsList);
              },
            },
          }}
          defaultSorter={{ field: 'created', order: 'DESCEND' }}
          request={fetchData}
        />
      ) : (
        <Spin
          tip={i18n.t('global.loading')}
          style={{
            marginLeft: '50%',
            marginTop: 100,
            transform: 'translateX(-50%)',
          }}
        />
      )}
      <DisposeModal
        open={disposeModalOpen}
        confirmLoading={confirmLoading}
        selectedRowList={selectRowsList}
        onSubmit={disposeModalOnSubmit}
        onDelete={disposeModalOnDelete}
        onCancel={() => {
          setDisposeModalOpen(false);
          setSelectRowsList([]);
          setSelectedRowKeys([]);
        }}
      />
    </div>
  );
};
export default BorrowRepay;
