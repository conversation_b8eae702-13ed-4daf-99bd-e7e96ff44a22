import { Form, Input, InputNumber, Switch } from 'antd';
import { FormProps } from 'antd/lib/form';
import i18n from 'common/utils/I18n';
import React, { useEffect, useRef } from 'react';

import { ISizeGrpItem } from '../data';

interface IGrpOperationFormProps extends FormProps {
  current?: Partial<ISizeGrpItem> | undefined;
}
const SizeGrpOperateForm: React.FC<IGrpOperationFormProps> = (props) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const form = props.form || Form.useForm()[0];
  const { current } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (current) {
      if (typeof current.disabled === 'boolean') {
        current.status = !current.disabled;
      }
      form.setFieldsValue(current);
      setTimeout(() => {
        if (current) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    }
  }, [current, form]);

  return (
    <Form layout="vertical" form={form} {...props}>
      <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
        <Input readOnly={!!current} ref={codeInputRef} />
      </Form.Item>
      <Form.Item name="name" label={i18n.t('global.name')} rules={canNotBeNullRules}>
        <Input ref={nameInputRef} />
      </Form.Item>
      <Form.Item label={i18n.t('global.remark')} name="remark">
        <Input.TextArea rows={3} />
      </Form.Item>
      <Form.Item label={i18n.t('global.sort')} name="sortId" rules={canNotBeNullRules}>
        <InputNumber />
      </Form.Item>
      {current && (
        <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
          <Switch />
        </Form.Item>
      )}
    </Form>
  );
};

export default SizeGrpOperateForm;
