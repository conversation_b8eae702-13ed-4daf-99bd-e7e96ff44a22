/** @type {import('tailwindcss').Config} */

import colors from 'tailwindcss/colors';

export default {
  important: true,
  darkMode: 'class',
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}', './src/common/**/*.{js,ts,jsx,tsx}'],
  // corePlugins: {
  //   preflight: false,
  // },
  theme: {
    extend: {},
    colors: {
      inherit: colors.inherit,
      current: colors.current,
      transparent: colors.transparent,
      black: colors.black,
      white: colors.white,
      slate: colors.slate,
      gray: colors.gray,
      zinc: colors.zinc,
      neutral: colors.neutral,
      stone: colors.stone,
      red: colors.red,
      orange: colors.orange,
      amber: colors.amber,
      yellow: colors.yellow,
      lime: colors.lime,
      green: colors.green,
      emerald: colors.emerald,
      teal: colors.teal,
      cyan: colors.cyan,
      sky: colors.sky,
      blue: colors.blue,
      indigo: colors.indigo,
      violet: colors.violet,
      purple: colors.purple,
      fuchsia: colors.fuchsia,
      pink: colors.pink,
      rose: colors.rose,
      // Custom colors
      // 'lead-orange': {
      //   100: '#FED5CC',
      //   200: '#FEC8BC',
      //   300: '#FEAC9A',
      //   400: '#FD9179',
      //   500: '#FD7557',
      //   600: '#FF6B4B',
      //   700: '#FD5F3D',
      //   800: '#fd5733',
      //   900: '#ff4e28',
      // },
      'lead-orange': '#FD7557',
      'lead-light-orange': '#FFF1EE',
      'lead-slate': '#6B7D99',
      'lead-light-slate': '#EBEFF5',
      'lead-blue': '#2F86FF',
      'lead-green': '#38A169',
      'lead-light-green': '#E7F4ED',
      'lead-red': '#E53E3E',
      'lead-light-red': '#FCE8E8',
      'lead-yellow': '#FBA93D',
      'lead-light-bg': '#F5F7FA',
      'lead-purple': '#AB59FC',
      'lead-dark': '#364459',
    },
  },
  plugins: [],
};
