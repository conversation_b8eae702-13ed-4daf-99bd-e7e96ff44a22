import * as Bwib<PERSON>rder<PERSON><PERSON> from 'common/api/warehouse/BwibOrder';
import * as WibOrderApi from 'common/api/warehouse/WibOrder';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { useCallback, useContext } from 'react';

export const useCommonFn = () => {
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;

  const confirm = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmConfirmOrder'),
          content: orderCode || '',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BwibOrderApi.Confirm({
                  id: orderId,
                });
              } else {
                await WibOrderApi.Confirm({
                  id: orderId,
                });
              }

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    [currentUser.mode],
  );

  const fastReceipt = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmFastReceipt'),
          content: orderCode || '',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BwibOrderApi.FastReceipt({ id: orderId });
              } else {
                await WibOrderApi.FastReceipt({ id: orderId });
              }

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  const cancel = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmCancelOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BwibOrderApi.Cancel({ id: orderId });
              } else {
                await WibOrderApi.Cancel({ id: orderId });
              }
              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  const reset = useCallback(
    (orderId: string, orderCode?: string): Promise<any> =>
      new Promise((res, rej) => {
        NoticeUtil.confirm({
          title: i18n.t('global.confirmResetOrder'),
          content: orderCode || '',
          okType: 'danger',
          onOk: async () => {
            try {
              if (currentUser.mode === 'BINDING') {
                await BwibOrderApi.Reset({ id: orderId });
              } else {
                await WibOrderApi.Reset({ id: orderId });
              }

              NoticeUtil.success();
              res(true);
            } catch (e) {
              rej(e);
            }
          },
        });
      }),
    // eslint-disable-next-line
    [],
  );

  return { confirm, cancel, fastReceipt, reset };
};
