import { Input } from 'antd';
import * as BfmOrderApi from 'common/api/factory/BfmOrder';
import * as FmOrderApi from 'common/api/factory/FmOrder';
import BoxDetailsPreviewDrawer from 'common/components/BoxDetailsPreviewDrawer';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  PowerTableColumnType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import OperateModeSelect from 'common/components/Select/OperateModeSelect';
import Tag from 'common/components/Tag';
import useSetting from 'common/hooks/useSetting';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import React, { useCallback, useContext, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { DetailContext, TDetailContext } from '../DetailReducer';

export interface PreBoxTableInnerRef {
  /**
   * Load data.
   */
  load(): void;
}

interface IPreBoxTableProps {
  /**
   * Inner reference
   */
  innerRef?: React.MutableRefObject<PreBoxTableInnerRef | undefined>;
}

const PreBoxTable: React.FC<IPreBoxTableProps> = (props) => {
  const { innerRef } = props;
  const { state: globalState } = useContext<TGlobalContext>(GlobalContext);
  const { currentUser } = globalState;
  const { state: detailState } = useContext<TDetailContext>(DetailContext);
  const { orderRecord } = detailState;
  const { id: orderId } = orderRecord;
  const [currentBoxCode, setCurrentBoxCode] = useState<Record<string, any>[]>([]);
  const [currentBoxDetailRfidData, setCurrentBoxDetailRfidData] = useState<Record<string, any>[]>([]);
  const [boxedRfidData, setBoxedRfidData] = useState<Record<string, any>[]>([]);
  const [operateMode, setOperateMode] = useState<string>();
  const [boxDetailsPreviewDrawerOpen, setBoxDetailsPreviewDrawerOpen] = useState<boolean>(false);
  const [boxDetailsPreviewDrawerLoading, setBoxDetailsPreviewDrawerLoading] = useState<boolean>(false);
  const powerTableRef = useRef<IPowerTableInnerRef>();

  const { ENABLE_UNICODE: enableUnicode } = useSetting([{ code: 'ENABLE_UNICODE', valueType: 'BOOLEAN' }]);

  const fetchData = useCallback(
    (params) => {
      let res;
      if (currentUser.mode === 'BINDING') {
        res = BfmOrderApi.PreBoxes({
          ...params,
          orderId,
          partnerId: orderRecord.partnerId,
        });
      } else if (currentUser.mode === 'WRITE') {
        res = FmOrderApi.PreBoxes({
          ...params,
          orderId,
          partnerId: orderRecord.partnerId,
        });
      }
      return res;
    },
    [currentUser.mode, orderId, orderRecord.partnerId],
  );

  const openDetail = async (record) => {
    setBoxDetailsPreviewDrawerOpen(true);
    setBoxDetailsPreviewDrawerLoading(true);
    setCurrentBoxCode(record.boxCode);
    try {
      let preBoxRfidsRes;
      let boxsRes;
      if (currentUser.mode === 'BINDING') {
        preBoxRfidsRes = await BfmOrderApi.PreBoxRfids({
          boxId: record.boxId,
          orderId,
          enablePage: false,
        });
        boxsRes = await BfmOrderApi.Boxs({
          fmOrderId: orderId,
          enablePage: false,
        });
      } else {
        preBoxRfidsRes = await FmOrderApi.PreBoxRfids({
          boxId: record.boxId,
          orderId,
          enablePage: false,
        });
        boxsRes = await FmOrderApi.Boxs({
          fmOrderId: orderId,
          enablePage: false,
        });
      }

      const boxItem = boxsRes.data.find((n) => n.boxId === record.boxId);
      let boxRfidsRes: any = [];
      if (boxItem) {
        if (currentUser.mode === 'BINDING') {
          boxRfidsRes = await BfmOrderApi.Rfids({
            fmOrderBoxId: boxItem.id,
            fmOrderId: orderId,
            enablePage: false,
          });
        } else {
          boxRfidsRes = await FmOrderApi.Rfids({
            fmOrderBoxId: boxItem.id,
            fmOrderId: orderId,
            enablePage: false,
          });
        }

        setOperateMode(boxItem.operateMode);
      }
      setBoxedRfidData(boxRfidsRes.data);
      // setCurrentBoxDetailRfidData(preBoxRfidsRes.data.filter((n) => n.epc !== null));
      setCurrentBoxDetailRfidData(preBoxRfidsRes.data);
    } catch (e) {}
    setBoxDetailsPreviewDrawerLoading(false);
  };

  useImperativeHandle(innerRef, () => ({
    load: () => {
      powerTableRef.current?.load();
    },
  }));

  useEffect(() => {
    if (orderId) {
      powerTableRef.current?.load();
    }
  }, [orderId]);

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'boxCode',
      inputComponent: <Input placeholder={i18n.t('global.boxCode')} />,
    },
    {
      name: 'operateMode',
      inputComponent: <OperateModeSelect placeholder={i18n.t('global.operateMode')} />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.boxCode'),
      fixed: 'left',
      dataIndex: 'boxCode',
      ellipsis: true,
      valueType: 'link',
      onLinkClick: openDetail,
      auto: true,
      minWidth: 180,
    },
    {
      title: i18n.t('global.status'),
      dataIndex: 'status',
      width: 150,
      render(text, record) {
        return (
          <Tag
            color={
              {
                NEW: 'red',
                UPLOADED: 'blue',
                FINISHED: 'green',
              }[record.status]
            }
          >
            {record.statusDesc}
          </Tag>
        );
      },
    },
    {
      title: i18n.t('global.operateMode'),
      dataIndex: 'operateModeDesc',
      width: 200,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'qty',
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.inboundQty'),
      dataIndex: 'actQty',
      valueType: 'number',
      width: 200,
    },
    {
      title: i18n.t('global.diffQty'),
      dataIndex: 'diffQty',
      sorter: true,
      valueType: 'number',
      width: 200,
    },
  ];

  const actionColumn: PowerTableColumnType = {
    title: i18n.t('global.operation'),
    valueType: 'action',
    fixed: 'right',
    align: 'center',
    actionConfig: [],
  };

  if ((actionColumn.actionConfig ?? []).length > 0) columns.push(actionColumn);

  return (
    <>
      <PowerTable
        initialized
        rowKey="boxId"
        innerRef={powerTableRef}
        columns={columns}
        tableProps={{
          sticky: true,
          onRow: (record) => ({
            onClick: () => {
              openDetail(record);
            },
          }),
        }}
        request={fetchData}
        defaultPageSize={20}
        pagination
        refreshBtnVisible
        quickSearchPanelSubmitButtonVisible
        quickSearchFieldsConfig={quickSearchFieldsConfig}
      />
      <BoxDetailsPreviewDrawer
        title={
          <>
            {i18n.t('global.boxLine')} [{currentBoxCode}]
          </>
        }
        showCustomFields
        rfidsData={currentBoxDetailRfidData}
        unicodeVisible={enableUnicode}
        boxedData={boxedRfidData}
        operateMode={operateMode}
        actQtyVisible
        loading={boxDetailsPreviewDrawerLoading}
        open={boxDetailsPreviewDrawerOpen}
        onCancel={() => setBoxDetailsPreviewDrawerOpen(false)}
      />
    </>
  );
};

export default PreBoxTable;
