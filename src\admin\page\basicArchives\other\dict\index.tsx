/**
 * Create by codegen at 2022-07-13
 * 数据字典
 */
import AddDrawer from 'admin/page/basicArchives/other/dict/components/AddDrawer';
import DetailDrawer from 'admin/page/basicArchives/other/dict/components/DetailDrawer';
import { Input } from 'antd';
import * as DictApi from 'common/api/core/Dict';
import * as DictDataApi from 'common/api/core/DictData';
import PowerTable, {
  IPowerTableInnerRef,
  PowerTableColumnsType,
  SearchFieldsConfig,
} from 'common/components/PowerTable';
import SearchInput from 'common/components/SearchInput';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import React, { useCallback, useRef, useState } from 'react';

const Dict: React.FC = () => {
  const powerTableRef = useRef<IPowerTableInnerRef>();
  const [current, setCurrent] = useState<Record<string, any>>({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);

  const fetchData = useCallback(async (params: Record<string, any>) => {
    const res = await DictApi.List(params);
    const dictData = await DictDataApi.List({
      disabled: false,
      isPageable: false,
    });
    res.data.forEach((item: any) => {
      item.dictDataCount = dictData.data.filter((n) => n.dictId === item.id, 3).length;
    });
    return res;
  }, []);

  const editModalOnOk = async (values: Record<string, any>) => {
    values.code = current.code;
    setUpdating(true);
    try {
      if (typeof values.status === 'boolean') {
        values.disabled = !values.status;
        delete values.status;
      }
      await DictApi.Update({ ...current, ...values });
      setEditModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setUpdating(false);
  };

  const editModalOnCancel = () => {
    setEditModalVisible(false);
    powerTableRef.current?.load();
  };

  const editBtnOnClick = (record: Record<string, any>) => {
    setCurrent(record);
    setEditModalVisible(true);
  };

  const addModalOnOk = async (values: Record<string, any>) => {
    setUpdating(true);
    try {
      await DictApi.Create(values);
      setAddModalVisible(false);
      NoticeUtil.success();
      powerTableRef.current?.load();
    } catch (e) {}
    setUpdating(false);
  };

  // const addBtnOnClick = () => {
  //   setAddModalVisible(true);
  // };

  const addModalOnCancel = () => {
    setAddModalVisible(false);
  };

  // const deleteBtnOnClick = async (record: Record<string, any>) => {
  //   NoticeUtil.confirm({
  //     title: confirmDeleteMsg,
  //     content: `${record.code} - ${record.name}`,
  //     okType: 'danger',
  //     maskTransitionName: '',
  //     transitionName: '',
  //     onOk: async () => {
  //       try {
  //         await DictApi.Delete({ ids: [record.id] });
  //         NoticeUtil.success();
  //         powerTableRef.current?.load();
  //       } catch (e) {}
  //     },
  //   });
  // };

  const quickSearchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'code',
      label: i18n.t('global.code'),
      labelHidden: true,
      inputComponent: <SearchInput placeholder={i18n.t('global.searchCode')} autoFocus style={{ width: 280 }} />,
    },
  ];

  const searchFieldsConfig: SearchFieldsConfig = [
    {
      name: 'name',
      label: i18n.t('global.name'),
      inputComponent: <Input />,
    },
  ];

  const columns: PowerTableColumnsType = [
    {
      title: i18n.t('global.status'),
      dataIndex: 'disabled',
      valueType: 'disabledStatus',
      ellipsis: true,
      sorter: true,
      width: 120,
    },
    {
      title: i18n.t('global.code'),
      dataIndex: 'code',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
    },
    {
      title: i18n.t('global.name'),
      dataIndex: 'name',
      valueType: 'text',
      ellipsis: true,
      sorter: true,
    },
    {
      title: i18n.t('global.remark'),
      dataIndex: 'remark',
      valueType: 'text',
      ellipsis: true,
    },
    {
      title: i18n.t('global.count'),
      dataIndex: 'dictDataCount',
      valueType: 'number',
      ellipsis: true,
      // sorter: true,
      width: 200,
    },
  ];

  return (
    <div>
      <PowerTable
        initialized
        rowKey="id"
        columns={columns}
        innerRef={powerTableRef}
        quickSearchFieldsConfig={quickSearchFieldsConfig}
        searchFieldsConfig={searchFieldsConfig}
        enableDisabledTrigger
        defaultPageSize={20}
        settingToolVisible
        autoLoad
        enableCache
        cacheKey="DICT"
        tableProps={{
          sticky: {
            offsetHeader: 0,
          },
          onRow: (record) => ({
            onClick: () => {
              editBtnOnClick(record);
            },
          }),
        }}
        request={fetchData}
      />
      <DetailDrawer
        visible={editModalVisible}
        current={current}
        confirmLoading={updating}
        onOk={editModalOnOk}
        onCancel={editModalOnCancel}
      />
      <AddDrawer visible={addModalVisible} confirmLoading={updating} onOk={addModalOnOk} onCancel={addModalOnCancel} />
    </div>
  );
};

export default Dict;
