import { Col, DatePicker, Form, Input, Result, Row, Space, Steps, Tabs } from 'antd';
import * as SpTsOrderApi from 'common/api/sample/SpTsOrder';
import CloseButton from 'common/components/Button/Close';
import NextStepButton from 'common/components/Button/NextStep';
import OpenButton from 'common/components/Button/Open';
import PreStepButton from 'common/components/Button/PreStep';
import RetryButton from 'common/components/Button/Retry';
import Modal from 'common/components/Modal';
import SampleInventoryRangeSetting from 'common/components/SampleInventoryRangeSetting';
import PartnerSelect from 'common/components/Select/PartnerSelect';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n from 'common/utils/I18n';
import moment from 'moment';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import styles from './AddModal.module.css';

interface AddModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk?: () => void;
}

const disabledDate = (current) => current && current < moment().startOf('day');

const AddModal: React.FC<AddModalProps> = (props) => {
  const { visible, onCancel, onOk } = props;
  const [mode, setMode] = useState('ALL');
  const [rangeData, setRangeData] = useState<Record<string, any>[]>([]);
  const [result, setResult] = useState<'progress' | 'success' | 'error'>('progress');
  const [orderCode, setOrderCode] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [orderId, setOrderId] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const formValues = useRef<Record<string, any>>({});
  const { state } = useContext<TGlobalContext>(GlobalContext);
  const navigate = useNavigate();
  const { currentUser } = state;
  const initVal: any = {
    startTime: moment().startOf('day'),
    mode: 'ALL',
  };
  if (currentUser.type === 'SHOP') {
    initVal.partnerId = currentUser.partnerId;
  }
  const [form] = Form.useForm();
  const [tabsItems, setTabsItems] = useState<any[]>([]);

  const defaultTabsItems: Record<string, any>[] = useMemo(() => {
    return [
      {
        label: i18n.t('global.tsOrderModeAll'),
        key: 'ALL',
      },
      {
        label: i18n.t('global.tsOrderModeRandom'),
        key: 'RANDOM',
      },
    ];
  }, []);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const reset = () => {
    setOrderId('');
    setCurrentStep(0);
    setResult('progress');
    setErrorMsg('');
    setLoading(false);
    setMode('ALL');
  };

  const rangeSettingOnChange = (record) => {
    setRangeData(record);
  };

  const onSubmit = async () => {
    const payload = { ...formValues.current };

    setResult('progress');
    setLoading(true);
    try {
      const resp: any = await SpTsOrderApi.Create(payload, {
        throwError: false,
      });
      if (payload.mode === 'RANDOM') {
        await SpTsOrderApi.SetRange(
          {
            orderId: resp.id,
            rangeType: rangeData[0].propType,
            propIds: rangeData.map((n) => n.propId),
          },
          {
            timeout: 600000, // 10分钟
            throwError: false,
          },
        );
      }

      setResult('success');
      setOrderId(resp.id);
      setOrderCode(resp.code);
      if (onOk) {
        onOk();
      }
    } catch (e: any) {
      setErrorMsg(e?.response?.data?.message || '');
      setResult('error');
    }
    setLoading(false);
    setCurrentStep(currentStep + 1);
  };

  const formOnFinish = async (values) => {
    try {
      await form.validateFields();
      values.mode = mode;
      formValues.current = values;
      if (mode === 'ALL') {
        onSubmit();
      } else {
        setCurrentStep(currentStep + 1);
      }
    } catch (e) {}
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      reset();
    }
  }, [visible, form]);

  const linkToNewOrder = () => {
    navigate(`/app/spts/${orderId}`);
    onCancel();
  };

  const stepItems = [
    {
      title: i18n.t('global.basicInformation'),
      description: <span className="text-lead-slate">{i18n.t('global.fillOrderBasicInfo')}</span>,
    },
    {
      title: i18n.t('global.result'),
    },
  ];

  if (mode === 'RANDOM') {
    stepItems.splice(1, 0, {
      title: i18n.t('global.setRange'),
      description: <span className="text-lead-slate">{i18n.t('global.setTsOrderRange')}</span>,
    });
  }

  useEffect(() => {
    const items = [...defaultTabsItems];
    items[0].disabled = mode === 'RANDOM' && currentStep === 1;
    setTabsItems(items);
  }, [mode, currentStep, setTabsItems, defaultTabsItems]);

  return (
    <Modal
      title={i18n.t('global.newInventory')}
      width={800}
      // width={mode === 'RANDOM' && currentStep === 1 ? 1200 : 750}
      open={visible}
      destroyOnClose
      footer={false}
      maskClosable={false}
      onCancel={onCancel}
    >
      <div className="flex flex-col">
        <Tabs activeKey={mode} size="small" onChange={(e) => setMode(e)} items={tabsItems} />
        <Steps
          size="small"
          className={`${styles.steps} mb-6`}
          current={currentStep}
          direction="horizontal"
          items={stepItems}
        />
        <Form
          form={form}
          layout="vertical"
          style={{
            display: currentStep === 0 ? 'block' : 'none',
          }}
          initialValues={initVal}
          onFinish={formOnFinish}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="partnerId" label={i18n.t('global.warehouse')} rules={canNotBeNullRules}>
                <PartnerSelect types={['WAREHOUSE']} sourceType="PERMISSION" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="startTime" label={i18n.t('global.startTime')} rules={canNotBeNullRules}>
                <DatePicker disabledDate={disabledDate} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label={i18n.t('global.remark')} name="remark">
                <Input.TextArea rows={3} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <NextStepButton htmlType="submit" loading={loading} />
            </Col>
          </Row>
        </Form>
        <div
          style={{
            display: mode === 'RANDOM' && currentStep === 1 ? 'block' : 'none',
            overflow: 'auto',
          }}
        >
          <SampleInventoryRangeSetting onChange={rangeSettingOnChange} />
          <Space style={{ float: 'left' }}>
            <NextStepButton
              onClick={() => {
                onSubmit();
              }}
              disabled={!(rangeData.length > 0)}
              loading={loading}
            />
            <PreStepButton
              onClick={() => {
                setCurrentStep(currentStep - 1);
              }}
            />
          </Space>
        </div>
        <div
          style={{
            display:
              (mode === 'RANDOM' && currentStep === 2) || (mode === 'ALL' && currentStep === 1) ? 'block' : 'none',
          }}
        >
          {result === 'success' && (
            <Result
              status="success"
              title={i18n.t('global.createdDone')}
              subTitle={`${i18n.t('global.orderCode')} : ${orderCode}`}
              extra={[
                <CloseButton
                  onClick={() => {
                    onCancel();
                  }}
                  key="close"
                />,
                <OpenButton onClick={linkToNewOrder} key="open" />,
              ]}
            />
          )}
          {result === 'error' && (
            <Result
              status="error"
              title={i18n.t('global.createdFail')}
              subTitle={errorMsg}
              extra={[
                <CloseButton
                  onClick={() => {
                    onCancel();
                  }}
                  key="close"
                />,
                <RetryButton
                  type="primary"
                  icon={undefined}
                  onClick={() => {
                    reset();
                  }}
                  key="retry"
                />,
              ]}
            />
          )}
        </div>
      </div>
    </Modal>
  );
};

export default AddModal;
