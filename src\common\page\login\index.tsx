import { <PERSON><PERSON>, Button, Checkbox, Form, Input, Select, Tabs, Tooltip } from 'antd';
import classnames from 'classnames';
import { baseUrl, setBaseUrl } from 'common/api/Base';
import * as ConfigApi from 'common/api/core/Config';
// import { buildTarget } from 'common/app/GlobalVars';
import ArrowDownSLineIcon from 'common/assets/icons/icon-arrow-down-s-line.svg?react';
import CloseCircleFillIcon from 'common/assets/icons/icon-close-circle-fill.svg?react';
import CloseFillIcon from 'common/assets/icons/icon-close-fill.svg?react';
import GlobalLineIcon from 'common/assets/icons/icon-global-line.svg?react';
import InformationLineIcon from 'common/assets/icons/icon-information-line.svg?react';
import AppTag from 'common/components/AppTag';
import useGlobalCommon from 'common/hooks/useGlobalCommon';
import Footer from 'common/layout/Footer';
import Logo from 'common/layout/Logo';
import { GlobalContext, TGlobalContext } from 'common/store/GlobalReducer';
import i18n, { LOCALE_KEY, localeList } from 'common/utils/I18n';
import * as LocalStorageUtil from 'common/utils/LocalStorage';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { storageKeys } from 'common/app/Persistence';

import './index.css';

const { TabPane } = Tabs;
const { Option } = Select;

type LoginType = 'TENANT' | 'WAREHOUSE' | 'SHOP' | 'FACTORY' | 'SAMPLE';

export interface LoginProps {
  /** 登录类型，不传递则可选 */
  type?: LoginType;
  /**
   * 是否启用系统初始化检查（未初始化则跳转至初始化界面）
   */
  enableInitPage?: boolean;
  /**
   * 是否允许编辑api地址
   */
  testMode?: boolean;
  /**
   * 登录成功
   */
  onOk?: () => void;
}

const devMode = !import.meta.env.PROD;

const Login: React.FC<LoginProps> = (props) => {
  // eslint-disable-next-line
  const { type: defaultType, enableInitPage, testMode } = props;
  const onOk = props.onOk || (() => Promise.resolve());
  const partnerCodeKey = `${defaultType || 'ADMIN'}_PARTNER_CODE`;

  const { state, dispatch } = useContext<TGlobalContext>(GlobalContext);
  const { tenantCode: tenantCodeFromState, locale, preChecking, permissionFetching, servicesFetching } = state;

  const { login, preCheck, fetchPermission, fetchServices } = useGlobalCommon();

  const tabVisible = !defaultType && defaultType !== 'TENANT';
  const defaultTheme = defaultType || 'ADMIN';
  const [loggingIn, setLoggingIn] = useState(false);
  const [type, setType] = useState<LoginType>(defaultType || 'WAREHOUSE');
  const [isSystemInitialized, setIsSystemInitialized] = useState(true);
  const [form] = Form.useForm();
  const [errorMsg, setErrorMsg] = useState('');
  const [errorOpen, setErrorOpen] = useState(false);
  const partnerCode = LocalStorageUtil.getItem(partnerCodeKey);
  const tenantCodeFromLocal = LocalStorageUtil.getItem(storageKeys.tenantCode, true);
  const tenantCodeInputRef = useRef<any>(null);
  const partnerCodeInputRef = useRef<any>(null);
  const accountInputRef = useRef<any>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const { from }: any = location.state || {
    from: { pathname: '/' },
  };

  const themeMap = {
    ADMIN: {
      btnClass: 'login-admin',
      img: 'bg-admin.svg',
    },
    WAREHOUSE: {
      btnClass:
        '!border--500 !bg-blue-500 border-blue-400 hover:!border-blue-400 hover:!bg-blue-400 focus:!border-blue-400 focus:!bg-blue-400 active:!bg-blue-600 active:!border-blue-600',
      img: 'bg-warehouse.svg',
    },
    SHOP: {
      btnClass:
        '!border-purple-500 !bg-purple-500 hover:!border-purple-400 hover:!bg-purple-400 focus:!border-purple-400 focus:!bg-purple-400 active:!bg-purple-600 active:!border-purple-600',
      img: 'bg-shop.svg',
    },
    FACTORY: {
      btnClass:
        '!border-green-600 !bg-green-600 hover:!border-green-500 hover:!bg-green-500 focus:!border-green-500 focus:!bg-green-500 active:!bg-green-700 active:!border-green-700',
      img: 'bg-factory.svg',
    },
    SAMPLE: {
      btnClass:
        '!border--500 !bg-lead-yellow border-yellow-400 hover:!border-yellow-400 hover:!bg-yellow-400 focus:!border-yellow-400 focus:!bg-yellow-400 active:!bg-yellow-600 active:!border-yellow-600',
      img: 'bg-sample.svg',
    },
  };

  const typeCodeDescMap = {
    WAREHOUSE: i18n.t('global.warehouseCode'),
    SHOP: i18n.t('global.shopCode'),
    FACTORY: i18n.t('global.factoryCode'),
    SAMPLE: i18n.t('global.warehouseCode'),
  };
  const typeCodeDesc = typeCodeDescMap[type];

  const onTypeChange = (key) => {
    setType(key);
  };

  // eslint-disable-next-line
  const onFinish = async (values) => {
    if (values.apiUrl) {
      setBaseUrl(values.apiUrl, testMode);
    }
    const finalType = type === 'SAMPLE' ? 'WAREHOUSE' : type;
    const payload = {
      ...values,
      // “样品端”使用仓库作为登陆单位
      partnerType: finalType,
    };
    if (type === 'TENANT') {
      payload.userType = type;
    } else {
      payload.empType = finalType;
    }
    // 测试模式或者（租户模式及开发环境）
    // 非测试模式则从GlobalContext中获取租户编码，common/App.tsx中会从URL获取并初始化
    if (testMode) {
      dispatch({ type: 'setTenantCode', payload: payload.tenantCode });
      LocalStorageUtil.setItem(storageKeys.tenantCode, payload.tenantCode, true);
    } else {
      payload.tenantCode = tenantCodeFromState;
    }
    // 租户缓存隔离(仅限与生产环境的测试模式下，开发环境不缓存)
    // 条件：生产环境 && 测试模式
    LocalStorageUtil.setTenantCode(payload.tenantCode);
    if (testMode) {
      // 如果当前租户缓存的语言存在则使用，否则使用默认全局的
      const currentLocale = LocalStorageUtil.getItem(LOCALE_KEY, true) || locale;
      LocalStorageUtil.setItem(LOCALE_KEY, currentLocale, true);
      dispatch({ type: 'setLocale', payload: currentLocale });
    }
    delete payload.apiUrl;

    try {
      // @ts-ignore
      await login(payload, {
        setErrorOpen,
        setErrorMsg,
        setLoggingIn,
      });
      dispatch({ type: 'setLoginType', payload: type });
      LocalStorageUtil.setItem(partnerCodeKey, values.partnerCode);

      await preCheck({
        setErrorOpen,
        setErrorMsg,
      });
      await fetchPermission();
      await fetchServices();

      onOk();

      if (enableInitPage) {
        if (!isSystemInitialized) {
          const resp: any = await ConfigApi.List({
            enablePage: false,
            code: 'WEB_INIT_TAG',
          });
          LocalStorageUtil.setItem('INIT', resp.data[0].value);
          setIsSystemInitialized(LocalStorageUtil.getItem('INIT'));
        }
      }

      if (from.pathname !== '/') {
        navigate(from, { replace: true });
      } else if (!enableInitPage || isSystemInitialized) {
        navigate({
          pathname: '/app',
        });
      } else {
        navigate({
          // pathname: '/app'
          pathname: '/init',
        });
      }
    } catch (e) {
      // eslint-disable-next-line
      console.log(e);
    }
  };

  const languageOnClick = (value) => {
    dispatch({ type: 'setLocale', payload: value });
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  const { img, btnClass } = themeMap[defaultTheme];

  useEffect(() => {
    setTimeout(() => {
      // 1. 如果为多租户模式且为开发模式，聚焦租户编码输入框
      // 2. 如果为普通登录模式，聚焦合作伙伴输入框
      // 3. 否则聚焦帐号输入框
      if (devMode) {
        tenantCodeInputRef.current?.focus({
          cursor: 'end',
        });
      } else if (type !== 'TENANT') {
        partnerCodeInputRef.current?.focus({
          cursor: 'end',
        });
      } else {
        accountInputRef.current?.focus({
          cursor: 'end',
        });
      }
    }, 300);
  }, [tenantCodeInputRef, partnerCodeInputRef, accountInputRef, type]);

  useEffect(() => {
    if (enableInitPage) {
      setIsSystemInitialized(false);
    }
  }, [enableInitPage]);

  useEffect(() => {
    setIsSystemInitialized(LocalStorageUtil.getItem('INIT'));
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      apiUrl: baseUrl,
    });
  }, [form]);

  return (
    <div className="login-page flex h-screen w-screen items-center justify-center">
      <div className="flex h-screen flex-1 flex-col items-center justify-between overflow-y-auto">
        <div className="flex flex-auto items-center justify-center">
          <div>
            <div className="my-6 flex items-center gap-x-3">
              {/* <LpgoIcon /> */}
              <Logo />
              <AppTag type={defaultTheme} />
            </div>
            {tabVisible && (
              <Tabs animated={false} defaultActiveKey="WAREHOUSE" onChange={onTypeChange}>
                <TabPane tab={i18n.t('global.warehouse')} key="WAREHOUSE" />
                <TabPane tab={i18n.t('global.shop')} key="SHOP" />
                <TabPane tab={i18n.t('global.factory')} key="FACTORY" />
              </Tabs>
            )}
            <Form
              form={form}
              layout="vertical"
              name="basic"
              size="large"
              className="w-[400px]"
              initialValues={{ rememberMe: false, partnerCode, tenantCode: tenantCodeFromLocal }}
              onFinish={onFinish}
            >
              {(testMode || devMode) && (
                <Form.Item
                  name="tenantCode"
                  label={
                    <span className="text-sm font-semibold uppercase leading-tight text-slate-500">
                      {i18n.t('global.tenantCode')}
                    </span>
                  }
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.pleaseInputTenatnCode'),
                    },
                  ]}
                >
                  <Input ref={tenantCodeInputRef} />
                </Form.Item>
              )}
              {type !== 'TENANT' && (
                <Form.Item
                  name="partnerCode"
                  label={
                    <span className="text-sm font-semibold uppercase leading-tight text-slate-500">{`${typeCodeDesc}`}</span>
                  }
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.inputCode'),
                    },
                  ]}
                >
                  <Input ref={partnerCodeInputRef} />
                </Form.Item>
              )}
              <Form.Item
                name="loginName"
                label={
                  <span className="text-sm font-semibold uppercase leading-tight text-slate-500">
                    {i18n.t('global.account')}
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: i18n.t('global.pleaseInputYourAccount'),
                  },
                ]}
              >
                <Input ref={accountInputRef} />
              </Form.Item>

              <Form.Item
                name="password"
                label={
                  <span className="text-sm font-semibold uppercase leading-tight text-slate-500">
                    {i18n.t('global.password')}
                  </span>
                }
                rules={[{ required: true, message: i18n.t('global.pleaseInputPassword') }]}
              >
                <Input.Password />
              </Form.Item>
              {testMode && (
                <Form.Item
                  name="apiUrl"
                  label={
                    <span className="text-sm font-semibold uppercase uppercase leading-tight text-slate-500">
                      {i18n.t('global.apiUrl')}
                    </span>
                  }
                  rules={[
                    {
                      required: true,
                      message: i18n.t('global.pleaseInputApiUrl'),
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              )}
              {errorOpen && (
                <Form.Item>
                  <Alert
                    type="error"
                    showIcon
                    icon={<CloseCircleFillIcon className="fill-red-500" />}
                    closeIcon={<CloseFillIcon className="h-6 w-6 fill-slate-400" />}
                    message={i18n.t('global.loginFailed')}
                    description={errorMsg}
                    onClose={() => setErrorOpen(false)}
                    closable
                  />
                </Form.Item>
              )}
              <Form.Item>
                <Button
                  className={classnames('h-12 w-full', btnClass)}
                  loading={loggingIn || preChecking || permissionFetching || servicesFetching}
                  type="primary"
                  htmlType="submit"
                >
                  {i18n.t('global.login')}
                </Button>
              </Form.Item>
              <Form.Item>
                <Form.Item noStyle name="rememberMe" valuePropName="checked">
                  <Checkbox>{i18n.t('global.rememberMe')}</Checkbox>
                </Form.Item>
                <Tooltip title={i18n.t('global.rememberMeExplain')} placement="right">
                  <InformationLineIcon className="inline-block fill-lead-blue" />
                </Tooltip>
              </Form.Item>
            </Form>
          </div>
        </div>
        <div className="flex h-12 w-full shrink-0 items-center justify-center text-sm font-medium leading-none text-slate-500">
          <GlobalLineIcon className="fill-slate-500" />
          <Select
            className="login-select w-24 text-sm font-medium leading-none text-slate-500"
            bordered={false}
            suffixIcon={<ArrowDownSLineIcon className="fill-slate-500" />}
            onChange={languageOnClick}
            value={locale}
          >
            {localeList.map((item) => (
              <Option key={item.code} value={item.code}>
                {item.name}
              </Option>
            ))}
          </Select>
        </div>
      </div>
      <div className="relative hidden h-screen flex-1 flex-col overflow-hidden lg:flex">
        <img
          alt="BACKGROUND"
          src={`img/${img}`}
          className="absolute bottom-0 left-0 right-0 top-0 -z-10 h-full w-full object-cover"
        />
        <div className="flex flex-auto flex-col items-center justify-center">
          <span className="text-2xl font-normal leading-10 text-white lg:text-3xl">Internet of Things (IoT)</span>
          <span className="text-3xl font-semibold leading-10 text-white lg:text-4xl">Reshape Your Business</span>
        </div>
        <div className="h-12">
          <Footer className="text-white" />
        </div>
      </div>
    </div>
  );
};

const LoginWrap: React.FC<LoginProps> = (props) => {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    LocalStorageUtil.resetPrefix();
    setInitialized(true);
  }, []);

  return initialized ? (
    <Login {...props} />
  ) : (
    <p className="p-4 text-lg text-lead-orange">{i18n.t('global.loading')}</p>
  );
};

export default LoginWrap;
