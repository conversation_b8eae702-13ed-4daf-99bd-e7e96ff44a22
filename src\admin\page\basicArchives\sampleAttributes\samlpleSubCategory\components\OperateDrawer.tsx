import { Form, Input, Switch } from 'antd';
import * as SampleSubCategoryApi from 'common/api/sample/SampleSubCategory';
import Drawer from 'common/components/Drawer';
import DrawerFooter from 'common/components/DrawerFooter';
import Spin from 'common/components/Spin';
import i18n from 'common/utils/I18n';
import * as NoticeUtil from 'common/utils/Notice';
import { usePermission } from 'common/utils/Permission';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { ISampleSubCategoryItem } from '../data';

interface IOperationModalProps {
  visible: boolean;
  confirmLoading: boolean;
  subCategoryId: string | undefined;
  onSubmit: (values: ISampleSubCategoryItem) => void;
  onCancel: () => void;
}
const OperateDrawer: React.FC<IOperationModalProps> = (props) => {
  const [form] = Form.useForm();
  const { onSubmit, onCancel, subCategoryId, visible, confirmLoading } = props;
  const codeInputRef = useRef<any>(null);
  const nameInputRef = useRef<any>(null);
  const [current, setCurrent] = useState<Record<string, any> | undefined>();
  const [loading, setLoading] = useState(false);
  const [permission] = usePermission('A:BASE:SAMPLE_ATTRIBUTE:SUB_CATEGORY');
  const deletePermission = permission.codes.includes('DELETE');

  const fetchSubCategory = useCallback(async () => {
    setLoading(true);
    try {
      const subCategory: any = await SampleSubCategoryApi.Get({
        id: subCategoryId,
      });
      if (typeof subCategory.disabled === 'boolean') {
        subCategory.status = !subCategory.disabled;
      }
      form.setFieldsValue(subCategory);
      setCurrent(subCategory);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }, [subCategoryId, form]);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (subCategoryId) fetchSubCategory();
      setTimeout(() => {
        if (subCategoryId) {
          nameInputRef.current.focus();
        } else {
          codeInputRef.current.focus();
        }
      }, 300);
    } else {
      setCurrent(undefined);
    }
  }, [subCategoryId, visible, form, fetchSubCategory]);

  const handleSubmit = () => {
    if (!form) return;
    form.submit();
  };

  const handleFinish = (values: { [key: string]: any }) => {
    if (onSubmit) {
      onSubmit(values as ISampleSubCategoryItem);
    }
  };

  const title: React.ReactNode = current ? (
    <>
      {i18n.t('global.editSubCategory')}
      {` [${current.code}]`}
    </>
  ) : (
    i18n.t('global.newSubCategory')
  );

  const onClose = () => {
    onCancel();
    form.resetFields();
  };

  const deleteBtnOnClick = async () => {
    NoticeUtil.confirm({
      title: i18n.t('global.confirmDelete'),
      content: `${current?.code} - ${current?.name}`,
      okType: 'danger',
      onOk: async () => {
        try {
          await SampleSubCategoryApi.Delete({ id: current?.id });
          NoticeUtil.success();
          onClose();
        } catch (e) {}
      },
    });
  };

  const onRecover = () => {
    fetchSubCategory();
  };

  return (
    <Drawer
      title={title}
      bodyStyle={{ padding: '12px 24px' }}
      destroyOnClose
      footer={
        <DrawerFooter
          applyBtnProps={{
            loading: confirmLoading,
          }}
          onApply={handleSubmit}
          deletePermission={deletePermission && !!subCategoryId}
          onDelete={deleteBtnOnClick}
          cancelPermission={!current}
          onCancel={onClose}
          recoverPermission={!!current}
          onRecover={onRecover}
        />
      }
      open={visible}
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Form layout="vertical" form={form} onFinish={handleFinish}>
          <Form.Item name="code" label={i18n.t('global.code')} rules={canNotBeNullRules}>
            <Input readOnly={!!current} ref={codeInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.name')} name="name" rules={canNotBeNullRules}>
            <Input ref={nameInputRef} />
          </Form.Item>
          <Form.Item label={i18n.t('global.remark')} name="remark">
            <Input.TextArea rows={3} />
          </Form.Item>
          {current && (
            <Form.Item label={i18n.t('global.status')} valuePropName="checked" name="status">
              <Switch />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Drawer>
  );
};

export default OperateDrawer;
