import { Form, InputNumber } from 'antd';
import Modal from 'common/components/Modal';
import i18n from 'common/utils/I18n';
import React, { useEffect, useImperativeHandle, useRef } from 'react';

export interface WeightModalPropsRef {
  setFieldsValue: (values: Record<string, any>) => void;
  reset: () => void;
  focus: () => void;
}

interface WeightModalProps {
  open: boolean;
  onClose: () => void;
  onOk: (values: Record<string, any>) => void;
  innerRef?: React.MutableRefObject<WeightModalPropsRef | undefined>;
}

const WeightModal: React.FC<WeightModalProps> = (props) => {
  const [form] = Form.useForm();
  const { open, innerRef, onClose } = props;
  const weightInputRef = useRef<any>(null);

  const canNotBeNullRules = [
    {
      required: true,
      message: i18n.t('global.fieldCanNotBeNull'),
    },
  ];

  const onFinish = (values: any) => {
    props.onOk(values);
  };

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        weightInputRef.current?.focus();
      }, 500);
    } else {
      form.resetFields();
    }
  }, [open, form]);

  useImperativeHandle(innerRef, () => ({
    setFieldsValue: (values) => {
      form.setFieldsValue(values);
    },
    reset: () => {
      form.resetFields();
    },
    focus: () => {
      setTimeout(() => {
        weightInputRef.current?.focus();
      }, 300);
    },
  }));

  return (
    <Modal
      width={350}
      title={i18n.t('global.inputWeight')}
      open={open}
      onCancel={onClose}
      onOk={() => {
        form.submit();
      }}
    >
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item name="weight" rules={canNotBeNullRules} label={i18n.t('global.weight')}>
          <InputNumber
            ref={weightInputRef}
            addonAfter={i18n.t('global.gram')}
            min={1}
            precision={0}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WeightModal;
